<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown Theme Integration Test</title>
    <link rel="stylesheet" href="frontend/src/styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 2rem;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 1px solid var(--color-border);
            border-radius: var(--radius);
            background: var(--color-surface);
        }
        
        .theme-selector {
            margin-bottom: 2rem;
            padding: 1rem;
            background: var(--color-card);
            border-radius: var(--radius);
        }
        
        .dropdown-examples {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            align-items: flex-start;
        }
        
        .example-group {
            min-width: 200px;
        }
        
        .example-title {
            font-weight: var(--font-weight-semibold);
            margin-bottom: 1rem;
            color: var(--color-text-strong);
        }
    </style>
</head>
<body data-theme="cerulean">
    <div class="theme-selector">
        <h2>Theme Selector</h2>
        <p>Select a theme to test dropdown styling:</p>
        <div class="field">
            <div class="control">
                <div class="select">
                    <select id="themeSelector">
                        <option value="cerulean">Cerulean (Light)</option>
                        <option value="darkly">Darkly (Dark)</option>
                        <option value="flatly">Flatly (Light)</option>
                        <option value="solarized">Solarized (Dark)</option>
                        <option value="flatly-dark">Flatly Dark</option>
                        <option value="mini-me">Mini Me</option>
                        <option value="the-brave">The Brave</option>
                        <option value="gunmetal-dark">Gunmetal Dark</option>
                        <option value="medium-light">Medium Light</option>
                        <option value="jet-black-electric-blue">Jet Black Electric Blue</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Dropdown Theme Integration Test</h2>
        <p>Test that dropdown menus follow the current theme colors and styling.</p>
        <p><strong>Layout Test:</strong> Icons and text should be side by side, not stacked vertically.</p>
        
        <div class="dropdown-examples">
            <!-- User Profile Dropdown (like in sidebar) -->
            <div class="example-group">
                <div class="example-title">User Profile Dropdown</div>
                <div class="dropdown" id="userDropdown">
                    <div class="dropdown-trigger">
                        <button class="button is-ghost" aria-haspopup="true" aria-controls="dropdown-menu">
                            <span class="icon">
                                <i class="fas fa-ellipsis-v"></i>
                            </span>
                        </button>
                    </div>
                    <div class="dropdown-menu" id="dropdown-menu" role="menu">
                        <div class="dropdown-content">
                            <a class="dropdown-item">
                                <span class="icon">
                                    <i class="fas fa-cog"></i>
                                </span>
                                <span>Settings</span>
                            </a>
                            <a class="dropdown-item">
                                <span class="icon">
                                    <i class="fas fa-shield-alt"></i>
                                </span>
                                <span>Admin Panel</span>
                            </a>
                            <hr class="dropdown-divider">
                            <a class="dropdown-item">
                                <span class="icon">
                                    <i class="fas fa-sign-out-alt"></i>
                                </span>
                                <span>Sign Out</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Note Actions Dropdown -->
            <div class="example-group">
                <div class="example-title">Note Actions Dropdown</div>
                <div class="dropdown" id="noteDropdown">
                    <div class="dropdown-trigger">
                        <button class="button is-small" aria-haspopup="true">
                            <span class="icon">
                                <i class="fas fa-ellipsis-v"></i>
                            </span>
                        </button>
                    </div>
                    <div class="dropdown-menu" role="menu">
                        <div class="dropdown-content">
                            <a class="dropdown-item">
                                <span class="icon">
                                    <i class="fas fa-edit"></i>
                                </span>
                                <span>Edit</span>
                            </a>
                            <a class="dropdown-item">
                                <span class="icon">
                                    <i class="fas fa-star"></i>
                                </span>
                                <span>Favorite</span>
                            </a>
                            <a class="dropdown-item">
                                <span class="icon">
                                    <i class="fas fa-archive"></i>
                                </span>
                                <span>Archive</span>
                            </a>
                            <hr class="dropdown-divider">
                            <a class="dropdown-item is-danger">
                                <span class="icon">
                                    <i class="fas fa-trash"></i>
                                </span>
                                <span>Delete</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right-aligned Dropdown -->
            <div class="example-group">
                <div class="example-title">Right-aligned Dropdown</div>
                <div class="dropdown is-right" id="rightDropdown">
                    <div class="dropdown-trigger">
                        <button class="button is-primary" aria-haspopup="true">
                            <span>Options</span>
                            <span class="icon">
                                <i class="fas fa-angle-down"></i>
                            </span>
                        </button>
                    </div>
                    <div class="dropdown-menu" role="menu">
                        <div class="dropdown-content">
                            <a class="dropdown-item">Option 1</a>
                            <a class="dropdown-item">Option 2</a>
                            <a class="dropdown-item">Option 3</a>
                            <hr class="dropdown-divider">
                            <a class="dropdown-item">Special Option</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>Expected Behavior</h3>
        <ul>
            <li>✅ Dropdown backgrounds should match the current theme's surface color</li>
            <li>✅ Dropdown borders should use the theme's border color</li>
            <li>✅ Dropdown text should use the theme's text color</li>
            <li>✅ Hover states should use the theme's surface-hover color</li>
            <li>✅ Dropdowns should have proper shadows that match the theme (darker for dark themes)</li>
            <li>✅ All animations and transitions should be smooth</li>
            <li>✅ Focus states should be clearly visible</li>
        </ul>
    </div>

    <script>
        // Theme switching functionality
        const themeSelector = document.getElementById('themeSelector');
        const body = document.body;

        themeSelector.addEventListener('change', function() {
            body.setAttribute('data-theme', this.value);
        });

        // Dropdown functionality
        function setupDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const trigger = dropdown.querySelector('.dropdown-trigger');
            
            trigger.addEventListener('click', function(e) {
                e.stopPropagation();
                
                // Close other dropdowns
                document.querySelectorAll('.dropdown.is-active').forEach(d => {
                    if (d !== dropdown) {
                        d.classList.remove('is-active');
                    }
                });
                
                // Toggle this dropdown
                dropdown.classList.toggle('is-active');
            });
        }

        // Setup all dropdowns
        setupDropdown('userDropdown');
        setupDropdown('noteDropdown');
        setupDropdown('rightDropdown');

        // Close dropdowns when clicking outside
        document.addEventListener('click', function() {
            document.querySelectorAll('.dropdown.is-active').forEach(dropdown => {
                dropdown.classList.remove('is-active');
            });
        });

        // Prevent dropdown from closing when clicking inside
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });

        // Log theme changes for debugging
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    console.log('Theme changed to:', body.getAttribute('data-theme'));
                }
            });
        });

        observer.observe(body, {
            attributes: true,
            attributeFilter: ['data-theme']
        });
    </script>
</body>
</html>
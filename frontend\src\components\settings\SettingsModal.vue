<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="closeModal"></div>
    <div class="modal-content">
      <SettingsPanel @close="closeModal" />
    </div>
  </div>
</template>

<script setup lang="ts">
import SettingsPanel from './SettingsPanel.vue'

interface Props {
  isOpen: boolean
}

interface Emits {
  (e: 'close'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const closeModal = () => {
  emit('close')
}
</script>

<style scoped>
.modal-content {
  width: 90%;
  max-width: 900px;
  margin: 0 auto;
}

@media screen and (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 1rem auto;
  }
}
</style>
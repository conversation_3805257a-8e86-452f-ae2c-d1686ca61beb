import express from 'express';
import { authenticateToken } from '../middleware/auth';
import { AuditLogRepository } from '../repositories/AuditLogRepository';
import { AuditLogFilter } from '../models/AuditLog';

const router = express.Router();

// Get audit logs with filtering (admin only)
router.get('/logs', authenticateToken, async (req, res) => {
  try {
    // TODO: Add admin role check when role system is implemented
    // For now, users can only see their own audit logs
    const userId = req.user!.id;
    
    const filter: AuditLogFilter = {
      user_id: userId, // Restrict to user's own logs for now
      action: req.query.action as string,
      resource_type: req.query.resource_type as string,
      resource_id: req.query.resource_id as string,
      ip_address: req.query.ip_address as string,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
      offset: req.query.offset ? parseInt(req.query.offset as string) : 0
    };

    // Parse date filters
    if (req.query.start_date) {
      filter.start_date = new Date(req.query.start_date as string);
    }
    if (req.query.end_date) {
      filter.end_date = new Date(req.query.end_date as string);
    }

    const result = await AuditLogRepository.findByFilter(filter);
    
    res.json({
      logs: result.logs,
      total: result.total,
      limit: filter.limit,
      offset: filter.offset
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    res.status(500).json({ error: 'Failed to fetch audit logs' });
  }
});

// Get specific audit log by ID
router.get('/logs/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const auditLog = await AuditLogRepository.findById(id);
    
    if (!auditLog) {
      return res.status(404).json({ error: 'Audit log not found' });
    }

    // Users can only see their own audit logs (unless admin)
    if (auditLog.user_id !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    return res.json(auditLog);
  } catch (error) {
    console.error('Error fetching audit log:', error);
    return res.status(500).json({ error: 'Failed to fetch audit log' });
  }
});

// Get user's own audit logs
router.get('/my-logs', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;
    const offset = req.query.offset ? parseInt(req.query.offset as string) : 0;

    const logs = await AuditLogRepository.findByUserId(userId, limit, offset);
    
    res.json({
      logs,
      limit,
      offset
    });
  } catch (error) {
    console.error('Error fetching user audit logs:', error);
    res.status(500).json({ error: 'Failed to fetch audit logs' });
  }
});

// Get security events (recent authentication attempts, failures, etc.)
router.get('/security-events', authenticateToken, async (req, res) => {
  try {
    // TODO: Add admin role check when role system is implemented
    const hours = req.query.hours ? parseInt(req.query.hours as string) : 24;
    
    const events = await AuditLogRepository.findSecurityEvents(hours);
    
    // Filter to user's own events for now (until admin system is implemented)
    const userEvents = events.filter(event => event.user_id === req.user!.id);
    
    res.json({
      events: userEvents,
      hours
    });
  } catch (error) {
    console.error('Error fetching security events:', error);
    res.status(500).json({ error: 'Failed to fetch security events' });
  }
});

// Get failed attempts (for security monitoring)
router.get('/failed-attempts', authenticateToken, async (req, res) => {
  try {
    // TODO: Add admin role check when role system is implemented
    const hours = req.query.hours ? parseInt(req.query.hours as string) : 1;
    
    const attempts = await AuditLogRepository.findFailedAttempts(hours);
    
    // Filter to user's own attempts for now (until admin system is implemented)
    const userAttempts = attempts.filter(attempt => 
      attempt.user_id === req.user!.id || 
      attempt.ip_address === req.ip // Include attempts from same IP
    );
    
    res.json({
      attempts: userAttempts,
      hours
    });
  } catch (error) {
    console.error('Error fetching failed attempts:', error);
    res.status(500).json({ error: 'Failed to fetch failed attempts' });
  }
});

// Get audit statistics
router.get('/statistics', authenticateToken, async (req, res) => {
  try {
    // TODO: Add admin role check when role system is implemented
    const days = req.query.days ? parseInt(req.query.days as string) : 30;
    
    const stats = await AuditLogRepository.getStatistics(days);
    
    res.json({
      statistics: stats,
      days
    });
  } catch (error) {
    console.error('Error fetching audit statistics:', error);
    res.status(500).json({ error: 'Failed to fetch audit statistics' });
  }
});

// Export audit logs (for compliance)
router.get('/export', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    
    const filter: AuditLogFilter = {
      user_id: userId, // Users can only export their own logs
      limit: 10000 // Large limit for export
    };

    // Parse date filters
    if (req.query.start_date) {
      filter.start_date = new Date(req.query.start_date as string);
    }
    if (req.query.end_date) {
      filter.end_date = new Date(req.query.end_date as string);
    }

    const result = await AuditLogRepository.findByFilter(filter);
    
    // Set headers for file download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="audit-logs-${userId}-${new Date().toISOString().split('T')[0]}.json"`);
    
    res.json({
      export_date: new Date().toISOString(),
      user_id: userId,
      total_logs: result.total,
      date_range: {
        start: filter.start_date?.toISOString(),
        end: filter.end_date?.toISOString()
      },
      logs: result.logs
    });
  } catch (error) {
    console.error('Error exporting audit logs:', error);
    res.status(500).json({ error: 'Failed to export audit logs' });
  }
});

export default router;
/* Dropdown Components - Theme Integration */

/* Base dropdown styling */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown.is-active .dropdown-menu {
  display: block;
}

.dropdown-trigger {
  cursor: pointer;
}

/* Dropdown menu container */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000 !important;
  display: none;
  min-width: 12rem;
  padding-top: 0;
}

/* Right-aligned dropdowns */
.dropdown.is-right .dropdown-menu {
  left: auto;
  right: 0;
}

/* Dropdown content box */
.dropdown-content {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  padding: 0.5rem 0;
  max-height: 20rem;
  overflow-y: auto;
  margin-top: 0;
  z-index: 1001 !important;
  position: relative !important;
}

/* Dropdown items */
.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--color-text);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
  font-size: var(--font-size-sm);
  line-height: 1.2;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background: var(--color-surface-hover);
  color: var(--color-primary);
  outline: none;
}

.dropdown-item:active {
  background: var(--color-primary-alpha);
}

/* Dropdown item icons */
.dropdown-item .icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: opacity var(--transition-fast);
  font-size: 0.875rem;
}

/* Ensure icons and text are properly aligned */
.dropdown-item > .icon {
  margin-right: 0;
}

.dropdown-item > span:not(.icon) {
  flex: 1;
  display: inline-block;
}

/* Force horizontal layout for dropdown items */
.dropdown-content .dropdown-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 0.75rem !important;
}

/* Specific styling for user dropdown items */
.user-actions .dropdown-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 0.75rem !important;
  padding: 0.75rem 1rem !important;
}

.user-actions .dropdown-item .icon {
  flex-shrink: 0 !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
  margin-right: 0 !important;
}

.user-actions .dropdown-item span:not(.icon) {
  flex: 1 !important;
  white-space: nowrap !important;
}

.dropdown-item:hover .icon,
.dropdown-item:focus .icon {
  opacity: 1;
}

/* Dropdown divider */
.dropdown-divider {
  height: 1px;
  margin: 0.25rem 0;
  background: var(--color-border);
  border: none;
  opacity: 0.3;
}

/* User dropdown specific divider */
.user-actions .dropdown-divider {
  margin: 0.5rem 0;
  background: var(--color-border);
  opacity: 0.2;
}

/* Special dropdown item types */
.dropdown-item.is-danger {
  color: var(--color-danger);
}

.dropdown-item.is-danger:hover,
.dropdown-item.is-danger:focus {
  background: var(--color-danger-alpha);
  color: var(--color-danger);
}

.dropdown-item.is-warning {
  color: var(--color-warning);
}

.dropdown-item.is-warning:hover,
.dropdown-item.is-warning:focus {
  background: var(--color-warning-alpha);
  color: var(--color-warning);
}

/* User dropdown specific styling */
.user-actions .dropdown-menu {
  min-width: 10rem;
}

.user-actions .dropdown-item {
  font-size: 0.875rem;
}

/* Note actions dropdown styling */
.note-actions .dropdown-menu,
.note-item-actions .dropdown-menu {
  min-width: 8rem;
  z-index: 10000;
}

.note-actions .dropdown-content,
.note-item-actions .dropdown-content {
  padding: 0.25rem 0;
}

.note-actions .dropdown-item,
.note-item-actions .dropdown-item {
  padding: 0.375rem 0.75rem;
  font-size: 0.8125rem;
}

/* Dropdown animations */
.dropdown-menu {
  opacity: 0;
  transform: translateY(-0.5rem);
  transition: all var(--transition-fast);
  pointer-events: none;
}

.dropdown.is-active .dropdown-menu {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .dropdown-menu {
    min-width: 10rem;
    max-width: calc(100vw - 2rem);
  }

  .dropdown.is-right .dropdown-menu {
    left: auto;
    right: 0;
    transform-origin: top right;
  }

  .dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 0.9375rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .dropdown-content {
    border-width: 2px;
  }

  .dropdown-item {
    border-bottom: 1px solid transparent;
  }

  .dropdown-item:hover,
  .dropdown-item:focus {
    border-bottom-color: var(--color-primary);
  }
}

/* Dark theme specific adjustments */
[data-theme*='dark'] .dropdown-content,
[data-theme*='cyborg'] .dropdown-content,
[data-theme*='slate'] .dropdown-content {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Light theme specific adjustments */
[data-theme*='light'] .dropdown-content,
[data-theme*='cerulean'] .dropdown-content,
[data-theme*='flatly'] .dropdown-content {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Focus trap for accessibility */
.dropdown.is-active .dropdown-content {
  outline: none;
}

.dropdown-item:focus {
  position: relative;
}

.dropdown-item:focus::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid var(--color-primary);
  border-radius: var(--radius-sm);
  pointer-events: none;
}

/* Scrollbar styling for long dropdown lists */
.dropdown-content::-webkit-scrollbar {
  width: 0.25rem;
}

.dropdown-content::-webkit-scrollbar-track {
  background: var(--color-surface);
}

.dropdown-content::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-sm);
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-muted);
}

/* Debug and override any conflicting styles */
.dropdown-item,
.dropdown-content .dropdown-item,
.user-actions .dropdown-item,
a.dropdown-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 0.75rem !important;
  text-align: left !important;
  white-space: nowrap !important;
}

/* Ensure router-link dropdown items also follow the layout */
router-link.dropdown-item,
.dropdown-content router-link.dropdown-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 0.75rem !important;
}

/* Icon container specific styling */
.dropdown-item .icon,
.dropdown-content .dropdown-item .icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
  flex-shrink: 0 !important;
  margin: 0 !important;
}

/* Text span specific styling */
.dropdown-item > span:not(.icon),
.dropdown-content .dropdown-item > span:not(.icon) {
  display: inline-block !important;
  flex: 1 !important;
  line-height: 1.2 !important;
}

/* Override any Bulma conflicting styles */
.dropdown-item:not(.is-active):not(:hover) {
  background: transparent !important;
}

/* Ensure proper spacing and alignment */
.dropdown-content {
  min-width: 12rem !important;
  padding: 0.5rem 0 !important;
}

.user-actions .dropdown-content {
  min-width: 10rem !important;
}

/* Fix for black bar at top of dropdown */
.dropdown-menu {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.dropdown-content {
  margin-top: 0 !important;
  border-top: 1px solid var(--color-border) !important;
}

/* Solarized theme specific fixes */
[data-theme='solarized'] .dropdown-content {
  border-color: var(--color-border) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Remove any potential dark borders or shadows */
.dropdown-menu::before,
.dropdown-content::before {
  display: none !important;
}

/* Ensure no pseudo-elements are creating the black bar */
.dropdown-menu::after,
.dropdown-content::after {
  display: none !important;
}

/* Force clean borders */
.user-actions .dropdown-content {
  border-top: 1px solid var(--color-border) !important;
  border-left: 1px solid var(--color-border) !important;
  border-right: 1px solid var(--color-border) !important;
  border-bottom: 1px solid var(--color-border) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Uniform sizing for small action buttons */
.sidebar-section-header .button.is-small,
.user-actions .button.is-small,
.collapsed-create-button .button.is-small,
.header-actions .button.is-small {
  width: 2rem !important;
  height: 2rem !important;
  min-width: 2rem !important;
  min-height: 2rem !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1 !important;
  position: relative !important;
}

/* Ensure icons in small buttons are consistent */
.sidebar-section-header .button.is-small .icon,
.user-actions .button.is-small .icon,
.collapsed-create-button .button.is-small .icon,
.header-actions .button.is-small .icon {
  width: 1rem !important;
  height: 1rem !important;
  font-size: 0.875rem !important;
  margin: 0 !important;
}

/* Specific styling for dropdown trigger buttons */
.dropdown-trigger .button.is-small {
  width: 2rem !important;
  height: 2rem !important;
  min-width: 2rem !important;
  min-height: 2rem !important;
  padding: 0 !important;
  z-index: 1 !important;
  position: relative !important;
}

/* Ensure consistent button styling across all small action buttons */
.button.is-ghost.is-small {
  border: 1px solid transparent !important;
  background: transparent !important;
  transition: all var(--transition-fast) !important;
}

.button.is-ghost.is-small:hover {
  background: var(--color-surface-hover) !important;
  border-color: var(--color-border) !important;
}
/* Aggressive override to remove any black bars or unwanted styling */
.dropdown-menu,
.dropdown-content,
.user-actions .dropdown-menu,
.user-actions .dropdown-content {
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius) !important;
  margin: 0 !important;
  padding-top: 0 !important;
  outline: none !important;
}

/* Remove any potential overlay or pseudo-element causing the black bar */
.dropdown-menu > *:first-child,
.dropdown-content > *:first-child {
  margin-top: 0 !important;
  border-top: none !important;
}

/* Ensure dropdown content has clean top edge */
.dropdown-content {
  padding-top: 0.5rem !important;
  border-top-left-radius: var(--radius) !important;
  border-top-right-radius: var(--radius) !important;
}

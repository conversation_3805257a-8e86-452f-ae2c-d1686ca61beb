import { getDatabase } from './database';
import { promisify } from 'util';

export interface Migration {
  version: number;
  name: string;
  up: () => Promise<void>;
  down: () => Promise<void>;
}

export class MigrationManager {
  private static getDb() {
    return getDatabase();
  }

  static getDbPublic() {
    return getDatabase();
  }

  static async initializeMigrationTable(): Promise<void> {
    const db = this.getDb();
    const runAsync = promisify(db.run.bind(db));

    await runAsync(`
      CREATE TABLE IF NOT EXISTS migrations (
        version INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
  }

  static async getExecutedMigrations(): Promise<number[]> {
    const db = this.getDb();
    const allAsync = promisify(db.all.bind(db));

    const rows = await allAsync('SELECT version FROM migrations ORDER BY version') as any[];
    return rows.map((row: any) => row.version);
  }

  static async executeMigration(migration: Migration): Promise<void> {
    const db = this.getDb();
    const runAsync = promisify(db.run.bind(db));

    try {
      await migration.up();
      await new Promise<void>((resolve, reject) => {
        db.run('INSERT INTO migrations (version, name) VALUES (?, ?)', 
          [migration.version, migration.name], 
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
      console.log(`Migration ${migration.version} (${migration.name}) executed successfully`);
    } catch (error) {
      console.error(`Failed to execute migration ${migration.version}:`, error);
      throw error;
    }
  }

  static async rollbackMigration(migration: Migration): Promise<void> {
    const db = this.getDb();
    const runAsync = promisify(db.run.bind(db));

    try {
      await migration.down();
      await new Promise<void>((resolve, reject) => {
        db.run('DELETE FROM migrations WHERE version = ?', 
          [migration.version], 
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
      console.log(`Migration ${migration.version} (${migration.name}) rolled back successfully`);
    } catch (error) {
      console.error(`Failed to rollback migration ${migration.version}:`, error);
      throw error;
    }
  }

  static async runMigrations(migrations: Migration[]): Promise<void> {
    await this.initializeMigrationTable();
    const executedVersions = await this.getExecutedMigrations();

    const pendingMigrations = migrations.filter(
      migration => !executedVersions.includes(migration.version)
    );

    if (pendingMigrations.length === 0) {
      console.log('No pending migrations');
      return;
    }

    console.log(`Running ${pendingMigrations.length} pending migrations...`);

    for (const migration of pendingMigrations.sort((a, b) => a.version - b.version)) {
      await this.executeMigration(migration);
    }

    console.log('All migrations completed successfully');
  }
}

// Define migrations
export const migrations: Migration[] = [
  {
    version: 1,
    name: 'add_note_indexes',
    up: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      // Additional indexes for better performance
      await runAsync('CREATE INDEX IF NOT EXISTS idx_notes_note_type ON notes (note_type)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_notes_is_archived ON notes (is_archived)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_notes_group_id ON notes (group_id)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_note_versions_created_at ON note_versions (created_at)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_tags_name ON tags (name)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_note_tags_note_id ON note_tags (note_id)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_note_tags_tag_id ON note_tags (tag_id)');
    },
    down: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      await runAsync('DROP INDEX IF EXISTS idx_notes_note_type');
      await runAsync('DROP INDEX IF EXISTS idx_notes_is_archived');
      await runAsync('DROP INDEX IF EXISTS idx_notes_group_id');
      await runAsync('DROP INDEX IF EXISTS idx_note_versions_created_at');
      await runAsync('DROP INDEX IF EXISTS idx_tags_name');
      await runAsync('DROP INDEX IF EXISTS idx_note_tags_note_id');
      await runAsync('DROP INDEX IF EXISTS idx_note_tags_tag_id');
    }
  },
  {
    version: 2,
    name: 'add_full_text_search',
    up: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      // Create FTS virtual table for full-text search
      await runAsync(`
        CREATE VIRTUAL TABLE IF NOT EXISTS notes_fts USING fts5(
          note_id UNINDEXED,
          title,
          content,
          content='notes',
          content_rowid='rowid'
        )
      `);

      // Create triggers to keep FTS table in sync
      await runAsync(`
        CREATE TRIGGER IF NOT EXISTS notes_fts_insert AFTER INSERT ON notes BEGIN
          INSERT INTO notes_fts(note_id, title, content) VALUES (new.id, new.title, new.content);
        END
      `);

      await runAsync(`
        CREATE TRIGGER IF NOT EXISTS notes_fts_delete AFTER DELETE ON notes BEGIN
          DELETE FROM notes_fts WHERE note_id = old.id;
        END
      `);

      await runAsync(`
        CREATE TRIGGER IF NOT EXISTS notes_fts_update AFTER UPDATE ON notes BEGIN
          DELETE FROM notes_fts WHERE note_id = old.id;
          INSERT INTO notes_fts(note_id, title, content) VALUES (new.id, new.title, new.content);
        END
      `);

      // Populate FTS table with existing data
      await runAsync(`
        INSERT INTO notes_fts(note_id, title, content)
        SELECT id, title, content FROM notes
      `);
    },
    down: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      await runAsync('DROP TRIGGER IF EXISTS notes_fts_insert');
      await runAsync('DROP TRIGGER IF EXISTS notes_fts_delete');
      await runAsync('DROP TRIGGER IF EXISTS notes_fts_update');
      await runAsync('DROP TABLE IF EXISTS notes_fts');
    }
  },
  {
    version: 3,
    name: 'disable_fts_completely',
    up: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      try {
        // Drop all possible FTS triggers
        await runAsync('DROP TRIGGER IF EXISTS notes_fts_insert');
        await runAsync('DROP TRIGGER IF EXISTS notes_fts_delete');
        await runAsync('DROP TRIGGER IF EXISTS notes_fts_update');
        
        // Drop FTS table
        await runAsync('DROP TABLE IF EXISTS notes_fts');
        
        console.log('FTS functionality disabled successfully');
      } catch (error) {
        console.log('FTS cleanup completed (some items may not have existed)');
      }
    },
    down: async () => {
      console.log('FTS functionality can be re-enabled later');
    }
  },
  {
    version: 4,
    name: 'recreate_fts_with_extended_schema',
    up: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      // Create FTS virtual table with extended schema (without external content options)
      await runAsync(`
        CREATE VIRTUAL TABLE IF NOT EXISTS notes_fts USING fts5(
          note_id UNINDEXED,
          title,
          content,
          note_type UNINDEXED,
          user_id UNINDEXED,
          tags UNINDEXED
        )
      `);

      // Create triggers to keep FTS table in sync
      await runAsync(`
        CREATE TRIGGER IF NOT EXISTS notes_fts_insert AFTER INSERT ON notes BEGIN
          INSERT INTO notes_fts(note_id, title, content, note_type, user_id, tags)
          VALUES (new.id, new.title, new.content, new.note_type, new.user_id, '');
        END
      `);

      await runAsync(`
        CREATE TRIGGER IF NOT EXISTS notes_fts_update AFTER UPDATE ON notes BEGIN
          UPDATE notes_fts SET 
            title = new.title,
            content = new.content,
            note_type = new.note_type,
            user_id = new.user_id
          WHERE note_id = new.id;
        END
      `);

      await runAsync(`
        CREATE TRIGGER IF NOT EXISTS notes_fts_delete AFTER DELETE ON notes BEGIN
          DELETE FROM notes_fts WHERE note_id = old.id;
        END
      `);

      // Populate FTS table with existing data
      await runAsync(`
        INSERT INTO notes_fts(note_id, title, content, note_type, user_id, tags)
        SELECT id, title, content, note_type, user_id, '' FROM notes
      `);

      console.log('FTS functionality recreated with corrected schema');
    },
    down: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      await runAsync('DROP TRIGGER IF EXISTS notes_fts_insert');
      await runAsync('DROP TRIGGER IF EXISTS notes_fts_delete');
      await runAsync('DROP TRIGGER IF EXISTS notes_fts_update');
      await runAsync('DROP TABLE IF EXISTS notes_fts');
    }
  },
  {
    version: 5,
    name: 'add_templates_table',
    up: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      // Create templates table
      await runAsync(`
        CREATE TABLE IF NOT EXISTS templates (
          id TEXT PRIMARY KEY,
          user_id TEXT NOT NULL,
          group_id TEXT,
          name TEXT NOT NULL,
          description TEXT,
          note_type TEXT NOT NULL CHECK (note_type IN ('richtext', 'markdown', 'kanban')),
          content TEXT NOT NULL,
          is_public BOOLEAN DEFAULT 0,
          metadata TEXT DEFAULT '{}',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
          FOREIGN KEY (group_id) REFERENCES groups (id) ON DELETE CASCADE
        )
      `);

      // Create template_tags junction table
      await runAsync(`
        CREATE TABLE IF NOT EXISTS template_tags (
          template_id TEXT NOT NULL,
          tag_id TEXT NOT NULL,
          PRIMARY KEY (template_id, tag_id),
          FOREIGN KEY (template_id) REFERENCES templates (id) ON DELETE CASCADE,
          FOREIGN KEY (tag_id) REFERENCES tags (id) ON DELETE CASCADE
        )
      `);

      // Create indexes for better performance
      await runAsync('CREATE INDEX IF NOT EXISTS idx_templates_user_id ON templates (user_id)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_templates_group_id ON templates (group_id)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_templates_note_type ON templates (note_type)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_templates_is_public ON templates (is_public)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_templates_created_at ON templates (created_at)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_templates_updated_at ON templates (updated_at)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_template_tags_template_id ON template_tags (template_id)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_template_tags_tag_id ON template_tags (tag_id)');
    },
    down: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      await runAsync('DROP INDEX IF EXISTS idx_templates_user_id');
      await runAsync('DROP INDEX IF EXISTS idx_templates_group_id');
      await runAsync('DROP INDEX IF EXISTS idx_templates_note_type');
      await runAsync('DROP INDEX IF EXISTS idx_templates_is_public');
      await runAsync('DROP INDEX IF EXISTS idx_templates_created_at');
      await runAsync('DROP INDEX IF EXISTS idx_templates_updated_at');
      await runAsync('DROP INDEX IF EXISTS idx_template_tags_template_id');
      await runAsync('DROP INDEX IF EXISTS idx_template_tags_tag_id');
      await runAsync('DROP TABLE IF EXISTS template_tags');
      await runAsync('DROP TABLE IF EXISTS templates');
    }
  },
  {
    version: 6,
    name: 'add_audit_logs_table',
    up: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      // Create audit logs table for comprehensive logging
      await runAsync(`
        CREATE TABLE IF NOT EXISTS audit_logs (
          id TEXT PRIMARY KEY,
          user_id TEXT,
          session_id TEXT,
          action TEXT NOT NULL,
          resource_type TEXT NOT NULL,
          resource_id TEXT,
          ip_address TEXT NOT NULL,
          user_agent TEXT,
          request_method TEXT NOT NULL,
          request_path TEXT NOT NULL,
          request_body TEXT,
          response_status INTEGER NOT NULL,
          response_time_ms INTEGER NOT NULL,
          metadata TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
        )
      `);

      // Create indexes for performance
      await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs (user_id)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs (action)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON audit_logs (resource_type)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_id ON audit_logs (resource_id)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs (ip_address)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs (created_at)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_response_status ON audit_logs (response_status)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_session_id ON audit_logs (session_id)');
    },
    down: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      await runAsync('DROP INDEX IF EXISTS idx_audit_logs_user_id');
      await runAsync('DROP INDEX IF EXISTS idx_audit_logs_action');
      await runAsync('DROP INDEX IF EXISTS idx_audit_logs_resource_type');
      await runAsync('DROP INDEX IF EXISTS idx_audit_logs_resource_id');
      await runAsync('DROP INDEX IF EXISTS idx_audit_logs_ip_address');
      await runAsync('DROP INDEX IF EXISTS idx_audit_logs_created_at');
      await runAsync('DROP INDEX IF EXISTS idx_audit_logs_response_status');
      await runAsync('DROP INDEX IF EXISTS idx_audit_logs_session_id');
      await runAsync('DROP TABLE IF EXISTS audit_logs');
    }
  },
  {
    version: 7,
    name: 'add_admin_notifications_table',
    up: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      // Create admin notifications table
      await runAsync(`
        CREATE TABLE IF NOT EXISTS admin_notifications (
          id TEXT PRIMARY KEY,
          type TEXT NOT NULL CHECK (type IN ('critical', 'warning', 'info', 'success')),
          category TEXT NOT NULL CHECK (category IN ('content_report', 'user_action', 'system', 'security')),
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          read BOOLEAN DEFAULT 0,
          action_url TEXT,
          metadata TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME
        )
      `);

      // Create indexes for performance
      await runAsync('CREATE INDEX IF NOT EXISTS idx_admin_notifications_type ON admin_notifications (type)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_admin_notifications_category ON admin_notifications (category)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_admin_notifications_read ON admin_notifications (read)');
      await runAsync('CREATE INDEX IF NOT EXISTS idx_admin_notifications_created_at ON admin_notifications (created_at)');
      
      // Insert some sample notifications for testing
      const sampleNotifications = [
        {
          id: 'notif-1',
          type: 'critical',
          category: 'content_report',
          title: 'High Priority Content Report',
          message: 'New content report flagged as inappropriate content requiring immediate attention',
          read: 0,
          action_url: '/admin/reports',
          created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        },
        {
          id: 'notif-2',
          type: 'warning',
          category: 'system',
          title: 'High Error Rate Detected',
          message: 'System error rate exceeded 5% in the last hour. Please investigate server performance.',
          read: 0,
          action_url: '/admin/metrics',
          created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString()
        },
        {
          id: 'notif-3',
          type: 'info',
          category: 'user_action',
          title: 'New User Registration Spike',
          message: '25 new users registered in the last hour, which is above normal activity levels',
          read: 1,
          action_url: '/admin/users',
          created_at: new Date(Date.now() - 60 * 60 * 1000).toISOString()
        }
      ];

      for (const notification of sampleNotifications) {
        await new Promise<void>((resolve, reject) => {
          const stmt = db.prepare(`
            INSERT INTO admin_notifications (
              id, type, category, title, message, read, action_url, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `);
          
          stmt.run([
            notification.id,
            notification.type,
            notification.category,
            notification.title,
            notification.message,
            notification.read,
            notification.action_url,
            notification.created_at
          ], function(err) {
            if (err) {
              reject(err);
            } else {
              resolve();
            }
          });
          
          stmt.finalize();
        });
      }
    },
    down: async () => {
      const db = MigrationManager.getDbPublic();
      const runAsync = promisify(db.run.bind(db));

      await runAsync('DROP INDEX IF EXISTS idx_admin_notifications_type');
      await runAsync('DROP INDEX IF EXISTS idx_admin_notifications_category');
      await runAsync('DROP INDEX IF EXISTS idx_admin_notifications_read');
      await runAsync('DROP INDEX IF EXISTS idx_admin_notifications_created_at');
      await runAsync('DROP TABLE IF EXISTS admin_notifications');
    }
  }
];
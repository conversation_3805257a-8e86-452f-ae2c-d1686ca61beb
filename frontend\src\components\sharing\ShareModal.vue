<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="closeModal"></div>
    <div class="modal-card note-sharing-modal">
      <header class="modal-card-head">
        <p class="modal-card-title">Share Note</p>
        <button class="delete" @click="closeModal" aria-label="close"></button>
      </header>

      <section class="modal-card-body">
        <div class="sharing-container">
          <div v-if="error" class="notification is-danger">
            <button class="delete" @click="clearError"></button>
            {{ error }}
          </div>

          <!-- Note Info -->
          <div class="sharing-section">
            <div class="note-info-card">
              <div class="media">
                <div class="media-left">
                  <span class="icon is-large">
                    <i class="fas fa-file-alt" :class="getNoteTypeIcon(note?.noteType)"></i>
                  </span>
                </div>
                <div class="media-content">
                  <p class="title is-5">{{ note?.title }}</p>
                  <p class="subtitle is-6">{{ getNoteTypeDisplayName(note?.noteType) }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Existing Shares -->
          <div v-if="noteShares.length > 0" class="sharing-section">
            <h4 class="section-title">Existing Shares</h4>
            <div class="existing-shares-container">
              <div v-for="share in noteShares" :key="share.id" class="mb-4">
                <ShareItem 
                  :share="share" 
                  @update="handleUpdateShare"
                  @delete="handleDeleteShare"
                  @copy-url="handleCopyUrl"
                  @view-logs="handleViewLogs"
                />
              </div>
            </div>
          </div>

          <!-- Note Visibility -->
          <div class="sharing-section">
            <h4 class="section-title">Note Visibility</h4>
            <div class="field">
              <div class="control">
                <label class="radio">
                  <input
                    v-model="newShare.accessLevel"
                    value="private"
                    type="radio"
                    name="visibility"
                  />
                  <span class="radio-content">
                    <i class="fas fa-lock"></i>
                    <div class="radio-text">
                      <strong>Private</strong>
                      <p>Only you can access this note</p>
                    </div>
                  </span>
                </label>
              </div>
              
              <div class="control">
                <label class="radio">
                  <input
                    v-model="newShare.accessLevel"
                    value="shared"
                    type="radio"
                    name="visibility"
                  />
                  <span class="radio-content">
                    <i class="fas fa-users"></i>
                    <div class="radio-text">
                      <strong>Shared</strong>
                      <p>Share with specific people</p>
                    </div>
                  </span>
                </label>
              </div>

              <div class="control">
                <label class="radio">
                  <input
                    v-model="newShare.accessLevel"
                    value="unlisted"
                    type="radio"
                    name="visibility"
                  />
                  <span class="radio-content">
                    <i class="fas fa-link"></i>
                    <div class="radio-text">
                      <strong>Unlisted</strong>
                      <p>Anyone with the link can access</p>
                    </div>
                  </span>
                </label>
              </div>

              <div class="control">
                <label class="radio">
                  <input
                    v-model="newShare.accessLevel"
                    value="public"
                    type="radio"
                    name="visibility"
                  />
                  <span class="radio-content">
                    <i class="fas fa-globe"></i>
                    <div class="radio-text">
                      <strong>Public</strong>
                      <p>Anyone can find and access this note</p>
                    </div>
                  </span>
                </label>
              </div>
            </div>
          </div>

          <!-- Permissions -->
          <div class="sharing-section">
            <h4 class="section-title">Permissions</h4>
            <div class="permissions-container">
              <div class="permission-item">
                <div class="permission-icon">
                  <i class="fas fa-eye"></i>
                </div>
                <div class="permission-details">
                  <label class="checkbox">
                    <input 
                      type="checkbox" 
                      value="view" 
                      v-model="newShare.permissions"
                      :disabled="true"
                    >
                    <strong>View</strong>
                  </label>
                  <p>Can view the note content (always included)</p>
                </div>
              </div>

              <div class="permission-item">
                <div class="permission-icon">
                  <i class="fas fa-comment"></i>
                </div>
                <div class="permission-details">
                  <label class="checkbox">
                    <input 
                      type="checkbox" 
                      value="comment" 
                      v-model="newShare.permissions"
                    >
                    <strong>Comment</strong>
                  </label>
                  <p>Can add comments to the note</p>
                </div>
              </div>

              <div class="permission-item">
                <div class="permission-icon">
                  <i class="fas fa-edit"></i>
                </div>
                <div class="permission-details">
                  <label class="checkbox">
                    <input 
                      type="checkbox" 
                      value="edit" 
                      v-model="newShare.permissions"
                    >
                    <strong>Edit</strong>
                  </label>
                  <p>Can edit the note content</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Advanced Options -->
          <div class="sharing-section">
            <h4 class="section-title">Advanced Options</h4>
            
            <!-- Expiration Date -->
            <div class="advanced-option">
              <label class="checkbox">
                <input type="checkbox" v-model="hasExpiration">
                <strong>Set Expiration Date</strong>
              </label>
              <p class="help">Automatically disable sharing after a specific date</p>
              <div v-if="hasExpiration" class="control mt-3">
                <input 
                  type="datetime-local" 
                  class="input" 
                  v-model="expirationDate"
                  :min="minDate"
                >
              </div>
            </div>

            <!-- Password Protection -->
            <div class="advanced-option">
              <label class="checkbox">
                <input type="checkbox" v-model="hasPassword">
                <strong>Password Protection</strong>
              </label>
              <p class="help">Require a password to access the shared note</p>
              <div v-if="hasPassword" class="control mt-3">
                <input 
                  type="password" 
                  class="input" 
                  v-model="sharePassword"
                  placeholder="Enter password (min 6 characters)"
                  minlength="6"
                >
              </div>
            </div>

            <!-- IP Restrictions -->
            <div class="advanced-option">
              <label class="checkbox">
                <input type="checkbox" v-model="hasIpRestrictions">
                <strong>IP Address Restrictions</strong>
              </label>
              <p class="help">Limit access to specific IP addresses</p>
              <div v-if="hasIpRestrictions" class="mt-3">
                <div v-for="(ip, index) in allowedIps" :key="index" class="field has-addons mb-2">
                  <div class="control is-expanded">
                    <input 
                      type="text" 
                      class="input" 
                      v-model="allowedIps[index]"
                      placeholder="*********** or ***********/24"
                    >
                  </div>
                  <div class="control">
                    <button 
                      type="button" 
                      class="button is-danger" 
                      @click="removeIpAddress(index)"
                    >
                      <span class="icon">
                        <i class="fas fa-times"></i>
                      </span>
                    </button>
                  </div>
                </div>
                <button 
                  type="button" 
                  class="button is-small is-info" 
                  @click="addIpAddress"
                >
                  <span class="icon">
                    <i class="fas fa-plus"></i>
                  </span>
                  <span>Add IP Address</span>
                </button>
              </div>
            </div>

            <!-- Watermark -->
            <div class="advanced-option">
              <label class="checkbox">
                <input type="checkbox" v-model="newShare.watermark">
                <strong>Add Watermark</strong>
              </label>
              <p class="help">Add a watermark indicating the content is shared</p>
            </div>
          </div>
        </div>
      </section>

      <footer class="modal-card-foot">
        <button 
          class="button is-primary" 
          @click="handleCreateShare"
          :class="{ 'is-loading': isLoading }"
          :disabled="isLoading || !isFormValid"
        >
          <span class="icon">
            <i class="fas fa-share"></i>
          </span>
          <span>Create Share</span>
        </button>
        <button class="button" @click="closeModal">Close</button>
      </footer>
    </div>

    <!-- Copy Success Toast -->
    <div v-if="showCopySuccess" class="notification is-success copy-toast">
      <i class="fas fa-check"></i>
      <span>Share created successfully!</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useNoteSharesStore } from '../../stores/noteShares';
import { getAccessLevelDescription, getDefaultShareSettings, type CreateShareData, type NoteShare } from '../../types/noteShare';
import type { Note } from '../../services/noteService';
import ShareItem from './ShareItem.vue';

interface Props {
  isOpen: boolean;
  note: Note | null;
}

interface Emits {
  (e: 'close'): void;
  (e: 'share-created', share: NoteShare): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const shareStore = useNoteSharesStore();

// Reactive state
const newShare = ref<CreateShareData>({
  accessLevel: 'private',
  permissions: ['view'],
  watermark: false
});
const hasExpiration = ref(false);
const hasPassword = ref(false);
const hasIpRestrictions = ref(false);
const expirationDate = ref('');
const sharePassword = ref('');
const allowedIps = ref<string[]>([]);
const showCopySuccess = ref(false);

// Computed properties
const isLoading = computed(() => shareStore.isLoading);
const error = computed(() => shareStore.error);
const noteShares = computed(() => 
  props.note ? shareStore.getSharesForNote(props.note.id) : []
);

const minDate = computed(() => {
  const now = new Date();
  now.setMinutes(now.getMinutes() + 5); // Minimum 5 minutes from now
  return now.toISOString().slice(0, 16);
});

const isFormValid = computed(() => {
  if (!newShare.value.accessLevel || newShare.value.permissions.length === 0) {
    return false;
  }
  
  if (hasPassword.value && (!sharePassword.value || sharePassword.value.length < 6)) {
    return false;
  }
  
  if (hasExpiration.value && !expirationDate.value) {
    return false;
  }
  
  if (hasIpRestrictions.value && allowedIps.value.some(ip => !ip.trim())) {
    return false;
  }
  
  return true;
});

// Methods
const closeModal = () => {
  emit('close');
};

const clearError = () => {
  shareStore.clearError();
};

const resetForm = () => {
  newShare.value = {
    accessLevel: 'private',
    permissions: ['view'],
    watermark: false
  };
  hasExpiration.value = false;
  hasPassword.value = false;
  hasIpRestrictions.value = false;
  expirationDate.value = '';
  sharePassword.value = '';
  allowedIps.value = [];
};

const handleCreateShare = async () => {
  if (!props.note || !isFormValid.value) return;

  try {
    const shareData: CreateShareData = {
      ...newShare.value,
      expiresAt: hasExpiration.value ? expirationDate.value : undefined,
      password: hasPassword.value ? sharePassword.value : undefined,
      allowedIps: hasIpRestrictions.value ? allowedIps.value.filter(ip => ip.trim()) : undefined
    };

    const createdShare = await shareStore.createShare(props.note.id, shareData);
    emit('share-created', createdShare);
    
    // Show success message
    showCopySuccess.value = true;
    setTimeout(() => {
      showCopySuccess.value = false;
    }, 3000);
    
    resetForm();
  } catch (error) {
    // Error is handled by the store
  }
};

const handleUpdateShare = async (shareId: string, updateData: any) => {
  try {
    await shareStore.updateShare(shareId, updateData);
  } catch (error) {
    // Error is handled by the store
  }
};

const handleDeleteShare = async (shareId: string) => {
  if (confirm('Are you sure you want to delete this share? This action cannot be undone.')) {
    try {
      await shareStore.deleteShare(shareId);
    } catch (error) {
      // Error is handled by the store
    }
  }
};

const handleCopyUrl = async (shareUrl: string) => {
  const success = await shareStore.copyShareUrl(shareUrl);
  if (success) {
    // You might want to show a toast notification here
    console.log('Share URL copied to clipboard');
  }
};

const handleViewLogs = (shareId: string) => {
  // Navigate to access logs view or open logs modal
  console.log('View logs for share:', shareId);
};

const addIpAddress = () => {
  allowedIps.value.push('');
};

const removeIpAddress = (index: number) => {
  allowedIps.value.splice(index, 1);
};

const getNoteTypeIcon = (noteType?: string) => {
  switch (noteType) {
    case 'richtext':
      return 'fa-file-alt';
    case 'markdown':
      return 'fa-markdown';
    case 'kanban':
      return 'fa-columns';
    default:
      return 'fa-file';
  }
};

const getNoteTypeDisplayName = (noteType?: string) => {
  switch (noteType) {
    case 'richtext':
      return 'Rich Text';
    case 'markdown':
      return 'Markdown';
    case 'kanban':
      return 'Kanban Board';
    default:
      return 'Unknown';
  }
};

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (isOpen && props.note) {
    // Load existing shares for the note
    shareStore.loadNoteShares(props.note.id);
  } else if (!isOpen) {
    // Reset form when modal closes
    resetForm();
    shareStore.clearError();
  }
});

// Ensure view permission is always included
watch(() => newShare.value.permissions, (permissions) => {
  if (!permissions.includes('view')) {
    newShare.value.permissions.push('view');
  }
}, { deep: true });

onMounted(() => {
  // Ensure view permission is included by default
  if (!newShare.value.permissions.includes('view')) {
    newShare.value.permissions.push('view');
  }
});
</script>

<style scoped>
.note-sharing-modal {
  width: 600px;
  max-width: 95vw;
  max-height: 90vh;
}

.modal-card-body {
  padding: 1.5rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.sharing-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.sharing-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 1.5rem;
}

.sharing-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #333;
}

/* Note Info Card */
.note-info-card {
  padding: 1rem;
  background: #f9f9f9;
  border-radius: 6px;
}

.existing-shares-container {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 1rem;
}

/* Radio buttons */
.radio {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.75rem;
}

.radio:hover {
  border-color: #3273dc;
  background: rgba(50, 115, 220, 0.05);
}

.radio input[type="radio"]:checked + .radio-content {
  color: #3273dc;
}

.radio input[type="radio"]:checked {
  accent-color: #3273dc;
}

.radio-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  flex: 1;
}

.radio-content i {
  font-size: 1.2rem;
  margin-top: 0.125rem;
}

.radio-text strong {
  display: block;
  margin-bottom: 0.25rem;
}

.radio-text p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

/* Permissions */
.permissions-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.permission-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: #f9f9f9;
  border-radius: 6px;
}

.permission-icon {
  width: 32px;
  height: 32px;
  background: #3273dc;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.permission-details {
  flex: 1;
}

.permission-details .checkbox {
  margin-bottom: 0.25rem;
}

.permission-details strong {
  font-size: 0.95rem;
}

.permission-details p {
  margin: 0;
  font-size: 0.85rem;
  color: #666;
  line-height: 1.4;
}

/* Advanced Options */
.advanced-option {
  padding: 1rem;
  background: #f9f9f9;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.advanced-option:last-child {
  margin-bottom: 0;
}

.advanced-option .checkbox {
  margin-bottom: 0.5rem;
}

.advanced-option .help {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
}

/* General checkbox styling */
.checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox input[type="checkbox"] {
  margin: 0;
}

/* Field styling */
.field.has-addons .control.is-expanded {
  flex: 1;
}

/* Copy Toast */
.copy-toast {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .note-sharing-modal {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
  }
  
  .modal-card-body {
    padding: 1rem;
  }
  
  .sharing-container {
    gap: 1.5rem;
  }
  
  .copy-toast {
    top: 1rem;
    right: 1rem;
    left: 1rem;
  }
}
</style>
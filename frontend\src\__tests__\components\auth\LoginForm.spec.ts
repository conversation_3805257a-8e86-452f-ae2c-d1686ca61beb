import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'
import LoginForm from '../../../components/auth/LoginForm.vue'

// Mock the auth store
vi.mock('../../../stores/auth', () => ({
  useAuthStore: () => ({
    isLoading: false,
    error: null,
    login: vi.fn().mockResolvedValue({ success: true })
  })
}))

describe('LoginForm', () => {
  let wrapper: any
  let router: any
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/dashboard', component: { template: '<div>Dashboard</div>' } }
      ]
    })
    
    wrapper = mount(LoginForm, {
      global: {
        plugins: [router, pinia]
      }
    })
  })

  it('renders login form correctly', () => {
    expect(wrapper.find('.card-header-title').text()).toContain('Sign In')
    expect(wrapper.find('input[type="email"]').exists()).toBe(true)
    expect(wrapper.find('input[type="password"]').exists()).toBe(true)
    expect(wrapper.find('button[type="submit"]').exists()).toBe(true)
  })

  it('shows validation errors for empty fields', async () => {
    const emailInput = wrapper.find('input[type="email"]')
    const passwordInput = wrapper.find('input[type="password"]')
    
    // Set empty values and trigger input events
    await emailInput.setValue('')
    await emailInput.trigger('input')
    await emailInput.trigger('blur')
    
    await passwordInput.setValue('')
    await passwordInput.trigger('input')
    await passwordInput.trigger('blur')
    
    // Wait for Vue to update
    await wrapper.vm.$nextTick()
    
    // Check that validation messages appear
    expect(wrapper.text()).toContain('Email is required')
    expect(wrapper.text()).toContain('Password is required')
  })

  it('enables submit button when form is valid', async () => {
    const emailInput = wrapper.find('input[type="email"]')
    const passwordInput = wrapper.find('input[type="password"]')
    const submitButton = wrapper.find('button[type="submit"]')
    
    // Initially disabled
    expect(submitButton.attributes('disabled')).toBeDefined()
    
    // Fill in valid data
    await emailInput.setValue('<EMAIL>')
    await passwordInput.setValue('password123')
    
    // Should be enabled now
    expect(submitButton.attributes('disabled')).toBeUndefined()
  })

  it('shows password toggle functionality', async () => {
    const passwordInput = wrapper.find('input[type="password"]')
    const toggleIcon = wrapper.find('.fa-eye')
    
    expect(passwordInput.attributes('type')).toBe('password')
    
    await toggleIcon.trigger('click')
    
    expect(passwordInput.attributes('type')).toBe('text')
  })
})
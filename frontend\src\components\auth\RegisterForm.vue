<template>
  <div class="auth-form">
    <div class="form-content">
      <form @submit.prevent="handleSubmit">
        <!-- Display Name Field -->
        <div class="field">
          <label class="label">Display Name</label>
          <div class="control has-icons-left">
            <input v-model="form.displayName" type="text" class="input"
              :class="{ 'is-danger': displayNameValidation.errors.length > 0 && displayNameTouched }"
              placeholder="Enter your display name" @blur="displayNameTouched = true" @input="validateDisplayName"
              :disabled="authStore.isLoading" />
            <span class="icon is-small is-left user-icon">
              <i class="fas fa-user"></i>
            </span>
          </div>
          <div v-if="displayNameValidation.errors.length > 0 && displayNameTouched" class="help is-danger">
            <div v-for="error in displayNameValidation.errors" :key="error">
              {{ error }}
            </div>
          </div>
        </div>

        <!-- Email Field -->
        <div class="field">
          <label class="label">Email</label>
          <div class="control has-icons-left">
            <input v-model="form.email" type="email" class="input"
              :class="{ 'is-danger': emailValidation.errors.length > 0 && emailTouched }" placeholder="Enter your email"
              @blur="emailTouched = true" @input="validateEmail" :disabled="authStore.isLoading" />
            <span class="icon is-small is-left email-icon">
              <i class="fas fa-envelope"></i>
            </span>
          </div>
          <div v-if="emailValidation.errors.length > 0 && emailTouched" class="help is-danger">
            <div v-for="error in emailValidation.errors" :key="error">
              {{ error }}
            </div>
          </div>
        </div>

        <!-- Password Field -->
        <div class="field">
          <label class="label">Password</label>
          <div class="control has-icons-left has-icons-right">
            <input v-model="form.password" :type="showPassword ? 'text' : 'password'" class="input"
              :class="{ 'is-danger': passwordValidation.errors.length > 0 && passwordTouched }"
              placeholder="Enter your password" @blur="passwordTouched = true" @input="onPasswordInput"
              @keyup="onPasswordInput" :disabled="authStore.isLoading" />
            <span class="icon is-small is-left password-icon">
              <i class="fas fa-lock"></i>
            </span>
            <span class="icon is-small is-right is-clickable eye-icon" @click="showPassword = !showPassword">
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </span>
          </div>
          <div v-if="passwordValidation.errors.length > 0 && passwordTouched" class="help is-danger">
            <div v-for="error in passwordValidation.errors" :key="error">
              {{ error }}
            </div>
          </div>
          <div v-else class="help">
            <div class="password-strength">
              <div class="password-requirements">
                <div class="requirement" :class="{ 'is-success': hasMinLength }">
                  <span class="icon is-small requirement-icon">
                    <i :class="hasMinLength ? 'fas fa-check' : 'fas fa-times'"></i>
                  </span>
                  <span>At least 8 characters</span>
                </div>
                <div class="requirement" :class="{ 'is-success': hasUppercase }">
                  <span class="icon is-small requirement-icon">
                    <i :class="hasUppercase ? 'fas fa-check' : 'fas fa-times'"></i>
                  </span>
                  <span>One uppercase letter</span>
                </div>
                <div class="requirement" :class="{ 'is-success': hasLowercase }">
                  <span class="icon is-small requirement-icon">
                    <i :class="hasLowercase ? 'fas fa-check' : 'fas fa-times'"></i>
                  </span>
                  <span>One lowercase letter</span>
                </div>
                <div class="requirement" :class="{ 'is-success': hasNumber }">
                  <span class="icon is-small requirement-icon">
                    <i :class="hasNumber ? 'fas fa-check' : 'fas fa-times'"></i>
                  </span>
                  <span>One number</span>
                </div>
                <div class="requirement" :class="{ 'is-success': hasSpecialChar }">
                  <span class="icon is-small requirement-icon">
                    <i :class="hasSpecialChar ? 'fas fa-check' : 'fas fa-times'"></i>
                  </span>
                  <span>One special character</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Confirm Password Field -->
        <div class="field">
          <label class="label">Confirm Password</label>
          <div class="control has-icons-left has-icons-right">
            <input v-model="form.confirmPassword" :type="showConfirmPassword ? 'text' : 'password'" class="input"
              :class="{ 'is-danger': confirmPasswordValidation.errors.length > 0 && confirmPasswordTouched }"
              placeholder="Confirm your password" @blur="confirmPasswordTouched = true" @input="onConfirmPasswordInput"
              @keyup="onConfirmPasswordInput" :disabled="authStore.isLoading" />
            <span class="icon is-small is-left password-icon">
              <i class="fas fa-lock"></i>
            </span>
            <span class="icon is-small is-right is-clickable eye-icon"
              @click="showConfirmPassword = !showConfirmPassword">
              <i :class="showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </span>
          </div>
          <div v-if="confirmPasswordValidation.errors.length > 0 && confirmPasswordTouched" class="help is-danger">
            <div v-for="error in confirmPasswordValidation.errors" :key="error">
              {{ error }}
            </div>
          </div>
        </div>

        <!-- Success Message -->
        <div v-if="registrationSuccess" class="notification is-success is-light">
          <button class="delete" @click="registrationSuccess = false"></button>
          Registration successful! Please check your email to verify your account.
        </div>

        <!-- Error Message -->
        <div v-if="authStore.error" class="notification is-danger is-light">
          <button class="delete" @click="authStore.error = null"></button>
          {{ authStore.error }}
        </div>

        <!-- Submit Button -->
        <div class="field">
          <div class="control">
            <button type="submit" class="button is-primary is-fullwidth" :class="{ 'is-loading': authStore.isLoading }"
              :disabled="!isFormValid || authStore.isLoading">
              <span class="icon signup-icon">
                <i class="fas fa-user-plus"></i>
              </span>
              <span>Create Account</span>
            </button>
          </div>
        </div>

        <!-- Google Sign-In -->
        <div class="field">
          <div class="has-text-centered mb-3">
            <span class="is-size-7 has-text-grey">or</span>
          </div>
          <GoogleSignInButton button-text="Sign up with Google" mode="signup" @success="handleGoogleSuccess"
            @error="handleGoogleError" />
        </div>

        <hr />

        <div class="field">
          <div class="has-text-centered">
            <p class="is-size-7">
              Already have an account?
              <router-link to="/login" class="has-text-weight-semibold">
                Sign in
              </router-link>
            </p>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import {
  validateField,
  emailRules,
  passwordRules,
  displayNameRules,
  confirmPasswordRule
} from '../../utils/validation'
import type { ValidationResult } from '../../utils/validation'
import GoogleSignInButton from './GoogleSignInButton.vue'

const router = useRouter()
const authStore = useAuthStore()

const form = reactive({
  displayName: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const showPassword = ref(false)
const showConfirmPassword = ref(false)
const displayNameTouched = ref(false)
const emailTouched = ref(false)
const passwordTouched = ref(false)
const confirmPasswordTouched = ref(false)
const registrationSuccess = ref(false)

const displayNameValidation = ref<ValidationResult>({ isValid: true, errors: [] })
const emailValidation = ref<ValidationResult>({ isValid: true, errors: [] })
const passwordValidation = ref<ValidationResult>({ isValid: true, errors: [] })
const confirmPasswordValidation = ref<ValidationResult>({ isValid: true, errors: [] })

// Password strength indicators
const hasMinLength = computed(() => form.password.length >= 8)
const hasUppercase = computed(() => /[A-Z]/.test(form.password))
const hasLowercase = computed(() => /[a-z]/.test(form.password))
const hasNumber = computed(() => /\d/.test(form.password))
const hasSpecialChar = computed(() => /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?~`]/.test(form.password))

const validateDisplayName = () => {
  displayNameValidation.value = validateField(form.displayName, displayNameRules)
}

const validateEmail = () => {
  emailValidation.value = validateField(form.email, emailRules)
}

const validatePassword = () => {
  passwordValidation.value = validateField(form.password, passwordRules)
  // Re-validate confirm password when password changes
  if (confirmPasswordTouched.value) {
    validateConfirmPassword()
  }
}

const onPasswordInput = (event: Event) => {
  passwordTouched.value = true
  validatePassword()
}

const onConfirmPasswordInput = (event: Event) => {
  confirmPasswordTouched.value = true
  validateConfirmPassword()
}

const validateConfirmPassword = () => {
  const rules = [confirmPasswordRule(form.password)]
  confirmPasswordValidation.value = validateField(form.confirmPassword, rules)
}

// Watchers to automatically validate fields on change
watch(() => form.displayName, () => {
  if (displayNameTouched.value) {
    validateDisplayName()
  }
})

watch(() => form.email, () => {
  if (emailTouched.value) {
    validateEmail()
  }
})

watch(() => form.password, () => {
  validatePassword()
  passwordTouched.value = true // Auto-mark as touched when user starts typing
})

watch(() => form.confirmPassword, () => {
  if (confirmPasswordTouched.value) {
    validateConfirmPassword()
  }
})

const isFormValid = computed(() => {
  return displayNameValidation.value.isValid &&
    emailValidation.value.isValid &&
    passwordValidation.value.isValid &&
    confirmPasswordValidation.value.isValid &&
    form.displayName.trim() !== '' &&
    form.email.trim() !== '' &&
    form.password !== '' &&
    form.confirmPassword !== ''
})

const handleSubmit = async () => {
  // Mark all fields as touched
  displayNameTouched.value = true
  emailTouched.value = true
  passwordTouched.value = true
  confirmPasswordTouched.value = true

  // Validate all fields
  validateDisplayName()
  validateEmail()
  validatePassword()
  validateConfirmPassword()

  if (!isFormValid.value) {
    return
  }

  const result = await authStore.register({
    displayName: form.displayName.trim(),
    email: form.email.trim(),
    password: form.password
  })

  if (result.success) {
    registrationSuccess.value = true
    // Reset form
    form.displayName = ''
    form.email = ''
    form.password = ''
    form.confirmPassword = ''
    // Reset touched states
    displayNameTouched.value = false
    emailTouched.value = false
    passwordTouched.value = false
    confirmPasswordTouched.value = false
  }
}

const handleGoogleSuccess = (message: string) => {
  // Clear any existing errors
  authStore.error = null

  // Redirect to dashboard
  router.push('/dashboard')
}

const handleGoogleError = (error: string) => {
  authStore.error = error
}
</script>

<style scoped>
.is-clickable {
  cursor: pointer;
}

.auth-form {
  width: 100%;
}

.form-content {
  padding: 2rem;
}

/* Colored Icons */
.user-icon i {
  color: #9b59b6;
}

.email-icon i {
  color: #3498db;
}

.password-icon i {
  color: #e74c3c;
}

.eye-icon i {
  color: #95a5a6;
  transition: color 0.2s ease;
}

.eye-icon:hover i {
  color: #34495e;
}

.signup-icon i {
  color: white;
}

.requirement-icon i.fa-check {
  color: #27ae60;
}

.requirement-icon i.fa-times {
  color: #e74c3c;
}

/* Enhanced Input Styling */
.input {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  padding-left: 2.5rem;
}

.input:focus {
  border-color: #a8edea;
  box-shadow: 0 0 0 0.125em rgba(168, 237, 234, 0.25);
}

.input.is-danger {
  border-color: #e74c3c;
}

/* Enhanced Button Styling */
.button.is-primary {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(168, 237, 234, 0.3);
  color: #667eea;
}

.button.is-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(168, 237, 234, 0.4);
}

.button.is-primary:active {
  transform: translateY(0);
}

/* Enhanced Labels */
.label {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* Enhanced Links */
.router-link-exact-active,
a {
  color: #a8edea;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #fed6e3;
}

/* Enhanced Notifications */
.notification.is-danger {
  background-color: #fdf2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
}

.notification.is-success {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  color: #166534;
}

/* Password Requirements Styling */
.password-requirements {
  font-size: 0.75rem;
  margin-top: 0.5rem;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.requirement {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
  color: #6c757d;
  transition: color 0.2s ease;
}

.requirement.is-success {
  color: #27ae60;
}

.requirement .icon {
  margin-right: 0.5rem;
}

/* Divider Styling */
hr {
  background-color: #e9ecef;
  height: 1px;
  margin: 1.5rem 0;
}

/* Field Spacing */
.field:not(:last-child) {
  margin-bottom: 1.5rem;
}
</style>
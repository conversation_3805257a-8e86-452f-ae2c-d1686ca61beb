import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';

// Enhanced rate limiting with express-rate-limit
export const createRateLimit = (options: {
  windowMs: number;
  max: number;
  message?: string;
  keyGenerator?: (req: Request) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  standardHeaders?: boolean;
  legacyHeaders?: boolean;
}) => {
  const {
    windowMs,
    max,
    message = 'Too many requests, please try again later',
    keyGenerator = (req: Request) => req.ip || 'unknown',
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    standardHeaders = true,
    legacyHeaders = false
  } = options;

  return rateLimit({
    windowMs,
    max,
    message: {
      error: message,
      code: 'RATE_LIMIT_EXCEEDED'
    },
    keyGenerator,
    skipSuccessfulRequests,
    skipFailedRequests,
    standardHeaders,
    legacyHeaders,
    handler: (req: Request, res: Response) => {
      res.status(429).json({
        error: message,
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
  });
};

// Slow down middleware for progressive delays
export const createSlowDown = (options: {
  windowMs: number;
  delayAfter: number;
  delayMs: number;
  maxDelayMs?: number;
  keyGenerator?: (req: Request) => string;
}) => {
  const {
    windowMs,
    delayAfter,
    delayMs,
    maxDelayMs = 10000, // 10 seconds max delay
    keyGenerator = (req: Request) => req.ip || 'unknown'
  } = options;

  return slowDown({
    windowMs,
    delayAfter,
    delayMs: (used: number, req: any) => {
      const delayAfterCount = req.slowDown?.limit || delayAfter;
      return Math.max(0, used - delayAfterCount) * delayMs;
    },
    maxDelayMs,
    keyGenerator,
    validate: { delayMs: false } // Disable the warning
  });
};

// Predefined rate limiters for common use cases
export const authRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per 15 minutes (strict for production)
  message: 'Too many authentication attempts, please try again later',
  keyGenerator: (req: Request) => `auth:${req.ip}:${req.body.email || 'unknown'}`,
  skipSuccessfulRequests: true,
  standardHeaders: true
});

export const generalRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // 1000 requests per 15 minutes
  message: 'Too many requests, please try again later',
  standardHeaders: true
});

export const passwordResetRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 password reset attempts per hour
  message: 'Too many password reset attempts, please try again later',
  keyGenerator: (req: Request) => `password-reset:${req.ip}:${req.body.email || 'unknown'}`,
  standardHeaders: true
});

export const apiRateLimit = createRateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // 100 API requests per minute
  message: 'Too many API requests, please try again later',
  standardHeaders: true
});

export const uploadRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 uploads per 15 minutes
  message: 'Too many upload attempts, please try again later',
  standardHeaders: true
});

// Slow down middleware for authentication endpoints
export const authSlowDown = createSlowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 2, // Start slowing down after 2 requests
  delayMs: 500, // Add 500ms delay per request after delayAfter
  maxDelayMs: 5000, // Maximum 5 second delay
  keyGenerator: (req: Request) => `auth-slow:${req.ip}:${req.body.email || 'unknown'}`
});

// Progressive slow down for general API
export const apiSlowDown = createSlowDown({
  windowMs: 1 * 60 * 1000, // 1 minute
  delayAfter: 50, // Start slowing down after 50 requests
  delayMs: 100, // Add 100ms delay per request
  maxDelayMs: 2000, // Maximum 2 second delay
});
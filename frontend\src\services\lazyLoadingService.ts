// Lazy Loading Service with Fallback Mechanisms
// Handles dynamic imports with graceful degradation and synchronous fallbacks

export interface LazyLoadConfig {
  maxRetries: number
  retryDelay: number
  fallbackToSync: boolean
  enableLogging: boolean
  timeout: number
}

export interface LazyLoadResult<T> {
  success: boolean
  module: T | null
  error?: Error | null
  fallbackUsed: boolean
  attempts: number
  loadTime: number
}

export interface LazyLoadAttempt {
  moduleName: string
  attempt: number
  timestamp: number
  error?: Error
  success: boolean
  loadTime: number
}

const DEFAULT_CONFIG: LazyLoadConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  fallbackToSync: true,
  enableLogging: true,
  timeout: 5000
}

class LazyLoadingService {
  private static instance: LazyLoadingService
  private config: LazyLoadConfig
  private loadAttempts: LazyLoadAttempt[] = []
  private syncFallbacks: Map<string, any> = new Map()
  private loadingCache: Map<string, Promise<any>> = new Map()

  private constructor(config: Partial<LazyLoadConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.setupSyncFallbacks()
  }

  static getInstance(config?: Partial<LazyLoadConfig>): LazyLoadingService {
    if (!LazyLoadingService.instance) {
      LazyLoadingService.instance = new LazyLoadingService(config)
    }
    return LazyLoadingService.instance
  }

  // Setup synchronous fallbacks for critical modules
  private setupSyncFallbacks() {
    // Basic auth store fallback
    this.syncFallbacks.set('auth', {
      useAuthStore: () => ({
        user: { value: null },
        token: { value: null },
        isAuthenticated: { value: false },
        isAdmin: { value: false },
        isLoading: { value: false },
        error: { value: null },
        isInitialized: { value: true },
        login: async () => ({ success: false, error: 'Auth service unavailable' }),
        logout: async () => {},
        initializeAuth: async () => {},
        initializeAuthWithTimeout: async () => {},
        resetInitialization: () => {}
      })
    })

    // Basic settings store fallback
    this.syncFallbacks.set('settings', {
      useSettingsStore: () => ({
        preferences: { 
          value: {
            theme: 'auto',
            language: 'en',
            timezone: 'UTC',
            autoSaveInterval: 30000,
            notifications: { email: true, push: true, mentions: true }
          }
        },
        isLoading: { value: false },
        error: { value: null },
        isDarkMode: { value: window.matchMedia('(prefers-color-scheme: dark)').matches },
        isInitialized: { value: true },
        loadSettings: async () => {},
        updateSettings: async () => ({ success: false, error: 'Settings service unavailable' }),
        initializeSettings: async () => {},
        resetInitialization: () => {},
        applyTheme: () => {
          const html = document.documentElement
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
          html.classList.toggle('dark', prefersDark)
          html.classList.toggle('light', !prefersDark)
        }
      })
    })

    // Basic cache service fallback
    this.syncFallbacks.set('cacheService', {
      cacheService: {
        isInitialized: false,
        preloadCriticalData: async () => {
          console.log('📝 Cache service fallback - no caching available')
        },
        get: () => null,
        set: () => {},
        clear: () => {},
        resetInitialization: () => {}
      }
    })

    // Basic performance service fallback
    this.syncFallbacks.set('performanceService', {
      performanceService: {
        trackUserInteraction: () => {},
        trackApiCall: () => {},
        getPerformanceMetrics: () => ({}),
        startContinuousMonitoring: () => {},
        stopMonitoring: () => {},
        exportPerformanceData: () => ({})
      }
    })
  }

  // Main lazy loading method with comprehensive fallback
  async loadModule<T>(
    moduleName: string,
    importFunction: () => Promise<T>,
    options: Partial<LazyLoadConfig> = {}
  ): Promise<LazyLoadResult<T>> {
    const config = { ...this.config, ...options }
    const startTime = performance.now()
    
    // Check if already loading this module
    const cacheKey = moduleName
    if (this.loadingCache.has(cacheKey)) {
      try {
        const cachedResult = await this.loadingCache.get(cacheKey)
        return {
          success: true,
          module: cachedResult,
          fallbackUsed: false,
          attempts: 0,
          loadTime: performance.now() - startTime
        }
      } catch (error) {
        // Continue with normal loading if cached promise failed
      }
    }

    let lastError: Error | null = null
    let attempts = 0

    // Try dynamic import with retries
    for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
      attempts = attempt
      const attemptStartTime = performance.now()

      try {
        // Create loading promise with timeout
        const loadingPromise = Promise.race([
          importFunction(),
          new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error(`Module ${moduleName} load timeout after ${config.timeout}ms`)), config.timeout)
          )
        ])

        // Cache the loading promise
        this.loadingCache.set(cacheKey, loadingPromise)

        const module = await loadingPromise
        const loadTime = performance.now() - attemptStartTime

        // Log successful load
        this.logAttempt(moduleName, attempt, true, loadTime)

        if (config.enableLogging) {
          console.log(`✅ Lazy loaded ${moduleName} successfully on attempt ${attempt} (${loadTime.toFixed(2)}ms)`)
        }

        // Clean up cache
        this.loadingCache.delete(cacheKey)

        return {
          success: true,
          module,
          fallbackUsed: false,
          attempts,
          loadTime: performance.now() - startTime
        }

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        const loadTime = performance.now() - attemptStartTime

        // Log failed attempt
        this.logAttempt(moduleName, attempt, false, loadTime, lastError)

        if (config.enableLogging) {
          console.warn(`⚠️ Lazy load attempt ${attempt}/${config.maxRetries} failed for ${moduleName}:`, lastError.message)
        }

        // Clean up cache on error
        this.loadingCache.delete(cacheKey)

        // Wait before retry (except on last attempt)
        if (attempt < config.maxRetries) {
          await new Promise(resolve => setTimeout(resolve, config.retryDelay * attempt))
        }
      }
    }

    // All dynamic import attempts failed - try synchronous fallback
    if (config.fallbackToSync && this.syncFallbacks.has(moduleName)) {
      try {
        const fallbackModule = this.syncFallbacks.get(moduleName)
        
        if (config.enableLogging) {
          console.warn(`🔄 Using synchronous fallback for ${moduleName} after ${attempts} failed attempts`)
        }

        // Dispatch event for user notification
        window.dispatchEvent(new CustomEvent('lazy-load-fallback', {
          detail: {
            moduleName,
            attempts,
            error: lastError?.message,
            fallbackType: 'synchronous'
          }
        }))

        return {
          success: true,
          module: fallbackModule as T,
          error: lastError,
          fallbackUsed: true,
          attempts,
          loadTime: performance.now() - startTime
        }

      } catch (fallbackError) {
        if (config.enableLogging) {
          console.error(`❌ Synchronous fallback failed for ${moduleName}:`, fallbackError)
        }

        lastError = fallbackError instanceof Error ? fallbackError : new Error(String(fallbackError))
      }
    }

    // Complete failure - return error result
    if (config.enableLogging) {
      console.error(`❌ Complete failure loading ${moduleName} after ${attempts} attempts`)
    }

    // Dispatch event for error tracking
    window.dispatchEvent(new CustomEvent('lazy-load-failure', {
      detail: {
        moduleName,
        attempts,
        error: lastError?.message,
        hasFallback: this.syncFallbacks.has(moduleName)
      }
    }))

    return {
      success: false,
      module: null,
      error: lastError,
      fallbackUsed: false,
      attempts,
      loadTime: performance.now() - startTime
    }
  }

  // Convenience methods for common modules
  async loadAuthStore() {
    return this.loadModule(
      'auth',
      () => import('../stores/auth'),
      { timeout: 3000 }
    )
  }

  async loadSettingsStore() {
    return this.loadModule(
      'settings',
      () => import('../stores/settings'),
      { timeout: 2000 }
    )
  }

  async loadCacheService() {
    return this.loadModule(
      'cacheService',
      () => import('./cacheService'),
      { timeout: 1500 }
    )
  }

  async loadPerformanceService() {
    return this.loadModule(
      'performanceService',
      () => import('./performanceService'),
      { timeout: 1000 }
    )
  }

  async loadServiceWorker() {
    return this.loadModule(
      'serviceWorker',
      () => import('../utils/serviceWorker'),
      { timeout: 2000, fallbackToSync: false } // No sync fallback for SW
    )
  }

  // Batch loading with parallel execution and individual error handling
  async loadModules<T extends Record<string, () => Promise<any>>>(
    modules: T,
    options: Partial<LazyLoadConfig> = {}
  ): Promise<Record<keyof T, LazyLoadResult<any>>> {
    const results = await Promise.allSettled(
      Object.entries(modules).map(async ([name, importFn]) => {
        const result = await this.loadModule(name, importFn, options)
        return { name, result }
      })
    )

    const resultMap = {} as Record<keyof T, LazyLoadResult<any>>
    
    results.forEach((settledResult, index) => {
      const moduleName = Object.keys(modules)[index] as keyof T
      
      if (settledResult.status === 'fulfilled') {
        resultMap[moduleName] = settledResult.value.result
      } else {
        // Create error result for failed batch item
        resultMap[moduleName] = {
          success: false,
          module: null,
          error: settledResult.reason,
          fallbackUsed: false,
          attempts: 0,
          loadTime: 0
        }
      }
    })

    return resultMap
  }

  // Register custom synchronous fallback
  registerSyncFallback(moduleName: string, fallbackModule: any) {
    this.syncFallbacks.set(moduleName, fallbackModule)
    
    if (this.config.enableLogging) {
      console.log(`📝 Registered synchronous fallback for ${moduleName}`)
    }
  }

  // Get loading statistics
  getLoadingStats() {
    const totalAttempts = this.loadAttempts.length
    const successfulLoads = this.loadAttempts.filter(a => a.success).length
    const failedLoads = totalAttempts - successfulLoads
    
    const moduleStats = this.loadAttempts.reduce((acc, attempt) => {
      if (!acc[attempt.moduleName]) {
        acc[attempt.moduleName] = { attempts: 0, successes: 0, failures: 0, avgLoadTime: 0 }
      }
      
      acc[attempt.moduleName].attempts++
      if (attempt.success) {
        acc[attempt.moduleName].successes++
      } else {
        acc[attempt.moduleName].failures++
      }
      
      return acc
    }, {} as Record<string, any>)

    // Calculate average load times
    Object.keys(moduleStats).forEach(moduleName => {
      const moduleAttempts = this.loadAttempts.filter(a => a.moduleName === moduleName && a.success)
      if (moduleAttempts.length > 0) {
        moduleStats[moduleName].avgLoadTime = 
          moduleAttempts.reduce((sum, a) => sum + a.loadTime, 0) / moduleAttempts.length
      }
    })

    return {
      totalAttempts,
      successfulLoads,
      failedLoads,
      successRate: totalAttempts > 0 ? (successfulLoads / totalAttempts) * 100 : 0,
      moduleStats,
      availableFallbacks: Array.from(this.syncFallbacks.keys())
    }
  }

  // Clear loading cache (useful for testing or forced reloads)
  clearCache() {
    this.loadingCache.clear()
    if (this.config.enableLogging) {
      console.log('🧹 Lazy loading cache cleared')
    }
  }

  // Private helper methods
  private logAttempt(
    moduleName: string,
    attempt: number,
    success: boolean,
    loadTime: number,
    error?: Error
  ) {
    const logEntry: LazyLoadAttempt = {
      moduleName,
      attempt,
      timestamp: Date.now(),
      success,
      loadTime,
      error
    }

    this.loadAttempts.push(logEntry)

    // Keep only last 100 attempts to prevent memory issues
    if (this.loadAttempts.length > 100) {
      this.loadAttempts = this.loadAttempts.slice(-100)
    }
  }
}

// Export singleton instance
export const lazyLoadingService = LazyLoadingService.getInstance()

// Types are already exported above with the interfaces
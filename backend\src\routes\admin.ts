import { Router } from 'express';
import { AdminController } from '../controllers/AdminController';
import { authenticateToken } from '../middleware/auth';
import { requireAdmin, requireSuperAdmin } from '../middleware/adminAuth';
import { body, param, query } from 'express-validator';
import { validateRequest } from '../middleware/validation';

const router = Router();

// All admin routes require authentication
router.use(authenticateToken);

// Admin dashboard and overview
router.get('/dashboard', 
  requireAdmin,
  AdminController.getDashboard as any
);

// User management routes
router.get('/users',
  requireAdmin,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('search').optional().isString().trim(),
    query('status').optional().isIn(['active', 'suspended', 'banned']),
    query('sortBy').optional().isIn(['email', 'display_name', 'created_at', 'updated_at']),
    query('sortOrder').optional().isIn(['asc', 'desc'])
  ],
  validateRequest('adminGetUsers'),
  AdminController.getUsers as any
);

router.put('/users/:id/status',
  requireAdmin,
  [
    param('id').isUUID(),
    body('status').isIn(['active', 'suspended', 'banned']),
    body('reason').optional().isString().trim().isLength({ max: 500 })
  ],
  validateRequest('adminUpdateUserStatus'),
  AdminController.updateUserStatus as any
);

router.put('/users/:id/admin',
  requireSuperAdmin, // Only super admin can promote/demote admins
  [
    param('id').isUUID(),
    body('isAdmin').isBoolean()
  ],
  validateRequest('adminToggleAdminStatus'),
  AdminController.toggleAdminStatus as any
);

// Content moderation routes
router.get('/content/reports',
  requireAdmin,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('status').optional().isIn(['pending', 'reviewed', 'resolved', 'dismissed'])
  ],
  validateRequest('adminGetContentReports'),
  AdminController.getContentReports as any
);

// System monitoring routes
router.get('/system/metrics',
  requireAdmin,
  [
    query('hours').optional().isInt({ min: 1, max: 168 }) // Max 1 week
  ],
  validateRequest('adminGetSystemMetrics'),
  AdminController.getSystemMetrics as any
);

router.get('/system/config',
  requireAdmin,
  AdminController.getSystemConfig as any
);

// System configuration routes (super admin only)
router.put('/system/config',
  requireSuperAdmin,
  [
    body('features').optional().isObject(),
    body('limits').optional().isObject(),
    body('maintenance').optional().isObject(),
    body('notifications').optional().isObject()
  ],
  validateRequest('adminUpdateSystemConfig'),
  AdminController.updateSystemConfig as any
);

// Maintenance mode toggle (super admin only)
router.post('/system/maintenance',
  requireSuperAdmin,
  [
    body('enabled').isBoolean(),
    body('message').optional().isString().trim().isLength({ max: 200 })
  ],
  validateRequest('adminToggleMaintenance'),
  AdminController.toggleMaintenanceMode as any
);

// Content moderation action routes
router.put('/content/reports/:id/status',
  requireAdmin,
  [
    param('id').isUUID(),
    body('status').isIn(['pending', 'under_review', 'resolved', 'dismissed']),
    body('resolution').optional().isString().trim().isLength({ max: 1000 })
  ],
  validateRequest('adminUpdateReportStatus'),
  AdminController.updateReportStatus as any
);

router.post('/content/reports/:id/action',
  requireAdmin,
  [
    param('id').isUUID(),
    body('actionType').isIn(['warn', 'suspend', 'ban', 'delete_content', 'no_action']),
    body('duration').optional().isInt({ min: 1 }),
    body('reason').isString().trim().isLength({ min: 1, max: 500 })
  ],
  validateRequest('adminCreateModerationAction'),
  AdminController.createModerationAction as any
);

// Admin notification routes
router.get('/notifications',
  requireAdmin,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('type').optional().isIn(['critical', 'warning', 'info', 'success']),
    query('category').optional().isIn(['content_report', 'user_action', 'system', 'security']),
    query('read').optional().isBoolean()
  ],
  validateRequest('adminGetNotifications'),
  AdminController.getNotifications as any
);

router.get('/notifications/unread-count',
  requireAdmin,
  AdminController.getUnreadNotificationCount as any
);

router.put('/notifications/:id/read',
  requireAdmin,
  [
    param('id').isUUID()
  ],
  validateRequest('adminMarkNotificationRead'),
  AdminController.markNotificationAsRead as any
);

router.put('/notifications/mark-all-read',
  requireAdmin,
  AdminController.markAllNotificationsAsRead as any
);

router.delete('/notifications/:id',
  requireAdmin,
  [
    param('id').isUUID()
  ],
  validateRequest('adminDeleteNotification'),
  AdminController.deleteNotification as any
);

export default router;
# Testing Guide

This document provides comprehensive instructions for running the test suite implemented in tasks 15.1 and 15.2 of the note-taking application.

## Overview

The project includes a comprehensive testing suite covering:
- **Backend Tests (Task 15.1)**: Unit tests, integration tests, security tests, API endpoint tests
- **Frontend Tests (Task 15.2)**: Component tests, E2E tests, performance tests, accessibility tests

## Prerequisites

Ensure you have the following installed:
- Node.js (v20.19.0 or >=22.12.0)
- npm or yarn package manager
- All project dependencies installed

```bash
# Install root dependencies
npm install

# Install backend dependencies
cd backend && npm install && cd ..

# Install frontend dependencies  
cd frontend && npm install && cd ..
```

## Quick Start

### Run All Tests
```bash
# From project root - runs both backend and frontend tests
npm test
```

### Run Tests by Category
```bash
# Backend tests only
npm run test:backend

# Frontend tests only
npm run test:frontend

# Watch mode for development (both backend and frontend)
npm run test:watch
```

## Backend Tests (Task 15.1)

The backend test suite includes comprehensive coverage of all API endpoints, authentication flows, database operations, and security measures.

### Test Structure
```
backend/src/__tests__/
├── controllers/          # Controller unit tests
├── integration/          # Integration tests
├── security/            # Security and validation tests
├── services/            # Service layer tests
├── unit/               # Unit tests
├── security.test.ts    # Main security test suite
├── shared-note.test.ts # Shared note functionality tests
└── setup.ts           # Test environment configuration
```

### Running Backend Tests

```bash
cd backend

# Run all backend tests
npm test

# Run tests in watch mode (for development)
npm run test:watch

# Run specific test files
npx vitest src/__tests__/security.test.ts --run
npx vitest src/__tests__/shared-note.test.ts --run

# Run tests by category
npx vitest src/__tests__/controllers/ --run
npx vitest src/__tests__/integration/ --run
npx vitest src/__tests__/security/ --run
npx vitest src/__tests__/services/ --run
npx vitest src/__tests__/unit/ --run

# Run with coverage report
npx vitest --coverage
```

### Backend Test Categories

1. **Unit Tests**: Individual function and method testing
2. **Integration Tests**: API endpoint testing with database
3. **Security Tests**: Authentication, authorization, input validation
4. **Controller Tests**: HTTP request/response handling
5. **Service Tests**: Business logic validation
6. **Database Tests**: Data persistence and retrieval

## Frontend Tests (Task 15.2)

The frontend test suite covers Vue components, user workflows, performance, and accessibility.

### Test Structure
```
frontend/src/__tests__/
├── accessibility/       # Accessibility compliance tests
├── components/         # Vue component unit tests
├── e2e/               # End-to-end user journey tests
├── performance/       # Performance regression tests
└── App.spec.ts       # Main application tests

frontend/src/router/__tests__/    # Router tests
frontend/src/views/__tests__/     # View component tests
```

### Running Frontend Tests

```bash
cd frontend

# Run all frontend tests
npm run test:unit

# Run tests in watch mode
npx vitest

# Run specific test categories
npx vitest src/__tests__/components/ --run
npx vitest src/__tests__/e2e/ --run
npx vitest src/__tests__/performance/ --run
npx vitest src/__tests__/accessibility/ --run

# Run with coverage report
npx vitest --coverage
```

### Frontend Test Categories

1. **Component Tests**: Vue component rendering and behavior
2. **E2E Tests**: Complete user workflows and interactions
3. **Performance Tests**: Load time and rendering performance
4. **Accessibility Tests**: WCAG compliance and screen reader support
5. **Router Tests**: Navigation and route guards
6. **Store Tests**: Pinia state management

## Test Configuration

### Backend Configuration
- **Framework**: Vitest
- **Environment**: Node.js
- **Setup**: `backend/src/__tests__/setup.ts`
- **Config**: `backend/vitest.config.ts`

### Frontend Configuration
- **Framework**: Vitest + Vue Test Utils
- **Environment**: jsdom
- **Config**: `frontend/vitest.config.ts`
- **Browser**: jsdom for DOM simulation

## Development Workflow

### Running Tests During Development

```bash
# Start both backend and frontend in watch mode
npm run test:watch

# Or run them separately in different terminals
cd backend && npm run test:watch
cd frontend && npx vitest
```

### Before Committing Code

```bash
# Run full test suite
npm test

# Check test coverage
cd backend && npx vitest --coverage
cd frontend && npx vitest --coverage
```

## Continuous Integration

For CI/CD pipelines, use:

```bash
# Single command for all tests
npm test

# Or run with coverage reports
cd backend && npx vitest --coverage --reporter=json --outputFile=coverage/coverage.json
cd frontend && npx vitest --coverage --reporter=json --outputFile=coverage/coverage.json
```

## Test Coverage Goals

The test suite aims for:
- **Backend**: >90% code coverage
- **Frontend**: >85% code coverage
- **Critical paths**: 100% coverage (auth, data persistence, security)

## Debugging Tests

### Backend Debugging
```bash
# Run specific test with verbose output
npx vitest src/__tests__/security.test.ts --run --reporter=verbose

# Debug with Node.js inspector
node --inspect-brk ./node_modules/.bin/vitest src/__tests__/security.test.ts
```

### Frontend Debugging
```bash
# Run with browser debugging
npx vitest --ui

# Debug specific component
npx vitest src/__tests__/components/NoteEditor.spec.ts --run --reporter=verbose
```

## Common Issues and Solutions

### Database Connection Issues
```bash
# Ensure test database is properly configured
cd backend
npm run test -- --reporter=verbose
```

### Frontend Component Mount Issues
```bash
# Check if all dependencies are properly mocked
cd frontend
npx vitest src/__tests__/components/ --run --reporter=verbose
```

### Performance Test Failures
```bash
# Run performance tests in isolation
cd frontend
npx vitest src/__tests__/performance/ --run --no-coverage
```

## Test Data and Fixtures

- **Backend**: Test fixtures in `backend/src/__tests__/fixtures/`
- **Frontend**: Mock data in `frontend/src/__tests__/mocks/`
- **Database**: Test database automatically created and cleaned up

## Security Testing

The security test suite validates:
- Input sanitization and validation
- Authentication and authorization
- XSS and SQL injection prevention
- Rate limiting and DDoS protection
- CSRF protection
- Security headers

## Performance Testing

Performance tests ensure:
- Page load times <2 seconds
- Search response times <500ms
- Component render times <100ms
- Memory usage within acceptable limits

## Accessibility Testing

Accessibility tests verify:
- WCAG 2.1 AA compliance
- Screen reader compatibility
- Keyboard navigation
- Color contrast ratios
- Focus management

## Contributing

When adding new features:
1. Write tests first (TDD approach)
2. Ensure all existing tests pass
3. Add appropriate test coverage
4. Update this documentation if needed

## Support

For test-related issues:
1. Check the test output for specific error messages
2. Verify all dependencies are installed
3. Ensure database connections are working
4. Check that environment variables are set correctly

---

**Last Updated**: Based on tasks 15.1 and 15.2 implementation
**Test Framework**: Vitest
**Coverage Target**: >85% overall coverage
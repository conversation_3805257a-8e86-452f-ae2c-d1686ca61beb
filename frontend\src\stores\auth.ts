import { defineS<PERSON> } from 'pinia'
import { ref, computed } from 'vue'

interface User {
  id: string
  email: string
  displayName: string
  avatarUrl?: string
  emailVerified: boolean
  oauth_provider?: string
  two_fa_secret?: string
  created_at?: string
  preferences?: any
  admin?: boolean
}

interface LoginCredentials {
  email: string
  password: string
}

interface RegisterData {
  email: string
  password: string
  displayName: string
}

interface ResetPasswordData {
  token: string
  password: string
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refresh_token'))
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const isInitialized = ref(false) // Add initialization flag
  const isLoggingOut = ref(false) // Add logout flag
  const isInitializing = ref(false) // Add flag to prevent concurrent initialization

  const isAuthenticated = computed(() => {
    // If logout is in progress, user is not authenticated
    if (isLoggingOut.value) return false
    
    // Check for logout in progress in sessionStorage
    const logoutInProgress = sessionStorage.getItem('logout_in_progress')
    if (logoutInProgress) return false
    
    // If no token, user is not authenticated
    if (!token.value) return false
    
    // User is authenticated if they have both token and user data
    if (token.value && user.value) return true
    
    // Also consider authenticated if we have a token (but not during logout)
    // This provides resilience during network issues or initialization delays
    if (token.value) return true
    
    return false
  })
  const isAdmin = computed(() => {
    if (!user.value) {
      // If user data isn't loaded yet but we have a token, 
      // check localStorage for cached admin status
      const cachedUser = localStorage.getItem('cached_user')
      if (cachedUser && token.value) {
        try {
          const userData = JSON.parse(cachedUser)
          return userData.admin === true || 
                 userData.preferences?.isAdmin === true || 
                 userData.preferences?.isAdmin === 1 || 
                 userData.email === '<EMAIL>'
        } catch (error) {
          console.warn('Failed to parse cached user data:', error)
        }
      }
      // If we have a token, assume non-admin until proven otherwise
      // This prevents redirecting logged-in users to login page
      if (token.value) return false
      return false
    }
    return user.value.admin === true || 
           user.value.preferences?.isAdmin === true || 
           user.value.preferences?.isAdmin === 1 || 
           user.value.email === '<EMAIL>'
  })

  const setTokens = (accessToken: string, refreshTokenValue: string) => {
    token.value = accessToken
    refreshToken.value = refreshTokenValue
    localStorage.setItem('auth_token', accessToken)
    localStorage.setItem('refresh_token', refreshTokenValue)
  }
  
  // Save user preferences for modals
  const saveModalPreferences = (preferences: { settingsModalOpen?: boolean, searchModalOpen?: boolean, shareModalOpen?: boolean }) => {
    try {
      if (preferences.settingsModalOpen !== undefined) {
        localStorage.setItem('settingsModalOpen', preferences.settingsModalOpen.toString())
      }
      if (preferences.searchModalOpen !== undefined) {
        localStorage.setItem('searchModalOpen', preferences.searchModalOpen.toString())
      }
      if (preferences.shareModalOpen !== undefined) {
        localStorage.setItem('shareModalOpen', preferences.shareModalOpen.toString())
      }
    } catch (error) {
      console.warn('Failed to save modal preferences:', error)
    }
  }
  
  // Load user preferences for modals
  const loadModalPreferences = () => {
    try {
      const preferences = {
        settingsModalOpen: localStorage.getItem('settingsModalOpen') === 'true',
        searchModalOpen: localStorage.getItem('searchModalOpen') === 'true',
        shareModalOpen: localStorage.getItem('shareModalOpen') === 'true'
      }
      return preferences
    } catch (error) {
      console.warn('Failed to load modal preferences:', error)
      return {
        settingsModalOpen: false,
        searchModalOpen: false,
        shareModalOpen: false
      }
    }
  }
  
  // Enhanced theme preference management
  const saveThemePreference = (mode: 'light' | 'dark' | 'auto', themeName?: string) => {
    try {
      // Save theme mode (light/dark/auto)
      localStorage.setItem('theme-mode', mode)
      
      // Save specific theme name if provided and not in auto mode
      if (themeName && mode !== 'auto') {
        localStorage.setItem('theme-name', themeName)
      } else if (mode === 'auto') {
        // Remove specific theme preference in auto mode
        localStorage.removeItem('theme-name')
      }
      
      // Keep legacy 'theme' key for backward compatibility
      localStorage.setItem('theme', mode)
    } catch (error) {
      console.warn('Failed to save theme preference:', error)
    }
  }
  
  // Load user theme preference with enhanced support
  const loadThemePreference = () => {
    try {
      const mode = localStorage.getItem('theme-mode') as 'light' | 'dark' | 'auto' | null
      const themeName = localStorage.getItem('theme-name')
      const legacyTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'auto' | null
      
      // Use new format if available, otherwise fall back to legacy
      const themeMode = mode || legacyTheme || 'auto'
      
      return {
        mode: themeMode,
        themeName: themeName || null
      }
    } catch (error) {
      console.warn('Failed to load theme preference:', error)
      return {
        mode: 'auto' as const,
        themeName: null
      }
    }
  }
  
  // Validate theme name against available themes
  const validateThemeName = (themeName: string, availableThemes: string[] = ['default', 'darkly', 'flatly', 'cerulean']) => {
    return availableThemes.includes(themeName)
  }
  
  // Get fallback theme based on mode
  const getFallbackTheme = (mode: 'light' | 'dark' | 'auto') => {
    switch (mode) {
      case 'dark':
        return 'darkly'
      case 'light':
        return 'default'
      case 'auto':
        // Use system preference to determine fallback
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        return prefersDark ? 'darkly' : 'default'
      default:
        return 'default'
    }
  }
  
  // Clear theme preferences
  const clearThemePreferences = () => {
    try {
      localStorage.removeItem('theme-mode')
      localStorage.removeItem('theme-name')
      localStorage.removeItem('theme') // Legacy key
    } catch (error) {
      console.warn('Failed to clear theme preferences:', error)
    }
  }

  const clearTokens = () => {
    token.value = null
    refreshToken.value = null
    user.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('cached_user')
    // Clear modal states
    localStorage.removeItem('settingsModalOpen')
    localStorage.removeItem('searchModalOpen')
    localStorage.removeItem('shareModalOpen')
    // Note: Don't clear theme preferences on logout - they should persist
  }

  // Force clear all auth state including session storage
  const forceClearAuth = () => {
    clearTokens()
    isLoggingOut.value = false
    isLoading.value = false
    isInitialized.value = false
    sessionStorage.removeItem('logout_in_progress')
  }

  const login = async (credentials: LoginCredentials) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Login failed')
      }

      const data = await response.json()
      setTokens(data.tokens.accessToken, data.tokens.refreshToken)
      user.value = data.user
      
      // Cache user data for admin status persistence
      localStorage.setItem('cached_user', JSON.stringify(data.user))
      
      // Ensure settings modal state is cleared
      localStorage.removeItem('settingsModalOpen')
      
      resetInitialization() // Reset flag to allow proper initialization
      
      // Dispatch event to notify other stores about login
      window.dispatchEvent(new CustomEvent('user-logged-in', {
        detail: { userId: data.user.id }
      }))
      
      return { success: true }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Login failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData: RegisterData) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: userData.email,
          password: userData.password,
          display_name: userData.displayName
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Registration failed')
      }

      const data = await response.json()
      return { success: true, message: data.message }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Registration failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    isLoading.value = true
    isLoggingOut.value = true
    
    // Mark logout in progress in sessionStorage (survives page refresh)
    sessionStorage.setItem('logout_in_progress', 'true')
    
    // Clear tokens immediately to prevent router guard issues
    const currentToken = token.value
    clearTokens()
    resetInitialization() // Reset initialization flag on logout
    
    try {
      if (currentToken) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${currentToken}`,
          },
        })
      }
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      // Use force clear to ensure all auth state is properly cleared
      forceClearAuth()
      
      // Dispatch event to notify other stores about logout
      window.dispatchEvent(new CustomEvent('user-logged-out'))
      
      // Dispatch event to notify UI components about logout
      window.dispatchEvent(new CustomEvent('auth-logout-complete'))
    }
  }

  const refreshAccessToken = async () => {
    if (!refreshToken.value) {
      clearTokens()
      return false
    }

    try {
      const response = await Promise.race([
        fetch('/api/auth/refresh-token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ refreshToken: refreshToken.value }),
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Token refresh timeout')), 5000)
        )
      ]) as Response

      if (!response.ok) {
        // Only clear tokens if we get a definitive auth error
        if (response.status === 401 || response.status === 403) {
          clearTokens()
        }
        return false
      }

      const data = await response.json()
      setTokens(data.tokens.accessToken, data.tokens.refreshToken)
      
      // Fetch user profile to update user data with timeout
      try {
        const profileResponse = await Promise.race([
          fetch('/api/auth/profile', {
            headers: {
              'Authorization': `Bearer ${data.tokens.accessToken}`,
            },
          }),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Profile fetch timeout')), 3000)
          )
        ]) as Response
        
        if (profileResponse.ok) {
          const profileData = await profileResponse.json()
          user.value = profileData.user
          // Cache user data for admin status persistence
          localStorage.setItem('cached_user', JSON.stringify(profileData.user))
        } else {
          // Try to use cached user data if profile fetch fails
          const cachedUser = localStorage.getItem('cached_user')
          if (cachedUser) {
            user.value = JSON.parse(cachedUser)
            console.log('Using cached user data due to profile fetch failure')
          }
        }
      } catch (profileError) {
        console.warn('Profile fetch failed, using cached data:', profileError)
        // Try to use cached user data
        const cachedUser = localStorage.getItem('cached_user')
        if (cachedUser) {
          user.value = JSON.parse(cachedUser)
        }
      }
      
      return true
    } catch (err) {
      console.warn('Token refresh failed:', err)
      // Only clear tokens on definitive auth failures, not network timeouts
      if (err instanceof Error && (err.message.includes('401') || err.message.includes('unauthorized') || err.message.includes('403'))) {
        clearTokens()
      }
      return false
    }
  }

  const forgotPassword = async (email: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to send reset email')
      }

      const data = await response.json()
      return { success: true, message: data.message }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to send reset email'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const resetPassword = async (resetData: ResetPasswordData) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resetData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Password reset failed')
      }

      const data = await response.json()
      return { success: true, message: data.message }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Password reset failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const verifyEmail = async (token: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Email verification failed')
      }

      const data = await response.json()
      if (user.value) {
        user.value.emailVerified = true
      }
      return { success: true, message: data.message }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Email verification failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const googleAuth = async (credential: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/google', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ credential }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Google authentication failed')
      }

      const data = await response.json()
      setTokens(data.tokens.accessToken, data.tokens.refreshToken)
      user.value = data.user
      
      return { success: true, message: data.message }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Google authentication failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const getGoogleAuthUrl = async () => {
    try {
      const response = await fetch('/api/auth/google/url')
      
      if (!response.ok) {
        throw new Error('Failed to get Google auth URL')
      }

      const data = await response.json()
      return { success: true, authUrl: data.authUrl }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to get Google auth URL'
      return { success: false, error: error.value }
    }
  }

  // Update user data (for profile updates)
  const updateUser = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
    }
  }

  // Initialize user from token on store creation
  const initializeAuth = async () => {
    console.log('Starting auth initialization...')
    
    // Check if logout is in progress
    const logoutInProgress = sessionStorage.getItem('logout_in_progress')
    if (logoutInProgress) {
      console.log('Logout in progress, clearing all auth data and skipping initialization')
      clearTokens()
      sessionStorage.removeItem('logout_in_progress')
      isInitialized.value = true
      return
    }
    
    // Prevent re-initialization if already done or if we're logging out
    if (isInitialized.value || isLoggingOut.value) {
      console.log('Auth already initialized or logging out, skipping...')
      return
    }

    // If we don't have a refresh token and no token, we're in guest mode
    if (!refreshToken.value && !token.value) {
      console.log('No tokens found, starting in guest mode')
      isInitialized.value = true
      console.log('Auth initialization completed')
      return
    }

    // If we have a token but no refresh token, use cached data (but not during logout)
    if (token.value && !refreshToken.value && !isLoggingOut.value) {
      console.log('Access token found but no refresh token, using cached user data if available')
      const cachedUser = localStorage.getItem('cached_user')
      if (cachedUser) {
        try {
          user.value = JSON.parse(cachedUser)
          console.log('Using cached user data')
        } catch (parseError) {
          console.warn('Failed to parse cached user data:', parseError)
        }
      }
      isInitialized.value = true
      console.log('Auth initialization completed')
      return
    }

    // Only try to refresh if we have a refresh token
    if (refreshToken.value) {
      try {
        console.log('Attempting to refresh access token...')
        const success = await refreshAccessToken()
        if (!success) {
          console.log('Token refresh failed, trying cached user data')
          // Try to use cached user data before clearing tokens (but not during logout)
          const cachedUser = localStorage.getItem('cached_user')
          if (cachedUser && token.value && !isLoggingOut.value) {
            try {
              user.value = JSON.parse(cachedUser)
              console.log('Using cached user data, tokens preserved')
            } catch (parseError) {
              console.warn('Failed to parse cached user data:', parseError)
              // Don't clear tokens immediately, let the app continue with cached data
            }
          } else if (isLoggingOut.value) {
            console.log('Logout in progress, not using cached data')
          }
        } else {
          console.log('Auth initialized successfully with existing tokens')
        }
      } catch (error) {
        console.warn('Auth initialization error:', error)
        // Try cached user data before giving up (but not during logout)
        const cachedUser = localStorage.getItem('cached_user')
        if (cachedUser && token.value && !isLoggingOut.value) {
          try {
            user.value = JSON.parse(cachedUser)
            console.log('Using cached user data due to initialization error')
          } catch (parseError) {
            console.warn('Failed to parse cached user data:', parseError)
          }
        } else if (isLoggingOut.value) {
          console.log('Logout in progress, not using cached data')
        }
      }
    } else {
      console.log('No refresh token found, starting in guest mode')
    }
    
    // Mark as initialized
    isInitialized.value = true
    console.log('Auth initialization completed')
  }

  // Initialize auth with timeout handling and graceful degradation
  const initializeAuthWithTimeout = async (timeoutMs: number = 5000) => {
    // Prevent re-initialization if already done or currently initializing
    if (isInitialized.value) {
      console.log('Auth already initialized, skipping timeout initialization...')
      return
    }
    
    if (isInitializing.value) {
      console.log('Auth initialization already in progress, waiting for completion...')
      // Wait for current initialization to complete instead of skipping
      let attempts = 0
      const maxAttempts = Math.ceil(timeoutMs / 100)
      
      while (isInitializing.value && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100))
        attempts++
      }
      
      if (isInitialized.value) {
        console.log('Auth initialization completed while waiting')
        return
      } else if (attempts >= maxAttempts) {
        console.warn('Timeout waiting for auth initialization to complete')
        return
      }
    }
    
    isInitializing.value = true

    try {
      // Use cached user data immediately if available while initializing in background
      const cachedUser = localStorage.getItem('cached_user')
      if (cachedUser && token.value && !user.value) {
        try {
          user.value = JSON.parse(cachedUser)
          console.log('Using cached user data while initializing auth in background')
        } catch (parseError) {
          console.warn('Failed to parse cached user data:', parseError)
        }
      }

      // Run initialization with increased timeout for better reliability
      await Promise.race([
        initializeAuth(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Auth initialization timeout')), timeoutMs)
        )
      ])
      console.log('Auth initialization completed successfully')
    } catch (error) {
      const err = error as unknown as { message?: string }
      console.warn('Auth initialization failed or timed out:', err?.message)
      
      // Only clear tokens if we get a definitive auth failure, not on timeout
      if (err?.message && (err.message.includes('401') || err.message.includes('unauthorized') || err.message.includes('invalid'))) {
        console.log('Clearing tokens due to auth failure')
        clearTokens()
        
        // Dispatch guest mode event
        window.dispatchEvent(new CustomEvent('auth-guest-mode', {
          detail: { reason: 'auth-failure' }
        }))
      } else {
        console.log('Auth timeout - keeping tokens and cached data, will retry later')
        // Don't clear tokens on timeout - they might still be valid
        // Keep using cached user data if we have it
        if (!user.value && token.value) {
          const cachedUser = localStorage.getItem('cached_user')
          if (cachedUser) {
            try {
              user.value = JSON.parse(cachedUser)
              console.log('Fallback to cached user data after timeout')
            } catch (parseError) {
              console.warn('Failed to parse cached user data:', parseError)
            }
          }
        }
        
        // Dispatch timeout event instead
        window.dispatchEvent(new CustomEvent('auth-timeout', {
          detail: { 
            reason: 'initialization-timeout',
            hasToken: !!token.value,
            hasUser: !!user.value
          }
        }))
      }
      
      // Mark as initialized to prevent retries
      isInitialized.value = true
      
      // Don't throw - allow app to continue
    } finally {
      isInitializing.value = false
    }
  }

  // Force re-initialization (for logout/login scenarios)
  const resetInitialization = () => {
    isInitialized.value = false
    isInitializing.value = false
  }

  // Retry auth initialization (useful after network issues)
  const retryInitialization = async () => {
    if (token.value && !isLoggingOut.value && !isInitializing.value) {
      console.log('Retrying auth initialization...')
      resetInitialization()
      await initializeAuthWithTimeout(5000)
    }
  }

  return {
    user,
    token,
    isAuthenticated,
    isAdmin,
    isLoading,
    error,
    isInitialized, // Export the flag
    isLoggingOut, // Export logout flag
    isInitializing, // Export initializing flag
    login,
    register,
    logout,
    refreshAccessToken,
    forgotPassword,
    resetPassword,
    verifyEmail,
    googleAuth,
    getGoogleAuthUrl,
    initializeAuth,
    initializeAuthWithTimeout,
    resetInitialization, // Export reset method
    retryInitialization, // Export retry method
    updateUser,
    setTokens,
    clearTokens,
    saveModalPreferences,
    loadModalPreferences,
    saveThemePreference,
    loadThemePreference,
    validateThemeName,
    getFallbackTheme,
    clearThemePreferences,
    forceClearAuth
  }
})
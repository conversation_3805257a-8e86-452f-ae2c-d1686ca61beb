<!DOCTYPE html>
<html>
<head>
    <title>Tag Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Tag Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Check if tags exist in database</h2>
        <button onclick="testTagsExist()">Test Tags Exist</button>
        <div id="tags-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Check if notes have tags</h2>
        <button onclick="testNotesWithTags()">Test Notes with Tags</button>
        <div id="notes-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Create a new tag</h2>
        <input type="text" id="new-tag-name" placeholder="Enter tag name" value="test-tag">
        <button onclick="testCreateTag()">Create Tag</button>
        <div id="create-tag-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        // You'll need to get a valid JWT token from the browser's localStorage
        // after logging in to the app
        function getAuthToken() {
            return localStorage.getItem('authToken') || 'Bearer your-token-here';
        }
        
        async function testTagsExist() {
            const resultDiv = document.getElementById('tags-result');
            try {
                const response = await fetch(`${API_BASE}/tags`, {
                    headers: {
                        'Authorization': getAuthToken(),
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Found ${data.tags.length} tags: ${data.tags.map(t => t.name).join(', ')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Error: ${response.status} - ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
        
        async function testNotesWithTags() {
            const resultDiv = document.getElementById('notes-result');
            try {
                const response = await fetch(`${API_BASE}/notes`, {
                    headers: {
                        'Authorization': getAuthToken(),
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const notesWithTags = data.notes.filter(note => note.tags && note.tags.length > 0);
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Found ${notesWithTags.length} notes with tags out of ${data.notes.length} total notes`;
                    
                    if (notesWithTags.length > 0) {
                        resultDiv.innerHTML += '<br><br>Notes with tags:<br>';
                        notesWithTags.forEach(note => {
                            resultDiv.innerHTML += `• ${note.title}: [${note.tags.map(t => t.name).join(', ')}]<br>`;
                        });
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Error: ${response.status} - ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
        
        async function testCreateTag() {
            const resultDiv = document.getElementById('create-tag-result');
            const tagName = document.getElementById('new-tag-name').value;
            
            if (!tagName) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Please enter a tag name';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/tags`, {
                    method: 'POST',
                    headers: {
                        'Authorization': getAuthToken(),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name: tagName })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Created tag: ${data.name} (ID: ${data.id})`;
                } else {
                    const errorData = await response.json();
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Error: ${response.status} - ${errorData.error || response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import RichTextEditor from '../RichTextEditor.vue'

describe('RichTextEditor', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(RichTextEditor, {
      props: {
        modelValue: '<p>Test content</p>',
        placeholder: 'Test placeholder'
      }
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('renders correctly', () => {
    expect(wrapper.find('.rich-text-editor').exists()).toBe(true)
    expect(wrapper.find('.toolbar').exists()).toBe(true)
    expect(wrapper.find('.editor-content').exists()).toBe(true)
  })

  it('displays toolbar buttons', () => {
    const toolbar = wrapper.find('.toolbar')
    
    // Check for basic formatting buttons
    expect(toolbar.find('[title="Bold (Ctrl+B)"]').exists()).toBe(true)
    expect(toolbar.find('[title="Italic (Ctrl+I)"]').exists()).toBe(true)
    expect(toolbar.find('[title="Underline (Ctrl+U)"]').exists()).toBe(true)
    
    // Check for header buttons
    expect(toolbar.find('[title="Heading 1"]').exists()).toBe(true)
    expect(toolbar.find('[title="Heading 2"]').exists()).toBe(true)
    expect(toolbar.find('[title="Heading 3"]').exists()).toBe(true)
    
    // Check for list buttons
    expect(toolbar.find('[title="Bullet List"]').exists()).toBe(true)
    expect(toolbar.find('[title="Numbered List"]').exists()).toBe(true)
    
    // Check for link and image buttons
    expect(toolbar.find('[title="Add Link (Ctrl+K)"]').exists()).toBe(true)
    expect(toolbar.find('[title="Add Image"]').exists()).toBe(true)
    expect(toolbar.find('[title="Upload Image"]').exists()).toBe(true)
    
    // Check for table buttons
    expect(toolbar.find('[title="Insert Table"]').exists()).toBe(true)
    expect(toolbar.find('[title="Add Column Before"]').exists()).toBe(true)
    expect(toolbar.find('[title="Delete Table"]').exists()).toBe(true)
    
    // Check for alignment buttons
    expect(toolbar.find('[title="Align Left"]').exists()).toBe(true)
    expect(toolbar.find('[title="Align Center"]').exists()).toBe(true)
    expect(toolbar.find('[title="Align Right"]').exists()).toBe(true)
    
    // Check for color and highlight
    expect(toolbar.find('[title="Text Color"]').exists()).toBe(true)
    expect(toolbar.find('[title="Highlight"]').exists()).toBe(true)
    
    // Check for undo/redo buttons
    expect(toolbar.find('[title="Undo (Ctrl+Z)"]').exists()).toBe(true)
    expect(toolbar.find('[title="Redo (Ctrl+Y)"]').exists()).toBe(true)
  })

  it('emits update:modelValue when content changes', async () => {
    // Wait for editor to be initialized
    await wrapper.vm.$nextTick()
    
    // Simulate content change by updating the model value
    await wrapper.setProps({ modelValue: '<p>Updated content</p>' })
    
    // The component should handle the prop change
    expect(wrapper.props('modelValue')).toBe('<p>Updated content</p>')
  })

  it('accepts disabled prop', async () => {
    await wrapper.setProps({ disabled: true })
    
    // The editor should be set to non-editable when disabled
    await wrapper.vm.$nextTick()
    
    if (wrapper.vm.editor) {
      expect(wrapper.vm.editor.isEditable).toBe(false)
    }
  })

  it('has keyboard shortcuts functionality', () => {
    // Check that keyboard shortcut handler is defined
    expect(typeof wrapper.vm.handleKeyDown).toBe('function')
  })

  it('has content validation functionality', () => {
    // Check that content validation function is defined
    expect(typeof wrapper.vm.validateContent).toBe('function')
    
    // Test basic sanitization
    const maliciousContent = '<script>alert("xss")</script><p>Safe content</p>'
    const sanitized = wrapper.vm.validateContent(maliciousContent)
    expect(sanitized).not.toContain('<script>')
    expect(sanitized).toContain('<p>Safe content</p>')
  })
})
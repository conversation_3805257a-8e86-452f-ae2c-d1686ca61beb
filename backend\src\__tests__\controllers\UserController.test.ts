import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { UserController } from '../../controllers/UserController';
import { User } from '../../models/User';
import { authMiddleware } from '../../middleware/auth';
import bcrypt from 'bcryptjs';

// Mock dependencies
vi.mock('../../models/User');
vi.mock('../../middleware/auth');
vi.mock('bcryptjs');

const app = express();
app.use(express.json());

// Mock auth middleware
vi.mocked(authMiddleware).mockImplementation((req: any, res, next) => {
  req.user = { id: 'user-123', email: '<EMAIL>' };
  next();
});

// Setup routes
const userController = new UserController();
app.get('/user/profile', authMiddleware, userController.getProfile.bind(userController));
app.put('/user/profile', authMiddleware, userController.updateProfile.bind(userController));
app.get('/user/settings', authMiddleware, userController.getSettings.bind(userController));
app.put('/user/settings', authMiddleware, userController.updateSettings.bind(userController));
app.put('/user/password', authMiddleware, userController.changePassword.bind(userController));
app.delete('/user/account', authMiddleware, userController.deleteAccount.bind(userController));
app.post('/user/export', authMiddleware, userController.exportData.bind(userController));

describe('UserController', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('GET /user/profile', () => {
    it('should return user profile', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        displayName: 'Test User',
        avatarUrl: 'https://example.com/avatar.jpg',
        emailVerified: true,
        twoFactorEnabled: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.mocked(User.findById).mockResolvedValue(mockUser);

      const response = await request(app)
        .get('/user/profile');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', 'user-123');
      expect(response.body).toHaveProperty('email', '<EMAIL>');
      expect(response.body).toHaveProperty('displayName', 'Test User');
      expect(response.body).not.toHaveProperty('passwordHash');
    });

    it('should return 404 if user not found', async () => {
      vi.mocked(User.findById).mockResolvedValue(null);

      const response = await request(app)
        .get('/user/profile');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'User not found');
    });
  });

  describe('PUT /user/profile', () => {
    it('should update user profile successfully', async () => {
      const mockUpdatedUser = {
        id: 'user-123',
        email: '<EMAIL>',
        displayName: 'Updated Name',
        avatarUrl: 'https://example.com/new-avatar.jpg',
        updatedAt: new Date()
      };

      vi.mocked(User.update).mockResolvedValue(mockUpdatedUser);

      const response = await request(app)
        .put('/user/profile')
        .send({
          displayName: 'Updated Name',
          avatarUrl: 'https://example.com/new-avatar.jpg'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('displayName', 'Updated Name');
      expect(User.update).toHaveBeenCalledWith('user-123', {
        displayName: 'Updated Name',
        avatarUrl: 'https://example.com/new-avatar.jpg'
      });
    });

    it('should validate display name length', async () => {
      const response = await request(app)
        .put('/user/profile')
        .send({
          displayName: 'A'.repeat(101) // Too long
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should validate avatar URL format', async () => {
      const response = await request(app)
        .put('/user/profile')
        .send({
          avatarUrl: 'invalid-url'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /user/settings', () => {
    it('should return user settings', async () => {
      const mockSettings = {
        theme: 'dark',
        language: 'en',
        timezone: 'UTC',
        autoSaveInterval: 30000,
        notifications: {
          email: true,
          push: false,
          mentions: true
        }
      };

      vi.mocked(User.getSettings).mockResolvedValue(mockSettings);

      const response = await request(app)
        .get('/user/settings');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('theme', 'dark');
      expect(response.body).toHaveProperty('autoSaveInterval', 30000);
      expect(response.body.notifications).toHaveProperty('email', true);
    });

    it('should return default settings if none exist', async () => {
      vi.mocked(User.getSettings).mockResolvedValue(null);

      const response = await request(app)
        .get('/user/settings');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('theme', 'light'); // Default theme
      expect(response.body).toHaveProperty('language', 'en'); // Default language
    });
  });

  describe('PUT /user/settings', () => {
    it('should update user settings successfully', async () => {
      const newSettings = {
        theme: 'dark',
        language: 'es',
        autoSaveInterval: 60000,
        notifications: {
          email: false,
          push: true,
          mentions: true
        }
      };

      vi.mocked(User.updateSettings).mockResolvedValue(newSettings);

      const response = await request(app)
        .put('/user/settings')
        .send(newSettings);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('theme', 'dark');
      expect(response.body).toHaveProperty('language', 'es');
      expect(User.updateSettings).toHaveBeenCalledWith('user-123', newSettings);
    });

    it('should validate theme values', async () => {
      const response = await request(app)
        .put('/user/settings')
        .send({
          theme: 'invalid-theme'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should validate auto-save interval range', async () => {
      const response = await request(app)
        .put('/user/settings')
        .send({
          autoSaveInterval: 1000 // Too short
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('PUT /user/password', () => {
    it('should change password successfully', async () => {
      const mockUser = {
        id: 'user-123',
        passwordHash: 'oldHashedPassword'
      };

      vi.mocked(User.findById).mockResolvedValue(mockUser);
      vi.mocked(bcrypt.compare).mockResolvedValue(true);
      vi.mocked(bcrypt.hash).mockResolvedValue('newHashedPassword');
      vi.mocked(User.updatePassword).mockResolvedValue(true);

      const response = await request(app)
        .put('/user/password')
        .send({
          currentPassword: 'oldPassword123',
          newPassword: 'newPassword456'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Password changed successfully');
      expect(User.updatePassword).toHaveBeenCalledWith('user-123', 'newHashedPassword');
    });

    it('should return 400 for incorrect current password', async () => {
      const mockUser = {
        id: 'user-123',
        passwordHash: 'hashedPassword'
      };

      vi.mocked(User.findById).mockResolvedValue(mockUser);
      vi.mocked(bcrypt.compare).mockResolvedValue(false);

      const response = await request(app)
        .put('/user/password')
        .send({
          currentPassword: 'wrongPassword',
          newPassword: 'newPassword456'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Current password is incorrect');
    });

    it('should validate new password strength', async () => {
      const response = await request(app)
        .put('/user/password')
        .send({
          currentPassword: 'oldPassword123',
          newPassword: '123' // Too weak
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('DELETE /user/account', () => {
    it('should delete account successfully with correct password', async () => {
      const mockUser = {
        id: 'user-123',
        passwordHash: 'hashedPassword'
      };

      vi.mocked(User.findById).mockResolvedValue(mockUser);
      vi.mocked(bcrypt.compare).mockResolvedValue(true);
      vi.mocked(User.delete).mockResolvedValue(true);

      const response = await request(app)
        .delete('/user/account')
        .send({
          password: 'correctPassword123'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Account deleted successfully');
      expect(User.delete).toHaveBeenCalledWith('user-123');
    });

    it('should return 400 for incorrect password', async () => {
      const mockUser = {
        id: 'user-123',
        passwordHash: 'hashedPassword'
      };

      vi.mocked(User.findById).mockResolvedValue(mockUser);
      vi.mocked(bcrypt.compare).mockResolvedValue(false);

      const response = await request(app)
        .delete('/user/account')
        .send({
          password: 'wrongPassword'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Password is incorrect');
    });
  });

  describe('POST /user/export', () => {
    it('should initiate data export successfully', async () => {
      const mockExportJob = {
        id: 'export-123',
        userId: 'user-123',
        status: 'pending',
        createdAt: new Date()
      };

      vi.mocked(User.createExportJob).mockResolvedValue(mockExportJob);

      const response = await request(app)
        .post('/user/export')
        .send({
          format: 'json',
          includeNotes: true,
          includeSettings: true
        });

      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('exportId', 'export-123');
      expect(response.body).toHaveProperty('message', 'Export job created');
    });

    it('should validate export format', async () => {
      const response = await request(app)
        .post('/user/export')
        .send({
          format: 'invalid-format'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should require at least one data type to export', async () => {
      const response = await request(app)
        .post('/user/export')
        .send({
          format: 'json',
          includeNotes: false,
          includeSettings: false
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });
});
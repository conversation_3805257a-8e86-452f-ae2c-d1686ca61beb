import crypto from 'crypto';
import { UserRepository } from '../repositories/UserRepository';

export interface TwoFactorSetupResult {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

export interface TwoFactorVerificationResult {
  success: boolean;
  backupCodeUsed?: boolean;
}

export class TwoFactorAuthService {
  private static readonly BACKUP_CODES_COUNT = 10;
  private static readonly BACKUP_CODE_LENGTH = 8;
  private static readonly TOTP_WINDOW = 30; // 30 seconds
  private static readonly TOTP_DIGITS = 6;

  /**
   * Generate a new 2FA secret and setup information for a user
   */
  static async setupTwoFactor(userId: string, appName: string = 'Note Taking App'): Promise<TwoFactorSetupResult> {
    try {
      const user = await UserRepository.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Generate a random secret (base32 encoded)
      const secret = this.generateSecret();
      
      // Generate backup codes
      const backupCodes = this.generateBackupCodes();
      
      // Create QR code URL for authenticator apps
      const qrCodeUrl = this.generateQRCodeUrl(user.email, secret, appName);

      // Store the secret and backup codes (hashed) in the database
      const hashedBackupCodes = backupCodes.map(code => this.hashBackupCode(code));
      
      await UserRepository.updateTwoFactorSecret(userId, secret, hashedBackupCodes);

      return {
        secret,
        qrCodeUrl,
        backupCodes
      };
    } catch (error) {
      console.error('Error setting up 2FA:', error);
      throw new Error('Failed to setup two-factor authentication');
    }
  }

  /**
   * Verify a TOTP code or backup code
   */
  static async verifyTwoFactor(userId: string, code: string): Promise<TwoFactorVerificationResult> {
    try {
      const user = await UserRepository.findById(userId);
      if (!user || !user.two_fa_secret) {
        return { success: false };
      }

      // First try TOTP verification
      if (this.verifyTOTP(user.two_fa_secret, code)) {
        return { success: true };
      }

      // If TOTP fails, try backup codes
      const backupCodeResult = await this.verifyBackupCode(userId, code);
      if (backupCodeResult) {
        return { success: true, backupCodeUsed: true };
      }

      return { success: false };
    } catch (error) {
      console.error('Error verifying 2FA:', error);
      return { success: false };
    }
  }

  /**
   * Disable 2FA for a user
   */
  static async disableTwoFactor(userId: string): Promise<void> {
    try {
      await UserRepository.updateTwoFactorSecret(userId, null, []);
    } catch (error) {
      console.error('Error disabling 2FA:', error);
      throw new Error('Failed to disable two-factor authentication');
    }
  }

  /**
   * Check if user has 2FA enabled
   */
  static async isTwoFactorEnabled(userId: string): Promise<boolean> {
    try {
      const user = await UserRepository.findById(userId);
      return !!(user && user.two_fa_secret);
    } catch (error) {
      console.error('Error checking 2FA status:', error);
      return false;
    }
  }

  /**
   * Generate backup codes for a user
   */
  static async regenerateBackupCodes(userId: string): Promise<string[]> {
    try {
      const user = await UserRepository.findById(userId);
      if (!user || !user.two_fa_secret) {
        throw new Error('2FA not enabled for user');
      }

      const backupCodes = this.generateBackupCodes();
      const hashedBackupCodes = backupCodes.map(code => this.hashBackupCode(code));
      
      await UserRepository.updateBackupCodes(userId, hashedBackupCodes);

      return backupCodes;
    } catch (error) {
      console.error('Error regenerating backup codes:', error);
      throw new Error('Failed to regenerate backup codes');
    }
  }

  /**
   * Generate a random base32 secret
   */
  private static generateSecret(): string {
    const buffer = crypto.randomBytes(20);
    return this.base32Encode(buffer);
  }

  /**
   * Generate backup codes
   */
  private static generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < this.BACKUP_CODES_COUNT; i++) {
      codes.push(this.generateBackupCode());
    }
    return codes;
  }

  /**
   * Generate a single backup code
   */
  private static generateBackupCode(): string {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let code = '';
    for (let i = 0; i < this.BACKUP_CODE_LENGTH; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
  }

  /**
   * Hash a backup code for storage
   */
  private static hashBackupCode(code: string): string {
    return crypto.createHash('sha256').update(code).digest('hex');
  }

  /**
   * Generate QR code URL for authenticator apps
   */
  private static generateQRCodeUrl(email: string, secret: string, appName: string): string {
    const issuer = encodeURIComponent(appName);
    const account = encodeURIComponent(email);
    return `otpauth://totp/${issuer}:${account}?secret=${secret}&issuer=${issuer}&digits=${this.TOTP_DIGITS}&period=${this.TOTP_WINDOW}`;
  }

  /**
   * Verify TOTP code
   */
  private static verifyTOTP(secret: string, code: string): boolean {
    const now = Math.floor(Date.now() / 1000);
    const timeStep = Math.floor(now / this.TOTP_WINDOW);

    // Check current time step and one step before/after for clock drift
    for (let i = -1; i <= 1; i++) {
      const testTimeStep = timeStep + i;
      const expectedCode = this.generateTOTP(secret, testTimeStep);
      if (expectedCode === code) {
        return true;
      }
    }

    return false;
  }

  /**
   * Generate TOTP code for a given time step
   */
  private static generateTOTP(secret: string, timeStep: number): string {
    const secretBuffer = this.base32Decode(secret);
    const timeBuffer = Buffer.alloc(8);
    timeBuffer.writeUInt32BE(0, 0);
    timeBuffer.writeUInt32BE(timeStep, 4);

    const hmac = crypto.createHmac('sha1', secretBuffer);
    hmac.update(timeBuffer);
    const hash = hmac.digest();

    const offset = hash[hash.length - 1] & 0x0f;
    const binary = 
      ((hash[offset] & 0x7f) << 24) |
      ((hash[offset + 1] & 0xff) << 16) |
      ((hash[offset + 2] & 0xff) << 8) |
      (hash[offset + 3] & 0xff);

    const otp = binary % Math.pow(10, this.TOTP_DIGITS);
    return otp.toString().padStart(this.TOTP_DIGITS, '0');
  }

  /**
   * Verify backup code
   */
  private static async verifyBackupCode(userId: string, code: string): Promise<boolean> {
    try {
      const hashedCode = this.hashBackupCode(code);
      const result = await UserRepository.useBackupCode(userId, hashedCode);
      return result;
    } catch (error) {
      console.error('Error verifying backup code:', error);
      return false;
    }
  }

  /**
   * Base32 encoding
   */
  private static base32Encode(buffer: Buffer): string {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let bits = 0;
    let value = 0;
    let output = '';

    for (let i = 0; i < buffer.length; i++) {
      value = (value << 8) | buffer[i];
      bits += 8;

      while (bits >= 5) {
        output += alphabet[(value >>> (bits - 5)) & 31];
        bits -= 5;
      }
    }

    if (bits > 0) {
      output += alphabet[(value << (5 - bits)) & 31];
    }

    return output;
  }

  /**
   * Base32 decoding
   */
  private static base32Decode(encoded: string): Buffer {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let bits = 0;
    let value = 0;
    let index = 0;
    const output = Buffer.alloc(Math.ceil(encoded.length * 5 / 8));

    for (let i = 0; i < encoded.length; i++) {
      const char = encoded.charAt(i).toUpperCase();
      const charIndex = alphabet.indexOf(char);
      
      if (charIndex === -1) {
        continue; // Skip invalid characters
      }

      value = (value << 5) | charIndex;
      bits += 5;

      if (bits >= 8) {
        output[index++] = (value >>> (bits - 8)) & 255;
        bits -= 8;
      }
    }

    return output.slice(0, index);
  }
}
#!/usr/bin/env node

/**
 * Enhanced build script with CSS optimization and bundle analysis
 * Monitors performance budgets and generates detailed reports
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// Performance budget configuration
const budgetConfig = JSON.parse(
  readFileSync(join(projectRoot, 'performance-budget.json'), 'utf-8')
);

console.log('🚀 Starting optimized build with CSS analysis...');

// Step 1: Clean previous build
console.log('🧹 Cleaning previous build...');
try {
  execSync('rm -rf dist', { cwd: projectRoot, stdio: 'inherit' });
} catch (error) {
  // Ignore error if dist doesn't exist
}

// Step 2: Run build with bundle analysis
console.log('📦 Building with bundle analysis...');
try {
  execSync('npm run build', { cwd: projectRoot, stdio: 'inherit' });
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Step 3: Analyze bundle sizes
console.log('📊 Analyzing bundle sizes...');
const distPath = join(projectRoot, 'dist');

if (!existsSync(distPath)) {
  console.error('❌ Build output not found');
  process.exit(1);
}

// Get file sizes
function getFileSize(filePath) {
  try {
    const stats = require('fs').statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

function formatSize(bytes) {
  const kb = bytes / 1024;
  const mb = kb / 1024;

  if (mb >= 1) {
    return `${mb.toFixed(2)}MB`;
  } else {
    return `${kb.toFixed(2)}KB`;
  }
}

// Analyze CSS files
const cssFiles = [];
const jsFiles = [];

function scanDirectory(dir, files, extension) {
  const fs = require('fs');
  const path = require('path');

  try {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        scanDirectory(fullPath, files, extension);
      } else if (item.endsWith(extension)) {
        const size = getFileSize(fullPath);
        const relativePath = path.relative(distPath, fullPath);
        files.push({
          name: item,
          path: relativePath,
          size: size,
          sizeFormatted: formatSize(size),
        });
      }
    }
  } catch (error) {
    console.warn(`Warning: Could not scan directory ${dir}:`, error.message);
  }
}

scanDirectory(distPath, cssFiles, '.css');
scanDirectory(distPath, jsFiles, '.js');

// Generate report
const report = {
  timestamp: new Date().toISOString(),
  css: {
    files: cssFiles,
    totalSize: cssFiles.reduce((sum, file) => sum + file.size, 0),
    count: cssFiles.length,
  },
  js: {
    files: jsFiles,
    totalSize: jsFiles.reduce((sum, file) => sum + file.size, 0),
    count: jsFiles.length,
  },
  budgets: budgetConfig.budgets,
  violations: [],
};

// Check budget violations
function checkBudget(type, size, name = '') {
  const budget = budgetConfig.budgets.find(b => {
    if (b.type === type) {
      if (b.name && name) {
        return name.match(new RegExp(b.name.replace('*', '.*')));
      }
      return !b.name;
    }
    return false;
  });

  if (!budget) return null;

  const parseSize = sizeStr => {
    const match = sizeStr.match(/^(\d+(?:\.\d+)?)(kb|mb)$/i);
    if (!match) return 0;

    const value = parseFloat(match[1]);
    const unit = match[2].toLowerCase();

    return unit === 'mb' ? value * 1024 * 1024 : value * 1024;
  };

  const warningThreshold = parseSize(budget.maximumWarning);
  const errorThreshold = parseSize(budget.maximumError);

  if (size > errorThreshold) {
    return { level: 'error', budget, actual: size, threshold: errorThreshold };
  } else if (size > warningThreshold) {
    return { level: 'warning', budget, actual: size, threshold: warningThreshold };
  }

  return null;
}

// Check CSS file budgets
cssFiles.forEach(file => {
  let budgetType = 'anyComponentStyle';

  if (file.name.includes('theme-')) {
    budgetType = 'bundle';
  }

  const violation = checkBudget(budgetType, file.size, file.name);
  if (violation) {
    report.violations.push({
      type: 'css',
      file: file.name,
      ...violation,
    });
  }
});

// Check JS file budgets
jsFiles.forEach(file => {
  const violation = checkBudget('bundle', file.size, file.name);
  if (violation) {
    report.violations.push({
      type: 'js',
      file: file.name,
      ...violation,
    });
  }
});

// Check total initial bundle size
const initialSize = report.css.totalSize + report.js.totalSize;
const initialViolation = checkBudget('initial', initialSize);
if (initialViolation) {
  report.violations.push({
    type: 'initial',
    file: 'total',
    ...initialViolation,
  });
}

// Save report
const reportPath = join(distPath, 'performance-report.json');
writeFileSync(reportPath, JSON.stringify(report, null, 2));

// Display results
console.log('\n📊 Build Analysis Results:');
console.log('='.repeat(50));

console.log(
  `\n📄 CSS Files (${report.css.count} files, ${formatSize(report.css.totalSize)} total):`
);
cssFiles.forEach(file => {
  console.log(`  ${file.name}: ${file.sizeFormatted}`);
});

console.log(
  `\n📜 JavaScript Files (${report.js.count} files, ${formatSize(report.js.totalSize)} total):`
);
jsFiles.forEach(file => {
  console.log(`  ${file.name}: ${file.sizeFormatted}`);
});

console.log(`\n📦 Total Bundle Size: ${formatSize(initialSize)}`);

// Display budget violations
if (report.violations.length > 0) {
  console.log('\n⚠️  Budget Violations:');
  report.violations.forEach(violation => {
    const icon = violation.level === 'error' ? '❌' : '⚠️ ';
    console.log(
      `  ${icon} ${violation.file}: ${formatSize(violation.actual)} (limit: ${formatSize(violation.threshold)})`
    );
  });

  if (budgetConfig.monitoring.failOnBudgetExceeded) {
    const errors = report.violations.filter(v => v.level === 'error');
    if (errors.length > 0) {
      console.error(`\n❌ Build failed due to ${errors.length} budget error(s)`);
      process.exit(1);
    }
  }
} else {
  console.log('\n✅ All performance budgets met!');
}

console.log(`\n📊 Detailed report saved to: ${reportPath}`);

// Generate bundle visualizer if available
if (existsSync(join(distPath, 'stats.html'))) {
  console.log(`📈 Bundle visualizer available at: dist/stats.html`);
}

console.log('\n🎉 Build completed successfully!');

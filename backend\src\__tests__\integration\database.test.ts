import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { Database } from 'sqlite3';
import { User } from '../../models/User';
import { Note } from '../../models/Note';
import { Group } from '../../models/Group';
import { runMigrations } from '../../config/migrations';
import path from 'path';
import fs from 'fs';

describe('Database Integration Tests', () => {
  let testDb: Database;
  const testDbPath = path.join(__dirname, 'test.db');

  beforeAll(async () => {
    // Create test database
    testDb = new Database(testDbPath);
    
    // Run migrations
    await runMigrations(testDb);
  });

  afterAll(async () => {
    // Close database connection
    testDb.close();
    
    // Clean up test database file
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath);
    }
  });

  beforeEach(async () => {
    // Clean up test data before each test
    await new Promise<void>((resolve, reject) => {
      testDb.serialize(() => {
        testDb.run('DELETE FROM note_shares');
        testDb.run('DELETE FROM note_tags');
        testDb.run('DELETE FROM note_versions');
        testDb.run('DELETE FROM notes');
        testDb.run('DELETE FROM group_members');
        testDb.run('DELETE FROM groups');
        testDb.run('DELETE FROM users', (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    });
  });

  describe('User Model Database Operations', () => {
    it('should create and retrieve a user', async () => {
      const userData = {
        email: '<EMAIL>',
        passwordHash: 'hashedPassword123',
        displayName: 'Test User'
      };

      // Create user
      const createdUser = await User.create(userData);
      expect(createdUser).toBeDefined();
      expect(createdUser.id).toBeDefined();
      expect(createdUser.email).toBe(userData.email);
      expect(createdUser.displayName).toBe(userData.displayName);

      // Retrieve user by ID
      const retrievedUser = await User.findById(createdUser.id);
      expect(retrievedUser).toBeDefined();
      expect(retrievedUser!.email).toBe(userData.email);

      // Retrieve user by email
      const userByEmail = await User.findByEmail(userData.email);
      expect(userByEmail).toBeDefined();
      expect(userByEmail!.id).toBe(createdUser.id);
    });

    it('should update user profile', async () => {
      const user = await User.create({
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        displayName: 'Original Name'
      });

      const updateData = {
        displayName: 'Updated Name',
        avatarUrl: 'https://example.com/avatar.jpg'
      };

      const updatedUser = await User.update(user.id, updateData);
      expect(updatedUser.displayName).toBe('Updated Name');
      expect(updatedUser.avatarUrl).toBe('https://example.com/avatar.jpg');
    });

    it('should handle user settings', async () => {
      const user = await User.create({
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        displayName: 'Settings User'
      });

      const settings = {
        theme: 'dark',
        language: 'es',
        autoSaveInterval: 60000,
        notifications: {
          email: true,
          push: false
        }
      };

      // Update settings
      const updatedSettings = await User.updateSettings(user.id, settings);
      expect(updatedSettings.theme).toBe('dark');
      expect(updatedSettings.language).toBe('es');

      // Retrieve settings
      const retrievedSettings = await User.getSettings(user.id);
      expect(retrievedSettings!.theme).toBe('dark');
      expect(retrievedSettings!.notifications.email).toBe(true);
    });

    it('should delete user and cascade related data', async () => {
      const user = await User.create({
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        displayName: 'Delete User'
      });

      // Create a note for the user
      await Note.create({
        title: 'User Note',
        content: 'Content',
        noteType: 'richtext',
        userId: user.id
      });

      // Delete user
      const deleted = await User.delete(user.id);
      expect(deleted).toBe(true);

      // Verify user is deleted
      const deletedUser = await User.findById(user.id);
      expect(deletedUser).toBeNull();

      // Verify related notes are deleted (cascade)
      const userNotes = await Note.findByUserId(user.id);
      expect(userNotes.notes).toHaveLength(0);
    });
  });

  describe('Note Model Database Operations', () => {
    let testUser: any;

    beforeEach(async () => {
      testUser = await User.create({
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        displayName: 'Note Test User'
      });
    });

    it('should create and retrieve notes', async () => {
      const noteData = {
        title: 'Test Note',
        content: 'This is test content',
        noteType: 'richtext' as const,
        userId: testUser.id,
        tags: ['test', 'important']
      };

      // Create note
      const createdNote = await Note.create(noteData);
      expect(createdNote).toBeDefined();
      expect(createdNote.id).toBeDefined();
      expect(createdNote.title).toBe(noteData.title);
      expect(createdNote.tags).toEqual(noteData.tags);

      // Retrieve note by ID
      const retrievedNote = await Note.findById(createdNote.id);
      expect(retrievedNote).toBeDefined();
      expect(retrievedNote!.title).toBe(noteData.title);
      expect(retrievedNote!.userId).toBe(testUser.id);
    });

    it('should handle note pagination and filtering', async () => {
      // Create multiple notes
      const notes = await Promise.all([
        Note.create({
          title: 'Rich Text Note',
          content: 'Rich content',
          noteType: 'richtext',
          userId: testUser.id,
          tags: ['work']
        }),
        Note.create({
          title: 'Markdown Note',
          content: '# Markdown content',
          noteType: 'markdown',
          userId: testUser.id,
          tags: ['personal']
        }),
        Note.create({
          title: 'Kanban Board',
          content: JSON.stringify({ columns: [] }),
          noteType: 'kanban',
          userId: testUser.id,
          tags: ['work', 'project']
        })
      ]);

      // Test pagination
      const page1 = await Note.findByUserId(testUser.id, { page: 1, limit: 2 });
      expect(page1.notes).toHaveLength(2);
      expect(page1.total).toBe(3);

      // Test filtering by type
      const markdownNotes = await Note.findByUserId(testUser.id, { type: 'markdown' });
      expect(markdownNotes.notes).toHaveLength(1);
      expect(markdownNotes.notes[0].noteType).toBe('markdown');

      // Test filtering by tags
      const workNotes = await Note.findByUserId(testUser.id, { tags: ['work'] });
      expect(workNotes.notes).toHaveLength(2);
    });

    it('should handle note versions', async () => {
      const note = await Note.create({
        title: 'Versioned Note',
        content: 'Original content',
        noteType: 'richtext',
        userId: testUser.id
      });

      // Update note (should create version)
      const updatedNote = await Note.update(note.id, {
        content: 'Updated content'
      });
      expect(updatedNote.content).toBe('Updated content');

      // Update again
      await Note.update(note.id, {
        content: 'Final content'
      });

      // Get versions
      const versions = await Note.getVersions(note.id);
      expect(versions).toHaveLength(2); // Original + first update
      expect(versions[0].content).toBe('Original content');
      expect(versions[1].content).toBe('Updated content');
    });

    it('should handle note deletion (soft delete)', async () => {
      const note = await Note.create({
        title: 'Delete Test',
        content: 'Content to delete',
        noteType: 'richtext',
        userId: testUser.id
      });

      // Delete note
      const deleted = await Note.delete(note.id);
      expect(deleted).toBe(true);

      // Note should not be found in regular queries
      const deletedNote = await Note.findById(note.id);
      expect(deletedNote).toBeNull();

      // But should exist in database with deleted flag
      const archivedNotes = await Note.findByUserId(testUser.id, { includeArchived: true });
      const archivedNote = archivedNotes.notes.find(n => n.id === note.id);
      expect(archivedNote).toBeDefined();
      expect(archivedNote!.isArchived).toBe(true);
    });
  });

  describe('Group Model Database Operations', () => {
    let testUser: any;
    let testUser2: any;

    beforeEach(async () => {
      testUser = await User.create({
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        displayName: 'Group Owner'
      });

      testUser2 = await User.create({
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        displayName: 'Group Member'
      });
    });

    it('should create and manage groups', async () => {
      const groupData = {
        name: 'Test Group',
        description: 'A test group',
        ownerId: testUser.id
      };

      // Create group
      const createdGroup = await Group.create(groupData);
      expect(createdGroup).toBeDefined();
      expect(createdGroup.name).toBe(groupData.name);
      expect(createdGroup.ownerId).toBe(testUser.id);

      // Retrieve group
      const retrievedGroup = await Group.findById(createdGroup.id);
      expect(retrievedGroup).toBeDefined();
      expect(retrievedGroup!.name).toBe(groupData.name);
    });

    it('should handle group membership', async () => {
      const group = await Group.create({
        name: 'Membership Test',
        description: 'Testing membership',
        ownerId: testUser.id
      });

      // Add member
      await Group.addMember(group.id, testUser2.id, 'editor');

      // Get group with members
      const groupWithMembers = await Group.findByIdWithMembers(group.id);
      expect(groupWithMembers!.members).toHaveLength(2); // Owner + added member
      
      const member = groupWithMembers!.members.find(m => m.userId === testUser2.id);
      expect(member).toBeDefined();
      expect(member!.role).toBe('editor');

      // Update member role
      await Group.updateMemberRole(group.id, testUser2.id, 'viewer');
      
      const updatedGroup = await Group.findByIdWithMembers(group.id);
      const updatedMember = updatedGroup!.members.find(m => m.userId === testUser2.id);
      expect(updatedMember!.role).toBe('viewer');

      // Remove member
      await Group.removeMember(group.id, testUser2.id);
      
      const finalGroup = await Group.findByIdWithMembers(group.id);
      expect(finalGroup!.members).toHaveLength(1); // Only owner
    });

    it('should handle group notes', async () => {
      const group = await Group.create({
        name: 'Notes Group',
        description: 'Group for notes',
        ownerId: testUser.id
      });

      // Create group note
      const groupNote = await Note.create({
        title: 'Group Note',
        content: 'Shared content',
        noteType: 'richtext',
        userId: testUser.id,
        groupId: group.id
      });

      // Get group notes
      const groupNotes = await Group.getNotes(group.id);
      expect(groupNotes).toHaveLength(1);
      expect(groupNotes[0].id).toBe(groupNote.id);
      expect(groupNotes[0].groupId).toBe(group.id);
    });
  });

  describe('Database Constraints and Relationships', () => {
    it('should enforce foreign key constraints', async () => {
      // Try to create note with non-existent user
      await expect(
        Note.create({
          title: 'Invalid Note',
          content: 'Content',
          noteType: 'richtext',
          userId: 'non-existent-user'
        })
      ).rejects.toThrow();
    });

    it('should handle unique constraints', async () => {
      const userData = {
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        displayName: 'Unique User'
      };

      // Create first user
      await User.create(userData);

      // Try to create second user with same email
      await expect(
        User.create(userData)
      ).rejects.toThrow();
    });

    it('should handle cascading deletes properly', async () => {
      const user = await User.create({
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        displayName: 'Cascade User'
      });

      const group = await Group.create({
        name: 'Cascade Group',
        description: 'Test cascading',
        ownerId: user.id
      });

      const note = await Note.create({
        title: 'Cascade Note',
        content: 'Content',
        noteType: 'richtext',
        userId: user.id,
        groupId: group.id
      });

      // Delete user should cascade to groups and notes
      await User.delete(user.id);

      // Verify cascading
      const deletedGroup = await Group.findById(group.id);
      expect(deletedGroup).toBeNull();

      const deletedNote = await Note.findById(note.id);
      expect(deletedNote).toBeNull();
    });
  });
});
/* FontAwesome Vendor Styles - Optimized for Theme System */
/* Custom FontAwesome build with only used icons */

/* Font face declarations using public directory paths */
@font-face {
  font-family: 'Font Awesome 7 Free';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/webfonts/fa-regular-400.woff2') format('woff2');
}

@font-face {
  font-family: 'Font Awesome 7 Free';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url('/webfonts/fa-solid-900.woff2') format('woff2');
}

@font-face {
  font-family: 'Font Awesome 7 Brands';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/webfonts/fa-brands-400.woff2') format('woff2');
}

/* Preload critical FontAwesome fonts for better performance */
/* This should be added to the HTML head via link preload tags */
/*
<link rel="preload" href="/node_modules/@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
<link rel="preload" href="/node_modules/@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff2" as="font" type="font/woff2" crossorigin>
*/

/* Core FontAwesome styles with theme integration */
.fa,
.fas,
.far,
.fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  color: currentColor; /* Inherit color for proper theming */
}

.fas {
  font-family: 'Font Awesome 7 Free';
  font-weight: 900;
}

.far {
  font-family: 'Font Awesome 7 Free';
  font-weight: 400;
}

.fab {
  font-family: 'Font Awesome 7 Brands';
  font-weight: 400;
}

/* Theme-aware icon colors with CSS custom properties */
:root {
  --icon-color-default: var(--color-text);
  --icon-color-muted: var(--color-text-muted);
  --icon-color-primary: var(--color-primary);
  --icon-color-success: var(--color-success);
  --icon-color-danger: var(--color-danger);
  --icon-color-warning: var(--color-warning-dark, var(--color-warning));
  --icon-color-info: var(--color-info);
  --icon-transition: var(--transition-fast, all 0.15s ease);
}

/* Base icon color inheritance */
.fas,
.far,
.fab {
  color: var(--icon-color-default);
  transition: var(--icon-transition);
}

/* Navigation icons */
.navbar-item .fas,
.navbar-item .far,
.navbar-item .fab {
  color: var(--navbar-text, var(--icon-color-default));
}

.navbar-item:hover .fas,
.navbar-item:hover .far,
.navbar-item:hover .fab {
  color: var(--icon-color-primary);
}

.navbar-item.is-active .fas,
.navbar-item.is-active .far,
.navbar-item.is-active .fab {
  color: var(--navbar-active-text, white);
}

/* Sidebar icons */
.sidebar-item .fas,
.sidebar-item .far,
.sidebar-item .fab {
  color: var(--sidebar-text, var(--icon-color-default));
}

.sidebar-item:hover .fas,
.sidebar-item:hover .far,
.sidebar-item:hover .fab {
  color: var(--icon-color-primary);
}

.sidebar-item.is-active .fas,
.sidebar-item.is-active .far,
.sidebar-item.is-active .fab {
  color: var(--sidebar-active-text, white);
}

/* Button icons - inherit from parent */
.button .fas,
.button .far,
.button .fab {
  color: currentColor;
}

/* Dropdown icons */
.dropdown-item .fas,
.dropdown-item .far,
.dropdown-item .fab {
  color: var(--icon-color-muted);
}

.dropdown-item:hover .fas,
.dropdown-item:hover .far,
.dropdown-item:hover .fab {
  color: var(--icon-color-primary);
}

.dropdown-item.is-active .fas,
.dropdown-item.is-active .far,
.dropdown-item.is-active .fab {
  color: var(--dropdown-active-text, white);
}

/* Card icons */
.card-header .fas,
.card-header .far,
.card-header .fab,
.card-footer .fas,
.card-footer .far,
.card-footer .fab {
  color: var(--icon-color-muted);
}

/* Form icons */
.control .icon .fas,
.control .icon .far,
.control .icon .fab {
  color: var(--icon-color-muted);
}

.control.has-icons-left .icon,
.control.has-icons-right .icon {
  color: var(--icon-color-muted);
  pointer-events: none;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 4;
}

.control.has-icons-left .icon {
  left: 0.75em;
}

.control.has-icons-right .icon {
  right: 0.75em;
}

/* Input focus state icons */
.control.has-icons-left .input:focus ~ .icon,
.control.has-icons-right .input:focus ~ .icon {
  color: var(--icon-color-primary);
}

/* Status and state icons with theme variables */
.has-text-success .fas,
.has-text-success .far,
.has-text-success .fab,
.icon.has-text-success {
  color: var(--icon-color-success) !important;
}

.has-text-danger .fas,
.has-text-danger .far,
.has-text-danger .fab,
.icon.has-text-danger {
  color: var(--icon-color-danger) !important;
}

.has-text-warning .fas,
.has-text-warning .far,
.has-text-warning .fab,
.icon.has-text-warning {
  color: var(--icon-color-warning) !important;
}

.has-text-info .fas,
.has-text-info .far,
.has-text-info .fab,
.icon.has-text-info {
  color: var(--icon-color-info) !important;
}

.has-text-primary .fas,
.has-text-primary .far,
.has-text-primary .fab,
.icon.has-text-primary {
  color: var(--icon-color-primary) !important;
}

.has-text-muted .fas,
.has-text-muted .far,
.has-text-muted .fab,
.icon.has-text-muted {
  color: var(--icon-color-muted) !important;
}

/* Theme-specific icon color overrides */
[data-theme='darkly'] {
  --icon-color-warning: var(--color-warning-light, #ffd83d);
}

[data-theme='default'] {
  --icon-color-warning: var(--color-warning-dark, #b8860b);
}

/* Interactive icon states */
.icon.is-clickable {
  cursor: pointer;
  transition: var(--icon-transition);
}

.icon.is-clickable:hover {
  color: var(--icon-color-primary);
  transform: scale(1.1);
}

.icon.is-clickable:active {
  transform: scale(0.95);
}

/* Icon definitions - Only include icons actually used in the app */
.fa-plus:before {
  content: '\f067';
}
.fa-search:before {
  content: '\f002';
}
.fa-times:before {
  content: '\f00d';
}
.fa-edit:before {
  content: '\f044';
}
.fa-trash:before {
  content: '\f1f8';
}
.fa-share-alt:before {
  content: '\f1e0';
}
.fa-copy:before {
  content: '\f0c5';
}
.fa-archive:before {
  content: '\f187';
}
.fa-star:before {
  content: '\f005';
}
.fa-users:before {
  content: '\f0c0';
}
.fa-cog:before {
  content: '\f013';
}
.fa-home:before {
  content: '\f015';
}
.fa-file-alt:before {
  content: '\f15c';
}
.fa-file:before {
  content: '\f15b';
}
.fa-clock:before {
  content: '\f017';
}
.fa-heart:before {
  content: '\f004';
}
.fa-folder:before {
  content: '\f07b';
}
.fa-user:before {
  content: '\f007';
}
.fa-envelope:before {
  content: '\f0e0';
}
.fa-lock:before {
  content: '\f023';
}
.fa-eye:before {
  content: '\f06e';
}
.fa-eye-slash:before {
  content: '\f070';
}
.fa-download:before {
  content: '\f019';
}
.fa-upload:before {
  content: '\f093';
}
.fa-save:before {
  content: '\f0c7';
}
.fa-print:before {
  content: '\f02f';
}
.fa-undo:before {
  content: '\f0e2';
}
.fa-redo:before {
  content: '\f01e';
}
.fa-history:before {
  content: '\f1da';
}
.fa-bold:before {
  content: '\f032';
}
.fa-italic:before {
  content: '\f033';
}
.fa-underline:before {
  content: '\f0cd';
}
.fa-align-left:before {
  content: '\f036';
}
.fa-align-center:before {
  content: '\f037';
}
.fa-align-right:before {
  content: '\f038';
}
.fa-list-ul:before {
  content: '\f0ca';
}
.fa-list-ol:before {
  content: '\f0cb';
}
.fa-link:before {
  content: '\f0c1';
}
.fa-image:before {
  content: '\f03e';
}
.fa-table:before {
  content: '\f0ce';
}
.fa-code:before {
  content: '\f121';
}
.fa-quote-left:before {
  content: '\f10d';
}
.fa-quote-right:before {
  content: '\f10e';
}
.fa-tasks:before {
  content: '\f0ae';
}
.fa-heading:before {
  content: '\f1dc';
}
.fa-strikethrough:before {
  content: '\f0cc';
}
.fa-superscript:before {
  content: '\f12b';
}
.fa-subscript:before {
  content: '\f12c';
}
.fa-text-height:before {
  content: '\f034';
}
.fa-text-width:before {
  content: '\f035';
}
.fa-font:before {
  content: '\f031';
}
.fa-palette:before {
  content: '\f53f';
}
.fa-highlighter:before {
  content: '\f591';
}
.fa-columns:before {
  content: '\f0db';
}
.fa-th:before {
  content: '\f00a';
}
.fa-th-list:before {
  content: '\f00b';
}
.fa-grip-vertical:before {
  content: '\f58e';
}
.fa-ellipsis-v:before {
  content: '\f142';
}
.fa-ellipsis-h:before {
  content: '\f141';
}
.fa-chevron-down:before {
  content: '\f078';
}
.fa-chevron-up:before {
  content: '\f077';
}
.fa-angle-down:before {
  content: '\f107';
}
.fa-chevron-left:before {
  content: '\f053';
}
.fa-chevron-right:before {
  content: '\f054';
}
.fa-arrow-up:before {
  content: '\f062';
}
.fa-arrow-down:before {
  content: '\f063';
}
.fa-arrow-left:before {
  content: '\f060';
}
.fa-arrow-right:before {
  content: '\f061';
}
.fa-sort:before {
  content: '\f0dc';
}
.fa-sort-up:before {
  content: '\f0de';
}
.fa-sort-down:before {
  content: '\f0dd';
}
.fa-sort-alpha-down:before {
  content: '\f15d';
}
.fa-filter:before {
  content: '\f0b0';
}
.fa-calendar:before {
  content: '\f133';
}
.fa-calendar-alt:before {
  content: '\f073';
}
.fa-bell:before {
  content: '\f0f3';
}
.fa-bell-slash:before {
  content: '\f1f6';
}
.fa-comment:before {
  content: '\f075';
}
.fa-comments:before {
  content: '\f086';
}
.fa-thumbs-up:before {
  content: '\f164';
}
.fa-thumbs-down:before {
  content: '\f165';
}
.fa-flag:before {
  content: '\f024';
}
.fa-bookmark:before {
  content: '\f02e';
}
.fa-tag:before {
  content: '\f02b';
}
.fa-tags:before {
  content: '\f02c';
}
.fa-layer-group:before {
  content: '\f5fd';
}
.fa-expand:before {
  content: '\f065';
}
.fa-compress:before {
  content: '\f066';
}
.fa-expand-alt:before {
  content: '\f424';
}
.fa-compress-alt:before {
  content: '\f422';
}
.fa-fullscreen:before {
  content: '\f0b2';
}
.fa-spinner:before {
  content: '\f110';
}
.fa-circle-notch:before {
  content: '\f1ce';
}
.fa-sync:before {
  content: '\f021';
}
.fa-sync-alt:before {
  content: '\f2f1';
}
.fa-check:before {
  content: '\f00c';
}
.fa-check-circle:before {
  content: '\f058';
}
.fa-times-circle:before {
  content: '\f057';
}
.fa-plus-circle:before {
  content: '\f055';
}
.fa-minus-circle:before {
  content: '\f056';
}
.fa-exclamation:before {
  content: '\f12a';
}
.fa-exclamation-circle:before {
  content: '\f06a';
}
.fa-exclamation-triangle:before {
  content: '\f071';
}
.fa-info:before {
  content: '\f129';
}
.fa-info-circle:before {
  content: '\f05a';
}
.fa-question:before {
  content: '\f128';
}
.fa-question-circle:before {
  content: '\f059';
}
.fa-lightbulb:before {
  content: '\f0eb';
}
.fa-database:before {
  content: '\f1c0';
}
.fa-server:before {
  content: '\f233';
}
.fa-chart-line:before {
  content: '\f201';
}
.fa-chart-bar:before {
  content: '\f080';
}
.fa-chart-pie:before {
  content: '\f200';
}
.fa-chart-area:before {
  content: '\f1fe';
}
.fa-tachometer-alt:before {
  content: '\f3fd';
}
.fa-memory:before {
  content: '\f538';
}
.fa-microchip:before {
  content: '\f2db';
}
.fa-hdd:before {
  content: '\f0a0';
}
.fa-wifi:before {
  content: '\f1eb';
}
.fa-signal:before {
  content: '\f012';
}
.fa-battery-full:before {
  content: '\f240';
}
.fa-plug:before {
  content: '\f1e6';
}
.fa-power-off:before {
  content: '\f011';
}
.fa-cube:before {
  content: '\f1b2';
}
.fa-cubes:before {
  content: '\f1b3';
}
.fa-puzzle-piece:before {
  content: '\f12e';
}
.fa-wrench:before {
  content: '\f0ad';
}
.fa-tools:before {
  content: '\f7d9';
}
.fa-hammer:before {
  content: '\f6e3';
}
.fa-screwdriver:before {
  content: '\f54a';
}
.fa-bug:before {
  content: '\f188';
}
.fa-shield-alt:before {
  content: '\f3ed';
}
.fa-key:before {
  content: '\f084';
}
.fa-unlock:before {
  content: '\f09c';
}
.fa-unlock-alt:before {
  content: '\f13e';
}
.fa-check-square:before {
  content: '\f14a';
}
.fa-heartbeat:before {
  content: '\f21e';
}
.fa-blog:before {
  content: '\f781';
}
.fa-utensils:before {
  content: '\f2e7';
}
.fa-flask:before {
  content: '\f0c3';
}
.fa-calculator:before {
  content: '\f1ec';
}
.fa-fire:before {
  content: '\f06d';
}
.fa-snowflake:before {
  content: '\f2dc';
}
.fa-ghost:before {
  content: '\f6e2';
}
.fa-graduation-cap:before {
  content: '\f19d';
}
.fa-plane:before {
  content: '\f072';
}
.fa-sticky-note:before {
  content: '\f249';
}
.fa-users-cog:before {
  content: '\f509';
}
.fa-users-slash:before {
  content: '\f506';
}
.fa-sign-out-alt:before {
  content: '\f2f5';
}
.fa-sign-in-alt:before {
  content: '\f2f6';
}

/* Brand icons */
.fa-google:before {
  content: '\f1a0';
}
.fa-github:before {
  content: '\f09b';
}
.fa-twitter:before {
  content: '\f099';
}
.fa-facebook:before {
  content: '\f09a';
}
.fa-linkedin:before {
  content: '\f08c';
}
.fa-markdown:before {
  content: '\f60f';
}

/* Icon sizing with CSS custom properties */
.fa-xs {
  font-size: 0.75em;
}
.fa-sm {
  font-size: 0.875em;
}
.fa-lg {
  font-size: 1.25em;
}
.fa-xl {
  font-size: 1.5em;
}
.fa-2x {
  font-size: 2em;
}
.fa-3x {
  font-size: 3em;
}
.fa-4x {
  font-size: 4em;
}
.fa-5x {
  font-size: 5em;
}

/* Icon positioning */
.fa-fw {
  width: 1.25em;
  text-align: center;
}

.fa-ul {
  list-style-type: none;
  margin-left: 2.5em;
  padding-left: 0;
}

.fa-ul > li {
  position: relative;
}

.fa-li {
  position: absolute;
  left: -2em;
  width: 2em;
  top: 0.14em;
  text-align: center;
}

.fa-li.fa-lg {
  left: -1.8em;
}

/* Icon borders and backgrounds with theme support */
.fa-border {
  border: solid 0.08em var(--color-border);
  border-radius: var(--radius-sm);
  padding: 0.2em 0.25em 0.15em;
}

.fa-pull-left {
  float: left;
  margin-right: 0.3em;
}

.fa-pull-right {
  float: right;
  margin-left: 0.3em;
}

/* Icon stacking */
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

.fa-stack-1x,
.fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

.fa-stack-1x {
  line-height: inherit;
}

.fa-stack-2x {
  font-size: 2em;
}

.fa-inverse {
  color: white;
}

/* Animation classes with theme-aware reduced motion */
.fa-spin {
  animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  animation: fa-pulse 2s infinite;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fa-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive icon sizing */
@media screen and (max-width: 768px) {
  .fa-lg {
    font-size: 1.2em;
  }
  .fa-2x {
    font-size: 1.8em;
  }
  .fa-3x {
    font-size: 2.4em;
  }
  .fa-4x {
    font-size: 3.2em;
  }
  .fa-5x {
    font-size: 4em;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .fa-spin,
  .fa-pulse {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .fa-border {
    border-width: 0.12em;
  }
}

/* Print styles */
@media print {
  .fas,
  .far,
  .fab {
    color: black !important;
  }

  .fa-spin,
  .fa-pulse {
    animation: none;
  }
}

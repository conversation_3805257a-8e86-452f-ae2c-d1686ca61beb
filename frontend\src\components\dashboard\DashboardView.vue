<template>
  <div class="dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1 class="dashboard-title">Dashboard</h1>
        <p class="dashboard-subtitle">Welcome back! Here's what's happening with your notes.</p>
      </div>
    </div>

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
      <!-- Quick Stats Row -->
      <div class="stats-row">
        <QuickStatsWidget />
      </div>

      <!-- Main Content Row -->
      <div class="main-row">
        <!-- Recent Notes Widget -->
        <div class="widget-container">
          <RecentNotesWidget />
        </div>

        <!-- Recent Activity Widget -->
        <div class="widget-container">
          <RecentActivityWidget />
        </div>
      </div>

      <!-- Groups Activity Row -->
      <div class="groups-row">
        <div class="widget-container full-width">
          <RecentGroupActivityWidget />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import QuickStatsWidget from './QuickStatsWidget.vue'
import RecentNotesWidget from './RecentNotesWidget.vue'
import RecentActivityWidget from './RecentActivityWidget.vue'
import RecentGroupActivityWidget from './RecentGroupActivityWidget.vue'
</script>

<style scoped>
.dashboard {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text-strong);
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  font-size: 1rem;
  color: var(--color-text-muted);
  margin: 0;
}

.dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stats-row {
  width: 100%;
}

.main-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.groups-row {
  width: 100%;
}

.widget-container {
  background: var(--card-background);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.widget-container.full-width {
  width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .main-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dashboard-grid {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .dashboard {
    padding: 0.75rem;
  }

  .dashboard-header {
    margin-bottom: 1rem;
  }
}
</style>
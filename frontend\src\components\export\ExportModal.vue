<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="closeModal"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">
          <i class="fas fa-download mr-2"></i>
          Export {{ isMultiple ? 'Notes' : 'Note' }}
        </p>
        <button class="delete" @click="closeModal" aria-label="close"></button>
      </header>

      <section class="modal-card-body">
        <!-- Export Format Selection -->
        <div class="field">
          <label class="label">Export Format</label>
          <div class="control">
            <div class="select is-fullwidth">
              <select v-model="exportOptions.format">
                <option value="pdf">PDF Document</option>
                <option value="html">HTML File</option>
                <option value="markdown">Markdown File</option>
              </select>
            </div>
          </div>
          <p class="help">Choose the format for your exported {{ isMultiple ? 'notes' : 'note' }}</p>
        </div>

        <!-- Options -->
        <div class="field">
          <label class="label">Export Options</label>
          <div class="control">
            <label class="checkbox">
              <input 
                type="checkbox" 
                v-model="exportOptions.includeMetadata"
              >
              Include metadata (creation date, word count, etc.)
            </label>
          </div>
        </div>

        <!-- Custom Styles (for HTML/PDF) -->
        <div class="field" v-if="exportOptions.format !== 'markdown'">
          <label class="label">Custom Styles (Optional)</label>
          <div class="control">
            <textarea 
              class="textarea" 
              v-model="exportOptions.customStyles"
              placeholder="Add custom CSS styles..."
              rows="4"
            ></textarea>
          </div>
          <p class="help">Add custom CSS to style your exported document</p>
        </div>

        <!-- Preview Info -->
        <div class="notification is-info is-light" v-if="isMultiple">
          <p><strong>{{ noteIds.length }}</strong> notes will be exported as a single {{ exportOptions.format.toUpperCase() }} file.</p>
          <p class="mt-2" v-if="noteIds.length > 10">
            <i class="fas fa-info-circle mr-1"></i>
            Large exports will be processed in the background. You'll receive a download link when ready.
          </p>
        </div>

        <!-- Export Progress -->
        <div v-if="isExporting" class="notification is-primary is-light">
          <div class="level">
            <div class="level-left">
              <div class="level-item">
                <i class="fas fa-spinner fa-spin mr-2"></i>
                {{ exportStatus }}
              </div>
            </div>
            <div class="level-right" v-if="exportProgress > 0">
              <div class="level-item">
                <progress class="progress is-primary" :value="exportProgress" max="100">
                  {{ exportProgress }}%
                </progress>
              </div>
            </div>
          </div>
        </div>

        <!-- Export Job Info -->
        <div v-if="exportJob" class="notification is-success is-light">
          <p><strong>Export job created!</strong></p>
          <p>Job ID: <code>{{ exportJob.id }}</code></p>
          <p>Status: <span class="tag" :class="getJobStatusClass(exportJob.status)">{{ exportJob.status }}</span></p>
          <div class="buttons mt-3">
            <button 
              class="button is-primary is-small" 
              @click="checkJobStatus"
              :loading="isCheckingStatus"
            >
              <i class="fas fa-sync mr-1"></i>
              Check Status
            </button>
            <button 
              class="button is-success is-small" 
              @click="downloadJobResult"
              :disabled="exportJob.status !== 'completed'"
            >
              <i class="fas fa-download mr-1"></i>
              Download
            </button>
          </div>
        </div>

        <!-- Error Display -->
        <div v-if="exportError" class="notification is-danger is-light">
          <p><strong>Export failed:</strong></p>
          <p>{{ exportError }}</p>
        </div>
      </section>

      <footer class="modal-card-foot">
        <button 
          class="button is-primary" 
          @click="startExport"
          :disabled="isExporting"
          :class="{ 'is-loading': isExporting }"
        >
          <i class="fas fa-download mr-2"></i>
          Export {{ isMultiple ? 'Notes' : 'Note' }}
        </button>
        <button class="button" @click="closeModal">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ExportService, type ExportOptions, type ExportJob } from '../../services/exportService';

interface Props {
  isOpen: boolean;
  noteIds: string[];
  noteTitle?: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'exported', result: { success: boolean; filename?: string; error?: string }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Reactive state
const exportOptions = ref<ExportOptions>({
  format: 'pdf',
  includeMetadata: true,
  customStyles: ''
});

const isExporting = ref(false);
const exportStatus = ref('');
const exportProgress = ref(0);
const exportJob = ref<ExportJob | null>(null);
const exportError = ref('');
const isCheckingStatus = ref(false);

// Computed properties
const isMultiple = computed(() => props.noteIds.length > 1);

// Methods
const closeModal = () => {
  if (!isExporting.value) {
    resetState();
    emit('close');
  }
};

const resetState = () => {
  isExporting.value = false;
  exportStatus.value = '';
  exportProgress.value = 0;
  exportJob.value = null;
  exportError.value = '';
  isCheckingStatus.value = false;
};

const startExport = async () => {
  if (props.noteIds.length === 0) {
    exportError.value = 'No notes selected for export';
    return;
  }

  try {
    isExporting.value = true;
    exportError.value = '';
    exportStatus.value = 'Preparing export...';
    exportProgress.value = 10;

    if (isMultiple.value) {
      // Export multiple notes
      exportStatus.value = 'Processing multiple notes...';
      exportProgress.value = 30;

      const result = await ExportService.exportMultipleNotes(props.noteIds, exportOptions.value);
      
      if ('jobId' in result) {
        // Large export - job created
        exportJob.value = await ExportService.getExportJob(result.jobId);
        exportStatus.value = 'Export job created. Processing in background...';
        exportProgress.value = 50;
        
        // Start polling for job completion
        pollJobStatus(result.jobId);
      } else {
        // Small export - immediate download
        exportStatus.value = 'Download ready!';
        exportProgress.value = 100;
        
        const filename = ExportService.getExportFilename(
          'notes_export', 
          exportOptions.value.format, 
          true
        );
        
        ExportService.downloadBlob(result, filename);
        
        emit('exported', { success: true, filename });
        setTimeout(() => {
          closeModal();
        }, 1000);
      }
    } else {
      // Export single note
      exportStatus.value = 'Exporting note...';
      exportProgress.value = 50;

      const blob = await ExportService.exportNote(props.noteIds[0], exportOptions.value);
      
      exportStatus.value = 'Download ready!';
      exportProgress.value = 100;
      
      const filename = ExportService.getExportFilename(
        props.noteTitle || 'note', 
        exportOptions.value.format
      );
      
      ExportService.downloadBlob(blob, filename);
      
      emit('exported', { success: true, filename });
      setTimeout(() => {
        closeModal();
      }, 1000);
    }
  } catch (error) {
    console.error('Export failed:', error);
    exportError.value = error instanceof Error ? error.message : 'Export failed';
    emit('exported', { success: false, error: exportError.value });
  } finally {
    isExporting.value = false;
  }
};

const pollJobStatus = async (jobId: string) => {
  try {
    await ExportService.pollExportJob(
      jobId,
      (job) => {
        exportJob.value = job;
        exportStatus.value = `Export ${job.status}...`;
        
        switch (job.status) {
          case 'pending':
            exportProgress.value = 60;
            break;
          case 'processing':
            exportProgress.value = 80;
            break;
          case 'completed':
            exportProgress.value = 100;
            exportStatus.value = 'Export completed!';
            break;
        }
      }
    );
  } catch (error) {
    console.error('Job polling failed:', error);
    exportError.value = error instanceof Error ? error.message : 'Job polling failed';
  }
};

const checkJobStatus = async () => {
  if (!exportJob.value) return;
  
  try {
    isCheckingStatus.value = true;
    exportJob.value = await ExportService.getExportJob(exportJob.value.id);
  } catch (error) {
    console.error('Failed to check job status:', error);
    exportError.value = 'Failed to check job status';
  } finally {
    isCheckingStatus.value = false;
  }
};

const downloadJobResult = async () => {
  if (!exportJob.value || exportJob.value.status !== 'completed') return;
  
  try {
    const blob = await ExportService.downloadExportJob(exportJob.value.id);
    const filename = ExportService.getExportFilename(
      'notes_export', 
      exportJob.value.format, 
      true
    );
    
    ExportService.downloadBlob(blob, filename);
    emit('exported', { success: true, filename });
  } catch (error) {
    console.error('Download failed:', error);
    exportError.value = 'Download failed';
  }
};

const getJobStatusClass = (status: string) => {
  switch (status) {
    case 'pending':
      return 'is-warning';
    case 'processing':
      return 'is-info';
    case 'completed':
      return 'is-success';
    case 'failed':
      return 'is-danger';
    default:
      return '';
  }
};

// Watch for modal open/close to reset state
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    resetState();
  }
});
</script>

<style scoped>
.progress {
  width: 200px;
}

.notification code {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

.textarea {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
}

.level-item .progress {
  margin-bottom: 0;
}
</style>
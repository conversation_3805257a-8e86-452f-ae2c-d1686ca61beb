// Analytics Service
// Basic analytics service for tracking user interactions and app performance

export interface AnalyticsEvent {
  name: string
  properties?: Record<string, any>
  timestamp: number
}

export interface AnalyticsConfig {
  enabled: boolean
  endpoint?: string
  batchSize: number
  flushInterval: number
}

class AnalyticsService {
  private static instance: AnalyticsService
  private config: AnalyticsConfig
  private events: AnalyticsEvent[] = []
  private isInitialized = false

  private constructor(config: Partial<AnalyticsConfig> = {}) {
    this.config = {
      enabled: false, // Disabled by default
      batchSize: 10,
      flushInterval: 30000, // 30 seconds
      ...config
    }
  }

  static getInstance(config?: Partial<AnalyticsConfig>): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService(config)
    }
    return AnalyticsService.instance
  }

  // Initialize analytics service
  initialize(): Promise<void> {
    return new Promise((resolve) => {
      try {
        if (!this.config.enabled) {
          console.log('📊 Analytics service disabled')
          this.isInitialized = true
          resolve()
          return
        }

        // Setup periodic flush
        setInterval(() => {
          this.flush()
        }, this.config.flushInterval)

        this.isInitialized = true
        console.log('📊 Analytics service initialized')
        resolve()
      } catch (error) {
        console.warn('⚠️ Analytics service initialization failed:', error)
        this.isInitialized = true // Mark as initialized to prevent retries
        resolve() // Don't reject - analytics failure shouldn't block the app
      }
    })
  }

  // Track an event
  track(name: string, properties?: Record<string, any>): void {
    if (!this.config.enabled || !this.isInitialized) {
      return
    }

    try {
      const event: AnalyticsEvent = {
        name,
        properties,
        timestamp: Date.now()
      }

      this.events.push(event)

      // Auto-flush if batch size reached
      if (this.events.length >= this.config.batchSize) {
        this.flush()
      }
    } catch (error) {
      console.warn('⚠️ Failed to track analytics event:', error)
    }
  }

  // Flush events to endpoint
  private flush(): void {
    if (!this.config.enabled || this.events.length === 0) {
      return
    }

    try {
      // In a real implementation, this would send events to an analytics endpoint
      console.log('📊 Analytics events (would be sent to endpoint):', this.events)
      
      // Clear events after "sending"
      this.events = []
    } catch (error) {
      console.warn('⚠️ Failed to flush analytics events:', error)
    }
  }

  // Get current status
  getStatus(): { enabled: boolean; initialized: boolean; queuedEvents: number } {
    return {
      enabled: this.config.enabled,
      initialized: this.isInitialized,
      queuedEvents: this.events.length
    }
  }
}

// Export singleton instance
export const analyticsService = AnalyticsService.getInstance()
<template>
  <div class="settings-panel">
    <div class="settings-header">
      <h2 class="title is-4">Settings</h2>
      <button class="delete is-large" @click="$emit('close')" aria-label="Close settings"></button>
    </div>

    <div class="settings-content">
      <div class="tabs is-boxed">
        <ul>
          <li :class="{ 'is-active': activeTab === 'preferences' }">
            <a @click="activeTab = 'preferences'">
              <span class="icon is-small">
                <i class="fas fa-cog"></i>
              </span>
              <span>Preferences</span>
            </a>
          </li>
          <li :class="{ 'is-active': activeTab === 'profile' }">
            <a @click="activeTab = 'profile'">
              <span class="icon is-small">
                <i class="fas fa-user"></i>
              </span>
              <span>Profile</span>
            </a>
          </li>
          <li :class="{ 'is-active': activeTab === 'account' }">
            <a @click="activeTab = 'account'">
              <span class="icon is-small">
                <i class="fas fa-shield-alt"></i>
              </span>
              <span>Account</span>
            </a>
          </li>
        </ul>
      </div>

      <div class="tab-content">
        <PreferencesTab v-if="activeTab === 'preferences'" />
        <ProfileTab v-if="activeTab === 'profile'" />
        <AccountTab v-if="activeTab === 'account'" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PreferencesTab from './PreferencesTab.vue'
import ProfileTab from './ProfileTab.vue'
import AccountTab from './AccountTab.vue'

defineEmits<{
  close: []
}>()

const activeTab = ref<'preferences' | 'profile' | 'account'>('preferences')
</script>

<style scoped>
.settings-panel {
  background: var(--card-background);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  box-shadow: var(--shadow);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.settings-header .title {
  margin: 0;
}

.settings-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tabs {
  margin: 0;
  border-bottom: 1px solid #e5e5e5;
}

.tabs ul {
  margin: 0;
  padding: 0 1.5rem;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

/* Dark mode styles */
:global(.dark) .settings-panel {
  background: #2b2b2b;
  color: #e5e5e5;
}

:global(.dark) .settings-header {
  background: #1e1e1e;
  border-bottom-color: #404040;
}

:global(.dark) .tabs {
  border-bottom-color: #404040;
}

:global(.dark) .tabs li.is-active a {
  background: #2b2b2b;
  border-color: #404040;
  color: #e5e5e5;
}

:global(.dark) .tabs li a {
  color: #b5b5b5;
}

:global(.dark) .tabs li a:hover {
  background: #404040;
  color: #e5e5e5;
}
</style>
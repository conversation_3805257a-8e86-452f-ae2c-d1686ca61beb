<template>
  <div class="advanced-search" v-if="show">
    <div class="card">
      <div class="card-header">
        <div class="card-header-title">
          <span class="icon">
            <i class="fas fa-sliders-h"></i>
          </span>
          <span>Advanced Search</span>
        </div>
        <div class="card-header-icon">
          <button class="button is-small is-ghost" @click="close">
            <span class="icon">
              <i class="fas fa-times"></i>
            </span>
          </button>
        </div>
      </div>
      
      <div class="card-content">
        <div class="columns">
          <!-- Left Column: Search Terms -->
          <div class="column is-half">
            <h5 class="title is-6 mb-3">Search Terms</h5>
            
            <!-- Include Terms -->
            <div class="field">
              <label class="label is-small">Include any of these words</label>
              <div class="control">
                <input
                  v-model="includeTermsInput"
                  type="text"
                  class="input is-small"
                  placeholder="word1 word2 word3"
                  @keydown.enter="addIncludeTerm"
                />
              </div>
              <div v-if="advancedFilters.includeTerms.length > 0" class="tags mt-2">
                <span
                  v-for="(term, index) in advancedFilters.includeTerms"
                  :key="`include-${index}`"
                  class="tag is-primary is-light"
                >
                  {{ term }}
                  <button
                    class="delete is-small"
                    @click="removeIncludeTerm(index)"
                  ></button>
                </span>
              </div>
            </div>

            <!-- Exclude Terms -->
            <div class="field">
              <label class="label is-small">Exclude these words</label>
              <div class="control">
                <input
                  v-model="excludeTermsInput"
                  type="text"
                  class="input is-small"
                  placeholder="word1 word2 word3"
                  @keydown.enter="addExcludeTerm"
                />
              </div>
              <div v-if="advancedFilters.excludeTerms.length > 0" class="tags mt-2">
                <span
                  v-for="(term, index) in advancedFilters.excludeTerms"
                  :key="`exclude-${index}`"
                  class="tag is-danger is-light"
                >
                  {{ term }}
                  <button
                    class="delete is-small"
                    @click="removeExcludeTerm(index)"
                  ></button>
                </span>
              </div>
            </div>

            <!-- Exact Phrases -->
            <div class="field">
              <label class="label is-small">Exact phrases</label>
              <div class="control">
                <input
                  v-model="exactPhrasesInput"
                  type="text"
                  class="input is-small"
                  placeholder="exact phrase here"
                  @keydown.enter="addExactPhrase"
                />
              </div>
              <div v-if="advancedFilters.exactPhrases.length > 0" class="tags mt-2">
                <span
                  v-for="(phrase, index) in advancedFilters.exactPhrases"
                  :key="`phrase-${index}`"
                  class="tag is-info is-light"
                >
                  "{{ phrase }}"
                  <button
                    class="delete is-small"
                    @click="removeExactPhrase(index)"
                  ></button>
                </span>
              </div>
            </div>

            <!-- Specific Field Search -->
            <div class="field">
              <label class="label is-small">Search in title only</label>
              <div class="control">
                <input
                  v-model="advancedFilters.titleSearch"
                  type="text"
                  class="input is-small"
                  placeholder="title keywords"
                />
              </div>
            </div>

            <div class="field">
              <label class="label is-small">Search in content only</label>
              <div class="control">
                <input
                  v-model="advancedFilters.contentSearch"
                  type="text"
                  class="input is-small"
                  placeholder="content keywords"
                />
              </div>
            </div>
          </div>

          <!-- Right Column: Filters -->
          <div class="column is-half">
            <h5 class="title is-6 mb-3">Filters</h5>

            <!-- Note Type Filter -->
            <div class="field">
              <label class="label is-small">Note Type</label>
              <div class="control">
                <div class="select is-small is-fullwidth">
                  <select v-model="filters.noteType">
                    <option value="">All Types</option>
                    <option value="richtext">Rich Text</option>
                    <option value="markdown">Markdown</option>
                    <option value="kanban">Kanban Board</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Tags Filter -->
            <div class="field">
              <label class="label is-small">Tags</label>
              <div class="control">
                <div class="dropdown" :class="{ 'is-active': showTagDropdown }">
                  <div class="dropdown-trigger">
                    <input
                      v-model="tagSearchInput"
                      type="text"
                      class="input is-small"
                      placeholder="Search tags..."
                      @focus="showTagDropdown = true"
                      @blur="hideTagDropdown"
                      @input="filterTags"
                    />
                  </div>
                  <div class="dropdown-menu" v-if="filteredTags.length > 0">
                    <div class="dropdown-content">
                      <a
                        v-for="tag in filteredTags"
                        :key="tag.id"
                        class="dropdown-item"
                        @mousedown="selectTag(tag.name)"
                      >
                        {{ tag.name }}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="filters.tags && filters.tags.length > 0" class="tags mt-2">
                <span
                  v-for="(tag, index) in filters.tags"
                  :key="`filter-tag-${index}`"
                  class="tag is-primary"
                >
                  {{ tag }}
                  <button
                    class="delete is-small"
                    @click="removeTag(index)"
                  ></button>
                </span>
              </div>
            </div>

            <!-- Date Range Filter -->
            <div class="field">
              <label class="label is-small">Date Range</label>
              <div class="field has-addons">
                <div class="control">
                  <input
                    v-model="filters.dateFrom"
                    type="date"
                    class="input is-small"
                    placeholder="From date"
                  />
                </div>
                <div class="control">
                  <span class="button is-small is-static">to</span>
                </div>
                <div class="control">
                  <input
                    v-model="filters.dateTo"
                    type="date"
                    class="input is-small"
                    placeholder="To date"
                  />
                </div>
              </div>
            </div>

            <!-- Quick Date Filters -->
            <div class="field">
              <label class="label is-small">Quick Date Filters</label>
              <div class="buttons are-small">
                <button class="button is-light" @click="setDateRange('today')">
                  Today
                </button>
                <button class="button is-light" @click="setDateRange('week')">
                  This Week
                </button>
                <button class="button is-light" @click="setDateRange('month')">
                  This Month
                </button>
                <button class="button is-light" @click="setDateRange('year')">
                  This Year
                </button>
              </div>
            </div>

            <!-- Archive Filter -->
            <div class="field">
              <div class="control">
                <label class="checkbox">
                  <input
                    v-model="filters.archived"
                    type="checkbox"
                  />
                  Include archived notes
                </label>
              </div>
            </div>

            <!-- Search Options -->
            <div class="field">
              <label class="label is-small">Sort Results</label>
              <div class="field has-addons">
                <div class="control">
                  <div class="select is-small">
                    <select v-model="sortBy">
                      <option value="relevance">Relevance</option>
                      <option value="updated_at">Last Modified</option>
                      <option value="created_at">Date Created</option>
                      <option value="title">Title</option>
                    </select>
                  </div>
                </div>
                <div class="control">
                  <div class="select is-small">
                    <select v-model="sortOrder">
                      <option value="desc">Descending</option>
                      <option value="asc">Ascending</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Search Query Preview -->
        <div v-if="queryPreview" class="field">
          <label class="label is-small">Generated Query</label>
          <div class="control">
            <input
              :value="queryPreview"
              type="text"
              class="input is-small"
              readonly
            />
          </div>
          <p class="help">This is the search query that will be executed</p>
        </div>
      </div>

      <div class="card-footer">
        <div class="card-footer-item">
          <div class="buttons">
            <button
              class="button is-primary"
              @click="performSearch"
              :disabled="!hasSearchCriteria"
            >
              <span class="icon">
                <i class="fas fa-search"></i>
              </span>
              <span>Search</span>
            </button>
            <button class="button is-light" @click="clearAll">
              <span class="icon">
                <i class="fas fa-eraser"></i>
              </span>
              <span>Clear All</span>
            </button>
            <button class="button is-light" @click="loadPreset">
              <span class="icon">
                <i class="fas fa-bookmark"></i>
              </span>
              <span>Load Preset</span>
            </button>
            <button class="button is-light" @click="savePreset">
              <span class="icon">
                <i class="fas fa-save"></i>
              </span>
              <span>Save Preset</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useSearchStore } from '../../stores/search'
import { useNotesStore } from '../../stores/notes'
import type { SearchFilters } from '../../services/searchService'

interface Props {
  show: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'search', query: string, filters: SearchFilters): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const searchStore = useSearchStore()
const notesStore = useNotesStore()

// Local state
const includeTermsInput = ref('')
const excludeTermsInput = ref('')
const exactPhrasesInput = ref('')
const tagSearchInput = ref('')
const showTagDropdown = ref(false)
const filteredTags = ref<any[]>([])

// Reactive filters
const filters = ref<SearchFilters>({
  noteType: undefined,
  tags: [],
  dateFrom: undefined,
  dateTo: undefined,
  archived: false
})

const sortBy = ref('relevance')
const sortOrder = ref('desc')

// Computed
const advancedFilters = computed(() => searchStore.advancedFilters)
const availableTags = computed(() => notesStore.tags)

const hasSearchCriteria = computed(() => {
  return (
    advancedFilters.value.includeTerms.length > 0 ||
    advancedFilters.value.excludeTerms.length > 0 ||
    advancedFilters.value.exactPhrases.length > 0 ||
    advancedFilters.value.titleSearch.trim() ||
    advancedFilters.value.contentSearch.trim() ||
    filters.value.noteType ||
    (filters.value.tags && filters.value.tags.length > 0) ||
    filters.value.dateFrom ||
    filters.value.dateTo ||
    filters.value.archived
  )
})

const queryPreview = computed(() => {
  if (!hasSearchCriteria.value) return ''
  return searchStore.buildAdvancedQuery()
})

// Methods
const addIncludeTerm = () => {
  const terms = includeTermsInput.value.trim().split(/\s+/).filter(t => t)
  if (terms.length > 0) {
    advancedFilters.value.includeTerms.push(...terms)
    includeTermsInput.value = ''
  }
}

const removeIncludeTerm = (index: number) => {
  advancedFilters.value.includeTerms.splice(index, 1)
}

const addExcludeTerm = () => {
  const terms = excludeTermsInput.value.trim().split(/\s+/).filter(t => t)
  if (terms.length > 0) {
    advancedFilters.value.excludeTerms.push(...terms)
    excludeTermsInput.value = ''
  }
}

const removeExcludeTerm = (index: number) => {
  advancedFilters.value.excludeTerms.splice(index, 1)
}

const addExactPhrase = () => {
  const phrase = exactPhrasesInput.value.trim()
  if (phrase) {
    advancedFilters.value.exactPhrases.push(phrase)
    exactPhrasesInput.value = ''
  }
}

const removeExactPhrase = (index: number) => {
  advancedFilters.value.exactPhrases.splice(index, 1)
}

const filterTags = () => {
  const query = tagSearchInput.value.toLowerCase()
  filteredTags.value = availableTags.value
    .filter(tag => tag.name.toLowerCase().includes(query))
    .slice(0, 10)
}

const selectTag = (tagName: string) => {
  if (!filters.value.tags) {
    filters.value.tags = []
  }
  
  if (!filters.value.tags.includes(tagName)) {
    filters.value.tags.push(tagName)
  }
  
  tagSearchInput.value = ''
  showTagDropdown.value = false
}

const removeTag = (index: number) => {
  if (filters.value.tags) {
    filters.value.tags.splice(index, 1)
  }
}

const hideTagDropdown = () => {
  setTimeout(() => {
    showTagDropdown.value = false
  }, 200)
}

const setDateRange = (range: string) => {
  const now = new Date()
  let fromDate: Date

  switch (range) {
    case 'today':
      fromDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      break
    case 'week':
      fromDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case 'month':
      fromDate = new Date(now.getFullYear(), now.getMonth(), 1)
      break
    case 'year':
      fromDate = new Date(now.getFullYear(), 0, 1)
      break
    default:
      return
  }

  filters.value.dateFrom = fromDate.toISOString().split('T')[0]
  filters.value.dateTo = now.toISOString().split('T')[0]
}

const performSearch = async () => {
  const query = searchStore.buildAdvancedQuery()
  
  searchStore.setSearchOptions({
    sortBy: sortBy.value as any,
    sortOrder: sortOrder.value as any
  })

  await searchStore.search(query, filters.value, { page: 1 })
  emit('search', query, filters.value)
  close()
}

const clearAll = () => {
  searchStore.clearAdvancedFilters()
  filters.value = {
    noteType: undefined,
    tags: [],
    dateFrom: undefined,
    dateTo: undefined,
    archived: false
  }
  includeTermsInput.value = ''
  excludeTermsInput.value = ''
  exactPhrasesInput.value = ''
  tagSearchInput.value = ''
}

const close = () => {
  emit('close')
}

const savePreset = () => {
  const preset = {
    advancedFilters: { ...advancedFilters.value },
    filters: { ...filters.value },
    sortBy: sortBy.value,
    sortOrder: sortOrder.value
  }
  
  const presetName = prompt('Enter a name for this search preset:')
  if (presetName) {
    const presets = JSON.parse(localStorage.getItem('searchPresets') || '{}')
    presets[presetName] = preset
    localStorage.setItem('searchPresets', JSON.stringify(presets))
  }
}

const loadPreset = () => {
  const presets = JSON.parse(localStorage.getItem('searchPresets') || '{}')
  const presetNames = Object.keys(presets)
  
  if (presetNames.length === 0) {
    alert('No saved presets found')
    return
  }
  
  const selectedPreset = prompt(`Select a preset:\n${presetNames.join('\n')}`)
  if (selectedPreset && presets[selectedPreset]) {
    const preset = presets[selectedPreset]
    
    // Load advanced filters
    Object.assign(advancedFilters.value, preset.advancedFilters)
    
    // Load filters
    Object.assign(filters.value, preset.filters)
    
    // Load sort options
    sortBy.value = preset.sortBy
    sortOrder.value = preset.sortOrder
  }
}

// Watch for tag input changes
watch(tagSearchInput, () => {
  if (tagSearchInput.value) {
    filterTags()
    showTagDropdown.value = true
  } else {
    showTagDropdown.value = false
  }
})

// Initialize
onMounted(() => {
  // Load available tags
  notesStore.loadTags()
  
  // Initialize filters from current search state
  const currentFilters = searchStore.searchFilters
  if (currentFilters) {
    Object.assign(filters.value, currentFilters)
  }
})
</script>

<style scoped>
.advanced-search {
  margin-bottom: 1rem;
}

.dropdown-menu {
  max-height: 200px;
  overflow-y: auto;
}

.tags {
  margin-bottom: 0.5rem;
}

.tags .tag {
  margin-bottom: 0.25rem;
}

.field:not(:last-child) {
  margin-bottom: 1rem;
}

.buttons {
  flex-wrap: wrap;
  gap: 0.5rem;
}

.card-footer-item {
  justify-content: flex-start;
}

/* Custom styling for readonly input */
input[readonly] {
  background-color: #f5f5f5;
  cursor: default;
}

/* Responsive design */
@media (max-width: 768px) {
  .columns {
    display: block;
  }
  
  .column {
    padding: 0.75rem 0;
  }
  
  .buttons {
    justify-content: center;
  }
  
  .card-footer-item {
    justify-content: center;
  }
}
</style>
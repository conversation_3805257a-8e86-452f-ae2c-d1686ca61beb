<template>
  <div class="notifications-widget">
    <div class="widget-header">
      <h3 class="widget-title">
        <span class="icon">
          <i class="fas fa-bell"></i>
        </span>
        Notifications
      </h3>
      <span class="notification-count" v-if="notifications.length > 0">
        {{ notifications.length }}
      </span>
    </div>

    <div class="widget-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-state">
        <div class="loader"></div>
      </div>

      <!-- Empty State -->
      <div v-else-if="notifications.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-bell-slash"></i>
        </div>
        <p>No new notifications</p>
      </div>

      <!-- Notifications List -->
      <div v-else class="notifications-list">
        <div v-for="notification in notifications" :key="notification.id" class="notification-item"
          :class="{ 'is-unread': !notification.read }">
          <div class="notification-icon">
            <i :class="getNotificationIcon(notification.type)"></i>
          </div>
          <div class="notification-content">
            <p class="notification-message">{{ notification.message }}</p>
            <span class="notification-time">{{ formatTime(notification.createdAt) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Mock data - replace with actual notification store
const loading = ref(false)
const notifications = ref([
  {
    id: 1,
    type: 'share',
    message: 'John shared a note with you',
    createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    read: false
  },
  {
    id: 2,
    type: 'group',
    message: 'You were added to "Project Alpha" group',
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    read: true
  }
])

// Methods
const getNotificationIcon = (type: string): string => {
  switch (type) {
    case 'share': return 'fas fa-share-alt'
    case 'group': return 'fas fa-users'
    case 'comment': return 'fas fa-comment'
    case 'system': return 'fas fa-cog'
    default: return 'fas fa-info-circle'
  }
}

const formatTime = (date: Date): string => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`

  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays}d ago`
}
</script>

<style scoped>
.notifications-widget {
  background: var(--card-background);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--color-border);
  background: var(--card-header-background);
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-strong);
  margin: 0;
}

.widget-title .icon {
  color: var(--color-warning);
}

.notification-count {
  background: var(--color-danger);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 1.5rem;
  text-align: center;
}

.widget-content {
  max-height: 300px;
  overflow-y: auto;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--color-text-muted);
  text-align: center;
}

.loader {
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-surface);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.empty-icon {
  font-size: 2rem;
  color: var(--color-border);
  margin-bottom: 1rem;
}

.notifications-list {
  padding: 0.5rem 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-left: 3px solid transparent;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background: var(--color-surface-hover);
}

.notification-item.is-unread {
  background: var(--color-surface);
  border-left-color: var(--color-primary);
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--color-surface);
  color: var(--color-text-muted);
  font-size: 0.875rem;
  flex-shrink: 0;
}

.notification-item.is-unread .notification-icon {
  background: var(--color-primary);
  color: white;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-message {
  font-size: 0.875rem;
  color: var(--color-text-strong);
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
}

.notification-time {
  font-size: 0.75rem;
  color: var(--color-text-light);
}
</style>
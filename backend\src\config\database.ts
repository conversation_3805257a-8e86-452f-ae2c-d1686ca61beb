import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import { MigrationManager, migrations } from './migrations';
import { SeedDataManager } from './seedData';

// Enable verbose mode for development
const sqlite = sqlite3.verbose();

let db: sqlite3.Database;

export interface DatabaseConnection {
  run: (sql: string, params?: any[]) => Promise<sqlite3.RunResult>;
  get: (sql: string, params?: any[]) => Promise<any>;
  all: (sql: string, params?: any[]) => Promise<any[]>;
  close: () => Promise<void>;
}

export async function initializeDatabase(): Promise<DatabaseConnection> {
  const dbPath = process.env.NODE_ENV === 'production' 
    ? process.env.DATABASE_PATH || './data/notes.db'
    : './dev.db';

  return new Promise((resolve, reject) => {
    db = new sqlite.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err);
        reject(err);
        return;
      }
      
      console.log(`Connected to SQLite database at ${dbPath}`);
      
      // Apply performance optimizations
      optimizeDatabase()
        .then(() => createTables())
        .then(() => MigrationManager.runMigrations(migrations))
        .then(() => {
          // Seed development data if in development mode
          if (process.env.NODE_ENV !== 'production') {
            return SeedDataManager.seedDevelopmentData();
          }
          return Promise.resolve();
        })
        .then(() => {
          const connection: DatabaseConnection = {
            run: promisify(db.run.bind(db)),
            get: promisify(db.get.bind(db)),
            all: promisify(db.all.bind(db)),
            close: promisify(db.close.bind(db))
          };
          resolve(connection);
        })
        .catch(reject);
    });
  });
}

async function optimizeDatabase(): Promise<void> {
  const runAsync = promisify(db.run.bind(db));

  try {
    // Enable WAL mode for better concurrency
    await runAsync('PRAGMA journal_mode = WAL');
    
    // Increase cache size (in KB) - default is 2MB, increase to 64MB
    await runAsync('PRAGMA cache_size = -65536');
    
    // Enable foreign key constraints
    await runAsync('PRAGMA foreign_keys = ON');
    
    // Set synchronous mode for better performance vs durability balance
    await runAsync('PRAGMA synchronous = NORMAL');
    
    // Optimize temp store
    await runAsync('PRAGMA temp_store = MEMORY');
    
    // Set mmap size for better I/O performance (256MB)
    await runAsync('PRAGMA mmap_size = 268435456');
    
    // Optimize page size (4KB is usually optimal)
    await runAsync('PRAGMA page_size = 4096');
    
    // Enable automatic index creation for WHERE clauses
    await runAsync('PRAGMA automatic_index = ON');
    
    // Set busy timeout to handle concurrent access
    await runAsync('PRAGMA busy_timeout = 30000');
    
    console.log('Database performance optimizations applied');
  } catch (error) {
    console.error('Error applying database optimizations:', error);
    // Don't throw - these are optimizations, not critical
  }
}

async function createTables(): Promise<void> {
  const runAsync = promisify(db.run.bind(db));

  try {
    // Users table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        display_name TEXT NOT NULL,
        avatar_url TEXT,
        preferences TEXT DEFAULT '{}',
        email_verified BOOLEAN DEFAULT FALSE,
        two_fa_secret TEXT,
        oauth_provider TEXT,
        oauth_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Add OAuth columns to existing users table if they don't exist
    try {
      await runAsync('ALTER TABLE users ADD COLUMN oauth_provider TEXT');
    } catch (err) {
      // Column already exists, ignore error
    }
    
    try {
      await runAsync('ALTER TABLE users ADD COLUMN oauth_id TEXT');
    } catch (err) {
      // Column already exists, ignore error
    }

    // Add backup_codes column for 2FA
    try {
      await runAsync('ALTER TABLE users ADD COLUMN backup_codes TEXT');
    } catch (err) {
      // Column already exists, ignore error
    }

    // Add admin column
    try {
      await runAsync('ALTER TABLE users ADD COLUMN admin BOOLEAN DEFAULT FALSE');
    } catch (err) {
      // Column already exists, ignore error
    }

    // Notes table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS notes (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        group_id TEXT,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        note_type TEXT NOT NULL CHECK (note_type IN ('richtext', 'markdown', 'kanban')),
        metadata TEXT DEFAULT '{}',
        is_archived BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Note versions table for revision history
    await runAsync(`
      CREATE TABLE IF NOT EXISTS note_versions (
        id TEXT PRIMARY KEY,
        note_id TEXT NOT NULL,
        content TEXT NOT NULL,
        version_number INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by TEXT NOT NULL,
        FOREIGN KEY (note_id) REFERENCES notes (id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `);

    // Tags table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS tags (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        icon TEXT NOT NULL DEFAULT 'fas fa-tag',
        color TEXT NOT NULL DEFAULT '#6c757d',
        is_predefined BOOLEAN NOT NULL DEFAULT 0,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        UNIQUE(user_id, name)
      )
    `);

    // Add new columns to existing tags table if they don't exist
    try {
      await runAsync('ALTER TABLE tags ADD COLUMN icon TEXT NOT NULL DEFAULT "fas fa-tag"');
    } catch (err) {
      // Column already exists, ignore error
    }
    
    try {
      await runAsync('ALTER TABLE tags ADD COLUMN color TEXT NOT NULL DEFAULT "#6c757d"');
    } catch (err) {
      // Column already exists, ignore error
    }
    
    try {
      await runAsync('ALTER TABLE tags ADD COLUMN is_predefined BOOLEAN NOT NULL DEFAULT 0');
    } catch (err) {
      // Column already exists, ignore error
    }
    
    try {
      await runAsync('ALTER TABLE tags ADD COLUMN updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP');
    } catch (err) {
      // Column already exists, ignore error
    }

    // Note-Tags junction table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS note_tags (
        note_id TEXT NOT NULL,
        tag_id TEXT NOT NULL,
        PRIMARY KEY (note_id, tag_id),
        FOREIGN KEY (note_id) REFERENCES notes (id) ON DELETE CASCADE,
        FOREIGN KEY (tag_id) REFERENCES tags (id) ON DELETE CASCADE
      )
    `);

    // Groups table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS groups (
        id TEXT PRIMARY KEY,
        owner_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        settings TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (owner_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Group members table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS group_members (
        group_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('admin', 'editor', 'viewer')),
        joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (group_id, user_id),
        FOREIGN KEY (group_id) REFERENCES groups (id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Note shares table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS note_shares (
        id TEXT PRIMARY KEY,
        note_id TEXT NOT NULL,
        share_token TEXT UNIQUE NOT NULL,
        access_level TEXT NOT NULL CHECK (access_level IN ('private', 'shared', 'unlisted', 'public')),
        permissions TEXT NOT NULL,
        expires_at DATETIME,
        password_hash TEXT,
        allowed_ips TEXT,
        watermark BOOLEAN DEFAULT 0,
        created_by TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        access_count INTEGER DEFAULT 0,
        last_accessed_at DATETIME,
        FOREIGN KEY (note_id) REFERENCES notes (id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Share access logs table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS share_access_logs (
        id TEXT PRIMARY KEY,
        share_id TEXT NOT NULL,
        ip_address TEXT NOT NULL,
        user_agent TEXT,
        accessed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        user_id TEXT,
        FOREIGN KEY (share_id) REFERENCES note_shares (id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
      )
    `);

    // Audit logs table for comprehensive logging
    await runAsync(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        session_id TEXT,
        action TEXT NOT NULL,
        resource_type TEXT NOT NULL,
        resource_id TEXT,
        ip_address TEXT NOT NULL,
        user_agent TEXT,
        request_method TEXT NOT NULL,
        request_path TEXT NOT NULL,
        request_body TEXT,
        response_status INTEGER NOT NULL,
        response_time_ms INTEGER NOT NULL,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
      )
    `);

    // Group invitations table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS group_invitations (
        id TEXT PRIMARY KEY,
        group_id TEXT NOT NULL,
        invited_by TEXT NOT NULL,
        invited_email TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('admin', 'editor', 'viewer')),
        token TEXT UNIQUE NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (group_id) REFERENCES groups (id) ON DELETE CASCADE,
        FOREIGN KEY (invited_by) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // FTS table and triggers are now managed by migrations
    // This ensures proper schema evolution and prevents conflicts

    // Create indexes for better performance
    await runAsync('CREATE INDEX IF NOT EXISTS idx_notes_user_id ON notes (user_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_notes_created_at ON notes (created_at)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_notes_updated_at ON notes (updated_at)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_notes_title ON notes (title)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_notes_note_type ON notes (note_type)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_notes_is_archived ON notes (is_archived)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_note_versions_note_id ON note_versions (note_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_tags_user_id ON tags (user_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_tags_name ON tags (name)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_note_tags_note_id ON note_tags (note_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_note_tags_tag_id ON note_tags (tag_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_group_members_user_id ON group_members (user_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_group_invitations_token ON group_invitations (token)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_group_invitations_group_id ON group_invitations (group_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_group_invitations_email ON group_invitations (invited_email)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_note_shares_note_id ON note_shares (note_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_note_shares_token ON note_shares (share_token)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_note_shares_created_by ON note_shares (created_by)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_share_access_share_id ON share_access_logs (share_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_share_access_ip ON share_access_logs (ip_address)');
    
    // Audit logs indexes for performance
    await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs (user_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs (action)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON audit_logs (resource_type)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_id ON audit_logs (resource_id)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs (ip_address)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs (created_at)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_response_status ON audit_logs (response_status)');
    await runAsync('CREATE INDEX IF NOT EXISTS idx_audit_logs_session_id ON audit_logs (session_id)');

    console.log('Database tables created successfully');
  } catch (error) {
    console.error('Error creating tables:', error);
    throw error;
  }
}

export function getDatabase(): sqlite3.Database {
  if (!db) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return db;
}
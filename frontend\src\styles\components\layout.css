/* Layout Component Styles */

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  width: 100%;
}

.container.is-fluid {
  max-width: none;
  padding: 0 var(--spacing-2);
}

.container.is-widescreen {
  max-width: 1344px;
}

.container.is-fullhd {
  max-width: 1408px;
}

/* Columns System */
.columns {
  display: flex;
  flex-wrap: wrap;
  margin: calc(-1 * var(--spacing-2));
}

.columns.is-mobile {
  display: flex;
}

.columns.is-desktop {
  display: flex;
}

@media screen and (max-width: 768px) {
  .columns:not(.is-mobile) {
    display: block;
  }

  .columns:not(.is-mobile) .column {
    width: 100% !important;
    flex: none;
  }
}

.columns.is-gapless {
  margin: 0;
}

.columns.is-gapless .column {
  padding: 0;
}

.columns.is-multiline {
  flex-wrap: wrap;
}

.columns.is-vcentered {
  align-items: center;
}

.columns.is-centered {
  justify-content: center;
}

/* Column */
.column {
  flex: 1;
  padding: var(--spacing-2);
}

.column.is-narrow {
  flex: none;
  width: unset;
}

.column.is-full {
  flex: none;
  width: 100%;
}

.column.is-four-fifths {
  flex: none;
  width: 80%;
}

.column.is-three-quarters {
  flex: none;
  width: 75%;
}

.column.is-two-thirds {
  flex: none;
  width: 66.6666%;
}

.column.is-half {
  flex: none;
  width: 50%;
}

.column.is-one-third {
  flex: none;
  width: 33.3333%;
}

.column.is-one-quarter {
  flex: none;
  width: 25%;
}

.column.is-one-fifth {
  flex: none;
  width: 20%;
}

/* Numeric columns */
.column.is-1 {
  flex: none;
  width: 8.33333%;
}
.column.is-2 {
  flex: none;
  width: 16.66667%;
}
.column.is-3 {
  flex: none;
  width: 25%;
}
.column.is-4 {
  flex: none;
  width: 33.33333%;
}
.column.is-5 {
  flex: none;
  width: 41.66667%;
}
.column.is-6 {
  flex: none;
  width: 50%;
}
.column.is-7 {
  flex: none;
  width: 58.33333%;
}
.column.is-8 {
  flex: none;
  width: 66.66667%;
}
.column.is-9 {
  flex: none;
  width: 75%;
}
.column.is-10 {
  flex: none;
  width: 83.33333%;
}
.column.is-11 {
  flex: none;
  width: 91.66667%;
}
.column.is-12 {
  flex: none;
  width: 100%;
}

/* Responsive column sizes */
@media screen and (max-width: 768px) {
  .column.is-full-mobile {
    flex: none;
    width: 100%;
  }
  .column.is-three-quarters-mobile {
    flex: none;
    width: 75%;
  }
  .column.is-two-thirds-mobile {
    flex: none;
    width: 66.6666%;
  }
  .column.is-half-mobile {
    flex: none;
    width: 50%;
  }
  .column.is-one-third-mobile {
    flex: none;
    width: 33.3333%;
  }
  .column.is-one-quarter-mobile {
    flex: none;
    width: 25%;
  }
}

@media screen and (min-width: 769px) and (max-width: 1023px) {
  .column.is-full-tablet {
    flex: none;
    width: 100%;
  }
  .column.is-three-quarters-tablet {
    flex: none;
    width: 75%;
  }
  .column.is-two-thirds-tablet {
    flex: none;
    width: 66.6666%;
  }
  .column.is-half-tablet {
    flex: none;
    width: 50%;
  }
  .column.is-one-third-tablet {
    flex: none;
    width: 33.3333%;
  }
  .column.is-one-quarter-tablet {
    flex: none;
    width: 25%;
  }
}

@media screen and (min-width: 1024px) {
  .column.is-full-desktop {
    flex: none;
    width: 100%;
  }
  .column.is-three-quarters-desktop {
    flex: none;
    width: 75%;
  }
  .column.is-two-thirds-desktop {
    flex: none;
    width: 66.6666%;
  }
  .column.is-half-desktop {
    flex: none;
    width: 50%;
  }
  .column.is-one-third-desktop {
    flex: none;
    width: 33.3333%;
  }
  .column.is-one-quarter-desktop {
    flex: none;
    width: 25%;
  }
}

/* Section */
.section {
  padding: var(--spacing-12) 0;
}

.section.is-small {
  padding: var(--spacing-6) 0;
}

.section.is-medium {
  padding: var(--spacing-16) 0;
}

.section.is-large {
  padding: var(--spacing-24) 0;
}

/* Hero */
.hero {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  padding: var(--spacing-16) 0;
  background: var(--color-surface);
}

.hero.is-small {
  padding: var(--spacing-8) 0;
}

.hero.is-medium {
  padding: var(--spacing-20) 0;
}

.hero.is-large {
  padding: var(--spacing-24) 0;
  min-height: 50vh;
}

.hero.is-fullheight {
  min-height: 100vh;
}

.hero.is-primary {
  background: var(--color-primary);
  color: white;
}

.hero.is-success {
  background: var(--color-success);
  color: white;
}

.hero.is-danger {
  background: var(--color-danger);
  color: white;
}

.hero.is-warning {
  background: var(--color-warning);
  color: rgba(0, 0, 0, 0.7);
}

.hero.is-info {
  background: var(--color-info);
  color: white;
}

.hero-body {
  padding: var(--spacing-12) var(--spacing-6);
  flex-grow: 1;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-head,
.hero-foot {
  flex-grow: 0;
  flex-shrink: 0;
}

/* Level (horizontal alignment) */
.level {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-4);
}

.level-left,
.level-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.level-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

@media screen and (max-width: 768px) {
  .level {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .level-left,
  .level-right {
    width: 100%;
    justify-content: center;
  }
}

/* Media Object */
.media {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  padding: var(--spacing-4) 0;
}

.media:not(:last-child) {
  border-bottom: 1px solid var(--color-border);
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-4);
}

.media-left,
.media-right {
  flex-shrink: 0;
}

.media-content {
  flex: 1;
  overflow: hidden;
}

.media-object {
  width: 64px;
  height: 64px;
  border-radius: var(--radius);
  object-fit: cover;
}

.media-object.is-rounded {
  border-radius: var(--radius-full);
}

/* Tile System */
.tile {
  align-items: stretch;
  display: block;
  flex-basis: 0;
  flex-grow: 1;
  flex-shrink: 1;
  min-height: min-content;
}

.tile.is-ancestor {
  margin-left: calc(-1 * var(--spacing-2));
  margin-right: calc(-1 * var(--spacing-2));
  margin-top: calc(-1 * var(--spacing-2));
}

.tile.is-ancestor:last-child {
  margin-bottom: calc(-1 * var(--spacing-2));
}

.tile.is-ancestor:not(:last-child) {
  margin-bottom: var(--spacing-2);
}

.tile.is-child {
  margin: var(--spacing-2);
}

.tile.is-parent {
  padding: var(--spacing-2);
}

.tile.is-vertical {
  flex-direction: column;
}

.tile.is-1 {
  flex: none;
  width: 8.33333%;
}
.tile.is-2 {
  flex: none;
  width: 16.66667%;
}
.tile.is-3 {
  flex: none;
  width: 25%;
}
.tile.is-4 {
  flex: none;
  width: 33.33333%;
}
.tile.is-5 {
  flex: none;
  width: 41.66667%;
}
.tile.is-6 {
  flex: none;
  width: 50%;
}
.tile.is-7 {
  flex: none;
  width: 58.33333%;
}
.tile.is-8 {
  flex: none;
  width: 66.66667%;
}
.tile.is-9 {
  flex: none;
  width: 75%;
}
.tile.is-10 {
  flex: none;
  width: 83.33333%;
}
.tile.is-11 {
  flex: none;
  width: 91.66667%;
}
.tile.is-12 {
  flex: none;
  width: 100%;
}

@media screen and (min-width: 769px) {
  .tile:not(.is-child) {
    display: flex;
  }
}

/* Box */
.box {
  background: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  color: var(--color-text);
  display: block;
  padding: var(--spacing-6);
  border: 1px solid var(--color-border);
}

.box:not(:last-child) {
  margin-bottom: var(--spacing-6);
}

/* Content */
.content {
  color: var(--color-text);
  line-height: var(--line-height-relaxed);
}

.content h1,
.content h2,
.content h3,
.content h4,
.content h5,
.content h6 {
  color: var(--color-text-strong);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-2);
  margin-top: var(--spacing-4);
}

.content h1:first-child,
.content h2:first-child,
.content h3:first-child,
.content h4:first-child,
.content h5:first-child,
.content h6:first-child {
  margin-top: 0;
}

.content p {
  margin-bottom: var(--spacing-4);
}

.content p:last-child {
  margin-bottom: 0;
}

.content ul,
.content ol {
  margin-bottom: var(--spacing-4);
  margin-left: var(--spacing-6);
}

.content ul {
  list-style: disc outside;
}

.content ol {
  list-style: decimal outside;
}

.content li {
  margin-bottom: var(--spacing-1);
}

.content blockquote {
  background: var(--color-surface);
  border-left: 4px solid var(--color-primary);
  padding: var(--spacing-4);
  margin: var(--spacing-4) 0;
}

.content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-4);
}

.content table th,
.content table td {
  border: 1px solid var(--color-border);
  padding: var(--spacing-2) var(--spacing-3);
  text-align: left;
}

.content table th {
  background: var(--color-surface);
  font-weight: var(--font-weight-semibold);
}

/* Responsive Layout */
@media screen and (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-2);
  }

  .section {
    padding: var(--spacing-6) 0;
  }

  .section.is-medium {
    padding: var(--spacing-8) 0;
  }

  .section.is-large {
    padding: var(--spacing-12) 0;
  }

  .hero {
    padding: var(--spacing-8) 0;
  }

  .hero.is-medium {
    padding: var(--spacing-12) 0;
  }

  .hero.is-large {
    padding: var(--spacing-16) 0;
  }

  .hero-body {
    padding: var(--spacing-6) var(--spacing-4);
  }

  .media {
    gap: var(--spacing-3);
  }

  .media-object {
    width: 48px;
    height: 48px;
  }

  .box {
    padding: var(--spacing-4);
  }

  .content ul,
  .content ol {
    margin-left: var(--spacing-4);
  }
}

<template>
  <div class="modal is-active">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card board-sharing-modal">
      <header class="modal-card-head">
        <p class="modal-card-title">Share Board</p>
        <button class="delete" @click="$emit('close')" aria-label="close"></button>
      </header>

      <section class="modal-card-body">
        <div class="sharing-container">
          <!-- Board Visibility -->
          <div class="sharing-section">
            <h4 class="section-title">Board Visibility</h4>
            <div class="field">
              <div class="control">
                <label class="radio">
                  <input
                    v-model="localShareSettings.isPublic"
                    :value="false"
                    type="radio"
                    name="visibility"
                    @change="updateShareSettings"
                  />
                  <span class="radio-content">
                    <i class="fas fa-lock"></i>
                    <div class="radio-text">
                      <strong>Private</strong>
                      <p>Only you and invited members can access this board</p>
                    </div>
                  </span>
                </label>
              </div>
              
              <div class="control">
                <label class="radio">
                  <input
                    v-model="localShareSettings.isPublic"
                    :value="true"
                    type="radio"
                    name="visibility"
                    @change="updateShareSettings"
                  />
                  <span class="radio-content">
                    <i class="fas fa-globe"></i>
                    <div class="radio-text">
                      <strong>Public</strong>
                      <p>Anyone with the link can view this board</p>
                    </div>
                  </span>
                </label>
              </div>
            </div>
          </div>

          <!-- Share Link -->
          <div v-if="localShareSettings.isPublic || localShareSettings.shareLink" class="sharing-section">
            <h4 class="section-title">Share Link</h4>
            <div class="share-link-container">
              <div class="field has-addons">
                <div class="control is-expanded">
                  <input
                    :value="shareLink"
                    class="input"
                    type="text"
                    readonly
                  />
                </div>
                <div class="control">
                  <button
                    @click="copyShareLink"
                    class="button is-primary"
                  >
                    <i class="fas fa-copy"></i>
                    <span>Copy</span>
                  </button>
                </div>
              </div>
              
              <div class="share-link-options">
                <div class="field">
                  <label class="checkbox">
                    <input
                      v-model="linkOptions.hasExpiration"
                      type="checkbox"
                      @change="updateLinkOptions"
                    />
                    Link expires
                  </label>
                </div>
                
                <div v-if="linkOptions.hasExpiration" class="field">
                  <div class="control">
                    <input
                      v-model="linkOptions.expiration"
                      type="datetime-local"
                      class="input is-small"
                      @change="updateLinkOptions"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>   
       <!-- Invite Members -->
          <div class="sharing-section">
            <h4 class="section-title">Invite Members</h4>
            <div class="invite-form">
              <div class="field has-addons">
                <div class="control is-expanded">
                  <input
                    v-model="inviteEmail"
                    class="input"
                    type="email"
                    placeholder="Enter email address"
                  />
                </div>
                <div class="control">
                  <div class="select">
                    <select v-model="invitePermission">
                      <option value="view">View</option>
                      <option value="edit">Edit</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>
                </div>
                <div class="control">
                  <button
                    @click="inviteMember"
                    class="button is-primary"
                    :disabled="!inviteEmail.trim()"
                  >
                    <i class="fas fa-plus"></i>
                    <span>Invite</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Current Members -->
          <div class="sharing-section">
            <h4 class="section-title">Members ({{ members.length }})</h4>
            <div class="members-list">
              <div
                v-for="member in members"
                :key="member.id"
                class="member-item"
              >
                <div class="member-info">
                  <div class="member-avatar">
                    <img
                      v-if="member.avatar"
                      :src="member.avatar"
                      :alt="member.name"
                      class="avatar"
                    />
                    <span v-else class="avatar-placeholder">
                      {{ member.name.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                  <div class="member-details">
                    <span class="member-name">{{ member.name }}</span>
                    <span class="member-email">{{ member.email }}</span>
                  </div>
                </div>
                
                <div class="member-actions">
                  <div class="select is-small">
                    <select
                      :value="getMemberPermission(member.id)"
                      @change="(e: Event) => updateMemberPermission(member.id, (e.target as HTMLSelectElement)?.value)"
                      :disabled="member.id === currentUserId"
                    >
                      <option value="view">View</option>
                      <option value="edit">Edit</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>
                  
                  <button
                    v-if="member.id !== currentUserId"
                    @click="removeMember(member.id)"
                    class="button is-small is-ghost has-text-danger"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Permission Levels Info -->
          <div class="sharing-section">
            <h4 class="section-title">Permission Levels</h4>
            <div class="permissions-info">
              <div class="permission-item">
                <div class="permission-icon">
                  <i class="fas fa-eye"></i>
                </div>
                <div class="permission-details">
                  <strong>View</strong>
                  <p>Can view the board and cards but cannot make changes</p>
                </div>
              </div>
              
              <div class="permission-item">
                <div class="permission-icon">
                  <i class="fas fa-edit"></i>
                </div>
                <div class="permission-details">
                  <strong>Edit</strong>
                  <p>Can view and edit cards, but cannot change board settings</p>
                </div>
              </div>
              
              <div class="permission-item">
                <div class="permission-icon">
                  <i class="fas fa-crown"></i>
                </div>
                <div class="permission-details">
                  <strong>Admin</strong>
                  <p>Full access including board settings and member management</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <footer class="modal-card-foot">
        <button
          @click="saveSettings"
          class="button is-primary"
        >
          <i class="fas fa-save"></i>
          <span>Save Settings</span>
        </button>
        <button @click="$emit('close')" class="button">
          Close
        </button>
      </footer>
    </div>

    <!-- Copy Success Toast -->
    <div v-if="showCopySuccess" class="notification is-success copy-toast">
      <i class="fas fa-check"></i>
      <span>Link copied to clipboard!</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { BoardShareSettings, KanbanAssignee } from '../../types/kanban'

interface Props {
  shareSettings: BoardShareSettings
  members: KanbanAssignee[]
  currentUserId: string
  boardId: string
}

interface Emits {
  (e: 'close'): void
  (e: 'update:shareSettings', settings: BoardShareSettings): void
  (e: 'invite-member', email: string, permission: string): void
  (e: 'remove-member', memberId: string): void
  (e: 'update-member-permission', memberId: string, permission: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const localShareSettings = ref<BoardShareSettings>({ ...props.shareSettings })
const inviteEmail = ref('')
const invitePermission = ref('view')
const showCopySuccess = ref(false)

// Link options
const linkOptions = ref({
  hasExpiration: false,
  expiration: ''
})

// Computed
const shareLink = computed(() => {
  if (localShareSettings.value.shareLink) {
    return `${window.location.origin}/board/${props.boardId}/share/${localShareSettings.value.shareLink}`
  }
  return `${window.location.origin}/board/${props.boardId}`
})

// Methods
const updateShareSettings = () => {
  if (localShareSettings.value.isPublic && !localShareSettings.value.shareLink) {
    // Generate share link if making public
    localShareSettings.value.shareLink = generateShareLink()
  }
  emit('update:shareSettings', localShareSettings.value)
}

const updateLinkOptions = () => {
  if (linkOptions.value.hasExpiration && linkOptions.value.expiration) {
    localShareSettings.value.linkExpiration = linkOptions.value.expiration
  } else {
    localShareSettings.value.linkExpiration = undefined
  }
  updateShareSettings()
}

const generateShareLink = () => {
  return Math.random().toString(36).substr(2, 16)
}

const copyShareLink = async () => {
  try {
    await navigator.clipboard.writeText(shareLink.value)
    showCopySuccess.value = true
    setTimeout(() => {
      showCopySuccess.value = false
    }, 3000)
  } catch (error) {
    console.error('Failed to copy link:', error)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = shareLink.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    showCopySuccess.value = true
    setTimeout(() => {
      showCopySuccess.value = false
    }, 3000)
  }
}

const inviteMember = () => {
  if (!inviteEmail.value.trim()) return
  
  emit('invite-member', inviteEmail.value.trim(), invitePermission.value)
  inviteEmail.value = ''
  invitePermission.value = 'view'
}

const removeMember = (memberId: string) => {
  if (confirm('Are you sure you want to remove this member?')) {
    emit('remove-member', memberId)
  }
}

const updateMemberPermission = (memberId: string, permission: string) => {
  emit('update-member-permission', memberId, permission)
}

const getMemberPermission = (memberId: string) => {
  return localShareSettings.value.permissions[memberId] || 'view'
}

const saveSettings = () => {
  emit('update:shareSettings', localShareSettings.value)
  emit('close')
}

// Watch for external changes
watch(() => props.shareSettings, (newSettings) => {
  localShareSettings.value = { ...newSettings }
}, { deep: true })

// Initialize link options
watch(() => localShareSettings.value.linkExpiration, (expiration) => {
  if (expiration) {
    linkOptions.value.hasExpiration = true
    linkOptions.value.expiration = expiration
  }
}, { immediate: true })
</script>

<style scoped>
.board-sharing-modal {
  width: 600px;
  max-width: 95vw;
  max-height: 90vh;
}

.modal-card-body {
  padding: 1.5rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.sharing-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.sharing-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 1.5rem;
}

.sharing-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #333;
}

/* Radio buttons */
.radio {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.75rem;
}

.radio:hover {
  border-color: #3273dc;
  background: rgba(50, 115, 220, 0.05);
}

.radio input[type="radio"]:checked + .radio-content {
  color: #3273dc;
}

.radio input[type="radio"]:checked {
  accent-color: #3273dc;
}

.radio-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  flex: 1;
}

.radio-content i {
  font-size: 1.2rem;
  margin-top: 0.125rem;
}

.radio-text strong {
  display: block;
  margin-bottom: 0.25rem;
}

.radio-text p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

/* Share Link */
.share-link-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.share-link-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: #f9f9f9;
  border-radius: 6px;
}

/* Invite Form */
.invite-form {
  margin-bottom: 1rem;
}

/* Members List */
.members-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: #f9f9f9;
  border-radius: 6px;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #3273dc;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.member-name {
  font-weight: 600;
  font-size: 0.95rem;
}

.member-email {
  font-size: 0.85rem;
  color: #666;
}

.member-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Permissions Info */
.permissions-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.permission-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: #f9f9f9;
  border-radius: 6px;
}

.permission-icon {
  width: 32px;
  height: 32px;
  background: #3273dc;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.permission-details strong {
  display: block;
  margin-bottom: 0.25rem;
  font-size: 0.95rem;
}

.permission-details p {
  margin: 0;
  font-size: 0.85rem;
  color: #666;
  line-height: 1.4;
}

/* Copy Toast */
.copy-toast {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .board-sharing-modal {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
  }
  
  .modal-card-body {
    padding: 1rem;
  }
  
  .sharing-container {
    gap: 1.5rem;
  }
  
  .member-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .member-actions {
    align-self: stretch;
    justify-content: space-between;
  }
  
  .copy-toast {
    top: 1rem;
    right: 1rem;
    left: 1rem;
  }
}
</style>
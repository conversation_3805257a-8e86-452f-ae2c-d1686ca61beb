#!/usr/bin/env node

// Build optimization script for <500ms initialization target
// This script applies additional optimizations beyond Vite configuration

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Starting optimized build process...');

// Step 1: Clean previous build
console.log('🧹 Cleaning previous build...');
try {
  execSync('npm run build-only', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Step 2: Analyze bundle sizes
console.log('📊 Analyzing bundle sizes...');
const distPath = path.join(__dirname, '../dist');
const assetsPath = path.join(distPath, 'assets');

if (!fs.existsSync(assetsPath)) {
  console.error('❌ Assets directory not found');
  process.exit(1);
}

// Step 3: Apply post-build optimizations
console.log('⚡ Applying post-build optimizations...');

// Optimize CSS files
const cssFiles = fs.readdirSync(assetsPath).filter(file => file.endsWith('.css'));
cssFiles.forEach(file => {
  const filePath = path.join(assetsPath, file);
  const stats = fs.statSync(filePath);
  const sizeKB = Math.round(stats.size / 1024);

  console.log(`📄 CSS: ${file} - ${sizeKB}KB`);

  // If CSS file is too large, apply additional minification
  if (sizeKB > 200) {
    console.log(`⚠️  Large CSS file detected: ${file} (${sizeKB}KB)`);
    // Additional CSS optimization could be added here
  }
});

// Optimize JS files
const jsFiles = fs.readdirSync(assetsPath).filter(file => file.endsWith('.js'));
jsFiles.forEach(file => {
  const filePath = path.join(assetsPath, file);
  const stats = fs.statSync(filePath);
  const sizeKB = Math.round(stats.size / 1024);

  console.log(`📄 JS: ${file} - ${sizeKB}KB`);

  // Flag large JS files for review
  if (sizeKB > 300) {
    console.log(`⚠️  Large JS file detected: ${file} (${sizeKB}KB)`);
  }
});

// Step 4: Generate performance report
console.log('📈 Generating performance report...');

const totalSize = [...cssFiles, ...jsFiles].reduce((total, file) => {
  const filePath = path.join(assetsPath, file);
  const stats = fs.statSync(filePath);
  return total + stats.size;
}, 0);

const totalSizeKB = Math.round(totalSize / 1024);
const targetSizeKB = 1200; // 1.2MB target

console.log('\n📊 Build Optimization Report');
console.log('================================');
console.log(`Total bundle size: ${totalSizeKB}KB`);
console.log(`Target size: ${targetSizeKB}KB`);
console.log(`Status: ${totalSizeKB <= targetSizeKB ? '✅ PASS' : '❌ EXCEEDED'}`);

if (totalSizeKB > targetSizeKB) {
  const excess = totalSizeKB - targetSizeKB;
  console.log(`⚠️  Bundle size exceeded by ${excess}KB`);

  console.log('\n🔧 Optimization Recommendations:');
  console.log('• Review large files identified above');
  console.log('• Consider additional code splitting');
  console.log('• Remove unused dependencies');
  console.log('• Implement more aggressive tree shaking');
}

// Step 5: Create performance metrics baseline
console.log('\n📝 Creating performance baseline...');

const performanceBaseline = {
  timestamp: new Date().toISOString(),
  buildSize: {
    totalKB: totalSizeKB,
    targetKB: targetSizeKB,
    status: totalSizeKB <= targetSizeKB ? 'PASS' : 'FAIL',
  },
  files: {
    css: cssFiles.map(file => ({
      name: file,
      sizeKB: Math.round(fs.statSync(path.join(assetsPath, file)).size / 1024),
    })),
    js: jsFiles.map(file => ({
      name: file,
      sizeKB: Math.round(fs.statSync(path.join(assetsPath, file)).size / 1024),
    })),
  },
  targets: {
    initTime: 500, // ms
    bundleSize: 1200, // KB
    coreWebVitals: {
      fcp: 1000,
      lcp: 1500,
      tti: 2000,
      cls: 0.05,
      fid: 100,
    },
  },
};

fs.writeFileSync(
  path.join(distPath, 'performance-baseline.json'),
  JSON.stringify(performanceBaseline, null, 2)
);

console.log('✅ Performance baseline saved to dist/performance-baseline.json');

// Step 6: Final validation
console.log('\n🔍 Final validation...');

if (totalSizeKB <= targetSizeKB) {
  console.log('✅ Build optimization completed successfully!');
  console.log(`📦 Bundle size: ${totalSizeKB}KB (within ${targetSizeKB}KB target)`);
  process.exit(0);
} else {
  console.log('❌ Build optimization failed - bundle size exceeded');
  console.log(`📦 Bundle size: ${totalSizeKB}KB (exceeds ${targetSizeKB}KB target)`);
  process.exit(1);
}

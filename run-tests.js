#!/usr/bin/env node

const { spawn } = require("child_process");
const path = require("path");

async function runCommand(command, args, cwd) {
    return new Promise((resolve, reject) => {
        console.log(`\n🧪 Running: ${command} ${args.join(" ")} in ${cwd}`);

        const child = spawn(command, args, {
            cwd,
            stdio: "inherit",
            shell: true,
        });

        child.on("close", (code) => {
            if (code === 0) {
                console.log(`✅ ${command} completed successfully`);
                resolve();
            } else {
                console.log(`❌ ${command} failed with code ${code}`);
                reject(new Error(`Command failed: ${command}`));
            }
        });
    });
}

async function runAllTests() {
    try {
        console.log("🚀 Starting comprehensive test suite...\n");

        // Backend tests
        console.log("📦 Running Backend Tests (Task 15.1)");
        await runCommand("npm", ["test"], path.join(__dirname, "backend"));

        // Frontend tests
        console.log("\n🎨 Running Frontend Tests (Task 15.2)");
        await runCommand(
            "npm",
            ["run", "test:unit"],
            path.join(__dirname, "frontend")
        );

        console.log("\n🎉 All tests completed successfully!");
    } catch (error) {
        console.error("\n💥 Test suite failed:", error.message);
        process.exit(1);
    }
}

runAllTests();

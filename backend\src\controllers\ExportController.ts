import { Request, Response } from 'express';
import { ExportService, ExportOptions } from '../services/ExportService';
import { NoteRepository } from '../repositories/NoteRepository';

export class ExportController {
  /**
   * Export a single note
   * POST /api/notes/:id/export
   */
  static async exportNote(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const noteId = req.params.id;
      const { format, includeMetadata, customStyles } = req.body;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Validate format
      if (!format || !['pdf', 'html', 'markdown'].includes(format)) {
        res.status(400).json({ error: 'Invalid format. Must be pdf, html, or markdown' });
        return;
      }

      // Get the note
      const note = await NoteRepository.findById(noteId);
      if (!note) {
        res.status(404).json({ error: 'Note not found' });
        return;
      }

      // Check permissions
      if (note.userId !== userId) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const options: ExportOptions = {
        format,
        includeMetadata: includeMetadata !== false, // Default to true
        customStyles
      };

      const exportBuffer = await ExportService.exportNote(note, options);

      // Set appropriate headers
      const filename = `${note.title.replace(/[^a-zA-Z0-9]/g, '_')}.${format}`;
      const contentType = ExportController.getContentType(format);

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', exportBuffer.length);

      res.send(exportBuffer);
    } catch (error) {
      console.error('Error exporting note:', error);
      res.status(500).json({ 
        error: 'Export failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }

  /**
   * Export multiple notes
   * POST /api/notes/export
   */
  static async exportMultipleNotes(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const { noteIds, format, includeMetadata, customStyles } = req.body;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Validate input
      if (!noteIds || !Array.isArray(noteIds) || noteIds.length === 0) {
        res.status(400).json({ error: 'noteIds array is required' });
        return;
      }

      if (!format || !['pdf', 'html', 'markdown'].includes(format)) {
        res.status(400).json({ error: 'Invalid format. Must be pdf, html, or markdown' });
        return;
      }

      if (noteIds.length > 100) {
        res.status(400).json({ error: 'Too many notes. Maximum 100 notes per export' });
        return;
      }

      // Get all notes and verify permissions
      const notes = [];
      for (const noteId of noteIds) {
        const note = await NoteRepository.findById(noteId);
        if (!note) {
          res.status(404).json({ error: `Note not found: ${noteId}` });
          return;
        }

        if (note.userId !== userId) {
          // TODO: Add group permission check when groups are implemented
          res.status(403).json({ error: `Access denied to note: ${noteId}` });
          return;
        }

        notes.push(note);
      }

      // For large exports, create a job
      if (notes.length > 10) {
        const job = ExportService.createExportJob(userId, noteIds, format);
        
        // Process job asynchronously
        ExportService.processExportJob(job.id, notes).catch(error => {
          console.error('Error processing export job:', error);
        });

        res.json({
          jobId: job.id,
          status: job.status,
          message: 'Export job created. Check status using the job ID.'
        });
        return;
      }

      // For smaller exports, process immediately
      const options: ExportOptions = {
        format,
        includeMetadata: includeMetadata !== false,
        customStyles
      };

      const exportBuffer = await ExportService.exportMultipleNotes(notes, options);

      // Set appropriate headers
      const filename = `notes_export_${new Date().toISOString().split('T')[0]}.${format}`;
      const contentType = ExportController.getContentType(format);

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', exportBuffer.length);

      res.send(exportBuffer);
    } catch (error) {
      console.error('Error exporting multiple notes:', error);
      res.status(500).json({ 
        error: 'Export failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }

  /**
   * Get export job status
   * GET /api/exports/:jobId
   */
  static async getExportJob(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const jobId = req.params.jobId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const job = ExportService.getExportJob(jobId);
      if (!job) {
        res.status(404).json({ error: 'Export job not found' });
        return;
      }

      if (job.userId !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      res.json(job);
    } catch (error) {
      console.error('Error getting export job:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Download export job result
   * GET /api/exports/:jobId/download
   */
  static async downloadExportJob(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const jobId = req.params.jobId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const job = ExportService.getExportJob(jobId);
      if (!job) {
        res.status(404).json({ error: 'Export job not found' });
        return;
      }

      if (job.userId !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      if (job.status !== 'completed') {
        res.status(400).json({ error: 'Export job not completed', status: job.status });
        return;
      }

      // In a real implementation, you would retrieve the file from storage
      // For now, we'll regenerate the export (not ideal for production)
      const notes = [];
      for (const noteId of job.noteIds) {
        const note = await NoteRepository.findById(noteId);
        if (note && note.userId === userId) {
          notes.push(note);
        }
      }

      if (notes.length === 0) {
        res.status(404).json({ error: 'No accessible notes found for this job' });
        return;
      }

      const options: ExportOptions = {
        format: job.format,
        includeMetadata: true
      };

      const exportBuffer = await ExportService.exportMultipleNotes(notes, options);

      // Set appropriate headers
      const filename = `notes_export_${job.id}.${job.format}`;
      const contentType = ExportController.getContentType(job.format);

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', exportBuffer.length);

      res.send(exportBuffer);
    } catch (error) {
      console.error('Error downloading export job:', error);
      res.status(500).json({ 
        error: 'Download failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }

  /**
   * Get user's export history
   * GET /api/exports
   */
  static async getExportHistory(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const jobs = ExportService.getUserExportJobs(userId);

      res.json({ jobs });
    } catch (error) {
      console.error('Error getting export history:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Clean up old export jobs
   * POST /api/exports/cleanup
   */
  static async cleanupExportJobs(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Only allow cleanup for admin users or system processes
      // For now, allow any authenticated user to clean up their own jobs
      const maxAgeHours = parseInt(req.body.maxAgeHours) || 24;

      ExportService.cleanupOldJobs(maxAgeHours);

      res.json({ message: 'Cleanup completed' });
    } catch (error) {
      console.error('Error cleaning up export jobs:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get content type for export format
   */
  private static getContentType(format: string): string {
    switch (format) {
      case 'pdf':
        return 'application/pdf';
      case 'html':
        return 'text/html';
      case 'markdown':
        return 'text/markdown';
      default:
        return 'application/octet-stream';
    }
  }
}
/* Color Utility Classes */

/* Text Colors */
.has-text-primary {
  color: var(--color-primary) !important;
}
.has-text-primary-dark {
  color: var(--color-primary-dark) !important;
}
.has-text-primary-light {
  color: var(--color-primary-light) !important;
}

.has-text-success {
  color: var(--color-success) !important;
}
.has-text-success-dark {
  color: var(--color-success-dark) !important;
}
.has-text-success-light {
  color: var(--color-success-light) !important;
}

.has-text-danger {
  color: var(--color-danger) !important;
}
.has-text-danger-dark {
  color: var(--color-danger-dark) !important;
}
.has-text-danger-light {
  color: var(--color-danger-light) !important;
}

.has-text-warning {
  color: var(--color-warning-dark) !important;
}
.has-text-warning-dark {
  color: var(--color-warning-dark) !important;
}
.has-text-warning-light {
  color: var(--color-warning-light) !important;
}

.has-text-info {
  color: var(--color-info) !important;
}
.has-text-info-dark {
  color: var(--color-info-dark) !important;
}
.has-text-info-light {
  color: var(--color-info-light) !important;
}

.has-text-link {
  color: var(--color-link) !important;
}
.has-text-link-dark {
  color: var(--color-link-dark) !important;
}
.has-text-link-light {
  color: var(--color-link-light) !important;
}

/* Semantic Text Colors */
.has-text-white {
  color: #ffffff !important;
}
.has-text-black {
  color: #000000 !important;
}

.has-text-light {
  color: var(--color-text-light) !important;
}
.has-text-dark {
  color: var(--color-text-strong) !important;
}

.has-text-text {
  color: var(--color-text) !important;
}
.has-text-text-strong {
  color: var(--color-text-strong) !important;
}
.has-text-text-muted {
  color: var(--color-text-muted) !important;
}
.has-text-text-light {
  color: var(--color-text-light) !important;
}

/* Grayscale Text Colors */
.has-text-grey {
  color: #7a7a7a !important;
}
.has-text-grey-light {
  color: #b5b5b5 !important;
}
.has-text-grey-lighter {
  color: #dbdbdb !important;
}
.has-text-grey-dark {
  color: #4a4a4a !important;
}
.has-text-grey-darker {
  color: #363636 !important;
}

/* Background Colors */
.has-background-primary {
  background-color: var(--color-primary) !important;
}
.has-background-primary-dark {
  background-color: var(--color-primary-dark) !important;
}
.has-background-primary-light {
  background-color: var(--color-primary-light) !important;
}

.has-background-success {
  background-color: var(--color-success) !important;
}
.has-background-success-dark {
  background-color: var(--color-success-dark) !important;
}
.has-background-success-light {
  background-color: var(--color-success-light) !important;
}

.has-background-danger {
  background-color: var(--color-danger) !important;
}
.has-background-danger-dark {
  background-color: var(--color-danger-dark) !important;
}
.has-background-danger-light {
  background-color: var(--color-danger-light) !important;
}

.has-background-warning {
  background-color: var(--color-warning) !important;
}
.has-background-warning-dark {
  background-color: var(--color-warning-dark) !important;
}
.has-background-warning-light {
  background-color: var(--color-warning-light) !important;
}

.has-background-info {
  background-color: var(--color-info) !important;
}
.has-background-info-dark {
  background-color: var(--color-info-dark) !important;
}
.has-background-info-light {
  background-color: var(--color-info-light) !important;
}

.has-background-link {
  background-color: var(--color-link) !important;
}
.has-background-link-dark {
  background-color: var(--color-link-dark) !important;
}
.has-background-link-light {
  background-color: var(--color-link-light) !important;
}

/* Semantic Background Colors */
.has-background-white {
  background-color: #ffffff !important;
}
.has-background-black {
  background-color: #000000 !important;
}

.has-background-light {
  background-color: var(--color-surface) !important;
}
.has-background-dark {
  background-color: var(--color-text-strong) !important;
}

.has-background {
  background-color: var(--color-background) !important;
}
.has-background-surface {
  background-color: var(--color-surface) !important;
}
.has-background-surface-hover {
  background-color: var(--color-surface-hover) !important;
}

/* Grayscale Background Colors */
.has-background-grey {
  background-color: #7a7a7a !important;
}
.has-background-grey-light {
  background-color: #b5b5b5 !important;
}
.has-background-grey-lighter {
  background-color: #dbdbdb !important;
}
.has-background-grey-dark {
  background-color: #4a4a4a !important;
}
.has-background-grey-darker {
  background-color: #363636 !important;
}

/* Border Colors */
.has-border-primary {
  border-color: var(--color-primary) !important;
}
.has-border-success {
  border-color: var(--color-success) !important;
}
.has-border-danger {
  border-color: var(--color-danger) !important;
}
.has-border-warning {
  border-color: var(--color-warning) !important;
}
.has-border-info {
  border-color: var(--color-info) !important;
}
.has-border-link {
  border-color: var(--color-link) !important;
}

.has-border-white {
  border-color: #ffffff !important;
}
.has-border-black {
  border-color: #000000 !important;
}
.has-border-light {
  border-color: var(--color-surface) !important;
}
.has-border-dark {
  border-color: var(--color-text-strong) !important;
}

.has-border {
  border-color: var(--color-border) !important;
}
.has-border-hover {
  border-color: var(--color-border-hover) !important;
}

/* Notification Colors */
.has-background-success-notification {
  background-color: var(--notification-success-background) !important;
  color: var(--notification-success-text) !important;
  border-color: var(--notification-success-border) !important;
}

.has-background-danger-notification {
  background-color: var(--notification-danger-background) !important;
  color: var(--notification-danger-text) !important;
  border-color: var(--notification-danger-border) !important;
}

.has-background-warning-notification {
  background-color: var(--notification-warning-background) !important;
  color: var(--notification-warning-text) !important;
  border-color: var(--notification-warning-border) !important;
}

.has-background-info-notification {
  background-color: var(--notification-info-background) !important;
  color: var(--notification-info-text) !important;
  border-color: var(--notification-info-border) !important;
}

/* Opacity Utilities */
.has-opacity-0 {
  opacity: 0 !important;
}
.has-opacity-25 {
  opacity: 0.25 !important;
}
.has-opacity-50 {
  opacity: 0.5 !important;
}
.has-opacity-75 {
  opacity: 0.75 !important;
}
.has-opacity-100 {
  opacity: 1 !important;
}

/* Hover Color Utilities */
.has-text-primary-hover:hover {
  color: var(--color-primary) !important;
}
.has-text-success-hover:hover {
  color: var(--color-success) !important;
}
.has-text-danger-hover:hover {
  color: var(--color-danger) !important;
}
.has-text-warning-hover:hover {
  color: var(--color-warning-dark) !important;
}
.has-text-info-hover:hover {
  color: var(--color-info) !important;
}
.has-text-link-hover:hover {
  color: var(--color-link) !important;
}

.has-background-primary-hover:hover {
  background-color: var(--color-primary) !important;
}
.has-background-success-hover:hover {
  background-color: var(--color-success) !important;
}
.has-background-danger-hover:hover {
  background-color: var(--color-danger) !important;
}
.has-background-warning-hover:hover {
  background-color: var(--color-warning) !important;
}
.has-background-info-hover:hover {
  background-color: var(--color-info) !important;
}
.has-background-link-hover:hover {
  background-color: var(--color-link) !important;
}

.has-background-surface-hover:hover {
  background-color: var(--color-surface-hover) !important;
}

/* Focus Color Utilities */
.has-text-primary-focus:focus {
  color: var(--color-primary) !important;
}
.has-text-success-focus:focus {
  color: var(--color-success) !important;
}
.has-text-danger-focus:focus {
  color: var(--color-danger) !important;
}
.has-text-warning-focus:focus {
  color: var(--color-warning-dark) !important;
}
.has-text-info-focus:focus {
  color: var(--color-info) !important;
}
.has-text-link-focus:focus {
  color: var(--color-link) !important;
}

/* Color Combinations for Better Contrast */
.has-text-white-on-primary {
  color: #ffffff !important;
  background-color: var(--color-primary) !important;
}

.has-text-white-on-success {
  color: #ffffff !important;
  background-color: var(--color-success) !important;
}

.has-text-white-on-danger {
  color: #ffffff !important;
  background-color: var(--color-danger) !important;
}

.has-text-dark-on-warning {
  color: rgba(0, 0, 0, 0.7) !important;
  background-color: var(--color-warning) !important;
}

.has-text-white-on-info {
  color: #ffffff !important;
  background-color: var(--color-info) !important;
}

/* Gradient Backgrounds */
.has-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light)) !important;
}

.has-gradient-success {
  background: linear-gradient(135deg, var(--color-success), var(--color-success-light)) !important;
}

.has-gradient-danger {
  background: linear-gradient(135deg, var(--color-danger), var(--color-danger-light)) !important;
}

.has-gradient-warning {
  background: linear-gradient(135deg, var(--color-warning), var(--color-warning-light)) !important;
}

.has-gradient-info {
  background: linear-gradient(135deg, var(--color-info), var(--color-info-light)) !important;
}

/* Transparent Backgrounds */
.has-background-transparent {
  background-color: transparent !important;
}
.has-background-semi-transparent {
  background-color: rgba(0, 0, 0, 0.5) !important;
}
.has-background-overlay {
  background-color: var(--modal-overlay) !important;
}

/* Color Scheme Utilities */
.color-scheme-light {
  color-scheme: light;
}

.color-scheme-dark {
  color-scheme: dark;
}

.color-scheme-auto {
  color-scheme: light dark;
}

/* Print Color Utilities */
@media print {
  .has-text-black-print {
    color: #000000 !important;
  }
  .has-background-white-print {
    background-color: #ffffff !important;
  }
  .has-background-transparent-print {
    background-color: transparent !important;
  }
}

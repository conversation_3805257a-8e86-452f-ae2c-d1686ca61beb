import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { GroupService } from '../services/groupService';
import type { 
  GroupWithMembers, 
  CreateGroupData, 
  UpdateGroupData, 
  InviteUserData, 
  UpdateMemberRoleData,
  GroupInvitation,
  UserRole
} from '../types/group';
import { getPermissions } from '../types/group';

export const useGroupsStore = defineStore('groups', () => {
  // State
  const groups = ref<GroupWithMembers[]>([]);
  const currentGroup = ref<GroupWithMembers | null>(null);
  const groupInvitations = ref<GroupInvitation[]>([]);
  const loadedInvitationsGroupId = ref<string | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Getters
  const groupsCount = computed(() => groups.value.length);
  
  const currentUserRole = computed(() => {
    if (!currentGroup.value) return null;
    // This would need to be connected to the auth store to get current user ID
    // For now, we'll return null and handle this in components
    return null;
  });

  const currentUserPermissions = computed(() => {
    const role = currentUserRole.value;
    return role ? getPermissions(role as UserRole) : null;
  });

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value;
  };

  const setError = (message: string | null) => {
    error.value = message;
  };

  const clearError = () => {
    error.value = null;
  };

  // Load user's groups
  const loadGroups = async () => {
    try {
      setLoading(true);
      clearError();
      
      // Check if user is authenticated before making API call
      const { useAuthStore } = await import('./auth');
      const authStore = useAuthStore();
      
      if (!authStore.isAuthenticated || !authStore.token) {
        console.log('User not authenticated, skipping groups load');
        groups.value = [];
        return;
      }
      
      const userGroups = await GroupService.getUserGroups();
      groups.value = userGroups;
    } catch (err: any) {
      console.error('Error loading groups:', err);
      setError(err.message || 'Failed to load groups');
      
      // If it's an authentication error, clear the groups
      if (err.message?.includes('401') || err.message?.includes('Unauthorized')) {
        groups.value = [];
        console.log('Authentication error, cleared groups');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Load specific group
  const loadGroup = async (id: string) => {
    try {
      setLoading(true);
      clearError();
      
      const group = await GroupService.getGroup(id);
      currentGroup.value = group;
      
      // Update the group in the groups list if it exists
      const index = groups.value.findIndex(g => g.id === id);
      if (index !== -1) {
        groups.value[index] = group;
      }
      
      return group;
    } catch (err: any) {
      console.error('Error loading group:', err);
      setError(err.message || 'Failed to load group');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Create new group
  const createGroup = async (groupData: CreateGroupData) => {
    try {
      setLoading(true);
      clearError();
      
      const newGroup = await GroupService.createGroup(groupData);
      groups.value.unshift(newGroup);
      currentGroup.value = newGroup;
      
      return newGroup;
    } catch (err: any) {
      console.error('Error creating group:', err);
      setError(err.message || 'Failed to create group');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update group
  const updateGroup = async (id: string, updateData: UpdateGroupData) => {
    try {
      setLoading(true);
      clearError();
      
      const updatedGroup = await GroupService.updateGroup(id, updateData);
      
      // Update in groups list
      const index = groups.value.findIndex(g => g.id === id);
      if (index !== -1) {
        groups.value[index] = updatedGroup;
      }
      
      // Update current group if it's the same
      if (currentGroup.value?.id === id) {
        currentGroup.value = updatedGroup;
      }
      
      return updatedGroup;
    } catch (err: any) {
      console.error('Error updating group:', err);
      setError(err.message || 'Failed to update group');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete group
  const deleteGroup = async (id: string) => {
    try {
      setLoading(true);
      clearError();
      
      await GroupService.deleteGroup(id);
      
      // Remove from groups list
      groups.value = groups.value.filter(g => g.id !== id);
      
      // Clear current group if it's the deleted one
      if (currentGroup.value?.id === id) {
        currentGroup.value = null;
      }
    } catch (err: any) {
      console.error('Error deleting group:', err);
      setError(err.message || 'Failed to delete group');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Invite user to group
  const inviteUser = async (groupId: string, inviteData: InviteUserData) => {
    try {
      setLoading(true);
      clearError();
      
      await GroupService.inviteUser(groupId, inviteData);
      
      // Optionally reload group invitations
      if (currentGroup.value?.id === groupId) {
        await loadGroupInvitations(groupId);
      }
    } catch (err: any) {
      console.error('Error inviting user:', err);
      setError(err.message || 'Failed to invite user');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Accept invitation
  const acceptInvitation = async (token: string) => {
    try {
      setLoading(true);
      clearError();
      
      const group = await GroupService.acceptInvitation(token);
      
      // Add to groups list if not already there
      const existingIndex = groups.value.findIndex(g => g.id === group.id);
      if (existingIndex === -1) {
        groups.value.unshift(group);
      } else {
        groups.value[existingIndex] = group;
      }
      
      currentGroup.value = group;
      return group;
    } catch (err: any) {
      console.error('Error accepting invitation:', err);
      setError(err.message || 'Failed to accept invitation');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Remove member from group
  const removeMember = async (groupId: string, memberId: string) => {
    try {
      setLoading(true);
      clearError();
      
      await GroupService.removeMember(groupId, memberId);
      
      // Reload the group to get updated member list
      await loadGroup(groupId);
    } catch (err: any) {
      console.error('Error removing member:', err);
      setError(err.message || 'Failed to remove member');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update member role
  const updateMemberRole = async (groupId: string, memberId: string, roleData: UpdateMemberRoleData) => {
    try {
      setLoading(true);
      clearError();
      
      await GroupService.updateMemberRole(groupId, memberId, roleData);
      
      // Reload the group to get updated member list
      await loadGroup(groupId);
    } catch (err: any) {
      console.error('Error updating member role:', err);
      setError(err.message || 'Failed to update member role');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Load group invitations
  const loadGroupInvitations = async (groupId: string, force: boolean = false) => {
    // Don't reload if we already have invitations for this group and not currently loading
    if (!force && loadedInvitationsGroupId.value === groupId && !loading.value) {
      return groupInvitations.value;
    }

    try {
      setLoading(true);
      clearError();
      
      const invitations = await GroupService.getGroupInvitations(groupId);
      groupInvitations.value = invitations;
      loadedInvitationsGroupId.value = groupId;
      
      return invitations;
    } catch (err: any) {
      console.error('Error loading group invitations:', err);
      setError(err.message || 'Failed to load invitations');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Helper methods
  const getUserRole = (groupId: string, userId: string): UserRole | null => {
    const group = groups.value.find(g => g.id === groupId);
    if (!group) return null;
    
    const member = group.members.find(m => m.userId === userId);
    return member ? member.role as UserRole : null;
  };

  const isGroupOwner = (groupId: string, userId: string): boolean => {
    const group = groups.value.find(g => g.id === groupId);
    return group ? group.ownerId === userId : false;
  };

  const isGroupMember = (groupId: string, userId: string): boolean => {
    const group = groups.value.find(g => g.id === groupId);
    return group ? group.members.some(m => m.userId === userId) : false;
  };

  // Reset store
  const reset = () => {
    groups.value = [];
    currentGroup.value = null;
    groupInvitations.value = [];
    loadedInvitationsGroupId.value = null;
    loading.value = false;
    error.value = null;
  };

  return {
    // State
    groups,
    currentGroup,
    groupInvitations,
    loading,
    error,
    
    // Getters
    groupsCount,
    currentUserRole,
    currentUserPermissions,
    
    // Actions
    loadGroups,
    loadGroup,
    createGroup,
    updateGroup,
    deleteGroup,
    inviteUser,
    acceptInvitation,
    removeMember,
    updateMemberRole,
    loadGroupInvitations,
    
    // Helper methods
    getUserRole,
    isGroupOwner,
    isGroupMember,
    
    // Utility
    setError,
    clearError,
    reset
  };
});
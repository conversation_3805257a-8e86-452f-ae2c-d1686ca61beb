import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import CardDetailsModal from '../CardDetailsModal.vue'
import type { KanbanCard, KanbanAssignee } from '../../../types/kanban'

describe('CardDetailsModal', () => {
  let wrapper: any
  
  const mockCard: KanbanCard = {
    id: 'card-1',
    title: 'Test Card',
    description: 'Test description',
    position: 0,
    columnId: 'column-1',
    labels: [
      { id: 'label-1', name: 'Bug', color: '#ff3860' }
    ],
    assignees: [
      { id: 'user-1', name: '<PERSON>', email: '<EMAIL>' }
    ],
    priority: 'high',
    checklist: [
      { id: 'check-1', text: 'Test item', completed: false, createdAt: '2023-01-01T00:00:00Z' }
    ],
    attachments: [],
    comments: [],
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  }

  const mockBoardMembers: <PERSON><PERSON><PERSON><PERSON>signee[] = [
    { id: 'user-1', name: '<PERSON>', email: '<EMAIL>' },
    { id: 'user-2', name: '<PERSON> <PERSON>', email: '<EMAIL>' }
  ]

  beforeEach(() => {
    wrapper = mount(CardDetailsModal, {
      props: {
        card: mockCard,
        columnTitle: 'To Do',
        boardMembers: mockBoardMembers
      }
    })
  })

  it('renders card details correctly', () => {
    expect(wrapper.find('.modal-card-title').text()).toContain('Test Card')
    expect(wrapper.find('.column-name').text()).toBe('in To Do')
    expect(wrapper.find('.description-display').text()).toBe('Test description')
  })

  it('displays card labels', () => {
    const labels = wrapper.findAll('.label-tag')
    expect(labels).toHaveLength(1)
    expect(labels[0].text()).toBe('Bug')
  })

  it('displays assignees', () => {
    const assignees = wrapper.findAll('.assignee-item')
    expect(assignees).toHaveLength(1)
    expect(assignees[0].find('.assignee-name').text()).toBe('John Doe')
  })

  it('displays checklist items', () => {
    const checklistItems = wrapper.findAll('.checklist-item')
    expect(checklistItems).toHaveLength(1)
    expect(checklistItems[0].find('.checklist-text').text()).toBe('Test item')
  })

  it('shows priority selector', () => {
    const prioritySelect = wrapper.find('select')
    expect(prioritySelect.element.value).toBe('high')
  })

  it('emits close event when close button is clicked', async () => {
    await wrapper.find('.delete').trigger('click')
    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('can add new checklist item', async () => {
    const buttons = wrapper.findAll('.button')
    const addButton = buttons.find((btn: any) => btn.text().includes('Add Item'))
    await addButton.trigger('click')
    expect(wrapper.find('.checklist-editor').exists()).toBe(true)
  })
})
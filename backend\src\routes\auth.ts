import { Router } from 'express';
import { AuthController } from '../controllers/AuthController';
import { UserRepository } from '../repositories/UserRepository';
import { 
  validateRegistration, 
  validateLogin, 
  validatePasswordReset, 
  validatePasswordResetConfirm,
  validateRefreshToken,
  validateGoogleAuth
} from '../middleware/validation';
import { 
  authRateLimit, 
  passwordResetRateLimit 
} from '../middleware/rateLimiting';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// Public routes
router.post('/register', 
  authRateLimit,
  validateRegistration, 
  AuthController.register
);

router.post('/login', 
  authRateLimit,
  validateLogin, 
  AuthController.login
);

router.post('/refresh-token', 
  validateRefreshToken,
  AuthController.refreshToken
);

router.post('/forgot-password', 
  passwordResetRateLimit,
  validatePasswordReset, 
  AuthController.requestPasswordReset
);

router.post('/reset-password', 
  validatePasswordResetConfirm,
  AuthController.resetPassword
);

router.get('/verify-email/:token', 
  AuthController.verifyEmail
);

// Google OAuth routes
router.post('/google', 
  authRateLimit,
  validateGoogleAuth,
  AuthController.googleAuth
);

router.get('/google/url', 
  AuthController.googleAuthUrl
);

router.get('/google/callback', 
  AuthController.googleCallback
);

// Protected routes (require authentication)
router.post('/resend-verification', 
  authenticateToken,
  AuthController.resendVerificationEmail as any
);

router.get('/profile', 
  authenticateToken,
  AuthController.getProfile as any
);

// Debug endpoint to check admin status
router.get('/debug/admin-status', authenticateToken, async (req, res): Promise<void> => {
  try {
    const user = await UserRepository.findById(req.user!.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    const preferences = typeof user.preferences === 'string' 
      ? JSON.parse(user.preferences) 
      : user.preferences;
    
    const isAdmin = user.admin === true || 
                   preferences?.isAdmin === true || 
                   user.email === process.env.ADMIN_EMAIL ||
                   user.email === '<EMAIL>';

    res.json({
      email: user.email,
      adminField: user.admin,
      preferencesAdmin: preferences?.isAdmin,
      envAdminEmail: process.env.ADMIN_EMAIL,
      isAdmin,
      preferences
    });
  } catch (error) {
    console.error('Debug admin status error:', error);
    res.status(500).json({ error: 'Failed to check admin status' });
  }
});

router.post('/logout', 
  authenticateToken,
  AuthController.logout as any
);

export default router;
/**
 * Performance metrics collection utilities for regression testing
 */

export interface InitializationMetrics {
  startTime: number
  endTime: number
  duration: number
  storeInitTime: number
  domReadyTime: number
}

export interface CoreWebVitalsMetrics {
  fcp: number // First Contentful Paint
  lcp: number // Largest Contentful Paint
  tti: number // Time to Interactive
  cls: number // Cumulative Layout Shift
  fid: number // First Input Delay
}

export interface PerformanceMetrics {
  initialization: InitializationMetrics
  coreWebVitals: CoreWebVitalsMetrics
  timestamp: number
  userAgent: string
}

/**
 * Core Web Vitals measurement utility
 */
export class CoreWebVitalsMeasurer {
  private static instance: CoreWebVitalsMeasurer
  private metrics: Partial<CoreWebVitalsMetrics> = {}

  static getInstance(): CoreWebVitalsMeasurer {
    if (!CoreWebVitalsMeasurer.instance) {
      CoreWebVitalsMeasurer.instance = new CoreWebVitalsMeasurer()
    }
    return CoreWebVitalsMeasurer.instance
  }

  /**
   * Measure First Contentful Paint
   */
  measureFCP(): number {
    const paintEntries = performance.getEntriesByType('paint')
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    const fcp = fcpEntry ? fcpEntry.startTime : 0
    this.metrics.fcp = fcp
    return fcp
  }

  /**
   * Measure Largest Contentful Paint
   */
  measureLCP(): number {
    const lcpEntries = performance.getEntriesByType('largest-contentful-paint') as any[]
    const lcp = lcpEntries.length > 0 ? lcpEntries[lcpEntries.length - 1].startTime : 0
    this.metrics.lcp = lcp
    return lcp
  }

  /**
   * Measure Time to Interactive
   */
  measureTTI(): number {
    const navigation = performance.getEntriesByType('navigation')[0] as any
    const tti = navigation ? navigation.domInteractive - navigation.fetchStart : 0
    this.metrics.tti = tti
    return tti
  }

  /**
   * Measure Cumulative Layout Shift
   */
  measureCLS(): number {
    const clsEntries = performance.getEntriesByType('layout-shift') as any[]
    let cls = 0
    
    for (const entry of clsEntries) {
      if (!entry.hadRecentInput) {
        cls += entry.value
      }
    }
    
    this.metrics.cls = cls
    return cls
  }

  /**
   * Measure First Input Delay
   */
  measureFID(): number {
    const fidEntries = performance.getEntriesByType('first-input') as any[]
    const fid = fidEntries.length > 0 ? 
      fidEntries[0].processingStart - fidEntries[0].startTime : 0
    this.metrics.fid = fid
    return fid
  }

  /**
   * Get all Core Web Vitals metrics
   */
  getAllMetrics(): CoreWebVitalsMetrics {
    return {
      fcp: this.measureFCP(),
      lcp: this.measureLCP(),
      tti: this.measureTTI(),
      cls: this.measureCLS(),
      fid: this.measureFID()
    }
  }
}

/**
 * Initialization time measurement utility
 */
export class InitializationMeasurer {
  private static startTime: number = 0
  private static storeInitStart: number = 0
  private static domReadyStart: number = 0

  /**
   * Mark the start of app initialization
   */
  static markStart(): void {
    this.startTime = performance.now()
    performance.mark('app-init-start')
  }

  /**
   * Mark the start of store initialization
   */
  static markStoreInitStart(): void {
    this.storeInitStart = performance.now()
    performance.mark('store-init-start')
  }

  /**
   * Mark the completion of store initialization
   */
  static markStoreInitComplete(): void {
    performance.mark('store-init-complete')
  }

  /**
   * Mark DOM ready
   */
  static markDOMReady(): void {
    this.domReadyStart = performance.now()
    performance.mark('dom-ready')
  }

  /**
   * Mark the end of app initialization
   */
  static markEnd(): void {
    performance.mark('app-init-end')
  }

  /**
   * Get initialization metrics
   */
  static getMetrics(): InitializationMetrics {
    const endTime = performance.now()
    const duration = endTime - this.startTime

    // Calculate store initialization time
    let storeInitTime = 0
    try {
      performance.measure('store-init-duration', 'store-init-start', 'store-init-complete')
      const storeInitMeasure = performance.getEntriesByName('store-init-duration')[0]
      storeInitTime = storeInitMeasure ? storeInitMeasure.duration : 0
    } catch (error) {
      // Fallback calculation
      storeInitTime = this.storeInitStart > 0 ? endTime - this.storeInitStart : 0
    }

    // Calculate DOM ready time
    const domReadyTime = this.domReadyStart > 0 ? endTime - this.domReadyStart : 0

    return {
      startTime: this.startTime,
      endTime,
      duration,
      storeInitTime,
      domReadyTime
    }
  }
}

/**
 * Performance metrics collector
 */
export class PerformanceMetricsCollector {
  private static instance: PerformanceMetricsCollector

  static getInstance(): PerformanceMetricsCollector {
    if (!PerformanceMetricsCollector.instance) {
      PerformanceMetricsCollector.instance = new PerformanceMetricsCollector()
    }
    return PerformanceMetricsCollector.instance
  }

  /**
   * Collect all performance metrics
   */
  collectMetrics(): PerformanceMetrics {
    const coreWebVitals = CoreWebVitalsMeasurer.getInstance()
    const initialization = InitializationMeasurer.getMetrics()

    return {
      initialization,
      coreWebVitals: coreWebVitals.getAllMetrics(),
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    }
  }

  /**
   * Export metrics to localStorage for testing
   */
  exportToLocalStorage(metrics: PerformanceMetrics): void {
    try {
      localStorage.setItem('performance-metrics', JSON.stringify(metrics))
    } catch (error) {
      console.warn('Failed to save performance metrics to localStorage:', error)
    }
  }

  /**
   * Load metrics from localStorage
   */
  loadFromLocalStorage(): PerformanceMetrics | null {
    try {
      const stored = localStorage.getItem('performance-metrics')
      return stored ? JSON.parse(stored) : null
    } catch (error) {
      console.warn('Failed to load performance metrics from localStorage:', error)
      return null
    }
  }
}
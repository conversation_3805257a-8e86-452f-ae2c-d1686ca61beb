/* Button Component Styles */

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2) var(--spacing-4);
  border: 1px solid var(--button-border);
  border-radius: var(--radius);
  background: var(--button-background);
  color: var(--button-text);
  text-decoration: none;
  cursor: pointer;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-none);
  transition: var(--transition-fast);
  position: relative;
  vertical-align: top;
  white-space: nowrap;
  user-select: none;
}

.button:hover {
  background: var(--button-hover-background);
  border-color: var(--button-hover-border);
  transform: translateY(-1px);
}

.button:active {
  transform: translateY(0);
}

.button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* <PERSON><PERSON> Variants */
.button.is-primary {
  background: var(--color-primary) !important;
  color: var(--color-text) !important;
  border-color: var(--color-primary) !important;
}

.button.is-primary:hover {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.button.is-success {
  background: var(--color-success);
  color: white;
  border-color: var(--color-success);
}

.button.is-success:hover {
  background: var(--color-success-dark);
  border-color: var(--color-success-dark);
}

.button.is-danger {
  background: var(--color-danger);
  color: white;
  border-color: var(--color-danger);
}

.button.is-danger:hover {
  background: var(--color-danger-dark);
  border-color: var(--color-danger-dark);
}

.button.is-warning {
  background: var(--color-warning);
  color: rgba(0, 0, 0, 0.7);
  border-color: var(--color-warning);
}

.button.is-warning:hover {
  background: var(--color-warning-dark);
  border-color: var(--color-warning-dark);
}

.button.is-info {
  background: var(--color-info);
  color: white;
  border-color: var(--color-info);
}

.button.is-info:hover {
  background: var(--color-info-dark);
  border-color: var(--color-info-dark);
}

.button.is-link {
  background: var(--color-link);
  color: white;
  border-color: var(--color-link);
}

.button.is-link:hover {
  background: var(--color-link-dark);
  border-color: var(--color-link-dark);
}

/* Button Sizes */
.button.is-small {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.button.is-medium {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-lg);
}

.button.is-large {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-xl);
}

/* Button Modifiers */
.button.is-fullwidth {
  display: flex;
  width: 100%;
}

.button.is-loading {
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

.button.is-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.button.is-ghost {
  background: transparent;
  border-color: transparent;
  color: var(--color-text);
}

.button.is-ghost:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-border);
}

.button.is-outlined {
  background: transparent;
  color: var(--color-primary);
}

.button.is-outlined.is-primary {
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.button.is-outlined.is-primary:hover {
  background: var(--color-primary);
  color: white;
}

.button.is-outlined.is-success {
  color: var(--color-success);
  border-color: var(--color-success);
}

.button.is-outlined.is-success:hover {
  background: var(--color-success);
  color: white;
}

.button.is-outlined.is-danger {
  color: var(--color-danger);
  border-color: var(--color-danger);
}

.button.is-outlined.is-danger:hover {
  background: var(--color-danger);
  color: white;
}

.button.is-outlined.is-warning {
  color: var(--color-warning-dark);
  border-color: var(--color-warning);
}

.button.is-outlined.is-warning:hover {
  background: var(--color-warning);
  color: rgba(0, 0, 0, 0.7);
}

.button.is-outlined.is-info {
  color: var(--color-info);
  border-color: var(--color-info);
}

.button.is-outlined.is-info:hover {
  background: var(--color-info);
  color: white;
}

/* Button Groups */
.buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  align-items: center;
}

.buttons.has-addons {
  gap: 0;
}

.buttons.has-addons .button {
  border-radius: 0;
  margin-right: -1px;
}

.buttons.has-addons .button:first-child {
  border-top-left-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}

.buttons.has-addons .button:last-child {
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
  margin-right: 0;
}

.buttons.has-addons .button:hover,
.buttons.has-addons .button:focus {
  z-index: 2;
}

.buttons.has-addons .button:active {
  z-index: 3;
}

.buttons.is-centered {
  justify-content: center;
}

.buttons.is-right {
  justify-content: flex-end;
}

/* Animation for loading state */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive button adjustments */
@media screen and (max-width: 768px) {
  .button.is-responsive {
    font-size: var(--font-size-sm);
    padding: var(--spacing-2) var(--spacing-3);
  }

  .buttons {
    gap: var(--spacing-1);
  }
}

<template>
  <div class="theme-preview" :class="{
    'is-selected': selected,
    'is-loading': loading,
    'is-dark': theme.isDark
  }" @click="handleSelect" @keydown="handleKeydown" :tabindex="tabindex" role="radio" :aria-checked="selected"
    :aria-label="`Select ${theme.displayName} theme`" :aria-describedby="`theme-desc-${theme.name}`">
    <!-- Loading overlay -->
    <div class="loading-overlay" v-if="loading">
      <span class="icon">
        <i class="fas fa-spinner fa-spin"></i>
      </span>
    </div>

    <!-- Theme preview header -->
    <div class="theme-header">
      <div class="theme-colors">
        <div class="color-swatch primary" :style="{ backgroundColor: theme.preview.primary }"
          :title="`Primary color: ${theme.preview.primary}`"></div>
        <div class="color-swatch accent" :style="{ backgroundColor: theme.preview.accent }"
          :title="`Accent color: ${theme.preview.accent}`"></div>
      </div>

      <div class="selection-indicator" v-if="selected">
        <span class="icon">
          <i class="fas fa-check"></i>
        </span>
      </div>
    </div>

    <!-- Theme preview content -->
    <div class="theme-content" :style="{
      backgroundColor: theme.preview.background,
      color: theme.preview.text
    }">
      <!-- Mock UI elements -->
      <div class="mock-navbar" :style="{ backgroundColor: theme.preview.surface }">
        <div class="mock-nav-item active" :style="{ backgroundColor: theme.preview.primary }"></div>
        <div class="mock-nav-item"></div>
        <div class="mock-nav-item"></div>
      </div>

      <div class="mock-content">
        <div class="mock-card" :style="{ backgroundColor: theme.preview.surface }">
          <div class="mock-card-header" :style="{ borderBottomColor: theme.preview.accent }"></div>
          <div class="mock-card-body">
            <div class="mock-text-line primary" :style="{ backgroundColor: theme.preview.text }"></div>
            <div class="mock-text-line secondary" :style="{ backgroundColor: theme.preview.text, opacity: 0.6 }"></div>
          </div>
        </div>

        <div class="mock-button" :style="{ backgroundColor: theme.preview.primary }"></div>
      </div>
    </div>

    <!-- Theme info -->
    <div class="theme-info">
      <h4 class="theme-name">{{ theme.displayName }}</h4>
      <p class="theme-description" :id="`theme-desc-${theme.name}`">
        {{ theme.description }}
      </p>
      <div class="theme-category">
        <span class="tag" :class="getCategoryClass(theme.category)">
          {{ getCategoryLabel(theme.category) }}
        </span>
        <span class="tag" v-if="theme.isDark">
          <i class="fas fa-moon"></i>
          Dark
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { BulmaswatchTheme } from '@/types/theme'

interface Props {
  theme: BulmaswatchTheme
  selected?: boolean
  loading?: boolean
  tabindex?: number
}

interface Emits {
  (e: 'select', themeName: string): void
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  loading: false,
  tabindex: 0
})

const emit = defineEmits<Emits>()

const handleSelect = () => {
  if (!props.loading) {
    emit('select', props.theme.name)
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault()
    handleSelect()
  }
}

const getCategoryClass = (category: string) => {
  switch (category) {
    case 'light':
      return 'is-light'
    case 'dark':
      return 'is-dark'
    case 'colorful':
      return 'is-info'
    default:
      return ''
  }
}

const getCategoryLabel = (category: string) => {
  switch (category) {
    case 'light':
      return 'Light'
    case 'dark':
      return 'Dark'
    case 'colorful':
      return 'Colorful'
    default:
      return category
  }
}
</script>

<style scoped>
.theme-preview {
  position: relative;
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--card-background);
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

.theme-preview:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.theme-preview:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-alpha);
}

.theme-preview.is-selected {
  border-color: #3273dc;
  box-shadow: 0 0 0 3px rgba(50, 115, 220, 0.2);
}

.theme-preview.is-loading {
  pointer-events: none;
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(2px);
}

.loading-overlay .icon {
  font-size: 1.5rem;
  color: #3273dc;
}

/* Theme header */
.theme-header {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 5;
}

.theme-colors {
  display: flex;
  gap: 4px;
}

.color-swatch {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.selection-indicator {
  background: #3273dc;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Theme content preview */
.theme-content {
  flex: 1;
  padding: 32px 12px 12px;
  min-height: 160px;
  transition: all 0.3s ease;
}

.mock-navbar {
  height: 20px;
  border-radius: 4px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  padding: 0 6px;
  gap: 4px;
}

.mock-nav-item {
  width: 24px;
  height: 8px;
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.1);
}

.mock-nav-item.active {
  background: currentColor;
}

.mock-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mock-card {
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mock-card-header {
  height: 6px;
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.1);
  margin-bottom: 6px;
  border-bottom: 1px solid;
}

.mock-card-body {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mock-text-line {
  height: 4px;
  border-radius: 2px;
}

.mock-text-line.primary {
  width: 80%;
}

.mock-text-line.secondary {
  width: 60%;
}

.mock-button {
  height: 16px;
  border-radius: 4px;
  width: 50%;
  align-self: flex-start;
}

/* Theme info */
.theme-info {
  padding: 12px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.theme-name {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #363636;
}

.theme-description {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.theme-category {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.tag {
  font-size: 0.625rem;
  padding: 2px 6px;
  border-radius: 4px;
  background: #e5e5e5;
  color: #363636;
  display: flex;
  align-items: center;
  gap: 2px;
}

.tag.is-light {
  background: #fff3cd;
  color: #856404;
}

.tag.is-dark {
  background: #d1ecf1;
  color: #0c5460;
}

.tag.is-info {
  background: #cce7ff;
  color: #004085;
}

/* Dark mode styles */
:global(.dark) .theme-preview {
  background: #374151;
  border-color: #4b5563;
}

:global(.dark) .theme-preview:hover {
  border-color: #6366f1;
}

:global(.dark) .theme-preview:focus,
:global(.dark) .theme-preview.is-selected {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

:global(.dark) .loading-overlay {
  background: rgba(55, 65, 81, 0.8);
}

:global(.dark) .loading-overlay .icon {
  color: #6366f1;
}

:global(.dark) .selection-indicator {
  background: #6366f1;
}

:global(.dark) .theme-info {
  background: #4b5563;
  border-top-color: #6b7280;
}

:global(.dark) .theme-name {
  color: #e5e5e5;
}

:global(.dark) .theme-description {
  color: #9ca3af;
}

:global(.dark) .tag {
  background: #6b7280;
  color: #e5e5e5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-preview {
    min-height: 240px;
  }

  .theme-content {
    padding: 28px 8px 8px;
  }

  .theme-info {
    padding: 8px;
  }
}

/* Animation for theme switching */
.theme-preview.is-loading .theme-content {
  opacity: 0.6;
  transform: scale(0.98);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .theme-preview {
    transition: none;
  }

  .theme-preview:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .theme-preview {
    border-width: 3px;
  }

  .color-swatch {
    border-width: 3px;
  }
}
</style>
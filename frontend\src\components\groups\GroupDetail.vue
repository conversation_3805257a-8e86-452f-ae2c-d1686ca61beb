<template>
  <div class="group-detail">
    <!-- Loading state -->
    <div v-if="loading" class="loading-state">
      <div class="loader"></div>
      <p>Loading group...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="error-state">
      <div class="error-content">
        <span class="icon">
          <i class="fas fa-exclamation-circle"></i>
        </span>
        <p>{{ error }}</p>
        <div class="error-actions">
          <router-link to="/dashboard" class="button is-primary">
            <span class="icon">
              <i class="fas fa-home"></i>
            </span>
            <span>Back to Dashboard</span>
          </router-link>
          <button class="button is-light" @click="clearError">Dismiss</button>
        </div>
      </div>
    </div>

    <!-- Group not found -->
    <div v-else-if="!group" class="empty-state">
      <div class="empty-content">
        <span class="icon">
          <i class="fas fa-exclamation-triangle"></i>
        </span>
        <h3>Group not found</h3>
        <p>The group you're looking for doesn't exist or you don't have access to it.</p>
        <div class="empty-actions">
          <router-link to="/dashboard" class="button is-primary">
            <span class="icon">
              <i class="fas fa-home"></i>
            </span>
            <span>Back to Dashboard</span>
          </router-link>
          <router-link to="/groups" class="button is-light">
            Back to Groups
          </router-link>
        </div>
      </div>
    </div>

    <!-- Group content -->
    <div v-else class="group-content">
      <!-- Header Card -->
      <div class="group-header">
        <div class="header-main">
          <div class="group-info">
            <h1 class="group-title">{{ group.name }}</h1>
            <p v-if="group.description" class="group-description">
              {{ group.description }}
            </p>
            <div class="group-meta">
              <span class="meta-item">
                <span class="icon">
                  <i class="fas fa-users"></i>
                </span>
                <span>{{ group.memberCount }} member{{ group.memberCount !== 1 ? 's' : '' }}</span>
              </span>
              <span class="meta-item role-badge" :class="getRoleColor(safeUserRole)">
                {{ getRoleDisplayName(safeUserRole) }}
              </span>
            </div>
          </div>
          <div class="header-actions">
            <router-link 
              to="/dashboard"
              class="button is-light home-button"
              title="Back to Dashboard"
            >
              <span class="icon">
                <i class="fas fa-home"></i>
              </span>
              <span>Home</span>
            </router-link>
            <button 
              v-if="canInvite"
              class="button is-primary"
              @click="showInviteModal = true"
            >
              <span class="icon">
                <i class="fas fa-user-plus"></i>
              </span>
              <span>Invite Member</span>
            </button>
            <button 
              v-if="canEditSettings"
              class="button is-light"
              @click="showSettingsModal = true"
            >
              <span class="icon">
                <i class="fas fa-cog"></i>
              </span>
              <span>Settings</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Navigation Tabs -->
      <div class="group-tabs">
        <div class="tab-list">
          <button 
            class="tab-button"
            :class="{ 'is-active': activeTab === 'members' }"
            @click="activeTab = 'members'"
          >
            <span class="icon">
              <i class="fas fa-users"></i>
            </span>
            <span>Members</span>
          </button>
          <button 
            class="tab-button"
            :class="{ 'is-active': activeTab === 'notes' }"
            @click="activeTab = 'notes'"
          >
            <span class="icon">
              <i class="fas fa-sticky-note"></i>
            </span>
            <span>Notes</span>
          </button>
          <button 
            v-if="canEditSettings"
            class="tab-button"
            :class="{ 'is-active': activeTab === 'invitations' }"
            @click="activeTab = 'invitations'"
          >
            <span class="icon">
              <i class="fas fa-envelope"></i>
            </span>
            <span>Invitations</span>
          </button>
        </div>
      </div>

      <!-- Tab Content -->
      <div class="tab-content">
        <!-- Members Tab -->
        <div v-if="activeTab === 'members'">
          <GroupMembers 
            :group="group"
            :user-role="safeUserRole"
            @member-removed="onMemberRemoved"
            @role-updated="onRoleUpdated"
          />
        </div>

        <!-- Notes Tab -->
        <div v-else-if="activeTab === 'notes'">
          <GroupNotes 
            :group="group"
            :user-role="safeUserRole"
          />
        </div>

        <!-- Invitations Tab -->
        <div v-else-if="activeTab === 'invitations' && canEditSettings">
          <GroupInvitations 
            :group="group"
            @invitation-sent="onInvitationSent"
          />
        </div>
      </div>
    </div>

    <!-- Modals -->
    <InviteMemberModal 
      v-if="showInviteModal && group"
      :group="group"
      @close="showInviteModal = false"
      @invited="onMemberInvited"
    />

    <GroupSettingsModal 
      v-if="showSettingsModal && group"
      :group="group"
      @close="showSettingsModal = false"
      @updated="onGroupUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useGroupsStore } from '../../stores/groups';
import { useAuthStore } from '../../stores/auth';
import { getRoleDisplayName, getRoleColor, getPermissions } from '../../types/group';
import type { GroupWithMembers, UserRole } from '../../types/group';
import GroupMembers from './GroupMembers.vue';
import GroupNotes from './GroupNotes.vue';
import GroupInvitations from './GroupInvitations.vue';
import InviteMemberModal from './InviteMemberModal.vue';
import GroupSettingsModal from './GroupSettingsModal.vue';

const route = useRoute();
const groupsStore = useGroupsStore();
const authStore = useAuthStore();

const activeTab = ref('members');
const showInviteModal = ref(false);
const showSettingsModal = ref(false);

// Computed properties
const group = computed(() => groupsStore.currentGroup);
const loading = computed(() => groupsStore.loading);
const error = computed(() => groupsStore.error);

const userRole = computed((): UserRole | null => {
  if (!group.value || !authStore.user) return null;
  return groupsStore.getUserRole(group.value.id, authStore.user.id) as UserRole;
});

const safeUserRole = computed<UserRole>(() => userRole.value ?? 'viewer')

const permissions = computed(() => {
  return safeUserRole.value ? getPermissions(safeUserRole.value) : null;
});

const canInvite = computed(() => permissions.value?.canInvite || false);
const canEditSettings = computed(() => permissions.value?.canEditSettings || false);

// Methods
const clearError = () => {
  groupsStore.clearError();
};

const onMemberRemoved = () => {
  // Refresh group data
  if (group.value) {
    groupsStore.loadGroup(group.value.id);
  }
};

const onRoleUpdated = () => {
  // Refresh group data
  if (group.value) {
    groupsStore.loadGroup(group.value.id);
  }
};

const onMemberInvited = () => {
  showInviteModal.value = false;
  // Optionally refresh invitations if on that tab
  if (activeTab.value === 'invitations') {
    // This would trigger a refresh in the GroupInvitations component
  }
};

const onInvitationSent = () => {
  // Handle invitation sent event
};

const onGroupUpdated = (updatedGroup: GroupWithMembers) => {
  showSettingsModal.value = false;
  // Group is automatically updated in the store
};

// Watch for route changes
watch(() => route.params.id, async (newId) => {
  if (newId && typeof newId === 'string') {
    try {
      await groupsStore.loadGroup(newId);
    } catch (error) {
      console.error('Failed to load group:', error);
    }
  }
}, { immediate: true });

// Lifecycle
onMounted(async () => {
  const groupId = route.params.id as string;
  if (groupId) {
    try {
      await groupsStore.loadGroup(groupId);
    } catch (error) {
      console.error('Failed to load group:', error);
    }
  }
});
</script>

<style scoped>
.group-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

/* Loading state */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loader {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: #6c757d;
  margin: 0;
}

/* Error state */
.error-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.error-content .icon {
  color: #dc3545;
  font-size: 2rem;
}

.error-content p {
  color: #6c757d;
  margin: 0;
}

.error-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* Empty state */
.empty-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 3rem;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.empty-content .icon {
  color: #6c757d;
  font-size: 3rem;
}

.empty-content h3 {
  color: #363636;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.empty-content p {
  color: #6c757d;
  margin: 0;
}

.empty-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* Group content */
.group-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Header card */
.group-header {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.header-main {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 2rem;
}

.group-info {
  flex: 1;
}

.group-title {
  font-size: 2rem;
  font-weight: 700;
  color: #363636;
  margin: 0 0 0.5rem 0;
}

.group-description {
  color: #6c757d;
  font-size: 1.125rem;
  line-height: 1.4;
  margin: 0 0 1rem 0;
}

.group-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.9rem;
}

.meta-item .icon {
  color: #007bff;
}

.role-badge {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-weight: 500;
  font-size: 0.8rem;
}

.role-badge.is-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.role-badge.is-info {
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.role-badge.is-success {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

/* Header actions */
.header-actions {
  display: flex;
  gap: 0.75rem;
  flex-shrink: 0;
}

.button {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.button.is-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.button.is-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.button.is-light {
  background: #f8f9fa;
  color: #6c757d;
  border-color: #e9ecef;
}

.button.is-light:hover {
  background: #e9ecef;
  color: #495057;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

/* Home button specific styling */
.home-button {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.home-button:hover {
  background: #218838;
  border-color: #1e7e34;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

/* Navigation tabs */
.group-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-list {
  display: flex;
  border-bottom: 1px solid #e9ecef;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  color: #6c757d;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  background: #f8f9fa;
  color: #495057;
}

.tab-button.is-active {
  color: #007bff;
  border-bottom-color: #007bff;
  background: #f8f9fa;
}

/* Tab content */
.tab-content {
  background: white;
  border-radius: 0 0 8px 8px;
  min-height: 400px;
}

/* Responsive design */
@media (max-width: 768px) {
  .group-detail {
    padding: 1rem;
  }
  
  .group-header {
    padding: 1.5rem;
  }
  
  .header-main {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .header-actions {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .button {
    flex: 1;
    justify-content: center;
    min-width: 120px;
  }
  
  .home-button {
    flex: 0 0 auto;
    min-width: 100px;
  }
  
  .group-title {
    font-size: 1.5rem;
  }
  
  .group-description {
    font-size: 1rem;
  }
  
  .tab-list {
    flex-direction: column;
  }
  
  .tab-button {
    justify-content: center;
    border-bottom: none;
    border-right: 2px solid transparent;
  }
  
  .tab-button.is-active {
    border-bottom: none;
    border-right-color: #007bff;
  }
}

@media (max-width: 480px) {
  .group-detail {
    padding: 0.75rem;
  }
  
  .group-header {
    padding: 1rem;
  }
  
  .group-title {
    font-size: 1.25rem;
  }
  
  .tab-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .error-actions,
  .empty-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .error-actions .button,
  .empty-actions .button {
    width: 100%;
  }
}
</style>
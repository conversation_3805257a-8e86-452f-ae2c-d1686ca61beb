<template>
  <!-- This component handles global keyboard shortcuts -->
  <div class="keyboard-shortcuts-handler" style="display: none;">
    <!-- Invisible component that handles keyboard events -->
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

// Emits
const emit = defineEmits<{
  'open-search': []
  'create-note': []
  'toggle-sidebar': []
  'focus-editor': []
  'save-note': []
  'escape': []
}>()

// Composables
const router = useRouter()

// Keyboard shortcut handlers
const handleKeydown = (event: KeyboardEvent) => {
  const { key, ctrlKey, metaKey, shiftKey, altKey } = event
  const isModifierPressed = ctrlKey || metaKey

  // Prevent shortcuts when typing in input fields
  const target = event.target as HTMLElement
  const isInputField = target.tagName === 'INPUT' || 
                      target.tagName === 'TEXTAREA' || 
                      target.contentEditable === 'true'

  // Global shortcuts that work everywhere
  switch (true) {
    // Search: Ctrl+F / Cmd+F
    case isModifierPressed && key === 'f':
      event.preventDefault()
      emit('open-search')
      break

    // New note: Ctrl+N / Cmd+N
    case isModifierPressed && key === 'n':
      event.preventDefault()
      emit('create-note')
      break

    // Save: Ctrl+S / Cmd+S
    case isModifierPressed && key === 's':
      event.preventDefault()
      emit('save-note')
      break

    // Toggle sidebar: Ctrl+B / Cmd+B
    case isModifierPressed && key === 'b':
      event.preventDefault()
      emit('toggle-sidebar')
      break

    // Focus editor: Ctrl+E / Cmd+E
    case isModifierPressed && key === 'e' && !isInputField:
      event.preventDefault()
      emit('focus-editor')
      break

    // Navigation shortcuts
    case isModifierPressed && key === '1':
      event.preventDefault()
      router.push('/dashboard')
      break

    case isModifierPressed && key === '2':
      event.preventDefault()
      router.push('/dashboard/notes')
      break

    case isModifierPressed && key === '3':
      event.preventDefault()
      router.push('/dashboard/shared')
      break

    case isModifierPressed && key === '4':
      event.preventDefault()
      router.push('/dashboard/groups')
      break

    // Help: Ctrl+? / Cmd+?
    case isModifierPressed && shiftKey && key === '?':
      event.preventDefault()
      showKeyboardShortcuts()
      break

    // Escape key actions
    case key === 'Escape':
      handleEscape()
      break
  }
}

const handleEscape = () => {
  // Close any open modals, dropdowns, or overlays
  const activeElement = document.activeElement as HTMLElement
  
  // Remove focus from current element
  if (activeElement && activeElement.blur) {
    activeElement.blur()
  }

  // Close dropdowns
  const openDropdowns = document.querySelectorAll('.dropdown.is-active')
  openDropdowns.forEach(dropdown => {
    dropdown.classList.remove('is-active')
  })

  // Close modals
  const openModals = document.querySelectorAll('.modal.is-active')
  openModals.forEach(modal => {
    modal.classList.remove('is-active')
  })

  // Emit escape event for parent components to handle
  emit('escape' as any)
}

const showKeyboardShortcuts = () => {
  // This would open a modal or tooltip showing available shortcuts
  console.log('Keyboard shortcuts help')
  
  // For now, just log the shortcuts
  const shortcuts = [
    { keys: 'Ctrl/Cmd + F', action: 'Search notes' },
    { keys: 'Ctrl/Cmd + N', action: 'Create new note' },
    { keys: 'Ctrl/Cmd + S', action: 'Save current note' },
    { keys: 'Ctrl/Cmd + B', action: 'Toggle sidebar' },
    { keys: 'Ctrl/Cmd + E', action: 'Focus editor' },
    { keys: 'Ctrl/Cmd + 1', action: 'Go to Dashboard' },
    { keys: 'Ctrl/Cmd + 2', action: 'Go to Notes' },
    { keys: 'Ctrl/Cmd + 3', action: 'Go to Shared' },
    { keys: 'Ctrl/Cmd + 4', action: 'Go to Groups' },
    { keys: 'Ctrl/Cmd + Shift + ?', action: 'Show this help' },
    { keys: 'Escape', action: 'Close modals/dropdowns' }
  ]
  
  console.table(shortcuts)
}

// Lifecycle
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// Expose methods for parent components
defineExpose({
  showKeyboardShortcuts
})
</script>

<style scoped>
.keyboard-shortcuts-handler {
  position: absolute;
  visibility: hidden;
  pointer-events: none;
}
</style>
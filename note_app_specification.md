# Note-Taking Application Specification

## Core Features

### Multi-format Notes
Enable users to create and manage notes in four different formats:
- Rich Text
- Markdown (with live preview)
- Kanban (customizable boards with draggable cards)(Same style and functionality as Not<PERSON>'s)
- Canvas (Skip for now) (with 3 sub-types: ReactFlow for node-based diagrams, Excalidraw for hand-drawn style, and tldraw for whiteboard functionality)

### Intuitive Interface
Implement a two-panel layout:
- Left sidebar for navigation, groups, and tags, panel for note lists with clear type indicators
- Right main panel for format-specific editing
- Responsive design for seamless use across all devices, mobile first mindset

### Search and Organization
- Full-text search across all note types with highlighted results
- Tag-based organization system
- Group functionality for collaborative workspaces
- Keyboard shortcuts for efficient navigation (Ctrl+F/Cmd+F)

### Real-time Collaboration
- Shared groups with role-based permissions (admin, editor, viewer)
- Semi Real-time synchronization of edits across devices
- Visual indicators for shared notes
- User invitation system via email

### Customization and Export
- Support for light/dark themes
- Export options for all note types (PDF, Markdown, HTML)
- Templates for each note format
- User preferences synced across devices

## Requirements

### Functional Requirements

#### User Management
- User registration and authentication with email verification
- Google OAuth 2.0 integration for account creation and login
- Password reset functionality with secure token generation
- Two-factor authentication (2FA) support
- User profile management (avatar, display name, email preferences)
- Account deletion with data export option

#### Note Management
- Create, read, update, and delete (CRUD) operations for all note types
- Auto-save functionality with configurable intervals
- Version history and revision tracking
- Note templates for each format
- Bulk operations (delete, move, tag, export)
- Note archiving and restoration

#### Security Features
- End-to-end encryption for sensitive notes
- Role-based access control (RBAC) for shared content
- Audit logging for all user actions
- Content moderation and reporting system
- Data retention policies compliance
- Secure API endpoints with rate limiting

#### Collaboration Features
- Real-time collaborative editing with conflict resolution
- Comment and annotation system
- Activity feed for shared workspaces
- Offline mode with sync when reconnected
- Conflict resolution with merge strategies

### Non-Functional Requirements

#### Performance
- Initial page load time: < 2 seconds
- Search response time: < 500ms
- Real-time sync latency: < 200ms
- Support for 10,000+ notes per user
- Concurrent users: 1000+ per instance

#### Security
- Data encryption at rest and in transit
- Regular security audits and penetration testing
- GDPR and CCPA compliance
- Secure session management
- Input validation and sanitization

#### Scalability
- Horizontal scaling support
- Database sharding capabilities
- CDN integration for static assets
- Microservices architecture readiness

## Secure Note Sharing

### Access Control Levels
- **Public**: Accessible to anyone with the link
- **Unlisted**: Accessible only with direct link (no search indexing)
- **Shared**: Accessible to specific users or groups
- **Private**: Accessible only to the owner

### Permission System
- **Owner**: Full control (edit, delete, share, manage permissions)
- **Editor**: Can view and edit content, cannot delete or manage sharing
- **Commenter**: Can view and add comments, cannot edit content
- **Viewer**: Read-only access to content

### Security Measures
- Time-limited sharing links with expiration dates
- Password-protected shared notes
- IP address restrictions for sensitive content
- Watermarking for confidential documents
- Download restrictions for sensitive materials

### Audit Trail
- Complete log of all access attempts
- User activity tracking within shared notes
- Change history with user attribution
- Export of access logs for compliance

## Settings Panel

### User Preferences
- **Interface**: Theme selection (light/dark/auto), language, timezone
- **Editor**: Auto-save interval, spell check, syntax highlighting
- **Notifications**: Email preferences, push notifications, digest frequency
- **Privacy**: Data sharing preferences, analytics opt-out

### Account Settings
- **Profile**: Avatar, display name, bio, contact information
- **Security**: Password change, 2FA setup, session management
- **Billing**: Subscription details, payment methods, usage statistics
- **Data**: Export options, data retention preferences

### Workspace Settings
- **Groups**: Default permissions, invitation policies
- **Templates**: Custom note templates, default formats
- **Integrations**: Third-party service connections
- **Backup**: Automated backup schedules, cloud storage options

### Advanced Settings
- **API**: API key management, webhook configuration
- **Developer**: Custom CSS, JavaScript injection
- **Performance**: Cache settings, offline mode preferences
- **Accessibility**: Screen reader support, keyboard shortcuts

## Admin Panel

### User Management
- **User Overview**: Total users, active users, new registrations
- **User Details**: Profile information, activity logs, storage usage
- **User Actions**: Suspend, ban, promote to admin, reset password
- **Bulk Operations**: Mass user management, data export

### Content Moderation
- **Reported Content**: Review flagged notes, comments, and users
- **Content Filters**: Automated content scanning, keyword blocking
- **Moderation Queue**: Pending review items, action history
- **Policy Management**: Content guidelines, automated rules

### System Monitoring
- **Performance Metrics**: Response times, error rates, resource usage
- **Security Dashboard**: Failed login attempts, suspicious activity
- **Database Health**: Connection status, query performance, storage
- **API Usage**: Rate limiting, endpoint usage, abuse detection

### System Configuration
- **Feature Flags**: Enable/disable features, A/B testing
- **Email Settings**: SMTP configuration, template management
- **Storage Management**: File cleanup, backup verification
- **Maintenance Mode**: System downtime scheduling, user notifications

### Analytics and Reporting
- **Usage Statistics**: User engagement, feature adoption
- **Content Analytics**: Popular note types, collaboration patterns
- **Performance Reports**: System health, optimization opportunities
- **Business Metrics**: Revenue, user growth, retention rates

## Style Guidelines

### Color Palette
- Primary: White (#FFFFFF) or light grey (#F8F9FA) for main background
- Secondary: Dark grey (#343A40) for text and UI elements
- Accent: Teal (#008080) for interactive elements and highlights
- Success: Green (#28A745) for confirmations
- Warning: Amber (#FFC107) for cautionary actions
- Error: Red (#DC3545) for errors or destructive actions

### Typography
- Primary font: Inter for clean readability across platforms
- Monospace: JetBrains Mono for code blocks and markdown
- Font sizes: 14px base with scaling for hierarchy (12px, 14px, 16px, 20px, 24px)
- Line height: 1.5 for optimal readability

### Layout (Mobile first mindset)
- **Adaptive Panel Design**: The layout dynamically adjusts to screen size for an optimal experience on any device.
    - **Wide Screens (Desktop)**: A three-panel view is used, showing the File/Tag Explorer, the Note List, and the Editor simultaneously for maximum productivity.
    - **Narrower Screens (Tablet)**: The layout collapses to a two-panel view (Note List and Editor), with the File/Tag Explorer accessible via a toggle button.
    - **Mobile Screens (Phone)**: A single-panel view focuses on the Editor. A floating button reveals the Note List in an overlay sidebar for easy navigation.
- **Consistent Spacing**: Utilizes a 16px spacing scale (8px, 16px, 24px, 32px) to maintain a clean and organized look.
- **Card-based Design**: Note items in the list are displayed as cards with a clear visual hierarchy, showing the title, preview, and note-type indicator.
- **Context-aware Toolbars**: The editor's toolbar adapts its tools and options based on the currently active note format (e.g., Markdown, Rich Text).

### Icons and Visual Elements
- Distinctive icons for each note type (document, markdown, kanban, canvas types)
- Consistent icon style across the application
- Visual indicators for shared content (users icon)
- Minimal use of borders, favoring shadows and spacing for separation

## Technical Requirements

### Frontend
- Vue.js with TypeScript
- BulmaCSS for styling - (TAILWIND IS FORBIDDEN)
- Fitting library for Rich Text editing
- ReactFlow, Excalidraw, and tldraw for canvas functionality (Skip for now)

### Backend
- Node.js with Express
- SQLite (in dev) and MariaDB (in prod) for flexible data storage
- JWT for authentication

### Performance
- The app are in modules, each component Sidebar, RichText Editor, etc.. for easy expansion later.
- Initial load time under 2 seconds
- Real-time updates with less than 500ms latency
- Optimization for large documents
- Efficient handling of complex canvas interactions

## User Flows

### Note Creation
- User clicks "New Note" button
- User selects desired note format
- Format-specific editor loads with default template
- User inputs content with auto-save functionality

### Collaboration
- User creates or selects a group
- User invites members by email with specific roles
- User shares specific notes with the group
- Members can access and edit notes based on permissions

### Search and Retrieval
- User enters search query in global search bar
- Results display across all note types with content previews
- User can filter results by type, tags, or date
- User clicks result to open the specific note

### Secure Sharing
- User selects note to share
- User configures access permissions and restrictions
- System generates secure sharing link
- Recipients receive invitation with access instructions
- System tracks all access and modifications

### Settings Management
- User accesses settings panel from main navigation
- User navigates through categorized settings sections
- Changes are auto-saved with confirmation feedback
- Settings sync across all user devices
- Admin users access additional system configuration options
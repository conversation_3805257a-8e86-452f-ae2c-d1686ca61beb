<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: var(--color-background, #ffffff);
            color: var(--color-text, #000000);
            transition: all 0.3s ease;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 2px solid var(--color-border, #ddd);
            border-radius: 8px;
            background-color: var(--color-surface, #f8f9fa);
        }
        
        .theme-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .theme-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: var(--color-primary, #007bff);
            color: white;
            font-weight: bold;
            transition: all 0.2s ease;
        }
        
        .theme-btn:hover {
            background-color: var(--color-primary-hover, #0056b3);
            transform: translateY(-2px);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid var(--color-border, #ddd);
            border-radius: 6px;
            background-color: var(--color-surface, #ffffff);
        }
        
        .test-title {
            color: var(--color-primary, #007bff);
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .color-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        
        .color-box {
            padding: 20px;
            border-radius: 4px;
            text-align: center;
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .primary { background-color: var(--color-primary, #007bff); }
        .secondary { background-color: var(--color-secondary, #6c757d); }
        .success { background-color: var(--color-success, #28a745); }
        .danger { background-color: var(--color-danger, #dc3545); }
        .warning { background-color: var(--color-warning, #ffc107); color: #000; text-shadow: none; }
        .info { background-color: var(--color-info, #17a2b8); }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: var(--color-success, #28a745);
            color: white;
            text-align: center;
            font-weight: bold;
        }
        
        .error {
            background-color: var(--color-danger, #dc3545);
        }
        
        .info-box {
            background-color: var(--color-info, #17a2b8);
        }
        
        .warning-box {
            background-color: var(--color-warning, #ffc107);
            color: #000;
        }
        
        .current-theme {
            background-color: var(--color-primary, #007bff);
            color: white;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .css-variables {
            background-color: var(--color-surface, #f8f9fa);
            border: 1px solid var(--color-border, #ddd);
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        
        .variable-item {
            margin: 5px 0;
            padding: 5px;
            background-color: var(--color-background, #ffffff);
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Theme System Test Page</h1>
        
        <div class="current-theme" id="currentTheme">
            Current Theme: Loading...
        </div>
        
        <div class="theme-buttons">
            <button class="theme-btn" onclick="testTheme('default')">Default Theme</button>
            <button class="theme-btn" onclick="testTheme('darkly')">Darkly Theme</button>
            <button class="theme-btn" onclick="testTheme('flatly')">Flatly Theme</button>
            <button class="theme-btn" onclick="testTheme('cerulean')">Cerulean Theme</button>
        </div>
        
        <div class="status" id="status">
            Ready to test themes. Click a theme button above.
        </div>
        
        <div class="test-section">
            <div class="test-title">🎨 Color Palette Test</div>
            <div class="color-test">
                <div class="color-box primary">Primary</div>
                <div class="color-box secondary">Secondary</div>
                <div class="color-box success">Success</div>
                <div class="color-box danger">Danger</div>
                <div class="color-box warning">Warning</div>
                <div class="color-box info">Info</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">📝 Content Test</div>
            <p>This is a paragraph with <strong>bold text</strong> and <em>italic text</em>.</p>
            <p>Testing <span style="color: var(--color-primary, #007bff);">primary color text</span> and <span style="color: var(--color-success, #28a745);">success color text</span>.</p>
            <div class="status info-box">This is an info message using theme colors.</div>
            <div class="status warning-box">This is a warning message using theme colors.</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🔍 CSS Variables Inspection</div>
            <div class="css-variables" id="cssVariables">
                Loading CSS variables...
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">⚡ Performance Test</div>
            <button class="theme-btn" onclick="runPerformanceTest()">Run Theme Switch Performance Test</button>
            <div id="performanceResults"></div>
        </div>
    </div>

    <script>
        let currentTheme = 'default';
        let themeSwitchCount = 0;
        
        // Simulate theme switching
        function testTheme(themeName) {
            const startTime = performance.now();
            themeSwitchCount++;
            
            // Update current theme display
            currentTheme = themeName;
            document.getElementById('currentTheme').textContent = `Current Theme: ${themeName}`;
            
            // Simulate theme application
            document.documentElement.setAttribute('data-theme', themeName);
            
            // Update status
            const status = document.getElementById('status');
            status.textContent = `Theme switched to: ${themeName} (Switch #${themeSwitchCount})`;
            status.className = 'status';
            
            // Simulate theme loading delay
            setTimeout(() => {
                updateCSSVariables();
                const endTime = performance.now();
                const switchTime = endTime - startTime;
                
                status.innerHTML = `
                    Theme switched to: <strong>${themeName}</strong> (Switch #${themeSwitchCount})<br>
                    Switch time: <strong>${switchTime.toFixed(2)}ms</strong>
                `;
                
                // Color code the status based on performance
                if (switchTime < 50) {
                    status.style.backgroundColor = 'var(--color-success, #28a745)';
                } else if (switchTime < 100) {
                    status.style.backgroundColor = 'var(--color-warning, #ffc107)';
                    status.style.color = '#000';
                } else {
                    status.style.backgroundColor = 'var(--color-danger, #dc3545)';
                }
            }, 100);
        }
        
        // Update CSS variables display
        function updateCSSVariables() {
            const root = document.documentElement;
            const computedStyle = getComputedStyle(root);
            const variables = [
                '--color-primary',
                '--color-secondary', 
                '--color-success',
                '--color-danger',
                '--color-warning',
                '--color-info',
                '--color-background',
                '--color-surface',
                '--color-text',
                '--color-border'
            ];
            
            let html = '<strong>Current CSS Variables:</strong><br>';
            variables.forEach(variable => {
                const value = computedStyle.getPropertyValue(variable);
                if (value) {
                    html += `<div class="variable-item"><strong>${variable}:</strong> ${value}</div>`;
                } else {
                    html += `<div class="variable-item"><strong>${variable}:</strong> <em>not defined</em></div>`;
                }
            });
            
            document.getElementById('cssVariables').innerHTML = html;
        }
        
        // Performance test
        function runPerformanceTest() {
            const results = document.getElementById('performanceResults');
            results.innerHTML = '<p>Running performance test...</p>';
            
            const themes = ['default', 'darkly', 'flatly', 'cerulean'];
            const times = [];
            
            // Run multiple theme switches
            let completed = 0;
            themes.forEach((theme, index) => {
                setTimeout(() => {
                    const start = performance.now();
                    testTheme(theme);
                    
                    setTimeout(() => {
                        const end = performance.now();
                        times.push(end - start);
                        completed++;
                        
                        if (completed === themes.length) {
                            const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
                            const maxTime = Math.max(...times);
                            const minTime = Math.min(...times);
                            
                            results.innerHTML = `
                                <div class="test-section">
                                    <h4>Performance Results:</h4>
                                    <p><strong>Average switch time:</strong> ${avgTime.toFixed(2)}ms</p>
                                    <p><strong>Fastest switch:</strong> ${minTime.toFixed(2)}ms</p>
                                    <p><strong>Slowest switch:</strong> ${maxTime.toFixed(2)}ms</p>
                                    <p><strong>Total switches:</strong> ${times.length}</p>
                                </div>
                            `;
                        }
                    }, 150);
                }, index * 200);
            });
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateCSSVariables();
            document.getElementById('status').textContent = 'Theme system ready. Click a theme button to test.';
        });
        
        // Test theme switching on load
        setTimeout(() => {
            testTheme('default');
        }, 500);
    </script>
</body>
</html>

import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult, ValidationChain } from 'express-validator';
import { sanitizeHtml } from '../utils/sanitization';

// Enhanced validation error handler
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg,
      value: error.type === 'field' ? error.value : undefined
    }));

    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: formattedErrors
    });
    return;
  }
  
  next();
};

// Enhanced sanitization middleware
export const sanitizeAndValidate = (validations: ValidationChain[]) => {
  return [
    ...validations,
    handleValidationErrors
  ];
};

// Common validation rules
export const commonValidations = {
  email: body('email')
    .isEmail()
    .withMessage('Invalid email format')
    .normalizeEmail()
    .trim()
    .escape(),
    
  password: body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'),
    
  displayName: body('display_name')
    .isLength({ min: 2, max: 50 })
    .withMessage('Display name must be between 2 and 50 characters')
    .trim()
    .escape(),
    
  noteTitle: body('title')
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters')
    .trim()
    .customSanitizer(sanitizeHtml),
    
  noteContent: body('content')
    .isLength({ max: 1000000 })
    .withMessage('Content is too large (maximum 1MB)')
    .customSanitizer((value) => {
      if (typeof value === 'string') {
        return sanitizeHtml(value);
      }
      return value;
    }),
    
  noteType: body('noteType')
    .isIn(['richtext', 'markdown', 'kanban'])
    .withMessage('Invalid note type'),
    
  tags: body('tags')
    .optional()
    .isArray({ max: 20 })
    .withMessage('Maximum 20 tags allowed')
    .custom((tags) => {
      if (Array.isArray(tags)) {
        for (const tag of tags) {
          if (typeof tag !== 'string' || tag.length > 50 || tag.trim().length === 0) {
            throw new Error('Each tag must be a non-empty string with maximum 50 characters');
          }
        }
      }
      return true;
    }),
    
  uuid: (field: string) => param(field)
    .isUUID()
    .withMessage(`Invalid ${field} format`),
    
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1, max: 10000 })
      .withMessage('Page must be between 1 and 10000')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
      .toInt()
  ],
  
  searchQuery: query('q')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('Search query must be between 1 and 200 characters')
    .trim()
    .escape(),
    
  groupName: body('name')
    .isLength({ min: 3, max: 100 })
    .withMessage('Group name must be between 3 and 100 characters')
    .trim()
    .escape(),
    
  groupDescription: body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Group description must be less than 500 characters')
    .trim()
    .customSanitizer(sanitizeHtml),
    
  role: body('role')
    .isIn(['admin', 'editor', 'viewer'])
    .withMessage('Invalid role'),
    
  accessLevel: body('accessLevel')
    .isIn(['private', 'shared', 'unlisted', 'public'])
    .withMessage('Invalid access level'),
    
  permissions: body('permissions')
    .isArray({ min: 1 })
    .withMessage('At least one permission is required')
    .custom((permissions) => {
      const validPermissions = ['view', 'comment', 'edit'];
      for (const permission of permissions) {
        if (!validPermissions.includes(permission)) {
          throw new Error('Invalid permission');
        }
      }
      return true;
    }),
    
  ipAddress: (field: string) => body(field)
    .optional()
    .isIP()
    .withMessage(`Invalid IP address format for ${field}`),
    
  url: (field: string) => body(field)
    .optional()
    .isURL({ protocols: ['http', 'https'], require_protocol: true })
    .withMessage(`Invalid URL format for ${field}`)
    .isLength({ max: 500 })
    .withMessage(`${field} URL must be less than 500 characters`),
    
  theme: body('theme')
    .optional()
    .isIn(['light', 'dark', 'auto'])
    .withMessage('Invalid theme'),
    
  language: body('language')
    .optional()
    .isLength({ min: 2, max: 10 })
    .withMessage('Language code must be between 2 and 10 characters')
    .matches(/^[a-z]{2}(-[A-Z]{2})?$/)
    .withMessage('Invalid language code format'),
    
  timezone: body('timezone')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Timezone must be less than 50 characters'),
    
  autoSaveInterval: body('autoSaveInterval')
    .optional()
    .isInt({ min: 1000, max: 300000 })
    .withMessage('Auto-save interval must be between 1000ms and 300000ms')
    .toInt(),
    
  twoFactorCode: body('code')
    .isLength({ min: 6, max: 8 })
    .withMessage('Two-factor code must be 6-8 characters')
    .matches(/^[0-9A-Z]+$/)
    .withMessage('Two-factor code must contain only numbers and uppercase letters')
    .trim(),
    
  exportFormat: body('format')
    .isIn(['pdf', 'html', 'markdown'])
    .withMessage('Invalid export format'),
    
  customStyles: body('customStyles')
    .optional()
    .isLength({ max: 10000 })
    .withMessage('Custom styles must be less than 10000 characters')
    .customSanitizer(sanitizeHtml),
    
  templateName: body('name')
    .isLength({ min: 3, max: 100 })
    .withMessage('Template name must be between 3 and 100 characters')
    .trim()
    .escape(),
    
  boolean: (field: string) => body(field)
    .optional()
    .isBoolean()
    .withMessage(`${field} must be a boolean`)
    .toBoolean(),
    
  dateTime: (field: string) => body(field)
    .optional()
    .isISO8601()
    .withMessage(`${field} must be a valid ISO 8601 date`)
    .toDate(),
    
  positiveInteger: (field: string, min: number = 1, max: number = Number.MAX_SAFE_INTEGER) => body(field)
    .optional()
    .isInt({ min, max })
    .withMessage(`${field} must be an integer between ${min} and ${max}`)
    .toInt()
};

// Specific validation chains for different endpoints
export const validationChains = {
  // Authentication
  register: [
    commonValidations.email,
    commonValidations.password,
    commonValidations.displayName
  ],
  
  login: [
    commonValidations.email,
    body('password').notEmpty().withMessage('Password is required')
  ],
  
  passwordReset: [
    commonValidations.email
  ],
  
  passwordResetConfirm: [
    body('token').notEmpty().withMessage('Reset token is required'),
    commonValidations.password
  ],
  
  refreshToken: [
    body('refreshToken').notEmpty().withMessage('Refresh token is required')
  ],
  
  // Notes
  createNote: [
    commonValidations.noteTitle,
    commonValidations.noteContent,
    commonValidations.noteType,
    commonValidations.tags
  ],
  
  updateNote: [
    commonValidations.uuid('id'),
    body('title').optional().isLength({ min: 1, max: 200 }).withMessage('Title must be between 1 and 200 characters').trim().customSanitizer(sanitizeHtml),
    body('content').optional().isLength({ max: 1000000 }).withMessage('Content is too large').customSanitizer(sanitizeHtml),
    commonValidations.tags
  ],
  
  getNotes: [
    ...commonValidations.pagination,
    query('type').optional().isIn(['richtext', 'markdown', 'kanban']).withMessage('Invalid note type'),
    query('tags').optional().isString().withMessage('Tags must be a string'),
    commonValidations.searchQuery
  ],
  
  // Groups
  createGroup: [
    commonValidations.groupName,
    commonValidations.groupDescription,
    body('settings').optional().isObject().withMessage('Settings must be an object')
  ],
  
  updateGroup: [
    commonValidations.uuid('id'),
    body('name').optional().isLength({ min: 3, max: 100 }).withMessage('Group name must be between 3 and 100 characters').trim().escape(),
    body('description').optional().isLength({ max: 500 }).withMessage('Group description must be less than 500 characters').trim().customSanitizer(sanitizeHtml)
  ],
  
  inviteUser: [
    commonValidations.uuid('id'),
    commonValidations.email,
    commonValidations.role
  ],
  
  // Sharing
  createShare: [
    commonValidations.uuid('id'),
    commonValidations.accessLevel,
    commonValidations.permissions,
    body('expiresAt').optional().isISO8601().withMessage('Invalid expiration date').toDate(),
    body('password').optional().isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('allowedIps').optional().isArray().withMessage('Allowed IPs must be an array')
  ],
  
  // User settings
  updateSettings: [
    commonValidations.theme,
    body('themeName')
      .optional()
      .isString()
      .withMessage('Theme name must be a string')
      .isLength({ min: 1, max: 50 })
      .withMessage('Theme name must be between 1 and 50 characters')
      .isIn(['default', 'darkly', 'flatly', 'cerulean', 'cosmo', 'cyborg', 'journal', 'litera', 'lumen', 'minty', 'pulse', 'sandstone', 'simplex', 'sketchy', 'slate', 'solar', 'spacelab', 'superhero', 'united', 'yeti'])
      .withMessage('Invalid theme name'),
    commonValidations.language,
    commonValidations.timezone,
    commonValidations.autoSaveInterval,
    body('notifications').optional().isObject().withMessage('Notifications must be an object')
  ],
  
  updateProfile: [
    body('display_name').optional().isLength({ min: 2, max: 50 }).withMessage('Display name must be between 2 and 50 characters').trim().escape(),
    body('avatar_url')
      .optional()
      .custom((value) => {
        if (value === '' || value === null) {
          return true; // Allow empty string to remove avatar
        }
        if (typeof value !== 'string') {
          throw new Error('Avatar URL must be a string');
        }
        
        // Allow data URLs, http/https URLs, or relative URLs
        const isDataUrl = value.startsWith('data:image/');
        const isHttpUrl = /^https?:\/\//.test(value);
        const isRelativeUrl = value.startsWith('/');
        
        if (!isDataUrl && !isHttpUrl && !isRelativeUrl) {
          throw new Error('Avatar URL must be a valid HTTP/HTTPS URL, data URL, or relative URL');
        }
        
        // Different length limits based on URL type
        if (isDataUrl) {
          // Data URLs can be much larger (up to 2MB for base64 images)
          if (value.length > 2 * 1024 * 1024) {
            throw new Error('Avatar image is too large (maximum 2MB)');
          }
        } else {
          // Regular URLs should be reasonable length
          if (value.length > 500) {
            throw new Error('Avatar URL must be less than 500 characters');
          }
        }
        
        return true;
      })
      .trim()
  ],
  
  // Two-factor authentication
  verify2FA: [
    commonValidations.twoFactorCode
  ],
  
  // Export
  exportNote: [
    commonValidations.uuid('id'),
    commonValidations.exportFormat,
    commonValidations.boolean('includeMetadata'),
    commonValidations.customStyles
  ],
  
  exportMultipleNotes: [
    body('noteIds').isArray({ min: 1, max: 100 }).withMessage('Must provide 1-100 note IDs'),
    commonValidations.exportFormat,
    commonValidations.boolean('includeMetadata'),
    commonValidations.customStyles
  ],
  
  // Templates
  createTemplate: [
    commonValidations.templateName,
    body('description').optional().isLength({ max: 500 }).withMessage('Description must be less than 500 characters').trim().customSanitizer(sanitizeHtml),
    commonValidations.noteType,
    body('content').notEmpty().withMessage('Template content is required').isLength({ max: 1000000 }).withMessage('Content is too large'),
    commonValidations.boolean('isPublic'),
    commonValidations.tags
  ]
};

// Rate limiting validation
export const rateLimitValidation = (req: Request, res: Response, next: NextFunction): void => {
  // Check if rate limit headers are present (set by rate limiting middleware)
  const remaining = res.get('X-RateLimit-Remaining');
  const limit = res.get('X-RateLimit-Limit');
  
  if (remaining && limit) {
    const remainingCount = parseInt(remaining);
    const limitCount = parseInt(limit);
    
    // Warn when approaching rate limit
    if (remainingCount <= limitCount * 0.1) { // 10% remaining
      res.set('X-RateLimit-Warning', 'Approaching rate limit');
    }
  }
  
  next();
};

// Security validation middleware
export const securityValidation = (req: Request, res: Response, next: NextFunction): void => {
  // Check for suspicious patterns in request
  const suspiciousPatterns = [
    /(<script|javascript:)/i, // Removed data: from here since it's legitimate for images
    /(data:(?!image\/))/i, // Only block data: URLs that are NOT images
    /(union\s+select|drop\s+table|insert\s+into)/i,
    /(\.\.\/|\.\.\\)/,
    /(\x00|\x08|\x09|\x0a|\x0d)/
  ];
  
  const checkValue = (value: any): boolean => {
    if (typeof value === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(value));
    }
    if (Array.isArray(value)) {
      return value.some(checkValue);
    }
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(checkValue);
    }
    return false;
  };
  
  // Check request body, query, and params
  const suspicious = [
    req.body && checkValue(req.body),
    req.query && checkValue(req.query),
    req.params && checkValue(req.params)
  ].some(Boolean);
  
  if (suspicious) {
    res.status(400).json({
      error: 'Suspicious input detected',
      code: 'SUSPICIOUS_INPUT'
    });
    return;
  }
  
  next();
};
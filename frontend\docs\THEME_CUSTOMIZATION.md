# Theme Customization Guide

## Overview

This guide explains how to customize themes, create new themes, and extend the theme system in the Notes application.

## Theme System Architecture

### Theme Structure

Each theme consists of:
- **CSS custom properties** defining colors and values
- **Theme metadata** for UI display and management
- **Accessibility information** for contrast and usability
- **Preview data** for theme selection interface

### Theme Files Location

```
frontend/src/styles/themes/
├── themes.css              # Theme switching logic
└── bulmaswatch/            # Theme collection
    ├── default.css         # Default light theme
    ├── darkly.css          # Professional dark theme
    ├── flatly.css          # Modern flat theme
    └── cerulean.css        # Blue accent theme
```

## Creating a New Theme

### Step 1: Create Theme CSS File

Create a new CSS file in `themes/bulmaswatch/`:

```css
/* themes/bulmaswatch/my-custom-theme.css */

/**
 * My Custom Theme
 * A professional theme with custom branding colors
 */

[data-theme="my-custom-theme"] {
  /* === PRIMARY COLORS === */
  --color-primary: #6366f1;           /* Main brand color */
  --color-primary-light: #818cf8;     /* Lighter variant */
  --color-primary-dark: #4f46e5;      /* Darker variant */
  
  /* === SECONDARY COLORS === */
  --color-secondary: #64748b;         /* Secondary brand color */
  --color-secondary-light: #94a3b8;   /* Lighter variant */
  --color-secondary-dark: #475569;    /* Darker variant */
  
  /* === SEMANTIC COLORS === */
  --color-success: #10b981;           /* Success states */
  --color-success-light: #34d399;     /* Light success */
  --color-success-dark: #059669;      /* Dark success */
  
  --color-danger: #ef4444;            /* Error/danger states */
  --color-danger-light: #f87171;      /* Light danger */
  --color-danger-dark: #dc2626;       /* Dark danger */
  
  --color-warning: #f59e0b;           /* Warning states */
  --color-warning-light: #fbbf24;     /* Light warning */
  --color-warning-dark: #d97706;      /* Dark warning */
  
  --color-info: #3b82f6;              /* Information states */
  --color-info-light: #60a5fa;        /* Light info */
  --color-info-dark: #2563eb;         /* Dark info */
  
  /* === BACKGROUND COLORS === */
  --color-background: #ffffff;         /* Main background */
  --color-surface: #f8fafc;           /* Card/panel backgrounds */
  --color-surface-hover: #f1f5f9;     /* Hover state for surfaces */
  
  /* === TEXT COLORS === */
  --color-text: #1e293b;              /* Primary text */
  --color-text-strong: #0f172a;       /* Strong emphasis text */
  --color-text-muted: #64748b;        /* Secondary/muted text */
  --color-text-light: #94a3b8;        /* Light text */
  
  /* === BORDER COLORS === */
  --color-border: #e2e8f0;            /* Default borders */
  --color-border-hover: #cbd5e1;      /* Hover state borders */
  --color-border-focus: var(--color-primary); /* Focus state borders */
  
  /* === COMPONENT-SPECIFIC COLORS === */
  
  /* Navigation */
  --navbar-background: var(--color-surface);
  --navbar-text: var(--color-text);
  --navbar-border: var(--color-border);
  
  --sidebar-background: var(--color-background);
  --sidebar-text: var(--color-text);
  --sidebar-border: var(--color-border);
  --sidebar-item-hover: var(--color-surface-hover);
  --sidebar-item-active: var(--color-primary);
  
  /* Cards and Panels */
  --card-background: var(--color-background);
  --card-border: var(--color-border);
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  
  /* Forms */
  --input-background: var(--color-background);
  --input-border: var(--color-border);
  --input-border-focus: var(--color-primary);
  --input-text: var(--color-text);
  --input-placeholder: var(--color-text-muted);
  
  /* Buttons */
  --button-primary-background: var(--color-primary);
  --button-primary-text: #ffffff;
  --button-primary-hover: var(--color-primary-dark);
  
  --button-secondary-background: var(--color-surface);
  --button-secondary-text: var(--color-text);
  --button-secondary-hover: var(--color-surface-hover);
  --button-secondary-border: var(--color-border);
  
  /* Modals */
  --modal-background: var(--color-background);
  --modal-overlay: rgba(0, 0, 0, 0.5);
  --modal-border: var(--color-border);
  
  /* Tables */
  --table-background: var(--color-background);
  --table-header-background: var(--color-surface);
  --table-border: var(--color-border);
  --table-row-hover: var(--color-surface-hover);
  
  /* Dropdowns */
  --dropdown-background: var(--color-background);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);
  --dropdown-shadow: var(--card-shadow);
  
  /* Notifications */
  --notification-success-background: #ecfdf5;
  --notification-success-text: #065f46;
  --notification-success-border: #a7f3d0;
  
  --notification-danger-background: #fef2f2;
  --notification-danger-text: #991b1b;
  --notification-danger-border: #fecaca;
  
  --notification-warning-background: #fffbeb;
  --notification-warning-text: #92400e;
  --notification-warning-border: #fed7aa;
  
  --notification-info-background: #eff6ff;
  --notification-info-text: #1e40af;
  --notification-info-border: #bfdbfe;
  
  /* === SPACING AND SIZING === */
  --spacing-1: 0.25rem;    /* 4px */
  --spacing-2: 0.5rem;     /* 8px */
  --spacing-3: 0.75rem;    /* 12px */
  --spacing-4: 1rem;       /* 16px */
  --spacing-5: 1.25rem;    /* 20px */
  --spacing-6: 1.5rem;     /* 24px */
  --spacing-8: 2rem;       /* 32px */
  --spacing-10: 2.5rem;    /* 40px */
  --spacing-12: 3rem;      /* 48px */
  
  /* === TYPOGRAPHY === */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* === BORDERS AND RADIUS === */
  --radius: 0.375rem;          /* 6px */
  --radius-sm: 0.25rem;        /* 4px */
  --radius-lg: 0.5rem;         /* 8px */
  --radius-xl: 0.75rem;        /* 12px */
  --radius-full: 9999px;       /* Fully rounded */
  
  --border-width: 1px;
  --border-width-2: 2px;
  
  /* === SHADOWS === */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* === TRANSITIONS === */
  --transition-fast: all 0.15s ease;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;
  --transition-theme: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* === Z-INDEX SCALE === */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}
```

### Step 2: Register Theme in Configuration

Add the theme to the theme management system:

```typescript
// In src/composables/useTheme.ts or theme configuration file

const customTheme: BulmaswatchTheme = {
  name: 'my-custom-theme',
  displayName: 'My Custom Theme',
  description: 'A professional theme with custom branding colors',
  cssFile: 'my-custom-theme.css',
  localPath: '/src/styles/themes/bulmaswatch/my-custom-theme.css',
  preview: {
    primary: '#6366f1',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#1e293b',
    accent: '#3b82f6'
  },
  isDark: false,
  category: 'light'
}

// Add to available themes
availableThemes.value.push(customTheme)
```

### Step 3: Test Theme Accessibility

Ensure your theme meets accessibility standards:

```css
/* Test color contrast ratios */
/* Use tools like WebAIM Contrast Checker */

/* Minimum contrast ratios (WCAG AA): */
/* Normal text: 4.5:1 */
/* Large text: 3:1 */
/* UI components: 3:1 */

/* Example accessibility check */
[data-theme="my-custom-theme"] {
  /* Ensure sufficient contrast */
  --color-text: #1e293b;           /* Should have 4.5:1 contrast with background */
  --color-background: #ffffff;      /* White background */
  
  /* Test with online contrast checkers */
  /* Adjust colors if contrast is insufficient */
}
```

## Creating Dark Theme Variants

### Dark Theme Structure

```css
/* themes/bulmaswatch/my-custom-theme-dark.css */

[data-theme="my-custom-theme-dark"] {
  /* === BACKGROUND COLORS (Inverted) === */
  --color-background: #0f172a;         /* Dark background */
  --color-surface: #1e293b;            /* Dark surface */
  --color-surface-hover: #334155;      /* Dark surface hover */
  
  /* === TEXT COLORS (Inverted) === */
  --color-text: #f1f5f9;              /* Light text on dark */
  --color-text-strong: #ffffff;        /* Strong light text */
  --color-text-muted: #94a3b8;        /* Muted light text */
  --color-text-light: #64748b;        /* Light muted text */
  
  /* === BORDER COLORS (Adjusted) === */
  --color-border: #334155;            /* Dark borders */
  --color-border-hover: #475569;      /* Dark border hover */
  
  /* Keep primary colors similar but adjust for dark background */
  --color-primary: #818cf8;           /* Slightly lighter for dark bg */
  --color-primary-light: #a5b4fc;     /* Lighter variant */
  --color-primary-dark: #6366f1;      /* Darker variant */
  
  /* Adjust component backgrounds */
  --navbar-background: var(--color-surface);
  --sidebar-background: var(--color-background);
  --card-background: var(--color-surface);
  --modal-background: var(--color-surface);
  
  /* Adjust shadows for dark theme */
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}
```

## Customizing Existing Themes

### Override Specific Colors

Create a custom CSS file that overrides specific theme properties:

```css
/* themes/custom-overrides.css */

/* Override primary color in default theme */
[data-theme="default"] {
  --color-primary: #your-brand-color;
  --color-primary-light: #your-brand-light;
  --color-primary-dark: #your-brand-dark;
}

/* Override multiple themes */
[data-theme="default"],
[data-theme="flatly"] {
  --navbar-background: #your-custom-navbar-color;
}
```

### Brand-Specific Customizations

```css
/* Brand customizations */
:root {
  /* Company brand colors */
  --brand-primary: #your-primary;
  --brand-secondary: #your-secondary;
  --brand-accent: #your-accent;
}

/* Apply brand colors to all themes */
[data-theme] {
  --color-primary: var(--brand-primary);
  --button-primary-background: var(--brand-primary);
  --navbar-brand-color: var(--brand-primary);
}
```

## Advanced Theme Features

### Conditional Theme Styles

```css
/* Apply styles only in specific themes */
[data-theme="my-custom-theme"] .special-component {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
}

/* Apply styles to multiple themes */
[data-theme="darkly"],
[data-theme="my-custom-theme-dark"] .dark-only-feature {
  display: block;
}

[data-theme="default"],
[data-theme="flatly"] .light-only-feature {
  display: block;
}
```

### Theme-Specific Animations

```css
/* Different animations for different themes */
[data-theme="my-custom-theme"] .animated-element {
  animation: custom-bounce 0.5s ease-in-out;
}

@keyframes custom-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

/* Disable animations in minimal themes */
[data-theme="minimal"] * {
  animation: none !important;
  transition: none !important;
}
```

### Responsive Theme Adjustments

```css
/* Adjust theme for mobile devices */
@media screen and (max-width: 768px) {
  [data-theme="my-custom-theme"] {
    --spacing-4: 0.75rem;  /* Reduce spacing on mobile */
    --font-size-base: 0.9rem;  /* Smaller font on mobile */
  }
}

/* High DPI display adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  [data-theme="my-custom-theme"] {
    --border-width: 0.5px;  /* Thinner borders on high DPI */
  }
}
```

## Theme Testing

### Accessibility Testing

```bash
# Test color contrast
npm run test:contrast

# Test with screen readers
npm run test:accessibility

# Test keyboard navigation
npm run test:keyboard
```

### Visual Testing

```bash
# Generate screenshots for all themes
npm run test:visual-themes

# Compare theme variations
npm run test:theme-diff --base=default --compare=my-custom-theme
```

### Performance Testing

```bash
# Test theme loading performance
npm run test:theme-performance

# Analyze theme CSS bundle sizes
npm run analyze:themes
```

## Troubleshooting

### Common Issues

1. **Colors not applying**: Check CSS custom property names and theme selector
2. **Poor contrast**: Use contrast checking tools and adjust colors
3. **Theme not loading**: Verify theme registration and CSS file path
4. **Inconsistent appearance**: Ensure all components use CSS custom properties

### Debug Tools

```css
/* Debug theme variables */
[data-theme] {
  /* Show current theme name */
  --debug-theme: attr(data-theme);
}

/* Debug color values */
.debug-colors::before {
  content: 'Primary: ' var(--color-primary) ', Background: ' var(--color-background);
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  font-size: 12px;
  z-index: 9999;
}
```

### Performance Optimization

1. **Minimize theme CSS size**: Remove unused properties
2. **Use efficient selectors**: Avoid overly specific selectors
3. **Optimize custom properties**: Group related properties
4. **Test loading performance**: Monitor theme switching speed

## Best Practices

### Theme Design

✅ **Do:**
- Maintain consistent color relationships across themes
- Ensure sufficient contrast for accessibility
- Test themes across different screen sizes
- Use semantic color names
- Document theme purpose and usage

❌ **Don't:**
- Hardcode colors in component CSS
- Create themes with poor accessibility
- Ignore mobile and responsive considerations
- Use overly complex color schemes
- Skip testing with real content

### Development

✅ **Do:**
- Use CSS custom properties for all theme-related values
- Follow established naming conventions
- Test themes thoroughly before deployment
- Document custom themes and overrides
- Consider performance impact

❌ **Don't:**
- Override theme styles with `!important`
- Create themes without proper testing
- Ignore browser compatibility
- Skip accessibility validation
- Create overly large theme files

## Resources

### Tools
- [WebAIM Contrast Checker](https://webaim.org/resources/contrastchecker/)
- [Colour Contrast Analyser](https://www.tpgi.com/color-contrast-checker/)
- [Adobe Color](https://color.adobe.com/) - Color palette generator
- [Coolors](https://coolors.co/) - Color scheme generator

### References
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [CSS Custom Properties MDN](https://developer.mozilla.org/en-US/docs/Web/CSS/--*)
- [Color Theory Basics](https://www.interaction-design.org/literature/topics/color-theory)

### Examples
- Check existing themes in `themes/bulmaswatch/` for reference
- Review component CSS files for proper custom property usage
- Examine theme switching logic in `themes/themes.css`
import { Request, Response } from 'express';
import { SearchService, SearchFilters, SearchOptions } from '../services/SearchService';

export class SearchController {
  // GET /api/search - Full-text search across notes
  static async searchNotes(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Parse query parameters
      const query = req.query.q as string;
      const noteType = req.query.type as string;
      const tags = req.query.tags ? (req.query.tags as string).split(',').map(t => t.trim()) : undefined;
      const dateFrom = req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined;
      const dateTo = req.query.dateTo ? new Date(req.query.dateTo as string) : undefined;
      const isArchived = req.query.archived === 'true';

      // Pagination and sorting
      const page = Math.max(1, parseInt(req.query.page as string) || 1);
      const limit = Math.min(50, Math.max(1, parseInt(req.query.limit as string) || 20));
      const sortBy = (req.query.sortBy as string) || 'relevance';
      const sortOrder = (req.query.sortOrder as string) || 'desc';

      // Validate date range
      if (dateFrom && dateTo && dateFrom > dateTo) {
        res.status(400).json({ error: 'Invalid date range: dateFrom must be before dateTo' });
        return;
      }

      // Validate note type
      if (noteType && !['richtext', 'markdown', 'kanban'].includes(noteType)) {
        res.status(400).json({ error: 'Invalid note type. Must be richtext, markdown, or kanban' });
        return;
      }

      // Validate sort options
      if (!['relevance', 'created_at', 'updated_at', 'title'].includes(sortBy)) {
        res.status(400).json({ error: 'Invalid sortBy. Must be relevance, created_at, updated_at, or title' });
        return;
      }

      if (!['asc', 'desc'].includes(sortOrder)) {
        res.status(400).json({ error: 'Invalid sortOrder. Must be asc or desc' });
        return;
      }

      const filters: SearchFilters = {
        userId,
        query: query?.trim(),
        noteType: noteType as any,
        tags,
        dateFrom,
        dateTo,
        isArchived
      };

      const options: SearchOptions = {
        page,
        limit,
        sortBy: sortBy as any,
        sortOrder: sortOrder as any
      };

      const searchResult = await SearchService.search(filters, options);

      res.json({
        ...searchResult,
        pagination: {
          page,
          limit,
          total: searchResult.total,
          totalPages: Math.ceil(searchResult.total / limit)
        },
        filters: {
          query: query || null,
          noteType: noteType || null,
          tags: tags || null,
          dateFrom: dateFrom?.toISOString() || null,
          dateTo: dateTo?.toISOString() || null,
          isArchived
        }
      });
    } catch (error) {
      console.error('Search error:', error);
      res.status(500).json({ 
        error: 'Search failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // GET /api/search/suggestions - Get search suggestions
  static async getSearchSuggestions(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const query = req.query.q as string;
      
      if (!query || query.trim().length < 2) {
        res.json({ suggestions: [] });
        return;
      }

      // Get title suggestions
      const titleSuggestions = await SearchController.getTitleSuggestions(query.trim(), userId);
      
      // Get tag suggestions
      const tagSuggestions = await SearchController.getTagSuggestions(query.trim(), userId);

      res.json({
        suggestions: {
          titles: titleSuggestions,
          tags: tagSuggestions
        }
      });
    } catch (error) {
      console.error('Suggestions error:', error);
      res.status(500).json({ error: 'Failed to get suggestions' });
    }
  }

  // GET /api/search/stats - Get search statistics
  static async getSearchStats(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const stats = await SearchService.getSearchStats(userId);
      res.json(stats);
    } catch (error) {
      console.error('Search stats error:', error);
      res.status(500).json({ error: 'Failed to get search statistics' });
    }
  }

  // Helper method to get title suggestions
  private static async getTitleSuggestions(query: string, userId: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const db = require('../config/database').getDatabase();
      
      const suggestionQuery = `
        SELECT DISTINCT title
        FROM notes
        WHERE user_id = ? 
          AND is_archived = 0
          AND title LIKE ?
        ORDER BY updated_at DESC
        LIMIT 8
      `;

      db.all(suggestionQuery, [userId, `%${query}%`], (err: any, rows: any[]) => {
        if (err) {
          resolve([]); // Don't fail if suggestions fail
          return;
        }

        const suggestions = rows
          .map(row => row.title)
          .filter(title => title.toLowerCase() !== query.toLowerCase());

        resolve(suggestions);
      });
    });
  }

  // Helper method to get tag suggestions
  private static async getTagSuggestions(query: string, userId: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const db = require('../config/database').getDatabase();
      
      const tagQuery = `
        SELECT DISTINCT name
        FROM tags
        WHERE user_id = ? 
          AND name LIKE ?
        ORDER BY name
        LIMIT 8
      `;

      db.all(tagQuery, [userId, `%${query}%`], (err: any, rows: any[]) => {
        if (err) {
          resolve([]); // Don't fail if suggestions fail
          return;
        }

        const suggestions = rows
          .map(row => row.name)
          .filter(name => name.toLowerCase() !== query.toLowerCase());

        resolve(suggestions);
      });
    });
  }

  // POST /api/search/reindex - Reindex search data (admin/maintenance)
  static async reindexSearch(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Rebuild FTS index for user's notes
      const db = require('../config/database').getDatabase();
      
      // Clear existing FTS data for user
      await new Promise<void>((resolve, reject) => {
        db.run('DELETE FROM notes_fts WHERE user_id = ?', [userId], (err: any) => {
          if (err) reject(err);
          else resolve();
        });
      });

      // Reinsert all notes for user
      const notes = await new Promise<any[]>((resolve, reject) => {
        db.all(
          'SELECT id, title, content, note_type, user_id FROM notes WHERE user_id = ? AND is_archived = 0',
          [userId],
          (err: any, rows: any[]) => {
            if (err) reject(err);
            else resolve(rows);
          }
        );
      });

      for (const note of notes) {
        await new Promise<void>((resolve, reject) => {
          db.run(
            'INSERT INTO notes_fts(note_id, title, content, note_type, user_id, tags) VALUES (?, ?, ?, ?, ?, ?)',
            [note.id, note.title, note.content, note.note_type, note.user_id, ''],
            (err: any) => {
              if (err) reject(err);
              else resolve();
            }
          );
        });

        // Update tags for this note
        await SearchService.updateNoteTags(note.id);
      }

      res.json({ 
        message: 'Search index rebuilt successfully',
        notesReindexed: notes.length
      });
    } catch (error) {
      console.error('Reindex error:', error);
      res.status(500).json({ error: 'Failed to rebuild search index' });
    }
  }
}
import { getDatabase } from '../config/database';
import { Note } from '../models/Note';
import { Tag } from '../models/Tag';

export interface SearchFilters {
  userId: string;
  query?: string;
  noteType?: 'richtext' | 'markdown' | 'kanban';
  tags?: string[];
  dateFrom?: Date;
  dateTo?: Date;
  isArchived?: boolean;
}

export interface SearchOptions {
  page: number;
  limit: number;
  sortBy?: 'relevance' | 'created_at' | 'updated_at' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchResult {
  note: Note;
  tags: Tag[];
  highlights: {
    title?: string;
    content?: string;
  };
  relevanceScore: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  searchTime: number;
  suggestions?: string[];
}

export class SearchService {
  private static getDb() {
    return getDatabase();
  }

  static async search(filters: SearchFilters, options: SearchOptions): Promise<SearchResponse> {
    const startTime = Date.now();
    
    try {
      if (filters.query && filters.query.trim()) {
        return await this.fullTextSearch(filters, options, startTime);
      } else {
        return await this.filterSearch(filters, options, startTime);
      }
    } catch (error) {
      console.error('Search error:', error);
      throw new Error('Search failed');
    }
  }

  private static async fullTextSearch(
    filters: SearchFilters, 
    options: SearchOptions, 
    startTime: number
  ): Promise<SearchResponse> {
    const query = filters.query!.trim();
    const searchQuery = this.buildFTSQuery(query);
    
    let whereClause = 'WHERE fts.user_id = ? AND n.is_archived = ?';
    const params: any[] = [filters.userId, filters.isArchived ?? false];

    // Add additional filters
    if (filters.noteType) {
      whereClause += ' AND n.note_type = ?';
      params.push(filters.noteType);
    }

    if (filters.dateFrom) {
      whereClause += ' AND n.created_at >= ?';
      params.push(filters.dateFrom.toISOString());
    }

    if (filters.dateTo) {
      whereClause += ' AND n.created_at <= ?';
      params.push(filters.dateTo.toISOString());
    }

    // Handle tag filtering
    if (filters.tags && filters.tags.length > 0) {
      const tagPlaceholders = filters.tags.map(() => '?').join(',');
      whereClause += ` AND n.id IN (
        SELECT DISTINCT nt.note_id 
        FROM note_tags nt 
        JOIN tags t ON nt.tag_id = t.id 
        WHERE t.name IN (${tagPlaceholders}) AND t.user_id = ?
      )`;
      params.push(...filters.tags, filters.userId);
    }

    // Build sort clause
    let orderClause = 'ORDER BY ';
    if (options.sortBy === 'relevance' || !options.sortBy) {
      orderClause += 'fts.rank';
    } else {
      orderClause += `n.${options.sortBy} ${(options.sortOrder || 'desc').toUpperCase()}`;
    }

    const offset = (options.page - 1) * options.limit;

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM notes_fts fts
      JOIN notes n ON fts.note_id = n.id
      ${whereClause}
      AND fts MATCH ?
    `;

    // Main search query
    const mainQuery = `
      SELECT 
        n.*,
        fts.rank,
        highlight(fts, 1, '<mark>', '</mark>') as title_highlight,
        snippet(fts, 2, '<mark>', '</mark>', '...', 32) as content_highlight
      FROM notes_fts fts
      JOIN notes n ON fts.note_id = n.id
      ${whereClause}
      AND fts MATCH ?
      ${orderClause}
      LIMIT ? OFFSET ?
    `;

    const searchParams = [...params, searchQuery];
    const mainParams = [...searchParams, options.limit, offset];

    return new Promise((resolve, reject) => {
      // Get total count
      this.getDb().get(countQuery, searchParams, async (err, countRow) => {
        if (err) {
          reject(err);
          return;
        }

        const total = (countRow as any).total;

        // Get search results
        this.getDb().all(mainQuery, mainParams, async (err, rows) => {
          if (err) {
            reject(err);
            return;
          }

          try {
            const results = await Promise.all(
              (rows as any[]).map(async (row) => {
                const note = this.mapRowToNote(row);
                const tags = await this.getTagsForNote(note.id);
                
                return {
                  note,
                  tags,
                  highlights: {
                    title: row.title_highlight !== row.title ? row.title_highlight : undefined,
                    content: row.content_highlight
                  },
                  relevanceScore: row.rank || 0
                };
              })
            );

            const searchTime = Date.now() - startTime;
            const suggestions = await this.generateSuggestions(query, filters.userId);

            resolve({
              results,
              total,
              searchTime,
              suggestions
            });
          } catch (error) {
            reject(error);
          }
        });
      });
    });
  }

  private static async filterSearch(
    filters: SearchFilters, 
    options: SearchOptions, 
    startTime: number
  ): Promise<SearchResponse> {
    let whereClause = 'WHERE user_id = ? AND is_archived = ?';
    const params: any[] = [filters.userId, filters.isArchived ?? false];

    // Add filters
    if (filters.noteType) {
      whereClause += ' AND note_type = ?';
      params.push(filters.noteType);
    }

    if (filters.dateFrom) {
      whereClause += ' AND created_at >= ?';
      params.push(filters.dateFrom.toISOString());
    }

    if (filters.dateTo) {
      whereClause += ' AND created_at <= ?';
      params.push(filters.dateTo.toISOString());
    }

    // Handle tag filtering
    if (filters.tags && filters.tags.length > 0) {
      const tagPlaceholders = filters.tags.map(() => '?').join(',');
      whereClause += ` AND id IN (
        SELECT DISTINCT nt.note_id 
        FROM note_tags nt 
        JOIN tags t ON nt.tag_id = t.id 
        WHERE t.name IN (${tagPlaceholders}) AND t.user_id = ?
      )`;
      params.push(...filters.tags, filters.userId);
    }

    // Build sort clause
    const sortBy = options.sortBy === 'relevance' ? 'updated_at' : (options.sortBy || 'updated_at');
    const sortOrder = (options.sortOrder || 'desc').toUpperCase();
    const orderClause = `ORDER BY ${sortBy} ${sortOrder}`;

    const offset = (options.page - 1) * options.limit;

    // Count query
    const countQuery = `SELECT COUNT(*) as total FROM notes ${whereClause}`;
    
    // Main query
    const mainQuery = `
      SELECT * FROM notes 
      ${whereClause} 
      ${orderClause}
      LIMIT ? OFFSET ?
    `;

    const mainParams = [...params, options.limit, offset];

    return new Promise((resolve, reject) => {
      // Get total count
      this.getDb().get(countQuery, params, async (err, countRow) => {
        if (err) {
          reject(err);
          return;
        }

        const total = (countRow as any).total;

        // Get notes
        this.getDb().all(mainQuery, mainParams, async (err, rows) => {
          if (err) {
            reject(err);
            return;
          }

          try {
            const results = await Promise.all(
              (rows as any[]).map(async (row) => {
                const note = this.mapRowToNote(row);
                const tags = await this.getTagsForNote(note.id);
                
                return {
                  note,
                  tags,
                  highlights: {},
                  relevanceScore: 0
                };
              })
            );

            const searchTime = Date.now() - startTime;

            resolve({
              results,
              total,
              searchTime
            });
          } catch (error) {
            reject(error);
          }
        });
      });
    });
  }

  private static buildFTSQuery(query: string): string {
    // Clean and prepare the query for FTS5
    const cleanQuery = query
      .replace(/[^\w\s"'-]/g, ' ') // Remove special characters except quotes and hyphens
      .replace(/\s+/g, ' ')
      .trim();

    if (!cleanQuery) {
      return '*';
    }

    // Split into terms and handle phrases
    const terms: string[] = [];
    const regex = /"([^"]+)"|(\S+)/g;
    let match;

    while ((match = regex.exec(cleanQuery)) !== null) {
      if (match[1]) {
        // Quoted phrase
        terms.push(`"${match[1]}"`);
      } else if (match[2]) {
        // Individual term with prefix matching
        terms.push(`${match[2]}*`);
      }
    }

    return terms.join(' OR ');
  }

  private static async generateSuggestions(query: string, userId: string): Promise<string[]> {
    if (query.length < 3) {
      return [];
    }

    const suggestionQuery = `
      SELECT DISTINCT title
      FROM notes
      WHERE user_id = ? 
        AND is_archived = 0
        AND title LIKE ?
      ORDER BY updated_at DESC
      LIMIT 5
    `;

    return new Promise((resolve, reject) => {
      this.getDb().all(suggestionQuery, [userId, `%${query}%`], (err, rows) => {
        if (err) {
          resolve([]); // Don't fail search if suggestions fail
          return;
        }

        const suggestions = (rows as any[])
          .map(row => row.title)
          .filter(title => title.toLowerCase() !== query.toLowerCase());

        resolve(suggestions);
      });
    });
  }

  private static async getTagsForNote(noteId: string): Promise<Tag[]> {
    const query = `
      SELECT t.* FROM tags t
      JOIN note_tags nt ON t.id = nt.tag_id
      WHERE nt.note_id = ?
      ORDER BY t.name
    `;

    return new Promise((resolve, reject) => {
      this.getDb().all(query, [noteId], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }

        const tags = (rows as any[]).map((row: any) => ({
          id: row.id,
          name: row.name,
          userId: row.user_id,
          icon: row.icon || 'fas fa-tag',
          color: row.color || '#6c757d',
          isPredefined: Boolean(row.is_predefined),
          createdAt: new Date(row.created_at),
          updatedAt: new Date(row.updated_at)
        }));

        resolve(tags);
      });
    });
  }

  private static mapRowToNote(row: any): Note {
    return {
      id: row.id,
      userId: row.user_id,
      groupId: row.group_id,
      title: row.title,
      content: row.content,
      noteType: row.note_type,
      metadata: JSON.parse(row.metadata || '{}'),
      isArchived: Boolean(row.is_archived),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  // Update FTS table with tags for better search
  static async updateNoteTags(noteId: string): Promise<void> {
    const tagsQuery = `
      SELECT GROUP_CONCAT(t.name, ' ') as tags
      FROM tags t
      JOIN note_tags nt ON t.id = nt.tag_id
      WHERE nt.note_id = ?
    `;

    return new Promise((resolve, reject) => {
      this.getDb().get(tagsQuery, [noteId], (err, row) => {
        if (err) {
          reject(err);
          return;
        }

        const tags = (row as any)?.tags || '';
        const updateQuery = 'UPDATE notes_fts SET tags = ? WHERE note_id = ?';

        this.getDb().run(updateQuery, [tags, noteId], (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    });
  }

  // Get search statistics
  static async getSearchStats(userId: string): Promise<{
    totalNotes: number;
    notesByType: Record<string, number>;
    totalTags: number;
    recentSearches?: string[];
  }> {
    const statsQuery = `
      SELECT 
        COUNT(*) as total_notes,
        SUM(CASE WHEN note_type = 'richtext' THEN 1 ELSE 0 END) as richtext_count,
        SUM(CASE WHEN note_type = 'markdown' THEN 1 ELSE 0 END) as markdown_count,
        SUM(CASE WHEN note_type = 'kanban' THEN 1 ELSE 0 END) as kanban_count
      FROM notes 
      WHERE user_id = ? AND is_archived = 0
    `;

    const tagsQuery = 'SELECT COUNT(*) as total_tags FROM tags WHERE user_id = ?';

    return new Promise((resolve, reject) => {
      this.getDb().get(statsQuery, [userId], (err, notesRow) => {
        if (err) {
          reject(err);
          return;
        }

        this.getDb().get(tagsQuery, [userId], (err, tagsRow) => {
          if (err) {
            reject(err);
            return;
          }

          const stats = {
            totalNotes: (notesRow as any).total_notes,
            notesByType: {
              richtext: (notesRow as any).richtext_count,
              markdown: (notesRow as any).markdown_count,
              kanban: (notesRow as any).kanban_count
            },
            totalTags: (tagsRow as any).total_tags
          };

          resolve(stats);
        });
      });
    });
  }
}
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { AuthController } from '../../controllers/AuthController';
import { UserRepository } from '../../repositories/UserRepository';
import { JWTUtils } from '../../utils/jwt';

// Mock dependencies
vi.mock('../../repositories/UserRepository');
vi.mock('../../utils/jwt');

const app = express();
app.use(express.json());

// Setup routes
app.post('/auth/register', AuthController.register);
app.post('/auth/login', AuthController.login);
app.post('/auth/refresh', AuthController.refreshToken);
app.post('/auth/logout', AuthController.logout);
app.post('/auth/forgot-password', AuthController.forgotPassword);
app.post('/auth/reset-password', AuthController.resetPassword);

describe('AuthController', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('POST /auth/register', () => {
    it('should register a new user successfully', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        display_name: 'Test User',
        email_verified: false,
        created_at: new Date(),
        updated_at: new Date()
      };

      vi.mocked(UserRepository.findByEmail).mockResolvedValue(null);
      vi.mocked(UserRepository.create).mockResolvedValue(mockUser);
      vi.mocked(JWTUtils.generateTokenPair).mockReturnValue({
        accessToken: 'access-token',
        refreshToken: 'refresh-token'
      });
      vi.mocked(JWTUtils.generateEmailVerificationToken).mockReturnValue('verification-token');

      const response = await request(app)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          display_name: 'Test User'
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('accessToken');
      expect(UserRepository.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        display_name: 'Test User'
      });
    });

    it('should return 409 if user already exists', async () => {
      vi.mocked(UserRepository.findByEmail).mockResolvedValue({ id: 'existing-user' } as any);

      const response = await request(app)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          display_name: 'Test User'
        });

      expect(response.status).toBe(409);
      expect(response.body).toHaveProperty('error', 'User already exists');
    });

    it('should return 400 for invalid email format', async () => {
      const response = await request(app)
        .post('/auth/register')
        .send({
          email: 'invalid-email',
          password: 'password123',
          displayName: 'Test User'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 400 for weak password', async () => {
      const response = await request(app)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: '123',
          displayName: 'Test User'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('POST /auth/login', () => {
    it('should login user successfully with valid credentials', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        emailVerified: true,
        twoFactorEnabled: false
      };

      vi.mocked(User.findByEmail).mockResolvedValue(mockUser);
      vi.mocked(bcrypt.compare).mockResolvedValue(true);
      vi.mocked(jwt.sign).mockReturnValue('access-token');

      const response = await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('accessToken', 'access-token');
      expect(response.body).toHaveProperty('user');
    });

    it('should return 401 for invalid credentials', async () => {
      vi.mocked(User.findByEmail).mockResolvedValue(null);

      const response = await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error', 'Invalid credentials');
    });

    it('should return 401 for unverified email', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        emailVerified: false
      };

      vi.mocked(User.findByEmail).mockResolvedValue(mockUser);
      vi.mocked(bcrypt.compare).mockResolvedValue(true);

      const response = await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error', 'Email not verified');
    });

    it('should handle 2FA requirement', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'hashedPassword',
        emailVerified: true,
        twoFactorEnabled: true
      };

      vi.mocked(User.findByEmail).mockResolvedValue(mockUser);
      vi.mocked(bcrypt.compare).mockResolvedValue(true);

      const response = await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('requiresTwoFactor', true);
    });
  });

  describe('POST /auth/refresh', () => {
    it('should refresh token successfully', async () => {
      const mockPayload = { userId: 'user-123', type: 'refresh' };
      vi.mocked(jwt.verify).mockReturnValue(mockPayload);
      vi.mocked(jwt.sign).mockReturnValue('new-access-token');

      const response = await request(app)
        .post('/auth/refresh')
        .send({
          refreshToken: 'valid-refresh-token'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('accessToken', 'new-access-token');
    });

    it('should return 401 for invalid refresh token', async () => {
      vi.mocked(jwt.verify).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      const response = await request(app)
        .post('/auth/refresh')
        .send({
          refreshToken: 'invalid-token'
        });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error', 'Invalid refresh token');
    });
  });

  describe('POST /auth/forgot-password', () => {
    it('should send password reset email for valid user', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      vi.mocked(User.findByEmail).mockResolvedValue(mockUser);
      vi.mocked(jwt.sign).mockReturnValue('reset-token');

      const response = await request(app)
        .post('/auth/forgot-password')
        .send({
          email: '<EMAIL>'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Password reset email sent');
    });

    it('should return success even for non-existent user (security)', async () => {
      vi.mocked(User.findByEmail).mockResolvedValue(null);

      const response = await request(app)
        .post('/auth/forgot-password')
        .send({
          email: '<EMAIL>'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Password reset email sent');
    });
  });

  describe('POST /auth/reset-password', () => {
    it('should reset password successfully with valid token', async () => {
      const mockPayload = { userId: 'user-123', type: 'password-reset' };
      vi.mocked(jwt.verify).mockReturnValue(mockPayload);
      vi.mocked(bcrypt.hash).mockResolvedValue('newHashedPassword');
      vi.mocked(User.updatePassword).mockResolvedValue(true);

      const response = await request(app)
        .post('/auth/reset-password')
        .send({
          token: 'valid-reset-token',
          newPassword: 'newPassword123'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Password reset successfully');
    });

    it('should return 400 for invalid reset token', async () => {
      vi.mocked(jwt.verify).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      const response = await request(app)
        .post('/auth/reset-password')
        .send({
          token: 'invalid-token',
          newPassword: 'newPassword123'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Invalid or expired reset token');
    });
  });
});
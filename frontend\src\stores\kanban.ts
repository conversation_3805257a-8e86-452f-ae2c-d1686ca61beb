import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { 
  KanbanBoard, 
  KanbanColumn, 
  KanbanCard, 
  KanbanLabel,
  DragEvent,
  ColumnDragEvent,
  BoardTemplate
} from '@/types/kanban'
import { http } from '@/utils/http'

export const useKanbanStore = defineStore('kanban', () => {
  // State
  const boards = ref<KanbanBoard[]>([])
  const currentBoard = ref<KanbanBoard | null>(null)
  const templates = ref<BoardTemplate[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Actions
  const fetchBoards = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      // For demo purposes, use mock data
      // In production, uncomment the API call below
      // const response = await http.get('/api/kanban/boards')
      // boards.value = response.data
      
      // Mock data for demonstration
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay
      
      boards.value = [
        {
          id: '1',
          title: 'Project Alpha',
          description: 'Main project development board',
          columns: [
            {
              id: 'col-1',
              title: 'To Do',
              position: 0,
              boardId: '1',
              color: '#ff6b6b',
              cards: [
                {
                  id: 'card-1',
                  title: 'Design user interface',
                  description: 'Create mockups for the main dashboard',
                  position: 0,
                  columnId: 'col-1',
                  labels: [{ id: 'label-1', name: 'Design', color: '#007bff' }],
                  assignees: [{ id: 'user-1', name: 'John Doe', email: '<EMAIL>' }],
                  priority: 'high',
                  checklist: [],
                  attachments: [],
                  comments: [],
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString()
                }
              ],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: 'col-2',
              title: 'In Progress',
              position: 1,
              boardId: '1',
              color: '#ffa726',
              cards: [
                {
                  id: 'card-2',
                  title: 'Implement authentication',
                  description: 'Set up user login and registration',
                  position: 0,
                  columnId: 'col-2',
                  labels: [{ id: 'label-2', name: 'Backend', color: '#28a745' }],
                  assignees: [{ id: 'user-2', name: 'Jane Smith', email: '<EMAIL>' }],
                  priority: 'medium',
                  checklist: [],
                  attachments: [],
                  comments: [],
                  color: '#e3f2fd',
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString()
                }
              ],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: 'col-3',
              title: 'Done',
              position: 2,
              boardId: '1',
              color: '#66bb6a',
              cards: [
                {
                  id: 'card-3',
                  title: 'Set up project repository',
                  description: 'Initialize Git repository and basic structure',
                  position: 0,
                  columnId: 'col-3',
                  labels: [{ id: 'label-3', name: 'Setup', color: '#6c757d' }],
                  assignees: [{ id: 'user-1', name: 'John Doe', email: '<EMAIL>' }],
                  priority: 'low',
                  checklist: [],
                  attachments: [],
                  comments: [],
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString()
                }
              ],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ],
          labels: [
            { id: 'label-1', name: 'Design', color: '#007bff' },
            { id: 'label-2', name: 'Backend', color: '#28a745' },
            { id: 'label-3', name: 'Setup', color: '#6c757d' }
          ],
          members: [
            { id: 'user-1', name: 'John Doe', email: '<EMAIL>' },
            { id: 'user-2', name: 'Jane Smith', email: '<EMAIL>' }
          ],
          settings: {
            allowComments: true,
            allowAttachments: true,
            cardCoverImages: true,
            votingEnabled: false,
            dueDateReminders: true,
            backgroundColor: '#f8f9fa'
          },
          shareSettings: {
            isPublic: false,
            allowedUsers: [],
            permissions: {}
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          title: 'Marketing Campaign',
          description: 'Q1 marketing initiatives and campaigns',
          columns: [
            {
              id: 'col-4',
              title: 'Ideas',
              position: 0,
              boardId: '2',
              cards: [],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: 'col-5',
              title: 'In Review',
              position: 1,
              boardId: '2',
              cards: [],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: 'col-6',
              title: 'Published',
              position: 2,
              boardId: '2',
              cards: [],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ],
          labels: [],
          members: [
            { id: 'user-1', name: 'John Doe', email: '<EMAIL>' }
          ],
          settings: {
            allowComments: true,
            allowAttachments: true,
            cardCoverImages: false,
            votingEnabled: true,
            dueDateReminders: true,
            backgroundColor: '#fff3e0'
          },
          shareSettings: {
            isPublic: true,
            allowedUsers: [],
            permissions: {}
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch boards'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const getBoard = async (boardId: string): Promise<KanbanBoard> => {
    isLoading.value = true
    error.value = null
    
    try {
      // For demo purposes, use mock data
      // In production, uncomment the API call below
      // const response = await http.get(`/api/kanban/boards/${boardId}`)
      // currentBoard.value = response.data
      // return response.data
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // If boards are not loaded, load them first
      if (boards.value.length === 0) {
        await fetchBoards()
      }
      
      let board = boards.value.find(b => b.id === boardId)
      
      // If board doesn't exist, create a new one for inline editing
      if (!board) {
        board = await createBoardWithId(boardId)
      }
      
      currentBoard.value = board
      return board
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch board'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const createBoardWithId = async (boardId: string): Promise<KanbanBoard> => {
    const newBoard: KanbanBoard = {
      id: boardId,
      title: 'New Kanban Board',
      description: '',
      columns: [
        {
          id: `${boardId}-col-1`,
          title: 'To Do',
          position: 0,
          boardId: boardId,
          color: '#ff6b6b',
          cards: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: `${boardId}-col-2`,
          title: 'In Progress',
          position: 1,
          boardId: boardId,
          color: '#ffa726',
          cards: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: `${boardId}-col-3`,
          title: 'Done',
          position: 2,
          boardId: boardId,
          color: '#66bb6a',
          cards: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      labels: [
        { id: `${boardId}-label-1`, name: 'Important', color: '#ff6b6b' },
        { id: `${boardId}-label-2`, name: 'Feature', color: '#42a5f5' },
        { id: `${boardId}-label-3`, name: 'Bug', color: '#ffa726' }
      ],
      members: [
        { id: 'user-1', name: 'You', email: '<EMAIL>' }
      ],
      settings: {
        allowComments: true,
        allowAttachments: true,
        cardCoverImages: true,
        votingEnabled: false,
        dueDateReminders: true,
        backgroundColor: '#f8f9fa'
      },
      shareSettings: {
        isPublic: false,
        allowedUsers: [],
        permissions: {}
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    boards.value.push(newBoard)
    return newBoard
  }

  const createBoard = async (boardData: Partial<KanbanBoard>): Promise<KanbanBoard> => {
    isLoading.value = true
    error.value = null
    
    try {
      // For demo purposes, use mock data
      // In production, uncomment the API call below
      // const response = await http.post('/api/kanban/boards', boardData)
      // const newBoard = response.data
      // boards.value.push(newBoard)
      // return newBoard
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newBoard: KanbanBoard = {
        id: `board-${Date.now()}`,
        title: boardData.title || 'Untitled Board',
        description: boardData.description || '',
        columns: boardData.columns || [],
        labels: boardData.labels || [],
        members: boardData.members || [],
        settings: boardData.settings || {
          allowComments: true,
          allowAttachments: true,
          cardCoverImages: true,
          votingEnabled: false,
          dueDateReminders: true,
          backgroundColor: '#f8f9fa'
        },
        shareSettings: boardData.shareSettings || {
          isPublic: false,
          allowedUsers: [],
          permissions: {}
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      boards.value.push(newBoard)
      return newBoard
    } catch (err: any) {
      error.value = err.message || 'Failed to create board'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateBoard = async (boardId: string, updates: Partial<KanbanBoard>): Promise<KanbanBoard> => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await http.put(`/api/kanban/boards/${boardId}`, updates)
      const updatedBoard = response.data
      
      // Update in boards array
      const index = boards.value.findIndex(board => board.id === boardId)
      if (index !== -1) {
        boards.value[index] = updatedBoard as KanbanBoard
      }
      
      // Update current board if it's the same
      if (currentBoard.value?.id === boardId) {
        currentBoard.value = updatedBoard as KanbanBoard
      }
      
      return updatedBoard as KanbanBoard
    } catch (err: any) {
      error.value = err.message || 'Failed to update board'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteBoard = async (boardId: string): Promise<void> => {
    isLoading.value = true
    error.value = null
    
    try {
      await http.delete(`/api/kanban/boards/${boardId}`)
      boards.value = boards.value.filter(board => board.id !== boardId)
      
      if (currentBoard.value?.id === boardId) {
        currentBoard.value = null
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete board'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Column actions
  const createColumn = async (columnData: Partial<KanbanColumn>): Promise<KanbanColumn> => {
    isLoading.value = true
    error.value = null
    
    try {
      // Mock implementation - create column locally
      await new Promise(resolve => setTimeout(resolve, 200)) // Simulate API delay
      
      const newColumn: KanbanColumn = {
        id: `col-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        title: columnData.title || 'New Column',
        position: columnData.position || 0,
        boardId: columnData.boardId!,
        color: columnData.color,
        cards: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      return newColumn
    } catch (err: any) {
      error.value = err.message || 'Failed to create column'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateColumn = async (columnId: string, updates: Partial<KanbanColumn>): Promise<KanbanColumn> => {
    isLoading.value = true
    error.value = null
    
    try {
      // Mock implementation - update column locally
      await new Promise(resolve => setTimeout(resolve, 200)) // Simulate API delay
      
      // Find and update the column in the current board
      if (currentBoard.value) {
        const columnIndex = currentBoard.value.columns.findIndex(col => col.id === columnId)
        if (columnIndex !== -1) {
          const updatedColumn = { ...currentBoard.value.columns[columnIndex], ...updates, updatedAt: new Date().toISOString() }
          currentBoard.value.columns[columnIndex] = updatedColumn
          return updatedColumn
        }
      }
      
      throw new Error('Column not found')
    } catch (err: any) {
      error.value = err.message || 'Failed to update column'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteColumn = async (columnId: string): Promise<void> => {
    isLoading.value = true
    error.value = null
    
    try {
      // Mock implementation - delete column locally
      await new Promise(resolve => setTimeout(resolve, 200)) // Simulate API delay
      
      // Find and remove the column from the current board
      if (currentBoard.value) {
        const columnIndex = currentBoard.value.columns.findIndex(col => col.id === columnId)
        if (columnIndex !== -1) {
          currentBoard.value.columns.splice(columnIndex, 1)
          return
        }
      }
      
      throw new Error('Column not found')
    } catch (err: any) {
      error.value = err.message || 'Failed to delete column'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const moveColumn = async (dragEvent: ColumnDragEvent): Promise<void> => {
    isLoading.value = true
    error.value = null
    
    try {
      // Mock implementation - move column locally
      await new Promise(resolve => setTimeout(resolve, 200)) // Simulate API delay
      
      if (currentBoard.value) {
        const columnIndex = currentBoard.value.columns.findIndex(col => col.id === dragEvent.columnId)
        if (columnIndex !== -1) {
          const [column] = currentBoard.value.columns.splice(columnIndex, 1)
          column.position = dragEvent.newPosition
          currentBoard.value.columns.splice(dragEvent.newPosition, 0, column)
          
          // Update positions for other columns
          currentBoard.value.columns.forEach((col, index) => {
            col.position = index
          })
        }
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to move column'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Card actions
  const createCard = async (cardData: Partial<KanbanCard>): Promise<KanbanCard> => {
    isLoading.value = true
    error.value = null
    
    try {
      // Mock implementation - create card locally
      await new Promise(resolve => setTimeout(resolve, 200)) // Simulate API delay
      
      const newCard: KanbanCard = {
        id: `card-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        columnId: cardData.columnId!,
        title: cardData.title || 'New Card',
        description: cardData.description || '',
        position: cardData.position || 0,
        labels: cardData.labels || [],
        assignees: cardData.assignees || [],
        priority: cardData.priority || 'medium',
        dueDate: cardData.dueDate,
        checklist: cardData.checklist || [],
        attachments: cardData.attachments || [],
        comments: cardData.comments || [],
        color: cardData.color,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      return newCard
    } catch (err: any) {
      error.value = err.message || 'Failed to create card'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateCard = async (cardId: string, updates: Partial<KanbanCard>): Promise<KanbanCard> => {
    isLoading.value = true
    error.value = null
    
    try {
      // Mock implementation - update card locally
      await new Promise(resolve => setTimeout(resolve, 200)) // Simulate API delay
      
      // Find the card in the current board and update it
      if (currentBoard.value) {
        for (const column of currentBoard.value.columns) {
          const cardIndex = column.cards.findIndex(card => card.id === cardId)
          if (cardIndex !== -1) {
            const updatedCard = { ...column.cards[cardIndex], ...updates, updatedAt: new Date().toISOString() }
            column.cards[cardIndex] = updatedCard
            return updatedCard
          }
        }
      }
      
      throw new Error('Card not found')
    } catch (err: any) {
      error.value = err.message || 'Failed to update card'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteCard = async (cardId: string): Promise<void> => {
    isLoading.value = true
    error.value = null
    
    try {
      // Mock implementation - delete card locally
      await new Promise(resolve => setTimeout(resolve, 200)) // Simulate API delay
      
      // Find and remove the card from the current board
      if (currentBoard.value) {
        for (const column of currentBoard.value.columns) {
          const cardIndex = column.cards.findIndex(card => card.id === cardId)
          if (cardIndex !== -1) {
            column.cards.splice(cardIndex, 1)
            return
          }
        }
      }
      
      throw new Error('Card not found')
    } catch (err: any) {
      error.value = err.message || 'Failed to delete card'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const moveCard = async (dragEvent: DragEvent): Promise<void> => {
    isLoading.value = true
    error.value = null
    
    try {
      // Mock implementation - move card locally
      await new Promise(resolve => setTimeout(resolve, 200)) // Simulate API delay
      
      if (currentBoard.value) {
        const sourceColumn = currentBoard.value.columns.find(col => col.id === dragEvent.sourceColumnId)
        const targetColumn = currentBoard.value.columns.find(col => col.id === dragEvent.targetColumnId)
        
        if (sourceColumn && targetColumn) {
          const cardIndex = sourceColumn.cards.findIndex(card => card.id === dragEvent.cardId)
          if (cardIndex !== -1) {
            const [card] = sourceColumn.cards.splice(cardIndex, 1)
            
            // Update positions in source column if different from target
            if (sourceColumn.id !== targetColumn.id) {
              sourceColumn.cards.forEach((card, index) => {
                card.position = index
              })
            }
            
            // Update card properties
            card.columnId = dragEvent.targetColumnId
            card.updatedAt = new Date().toISOString()
            
            // Insert card at the new position
            const insertPosition = Math.min(dragEvent.newPosition, targetColumn.cards.length)
            targetColumn.cards.splice(insertPosition, 0, card)
            
            // Update positions for all cards in target column
            targetColumn.cards.forEach((card, index) => {
              card.position = index
            })
            
            console.log('Card moved successfully:', {
              cardId: dragEvent.cardId,
              from: dragEvent.sourceColumnId,
              to: dragEvent.targetColumnId,
              newPosition: insertPosition,
              totalCards: targetColumn.cards.length
            })
          }
        }
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to move card'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Label actions
  const createLabel = async (boardId: string, labelData: Partial<KanbanLabel>): Promise<KanbanLabel> => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await http.post(`/api/kanban/boards/${boardId}/labels`, labelData)
      return response.data as KanbanLabel
    } catch (err: any) {
      error.value = err.message || 'Failed to create label'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateLabel = async (labelId: string, updates: Partial<KanbanLabel>): Promise<KanbanLabel> => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await http.put(`/api/kanban/labels/${labelId}`, updates)
      return response.data as KanbanLabel
    } catch (err: any) {
      error.value = err.message || 'Failed to update label'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteLabel = async (labelId: string): Promise<void> => {
    isLoading.value = true
    error.value = null
    
    try {
      await http.delete(`/api/kanban/labels/${labelId}`)
    } catch (err: any) {
      error.value = err.message || 'Failed to delete label'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Template actions
  const fetchTemplates = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await http.get('/api/kanban/templates')
      templates.value = response.data as BoardTemplate[]
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch templates'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const createBoardFromTemplate = async (templateId: string, boardData: Partial<KanbanBoard>): Promise<KanbanBoard> => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await http.post(`/api/kanban/templates/${templateId}/create-board`, boardData)
      const newBoard = response.data as KanbanBoard
      boards.value.push(newBoard)
      return newBoard
    } catch (err: any) {
      error.value = err.message || 'Failed to create board from template'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Search and filter
  const searchCards = async (boardId: string, query: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await http.get(`/api/kanban/boards/${boardId}/search`, {
        params: { q: query } as any
      })
      return response.data
    } catch (err: any) {
      error.value = err.message || 'Failed to search cards'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Utility functions
  const clearError = () => {
    error.value = null
  }

  const reset = () => {
    boards.value = []
    currentBoard.value = null
    templates.value = []
    isLoading.value = false
    error.value = null
  }

  return {
    // State
    boards,
    currentBoard,
    templates,
    isLoading,
    error,
    
    // Actions
    fetchBoards,
    getBoard,
    createBoard,
    createBoardWithId,
    updateBoard,
    deleteBoard,
    
    // Column actions
    createColumn,
    updateColumn,
    deleteColumn,
    moveColumn,
    
    // Card actions
    createCard,
    updateCard,
    deleteCard,
    moveCard,
    
    // Label actions
    createLabel,
    updateLabel,
    deleteLabel,
    
    // Template actions
    fetchTemplates,
    createBoardFromTemplate,
    
    // Search
    searchCards,
    
    // Utilities
    clearError,
    reset
  }
})
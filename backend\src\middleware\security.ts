import { Request, Response, NextFunction } from 'express';
import { JWTUtils } from '../utils/jwt';
import crypto from 'crypto';

// Cache to prevent repeated fingerprint mismatch logging
const fingerprintMismatchCache = new Set<string>();

// CSRF Protection
export interface CSRFOptions {
  secret?: string;
  cookieName?: string;
  headerName?: string;
  ignoreMethods?: string[];
}

const defaultCSRFOptions: Required<CSRFOptions> = {
  secret: process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production',
  cookieName: 'csrf-token',
  headerName: 'x-csrf-token',
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS']
};

export function csrfProtection(options: CSRFOptions = {}) {
  const config = { ...defaultCSRFOptions, ...options };

  return (req: Request, res: Response, next: NextFunction) => {
    // Skip CSRF protection for ignored methods
    if (config.ignoreMethods.includes(req.method)) {
      return next();
    }

    // Skip CSRF protection for API endpoints using JWT (they have their own protection)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return next();
    }

    // Generate CSRF token if not present
    let csrfToken = req.cookies[config.cookieName];
    if (!csrfToken) {
      csrfToken = generateCSRFToken(config.secret);
      res.cookie(config.cookieName, csrfToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });
    }

    // Verify CSRF token for state-changing requests
    const providedToken = req.headers[config.headerName] as string || req.body._csrf;
    if (!providedToken || !verifyCSRFToken(providedToken, config.secret)) {
      return res.status(403).json({
        error: 'Invalid CSRF token',
        code: 'CSRF_TOKEN_INVALID'
      });
    }

    next();
  };
}

function generateCSRFToken(secret: string): string {
  const timestamp = Date.now().toString();
  const randomBytes = crypto.randomBytes(16).toString('hex');
  const payload = `${timestamp}:${randomBytes}`;
  const signature = crypto.createHmac('sha256', secret).update(payload).digest('hex');
  return `${payload}:${signature}`;
}

function verifyCSRFToken(token: string, secret: string): boolean {
  try {
    const parts = token.split(':');
    if (parts.length !== 3) return false;

    const [timestamp, randomBytes, signature] = parts;
    const payload = `${timestamp}:${randomBytes}`;
    const expectedSignature = crypto.createHmac('sha256', secret).update(payload).digest('hex');

    // Verify signature
    if (!crypto.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'))) {
      return false;
    }

    // Check if token is not too old (24 hours)
    const tokenTime = parseInt(timestamp);
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    return (now - tokenTime) <= maxAge;
  } catch (error) {
    return false;
  }
}

// Security Headers Middleware
export function securityHeaders() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Content Security Policy
    res.setHeader('Content-Security-Policy', 
      "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com; " +
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
      "font-src 'self' https://fonts.gstatic.com; " +
      "img-src 'self' data: https:; " +
      "connect-src 'self' https://api.github.com https://accounts.google.com; " +
      "frame-src 'self' https://accounts.google.com; " +
      "object-src 'none'; " +
      "base-uri 'self';"
    );

    // X-Frame-Options (prevent clickjacking)
    res.setHeader('X-Frame-Options', 'DENY');

    // X-Content-Type-Options (prevent MIME sniffing)
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // Referrer Policy
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

    // Permissions Policy
    res.setHeader('Permissions-Policy', 
      'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()'
    );

    // X-XSS-Protection (legacy, but still useful for older browsers)
    res.setHeader('X-XSS-Protection', '1; mode=block');

    next();
  };
}

// Input Sanitization Middleware
export function sanitizeInput() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Recursively sanitize all string inputs
    function sanitizeObject(obj: any): any {
      if (typeof obj === 'string') {
        return sanitizeString(obj);
      } else if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      } else if (obj && typeof obj === 'object') {
        const sanitized: any = {};
        for (const [key, value] of Object.entries(obj)) {
          sanitized[key] = sanitizeObject(value);
        }
        return sanitized;
      }
      return obj;
    }

    // Sanitize request body
    if (req.body) {
      req.body = sanitizeObject(req.body);
    }

    // Sanitize query parameters
    if (req.query) {
      req.query = sanitizeObject(req.query);
    }

    next();
  };
}

function sanitizeString(input: string): string {
  // Remove null bytes
  let sanitized = input.replace(/\0/g, '');
  
  // Trim whitespace
  sanitized = sanitized.trim();
  
  // Remove potentially dangerous HTML/script tags (basic protection)
  sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  sanitized = sanitized.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '');
  sanitized = sanitized.replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '');
  sanitized = sanitized.replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '');
  
  // Remove javascript: and data: URLs
  sanitized = sanitized.replace(/javascript:/gi, '');
  sanitized = sanitized.replace(/data:/gi, '');
  
  return sanitized;
}

// Session Security Middleware
export function sessionSecurity() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Add session security headers
    if (req.user) {
      // Set secure session cookie attributes
      res.cookie('session-active', '1', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 15 * 60 * 1000 // 15 minutes
      });

      // Add user context to response headers (for debugging, remove in production)
      if (process.env.NODE_ENV !== 'production') {
        res.setHeader('X-User-ID', req.user.id);
      }
    }

    next();
  };
}

// JWT Security Enhancement
export function enhancedJWTValidation() {
  return async (req: Request, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return next();
    }

    try {
      // Verify token structure and signature with IP validation
      const payload = JWTUtils.verifyAccessToken(token, { 
        ipAddress: req.ip 
      });
      
      // Additional security checks
      
      // Check token age (additional check beyond expiration)
      const tokenAge = Date.now() - ((payload.iat || 0) * 1000);
      const maxTokenAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (tokenAge > maxTokenAge) {
        return res.status(401).json({
          error: 'Token too old, please refresh',
          code: 'TOKEN_TOO_OLD'
        });
      }

      // Check for suspicious token patterns
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
      req.tokenHash = tokenHash;

      // Check device consistency (if available)
      if (payload.deviceId) {
        const currentDeviceId = generateDeviceFingerprint(req);
        if (payload.deviceId !== currentDeviceId) {
          // Log suspicious activity but don't block (device fingerprints can change)
          // Only log once per user to avoid spam
          const cacheKey = `${payload.userId}-${currentDeviceId}`;
          if (!fingerprintMismatchCache.has(cacheKey)) {
            console.warn(`Device fingerprint mismatch for user ${payload.userId}`);
            fingerprintMismatchCache.add(cacheKey);
            
            // Clear cache after 1 hour to allow re-logging if needed
            setTimeout(() => {
              fingerprintMismatchCache.delete(cacheKey);
            }, 60 * 60 * 1000);
          }
        }
      }

      next();
    } catch (error) {
      // Token is invalid, but let the main auth middleware handle it
      next();
    }
  };
}

function generateDeviceFingerprint(req: Request): string {
  const userAgent = req.headers['user-agent'] || '';
  const acceptLanguage = req.headers['accept-language'] || '';
  const acceptEncoding = req.headers['accept-encoding'] || '';
  
  const fingerprint = `${userAgent}-${acceptLanguage}-${acceptEncoding}`;
  return crypto.createHash('sha256').update(fingerprint).digest('hex').substring(0, 16);
}

// Brute Force Protection
interface BruteForceAttempt {
  count: number;
  lastAttempt: number;
  blocked: boolean;
}

const bruteForceStore = new Map<string, BruteForceAttempt>();

export interface BruteForceOptions {
  maxAttempts?: number;
  windowMs?: number;
  blockDurationMs?: number;
  keyGenerator?: (req: Request) => string;
}

export function bruteForceProtection(options: BruteForceOptions = {}): (req: Request, res: Response, next: NextFunction) => void {
  const {
    maxAttempts = 5,
    windowMs = 15 * 60 * 1000, // 15 minutes
    blockDurationMs = 60 * 60 * 1000, // 1 hour
    keyGenerator = (req: Request) => `${req.ip}:${req.body.email || 'unknown'}`
  } = options;

  return (req: Request, res: Response, next: NextFunction) => {
    const key = keyGenerator(req);
    const now = Date.now();
    
    let attempt = bruteForceStore.get(key);
    
    if (!attempt) {
      attempt = { count: 0, lastAttempt: now, blocked: false };
      bruteForceStore.set(key, attempt);
    }

    // Check if currently blocked
    if (attempt.blocked && (now - attempt.lastAttempt) < blockDurationMs) {
      return res.status(429).json({
        error: 'Too many failed attempts. Account temporarily blocked.',
        code: 'ACCOUNT_BLOCKED',
        retryAfter: Math.ceil((blockDurationMs - (now - attempt.lastAttempt)) / 1000)
      });
    }

    // Reset if window has passed
    if ((now - attempt.lastAttempt) > windowMs) {
      attempt.count = 0;
      attempt.blocked = false;
    }

    // Override response to track failures
    const originalJson = res.json;
    res.json = function(body: any) {
      if (res.statusCode === 401 || res.statusCode === 403) {
        attempt!.count++;
        attempt!.lastAttempt = now;
        
        if (attempt!.count >= maxAttempts) {
          attempt!.blocked = true;
        }
        
        bruteForceStore.set(key, attempt!);
      } else if (res.statusCode < 400) {
        // Success - reset counter
        bruteForceStore.delete(key);
      }
      
      return originalJson.call(this, body);
    };

    next();
    return;
  };
}

// IP Whitelist/Blacklist Middleware
export interface IPFilterOptions {
  whitelist?: string[];
  blacklist?: string[];
  mode?: 'whitelist' | 'blacklist' | 'both';
}

export function ipFilter(options: IPFilterOptions = {}): (req: Request, res: Response, next: NextFunction) => void {
  const { whitelist = [], blacklist = [], mode = 'blacklist' } = options;

  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    
    // Check blacklist
    if ((mode === 'blacklist' || mode === 'both') && blacklist.length > 0) {
      if (isIPInList(clientIP, blacklist)) {
        return res.status(403).json({
          error: 'Access denied from this IP address',
          code: 'IP_BLOCKED'
        });
      }
    }

    // Check whitelist
    if ((mode === 'whitelist' || mode === 'both') && whitelist.length > 0) {
      if (!isIPInList(clientIP, whitelist)) {
        return res.status(403).json({
          error: 'Access denied. IP not in whitelist',
          code: 'IP_NOT_WHITELISTED'
        });
      }
    }

    next();
    return;
  };
}

function isIPInList(ip: string, list: string[]): boolean {
  return list.some(listIP => {
    // Support CIDR notation and exact matches
    if (listIP.includes('/')) {
      // CIDR notation - simplified check (would need proper CIDR library for production)
      const [network, prefixLength] = listIP.split('/');
      // For now, just do exact match - implement proper CIDR matching in production
      return ip === network;
    } else {
      return ip === listIP;
    }
  });
}

// Request Size Limiting
export function requestSizeLimit(maxSize: number = 10 * 1024 * 1024): (req: Request, res: Response, next: NextFunction) => void { // 10MB default
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.headers['content-length'] || '0');
    
    if (contentLength > maxSize) {
      return res.status(413).json({
        error: 'Request entity too large',
        code: 'REQUEST_TOO_LARGE',
        maxSize: maxSize
      });
    }

    next();
    return;
  };
}

// Extend Request interface for additional security properties
declare global {
  namespace Express {
    interface Request {
      tokenHash?: string;
    }
  }
}
<template>
  <div class="admin-performance-view">
    <div class="performance-header">
      <h1>Performance Monitoring</h1>
      <div class="header-actions">
        <button 
          @click="refreshData" 
          :disabled="isLoading"
          class="btn btn-primary"
        >
          <i class="icon-refresh" :class="{ 'spinning': isLoading }"></i>
          Refresh
        </button>
        <button 
          @click="exportData" 
          class="btn btn-secondary"
        >
          <i class="icon-download"></i>
          Export Data
        </button>
        <button 
          @click="clearMetrics" 
          class="btn btn-danger"
          :disabled="isLoading"
        >
          <i class="icon-trash"></i>
          Clear Metrics
        </button>
      </div>
    </div>

    <!-- Performance Overview Cards -->
    <div class="performance-overview" v-if="overview">
      <div class="metric-card">
        <div class="metric-header">
          <h3>Database Performance</h3>
          <div class="metric-status" :class="getDatabaseStatus()">
            {{ getDatabaseStatusText() }}
          </div>
        </div>
        <div class="metric-content">
          <div class="metric-item">
            <span class="label">Total Queries</span>
            <span class="value">{{ overview.database.totalQueries.toLocaleString() }}</span>
          </div>
          <div class="metric-item">
            <span class="label">Cache Hit Rate</span>
            <span class="value" :class="getCacheHitRateClass()">
              {{ overview.database.cacheHitRate.toFixed(1) }}%
            </span>
          </div>
          <div class="metric-item">
            <span class="label">Avg Execution Time</span>
            <span class="value" :class="getExecutionTimeClass()">
              {{ overview.database.averageExecutionTime.toFixed(2) }}ms
            </span>
          </div>
          <div class="metric-item">
            <span class="label">Slow Queries</span>
            <span class="value" :class="getSlowQueriesClass()">
              {{ overview.database.slowQueries }}
            </span>
          </div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <h3>Cache Status</h3>
          <div class="metric-status" :class="overview.cache.connected ? 'good' : 'danger'">
            {{ overview.cache.connected ? 'Connected' : 'Disconnected' }}
          </div>
        </div>
        <div class="metric-content">
          <div class="metric-item">
            <span class="label">Type</span>
            <span class="value">{{ overview.cache.type }}</span>
          </div>
          <div class="metric-item" v-if="overview.cache.memory">
            <span class="label">Memory Usage</span>
            <span class="value">{{ overview.cache.memory }}</span>
          </div>
          <div class="metric-item" v-if="overview.cache.keyspace">
            <span class="label">Keyspace</span>
            <span class="value">{{ overview.cache.keyspace }}</span>
          </div>
          <div class="metric-item" v-if="overview.cache.size">
            <span class="label">Keys</span>
            <span class="value">{{ overview.cache.size.toLocaleString() }}</span>
          </div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <h3>System Resources</h3>
          <div class="metric-status" :class="getSystemStatus()">
            {{ getSystemStatusText() }}
          </div>
        </div>
        <div class="metric-content" v-if="systemStats">
          <div class="metric-item">
            <span class="label">Memory Usage</span>
            <span class="value">{{ formatBytes(systemStats.memory.heapUsed) }} / {{ formatBytes(systemStats.memory.heapTotal) }}</span>
          </div>
          <div class="metric-item">
            <span class="label">Uptime</span>
            <span class="value">{{ formatUptime(systemStats.uptime) }}</span>
          </div>
          <div class="metric-item">
            <span class="label">Node Version</span>
            <span class="value">{{ systemStats.nodeVersion }}</span>
          </div>
          <div class="metric-item">
            <span class="label">Platform</span>
            <span class="value">{{ systemStats.platform }} ({{ systemStats.arch }})</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div v-if="overview" class="performance-charts">
      <!-- Database Performance Chart -->
      <div class="chart-container">
        <h3>Database Performance Trends</h3>
        <div class="chart-wrapper">
          <canvas ref="databaseChart"></canvas>
        </div>
      </div>

      <!-- System Resources Chart -->
      <div class="chart-container">
        <h3>System Memory Usage</h3>
        <div class="chart-wrapper">
          <canvas ref="memoryChart"></canvas>
        </div>
      </div>

      <!-- Query Execution Time Chart -->
      <div class="chart-container">
        <h3>Query Execution Times</h3>
        <div class="chart-wrapper">
          <canvas ref="queryChart"></canvas>
        </div>
      </div>

      <!-- Cache Hit Rate Chart -->
      <div class="chart-container">
        <h3>Cache Performance</h3>
        <div class="chart-wrapper">
          <canvas ref="cacheChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Slow Queries Section -->
    <div v-if="slowQueries.length > 0" class="slow-queries-section">
      <h3>Recent Slow Queries</h3>
      <div class="table-container">
        <table class="slow-queries-table">
          <thead>
            <tr>
              <th>Query</th>
              <th>Execution Time</th>
              <th>Rows Affected</th>
              <th>Cached</th>
              <th>Timestamp</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="query in slowQueries" :key="`${query.query}-${query.timestamp}`">
              <td class="query-cell">
                <code class="query-text">{{ truncateQuery(query.query) }}</code>
              </td>
              <td class="time-cell" :class="getExecutionTimeStatus(query.executionTime)">
                {{ query.executionTime.toFixed(2) }}ms
              </td>
              <td>{{ query.rowsAffected }}</td>
              <td>
                <span class="cache-badge" :class="query.cached ? 'cached' : 'not-cached'">
                  {{ query.cached ? 'Cached' : 'Not Cached' }}
                </span>
              </td>
              <td>{{ formatTimestamp(query.timestamp) }}</td>
              <td>
                <button 
                  class="btn btn-sm btn-secondary" 
                  @click="analyzeQuery(query)"
                >
                  Analyze
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Performance Recommendations -->
    <div v-if="recommendations.length > 0" class="recommendations-section">
      <h3>Performance Recommendations</h3>
      <div class="recommendations-list">
        <div 
          v-for="(recommendation, index) in recommendations" 
          :key="index"
          class="recommendation-item"
        >
          <i class="icon-lightbulb"></i>
          <span>{{ recommendation }}</span>
        </div>
      </div>
    </div>

    <!-- Query Analysis Modal -->
    <div v-if="showAnalysisModal" class="modal-overlay" @click="closeAnalysisModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>Query Analysis</h3>
          <button class="modal-close" @click="closeAnalysisModal">&times;</button>
        </div>
        <div class="modal-body">
          <div v-if="queryAnalysis" class="analysis-content">
            <div class="analysis-section">
              <h4>Query</h4>
              <pre class="query-code">{{ queryAnalysis.query }}</pre>
            </div>
            
            <div v-if="queryAnalysis.params.length > 0" class="analysis-section">
              <h4>Parameters</h4>
              <pre class="params-code">{{ JSON.stringify(queryAnalysis.params, null, 2) }}</pre>
            </div>
            
            <div v-if="queryAnalysis.analysis" class="analysis-section">
              <h4>Execution Plan</h4>
              <div class="plan-table">
                <table>
                  <thead>
                    <tr>
                      <th>Step</th>
                      <th>Operation</th>
                      <th>Detail</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(step, index) in queryAnalysis.analysis" :key="index">
                      <td>{{ index + 1 }}</td>
                      <td>{{ step.operation || 'N/A' }}</td>
                      <td>{{ step.detail || 'N/A' }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div v-else class="loading-analysis">
            <i class="icon-spinner spinning"></i>
            <span>Analyzing query...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="error-message">
      <i class="icon-alert"></i>
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted, nextTick } from 'vue'
import { performanceApiService } from '../services/performanceApiService'
import type { 
  PerformanceOverview, 
  SlowQuery, 
  SystemStats, 
  QueryAnalysis 
} from '../services/performanceApiService'
import {
  Chart,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  LineController,
  BarController,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  Filler
} from 'chart.js'
import 'chartjs-adapter-date-fns'

// Register Chart.js components
Chart.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  LineController,
  BarController,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  Filler
)

// Reactive state
const isLoading = ref(false)
const error = ref<string | null>(null)
const overview = ref<PerformanceOverview | null>(null)
const slowQueries = ref<SlowQuery[]>([])
const systemStats = ref<SystemStats | null>(null)
const recommendations = ref<string[]>([])
const showAnalysisModal = ref(false)
const queryAnalysis = ref<QueryAnalysis | null>(null)

// Chart references
const databaseChart = ref<HTMLCanvasElement>()
const memoryChart = ref<HTMLCanvasElement>()
const queryChart = ref<HTMLCanvasElement>()
const cacheChart = ref<HTMLCanvasElement>()

// Chart instances
let databaseChartInstance: Chart | null = null
let memoryChartInstance: Chart | null = null
let queryChartInstance: Chart | null = null
let cacheChartInstance: Chart | null = null

// Chart data
const chartData = ref({
  queryTrends: [] as Array<{ time: string; avgTime: number; count: number }>,
  memoryUsage: [] as Array<{ time: string; used: number; total: number }>,
  cacheHitRate: [] as Array<{ time: string; hitRate: number; totalQueries: number }>,
  executionTimes: [] as Array<{ time: string; min: number; avg: number; max: number }>
})

// Auto-refresh interval
let refreshInterval: number | null = null

// Methods
const loadPerformanceData = async () => {
  isLoading.value = true
  error.value = null
  
  try {
    // Load overview data
    overview.value = await performanceApiService.getOverview()
    
    // Load slow queries
    slowQueries.value = await performanceApiService.getSlowQueries(10)
    
    // Load system stats
    systemStats.value = await performanceApiService.getSystemStats()
    
    // Generate recommendations
    const report = await performanceApiService.getReport()
    recommendations.value = performanceApiService.generateRecommendations(report)
    
    // Update chart data
    updateChartData()
    
    // Render charts after data is loaded
    await nextTick()
    renderCharts()
    
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load performance data'
    console.error('Performance data loading error:', err)
  } finally {
    isLoading.value = false
  }
}

const updateChartData = () => {
  const now = new Date()
  const timeLabel = now.toLocaleTimeString()
  
  if (overview.value && systemStats.value) {
    // Update query trends
    chartData.value.queryTrends.push({
      time: timeLabel,
      avgTime: overview.value.database.averageExecutionTime,
      count: overview.value.database.totalQueries
    })
    
    // Update memory usage
    chartData.value.memoryUsage.push({
      time: timeLabel,
      used: systemStats.value.memory.heapUsed,
      total: systemStats.value.memory.heapTotal
    })
    
    // Update cache hit rate
    chartData.value.cacheHitRate.push({
      time: timeLabel,
      hitRate: overview.value.database.cacheHitRate,
      totalQueries: overview.value.database.totalQueries
    })
    
    // Update execution times
    chartData.value.executionTimes.push({
      time: timeLabel,
      min: overview.value.database.minExecutionTime,
      avg: overview.value.database.averageExecutionTime,
      max: overview.value.database.maxExecutionTime
    })
    
    // Keep only last 20 data points
    const maxPoints = 20
    if (chartData.value.queryTrends.length > maxPoints) {
      chartData.value.queryTrends = chartData.value.queryTrends.slice(-maxPoints)
      chartData.value.memoryUsage = chartData.value.memoryUsage.slice(-maxPoints)
      chartData.value.cacheHitRate = chartData.value.cacheHitRate.slice(-maxPoints)
      chartData.value.executionTimes = chartData.value.executionTimes.slice(-maxPoints)
    }
  }
}

const renderCharts = () => {
  renderDatabaseChart()
  renderMemoryChart()
  renderQueryChart()
  renderCacheChart()
}

const renderDatabaseChart = () => {
  if (!databaseChart.value) return
  
  if (databaseChartInstance) {
    databaseChartInstance.destroy()
  }
  
  const ctx = databaseChart.value.getContext('2d')!
  databaseChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: chartData.value.executionTimes.map(d => d.time),
      datasets: [
        {
          label: 'Average Execution Time (ms)',
          data: chartData.value.executionTimes.map(d => d.avg),
          borderColor: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true,
          tension: 0.4
        },
        {
          label: 'Max Execution Time (ms)',
          data: chartData.value.executionTimes.map(d => d.max),
          borderColor: '#ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          fill: false,
          tension: 0.4
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Time (ms)'
          }
        },
        x: {
          title: {
            display: true,
            text: 'Time'
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top'
        }
      }
    }
  })
}

const renderMemoryChart = () => {
  if (!memoryChart.value) return
  
  if (memoryChartInstance) {
    memoryChartInstance.destroy()
  }
  
  const ctx = memoryChart.value.getContext('2d')!
  memoryChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: chartData.value.memoryUsage.map(d => d.time),
      datasets: [
        {
          label: 'Used Memory',
          data: chartData.value.memoryUsage.map(d => d.used / 1024 / 1024), // Convert to MB
          borderColor: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          fill: true,
          tension: 0.4
        },
        {
          label: 'Total Memory',
          data: chartData.value.memoryUsage.map(d => d.total / 1024 / 1024), // Convert to MB
          borderColor: '#6b7280',
          backgroundColor: 'rgba(107, 114, 128, 0.1)',
          fill: false,
          tension: 0.4
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Memory (MB)'
          }
        },
        x: {
          title: {
            display: true,
            text: 'Time'
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top'
        }
      }
    }
  })
}

const renderQueryChart = () => {
  if (!queryChart.value) return
  
  if (queryChartInstance) {
    queryChartInstance.destroy()
  }
  
  const ctx = queryChart.value.getContext('2d')!
  queryChartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: chartData.value.queryTrends.map(d => d.time),
      datasets: [
        {
          label: 'Query Count',
          data: chartData.value.queryTrends.map(d => d.count),
          backgroundColor: 'rgba(59, 130, 246, 0.6)',
          borderColor: '#3b82f6',
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Query Count'
          }
        },
        x: {
          title: {
            display: true,
            text: 'Time'
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top'
        }
      }
    }
  })
}

const renderCacheChart = () => {
  if (!cacheChart.value) return
  
  if (cacheChartInstance) {
    cacheChartInstance.destroy()
  }
  
  const ctx = cacheChart.value.getContext('2d')!
  cacheChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: chartData.value.cacheHitRate.map(d => d.time),
      datasets: [
        {
          label: 'Cache Hit Rate (%)',
          data: chartData.value.cacheHitRate.map(d => d.hitRate),
          borderColor: '#8b5cf6',
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          fill: true,
          tension: 0.4
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          title: {
            display: true,
            text: 'Hit Rate (%)'
          }
        },
        x: {
          title: {
            display: true,
            text: 'Time'
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top'
        }
      }
    }
  })
}

const refreshData = () => {
  loadPerformanceData()
}

const exportData = async () => {
  try {
    const blob = await performanceApiService.exportData()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (err) {
    error.value = 'Failed to export performance data'
  }
}

const clearMetrics = async () => {
  if (!confirm('Are you sure you want to clear all performance metrics?')) {
    return
  }
  
  try {
    await performanceApiService.clearMetrics()
    chartData.value.queryTrends = []
    chartData.value.memoryUsage = []
    await loadPerformanceData()
  } catch (err) {
    error.value = 'Failed to clear metrics'
  }
}

// Status helpers
const getDatabaseStatus = () => {
  if (!overview.value) return 'unknown'
  const { averageExecutionTime, cacheHitRate, slowQueries, totalQueries } = overview.value.database
  
  if (averageExecutionTime > 200 || cacheHitRate < 50 || (slowQueries / totalQueries) > 0.1) {
    return 'danger'
  }
  if (averageExecutionTime > 100 || cacheHitRate < 70 || (slowQueries / totalQueries) > 0.05) {
    return 'warning'
  }
  return 'good'
}

const getDatabaseStatusText = () => {
  const status = getDatabaseStatus()
  return status === 'good' ? 'Healthy' : status === 'warning' ? 'Warning' : 'Critical'
}

const getSystemStatus = () => {
  if (!systemStats.value) return 'unknown'
  const memoryUsage = (systemStats.value.memory.heapUsed / systemStats.value.memory.heapTotal) * 100
  
  if (memoryUsage > 85) return 'danger'
  if (memoryUsage > 70) return 'warning'
  return 'good'
}

const getSystemStatusText = () => {
  const status = getSystemStatus()
  return status === 'good' ? 'Healthy' : status === 'warning' ? 'Warning' : 'Critical'
}

const getCacheHitRateClass = () => {
  if (!overview.value) return ''
  const rate = overview.value.database.cacheHitRate
  if (rate >= 80) return 'good'
  if (rate >= 60) return 'warning'
  return 'danger'
}

const getExecutionTimeClass = () => {
  if (!overview.value) return ''
  const time = overview.value.database.averageExecutionTime
  if (time <= 50) return 'good'
  if (time <= 100) return 'warning'
  return 'danger'
}

const getSlowQueriesClass = () => {
  if (!overview.value) return ''
  const { slowQueries, totalQueries } = overview.value.database
  const rate = slowQueries / totalQueries
  if (rate <= 0.02) return 'good'
  if (rate <= 0.05) return 'warning'
  return 'danger'
}

// Utility functions
const formatBytes = (bytes: number): string => {
  return performanceApiService.formatBytes(bytes)
}

const formatUptime = (seconds: number): string => {
  return performanceApiService.formatUptime(seconds)
}

const truncateQuery = (query: string): string => {
  return query.length > 100 ? query.substring(0, 100) + '...' : query
}

const formatTimestamp = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString()
}

const getExecutionTimeStatus = (time: number): string => {
  if (time <= 50) return 'good'
  if (time <= 200) return 'warning'
  return 'danger'
}

const analyzeQuery = async (query: SlowQuery) => {
  showAnalysisModal.value = true
  queryAnalysis.value = null
  
  try {
    queryAnalysis.value = await performanceApiService.analyzeQuery(query.query, query.params)
  } catch (err) {
    console.error('Failed to analyze query:', err)
    error.value = 'Failed to analyze query'
  }
}

const closeAnalysisModal = () => {
  showAnalysisModal.value = false
  queryAnalysis.value = null
}

// Lifecycle
onMounted(() => {
  loadPerformanceData()
  
  // Set up auto-refresh every 30 seconds
  refreshInterval = window.setInterval(() => {
    loadPerformanceData()
  }, 30000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
  
  // Clean up chart instances
  if (databaseChartInstance) databaseChartInstance.destroy()
  if (memoryChartInstance) memoryChartInstance.destroy()
  if (queryChartInstance) queryChartInstance.destroy()
  if (cacheChartInstance) cacheChartInstance.destroy()
})
</script>

<style scoped>
.admin-performance-view {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.performance-header h1 {
  margin: 0;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #4b5563;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.performance-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.metric-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.metric-status {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.metric-status.good {
  background-color: #dcfce7;
  color: #166534;
}

.metric-status.warning {
  background-color: #fef3c7;
  color: #92400e;
}

.metric-status.danger {
  background-color: #fee2e2;
  color: #991b1b;
}

.metric-content {
  display: grid;
  gap: 0.75rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-item .label {
  color: #6b7280;
  font-size: 0.875rem;
}

.metric-item .value {
  font-weight: 600;
  color: #1f2937;
}

.metric-item .value.good {
  color: #059669;
}

.metric-item .value.warning {
  color: #d97706;
}

.metric-item .value.danger {
  color: #dc2626;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
  margin-top: 1rem;
}

.performance-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.chart-container {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  height: 300px;
  display: flex;
  flex-direction: column;
}

.chart-container h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
  flex-shrink: 0;
}

.chart-wrapper {
  flex: 1;
  position: relative;
  height: 100%;
  min-height: 200px;
}

.chart-container canvas {
  width: 100% !important;
  height: 100% !important;
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  background-color: #f9fafb;
  border-radius: 0.375rem;
}

.simple-chart {
  height: 200px;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: end;
  gap: 2px;
  padding: 1rem 0;
}

.chart-bar {
  flex: 1;
  background-color: #3b82f6;
  border-radius: 2px 2px 0 0;
  min-height: 4px;
  transition: all 0.2s;
}

.chart-bar:hover {
  background-color: #2563eb;
}

.chart-bar.memory-bar {
  background-color: #10b981;
}

.chart-bar.memory-bar:hover {
  background-color: #059669;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6b7280;
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
}

.slow-queries-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.slow-queries-section h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.table-container {
  overflow-x: auto;
}

.slow-queries-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.slow-queries-table th,
.slow-queries-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.slow-queries-table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.query-cell {
  max-width: 300px;
}

.query-text {
  display: block;
  background-color: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  word-break: break-all;
}

.time-cell.good {
  color: #059669;
}

.time-cell.warning {
  color: #d97706;
}

.time-cell.danger {
  color: #dc2626;
}

.cache-badge {
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.cache-badge.cached {
  background-color: #dcfce7;
  color: #166534;
}

.cache-badge.not-cached {
  background-color: #fee2e2;
  color: #991b1b;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.recommendations-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.recommendations-section h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.recommendations-list {
  display: grid;
  gap: 0.75rem;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #fffbeb;
  border: 1px solid #fed7aa;
  border-radius: 0.375rem;
}

.recommendation-item i {
  color: #d97706;
  margin-top: 0.125rem;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 0.5rem;
  max-width: 800px;
  max-height: 80vh;
  width: 90%;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.analysis-section {
  margin-bottom: 1.5rem;
}

.analysis-section h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
}

.query-code,
.params-code {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.375rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.plan-table {
  overflow-x: auto;
}

.plan-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.plan-table th,
.plan-table td {
  padding: 0.5rem;
  text-align: left;
  border: 1px solid #e5e7eb;
}

.plan-table th {
  background-color: #f9fafb;
  font-weight: 600;
}

.loading-analysis {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
}

@media (max-width: 768px) {
  .admin-performance-view {
    padding: 1rem;
  }
  
  .performance-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .performance-overview {
    grid-template-columns: 1fr;
  }
  
  .performance-charts {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    width: 95%;
    margin: 1rem;
  }
}
</style>
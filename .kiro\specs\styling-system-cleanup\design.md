# Design Document

## Overview

This design outlines the modernization of the styling system by organizing CSS into separate files, implementing Bulmaswatch themes for light/dark mode support, and creating a maintainable architecture. The current system uses a custom minimal CSS framework with basic dark mode support through media queries. We will enhance this with proper theme management, organized file structure, and professional Bulmaswatch themes.

## Architecture

### Current State Analysis

The application currently uses:
- `frontend/src/styles/minimal.css` - Custom CSS framework with Bulma-like classes
- `frontend/src/styles/fontawesome-custom.css` - Custom FontAwesome build
- Inline styles in Vue components with scoped CSS
- Basic dark mode support via `@media (prefers-color-scheme: dark)`
- Theme preference storage in localStorage via `authStore.saveThemePreference()`

### Target Architecture

```
frontend/src/styles/
├── base/
│   ├── reset.css           # CSS reset and normalize
│   ├── typography.css      # Font definitions and text styles
│   └── variables.css       # CSS custom properties for themes
├── themes/
│   ├── light.css          # Light theme (default Bulmaswatch)
│   ├── dark.css           # Dark theme (Bulmaswatch dark variant)
│   ├── themes.css         # Theme switching logic and transitions
│   └── bulmaswatch/       # Downloaded Bulmaswatch theme files
│       ├── default.css    # Clean light theme
│       ├── darkly.css     # Professional dark theme
│       ├── cerulean.css   # Blue accent theme
│       └── flatly.css     # Modern flat theme
├── components/
│   ├── buttons.css        # Button component styles
│   ├── forms.css          # Form component styles
│   ├── navigation.css     # Navigation and sidebar styles
│   ├── modals.css         # Modal and overlay styles
│   ├── cards.css          # Card component styles
│   └── layout.css         # Layout and grid styles
├── utilities/
│   ├── spacing.css        # Margin and padding utilities
│   ├── colors.css         # Color utility classes
│   ├── typography.css     # Text utility classes
│   └── responsive.css     # Responsive utility classes
├── vendor/
│   └── fontawesome.css    # Optimized FontAwesome styles
└── main.css               # Main entry point that imports all styles
```

## Components and Interfaces

### Theme Management System

#### ThemeManager Class
```typescript
interface ThemeConfig {
  name: string
  displayName: string
  cssFile: string
  preview: {
    primary: string
    background: string
    text: string
  }
}

interface ThemeManager {
  currentTheme: string
  availableThemes: ThemeConfig[]
  systemPreference: 'light' | 'dark'
  
  setTheme(theme: 'light' | 'dark' | 'auto' | string): Promise<void>
  loadTheme(themeName: string): Promise<void>
  applyTheme(themeName: string): void
  watchSystemPreference(): void
  getThemePreview(themeName: string): ThemeConfig['preview']
}
```

#### Theme Store Integration
```typescript
// Extend existing settings store
interface SettingsStore {
  theme: {
    current: string
    mode: 'light' | 'dark' | 'auto'
    availableThemes: ThemeConfig[]
  }
  
  setThemeMode(mode: 'light' | 'dark' | 'auto'): Promise<void>
  setTheme(themeName: string): Promise<void>
  loadAvailableThemes(): Promise<void>
}
```

### CSS Architecture

#### CSS Custom Properties Structure
```css
:root {
  /* Base colors */
  --color-primary: #3273dc;
  --color-primary-dark: #2366d1;
  --color-success: #23d160;
  --color-danger: #ff3860;
  --color-warning: #ffdd57;
  --color-info: #209cee;
  
  /* Semantic colors */
  --color-background: #ffffff;
  --color-surface: #f8f9fa;
  --color-text: #363636;
  --color-text-muted: #6b7280;
  --color-border: #dbdbdb;
  
  /* Component colors */
  --navbar-background: var(--color-surface);
  --sidebar-background: var(--color-surface);
  --card-background: var(--color-background);
  
  /* Transitions */
  --transition-theme: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease;
}

[data-theme="dark"] {
  --color-background: #1a1a1a;
  --color-surface: #2d2d2d;
  --color-text: #e0e0e0;
  --color-text-muted: #9ca3af;
  --color-border: #404040;
}
```

#### Component Organization
Each component CSS file will follow this structure:
```css
/* Component: Button */
.button {
  /* Base styles using CSS custom properties */
  background: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-border);
  transition: var(--transition-fast);
}

.button:hover {
  background: var(--color-surface);
}

.button.is-primary {
  background: var(--color-primary);
  color: white;
}
```

## Data Models

### Theme Configuration Model
```typescript
interface BulmaswatchTheme {
  name: string
  displayName: string
  description: string
  cssUrl: string
  localPath: string
  preview: {
    primary: string
    background: string
    surface: string
    text: string
    accent: string
  }
  isDark: boolean
  category: 'light' | 'dark' | 'colorful'
}

interface ThemeSettings {
  mode: 'light' | 'dark' | 'auto'
  selectedTheme: string
  customThemes: BulmaswatchTheme[]
  transitionDuration: number
  preloadThemes: boolean
}
```

### CSS Build Configuration
```typescript
interface StylesBuildConfig {
  entryPoint: string
  outputDir: string
  themes: {
    enabled: string[]
    preload: string[]
    lazy: string[]
  }
  optimization: {
    purgeCSS: boolean
    minify: boolean
    splitChunks: boolean
  }
  features: {
    customProperties: boolean
    transitions: boolean
    responsive: boolean
  }
}
```

## Error Handling

### Theme Loading Errors
```typescript
class ThemeLoadError extends Error {
  constructor(
    public themeName: string,
    public reason: 'network' | 'parse' | 'missing',
    message: string
  ) {
    super(message)
    this.name = 'ThemeLoadError'
  }
}

interface ThemeErrorHandler {
  handleThemeLoadError(error: ThemeLoadError): void
  fallbackToDefaultTheme(): void
  retryThemeLoad(themeName: string, maxRetries: number): Promise<void>
}
```

### CSS Loading Strategy
1. **Critical CSS**: Inline base styles in HTML for immediate rendering
2. **Theme CSS**: Load theme-specific styles asynchronously
3. **Component CSS**: Load component styles on demand
4. **Fallback**: Always provide fallback to default light theme

### Error Recovery
- Network failures: Retry with exponential backoff
- Parse errors: Fall back to default theme
- Missing themes: Show user-friendly error and theme selector
- FOUC prevention: Use CSS-in-JS for critical styles during theme transitions

## Testing Strategy

### Visual Regression Testing
```typescript
interface ThemeTestSuite {
  testThemeTransitions(): Promise<void>
  testComponentAppearance(theme: string): Promise<void>
  testResponsiveDesign(theme: string): Promise<void>
  testAccessibilityContrast(theme: string): Promise<void>
}
```

### Test Scenarios
1. **Theme Switching**: Verify smooth transitions between all theme combinations
2. **System Preference**: Test auto mode with system theme changes
3. **Persistence**: Verify theme preferences persist across sessions
4. **Performance**: Measure theme loading and switching performance
5. **Accessibility**: Test color contrast ratios for all themes
6. **Responsive**: Verify themes work across all breakpoints

### Component Testing
```typescript
// Example test for theme-aware components
describe('Button Component Theming', () => {
  it('should apply correct colors for light theme', () => {
    setTheme('light')
    const button = render(<Button variant="primary" />)
    expect(button).toHaveStyle('background-color: #3273dc')
  })
  
  it('should transition smoothly between themes', async () => {
    const button = render(<Button variant="primary" />)
    setTheme('dark')
    await waitFor(() => {
      expect(button).toHaveStyle('background-color: #4f46e5')
    })
  })
})
```

### Performance Testing
- Bundle size impact measurement
- Theme loading time benchmarks
- CSS parsing performance
- Memory usage monitoring
- First paint and largest contentful paint metrics

## Implementation Phases

### Phase 1: File Organization (Week 1)
1. Create new directory structure
2. Split existing `minimal.css` into component files
3. Extract CSS custom properties
4. Update import statements in main.ts

### Phase 2: Bulmaswatch Integration (Week 1-2)
1. Download and evaluate Bulmaswatch themes
2. Create theme configuration system
3. Implement theme loading mechanism
4. Add theme switching logic

### Phase 3: Theme Management UI (Week 2)
1. Enhance settings modal with theme selector
2. Add theme preview functionality
3. Implement smooth transitions
4. Add accessibility features

### Phase 4: Optimization and Testing (Week 2-3)
1. Implement CSS optimization
2. Add performance monitoring
3. Conduct visual regression testing
4. Optimize bundle sizes

## Bulmaswatch Theme Selection

Based on research of https://jenil.github.io/bulmaswatch/, we will implement these themes:

### Light Themes
- **Default**: Clean, minimal light theme (base Bulma)
- **Flatly**: Modern flat design with subtle shadows
- **Cerulean**: Professional blue accent theme
- **Cosmo**: Friendly, approachable design

### Dark Themes  
- **Darkly**: Professional dark theme with good contrast
- **Cyborg**: High-contrast dark theme
- **Slate**: Subtle dark theme with blue accents
- **Solar**: Warm dark theme with orange accents

### Implementation Strategy
1. Download theme CSS files during build process
2. Process themes to extract CSS custom properties
3. Create theme manifests with metadata
4. Implement lazy loading for non-default themes
5. Add theme preview generation

## Performance Considerations

### Bundle Size Optimization
- Split themes into separate chunks
- Lazy load non-default themes
- Use CSS custom properties to reduce duplication
- Implement CSS purging for unused styles

### Loading Strategy
- Preload user's preferred theme
- Cache themes in localStorage
- Use service worker for offline theme availability
- Implement progressive enhancement

### Transition Performance
- Use CSS transforms instead of layout changes
- Batch DOM updates during theme switches
- Use `will-change` property for transitioning elements
- Implement reduced motion preferences

## Accessibility

### Color Contrast
- Ensure WCAG AA compliance for all themes
- Test with automated contrast checking
- Provide high contrast theme option
- Support Windows High Contrast mode

### User Preferences
- Respect `prefers-color-scheme` media query
- Support `prefers-reduced-motion`
- Provide keyboard navigation for theme selector
- Add screen reader announcements for theme changes

### Focus Management
- Maintain focus visibility across themes
- Ensure focus indicators have sufficient contrast
- Test with keyboard-only navigation
- Verify screen reader compatibility
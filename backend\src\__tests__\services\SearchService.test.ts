import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SearchService } from '../../services/SearchService';
import { Note } from '../../models/Note';

// Mock dependencies
vi.mock('../../models/Note');

describe('SearchService', () => {
  let searchService: SearchService;

  beforeEach(() => {
    vi.clearAllMocks();
    searchService = new SearchService();
  });

  describe('searchNotes', () => {
    it('should return search results with highlighting', async () => {
      const mockNotes = [
        {
          id: 'note-1',
          title: 'JavaScript Tutorial',
          content: 'Learn JavaScript fundamentals and advanced concepts',
          noteType: 'richtext',
          userId: 'user-123',
          tags: ['programming', 'javascript'],
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'note-2',
          title: 'React Components',
          content: 'Building reusable components in React with JavaScript',
          noteType: 'markdown',
          userId: 'user-123',
          tags: ['react', 'javascript'],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      vi.mocked(Note.search).mockResolvedValue({
        notes: mockNotes,
        total: 2
      });

      const results = await searchService.searchNotes('user-123', 'JavaScript', {
        page: 1,
        limit: 10
      });

      expect(results.notes).toHaveLength(2);
      expect(results.total).toBe(2);
      expect(results.notes[0].highlights).toBeDefined();
      expect(results.notes[0].highlights.title).toContain('<mark>JavaScript</mark>');
    });

    it('should filter by note type', async () => {
      const mockNotes = [
        {
          id: 'note-1',
          title: 'Markdown Note',
          content: 'This is a markdown note',
          noteType: 'markdown',
          userId: 'user-123',
          tags: [],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      vi.mocked(Note.search).mockResolvedValue({
        notes: mockNotes,
        total: 1
      });

      const results = await searchService.searchNotes('user-123', 'markdown', {
        type: 'markdown',
        page: 1,
        limit: 10
      });

      expect(Note.search).toHaveBeenCalledWith('user-123', 'markdown', {
        type: 'markdown',
        page: 1,
        limit: 10
      });
      expect(results.notes).toHaveLength(1);
      expect(results.notes[0].noteType).toBe('markdown');
    });

    it('should filter by tags', async () => {
      const mockNotes = [
        {
          id: 'note-1',
          title: 'Work Note',
          content: 'Important work content',
          noteType: 'richtext',
          userId: 'user-123',
          tags: ['work', 'important'],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      vi.mocked(Note.search).mockResolvedValue({
        notes: mockNotes,
        total: 1
      });

      const results = await searchService.searchNotes('user-123', 'work', {
        tags: ['work', 'important'],
        page: 1,
        limit: 10
      });

      expect(Note.search).toHaveBeenCalledWith('user-123', 'work', {
        tags: ['work', 'important'],
        page: 1,
        limit: 10
      });
      expect(results.notes[0].tags).toContain('work');
      expect(results.notes[0].tags).toContain('important');
    });

    it('should filter by date range', async () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-12-31');
      
      const mockNotes = [
        {
          id: 'note-1',
          title: 'Recent Note',
          content: 'Recent content',
          noteType: 'richtext',
          userId: 'user-123',
          tags: [],
          createdAt: new Date('2023-06-15'),
          updatedAt: new Date('2023-06-15')
        }
      ];

      vi.mocked(Note.search).mockResolvedValue({
        notes: mockNotes,
        total: 1
      });

      const results = await searchService.searchNotes('user-123', 'recent', {
        dateFrom: startDate,
        dateTo: endDate,
        page: 1,
        limit: 10
      });

      expect(Note.search).toHaveBeenCalledWith('user-123', 'recent', {
        dateFrom: startDate,
        dateTo: endDate,
        page: 1,
        limit: 10
      });
      expect(results.notes).toHaveLength(1);
    });

    it('should handle empty search results', async () => {
      vi.mocked(Note.search).mockResolvedValue({
        notes: [],
        total: 0
      });

      const results = await searchService.searchNotes('user-123', 'nonexistent', {
        page: 1,
        limit: 10
      });

      expect(results.notes).toHaveLength(0);
      expect(results.total).toBe(0);
    });

    it('should handle search performance requirements', async () => {
      const mockNotes = Array.from({ length: 100 }, (_, i) => ({
        id: `note-${i}`,
        title: `Note ${i}`,
        content: `Content for note ${i}`,
        noteType: 'richtext' as const,
        userId: 'user-123',
        tags: [`tag-${i}`],
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      vi.mocked(Note.search).mockResolvedValue({
        notes: mockNotes,
        total: 100
      });

      const startTime = Date.now();
      const results = await searchService.searchNotes('user-123', 'note', {
        page: 1,
        limit: 100
      });
      const endTime = Date.now();

      const searchTime = endTime - startTime;
      expect(searchTime).toBeLessThan(500); // Should be under 500ms
      expect(results.notes).toHaveLength(100);
    });
  });

  describe('searchSuggestions', () => {
    it('should return search suggestions based on query', async () => {
      const mockSuggestions = [
        'JavaScript',
        'JavaScript Tutorial',
        'JavaScript React',
        'JavaScript Node.js'
      ];

      vi.mocked(Note.getSearchSuggestions).mockResolvedValue(mockSuggestions);

      const suggestions = await searchService.getSearchSuggestions('user-123', 'java');

      expect(suggestions).toHaveLength(4);
      expect(suggestions).toContain('JavaScript');
      expect(Note.getSearchSuggestions).toHaveBeenCalledWith('user-123', 'java');
    });

    it('should limit number of suggestions', async () => {
      const mockSuggestions = Array.from({ length: 20 }, (_, i) => `Suggestion ${i}`);

      vi.mocked(Note.getSearchSuggestions).mockResolvedValue(mockSuggestions);

      const suggestions = await searchService.getSearchSuggestions('user-123', 'suggestion');

      expect(suggestions.length).toBeLessThanOrEqual(10); // Should limit to 10
    });

    it('should handle empty suggestions', async () => {
      vi.mocked(Note.getSearchSuggestions).mockResolvedValue([]);

      const suggestions = await searchService.getSearchSuggestions('user-123', 'nonexistent');

      expect(suggestions).toHaveLength(0);
    });
  });

  describe('highlightText', () => {
    it('should highlight search terms in text', () => {
      const text = 'This is a JavaScript tutorial about JavaScript programming';
      const query = 'JavaScript';

      const highlighted = searchService.highlightText(text, query);

      expect(highlighted).toBe('This is a <mark>JavaScript</mark> tutorial about <mark>JavaScript</mark> programming');
    });

    it('should handle case-insensitive highlighting', () => {
      const text = 'JavaScript and javascript are the same';
      const query = 'javascript';

      const highlighted = searchService.highlightText(text, query);

      expect(highlighted).toBe('<mark>JavaScript</mark> and <mark>javascript</mark> are the same');
    });

    it('should handle multiple search terms', () => {
      const text = 'Learn React and JavaScript for web development';
      const query = 'React JavaScript';

      const highlighted = searchService.highlightText(text, query);

      expect(highlighted).toContain('<mark>React</mark>');
      expect(highlighted).toContain('<mark>JavaScript</mark>');
    });

    it('should handle special characters in search query', () => {
      const text = 'Use console.log() for debugging';
      const query = 'console.log()';

      const highlighted = searchService.highlightText(text, query);

      expect(highlighted).toBe('Use <mark>console.log()</mark> for debugging');
    });

    it('should not highlight if no matches found', () => {
      const text = 'This is some text';
      const query = 'nonexistent';

      const highlighted = searchService.highlightText(text, query);

      expect(highlighted).toBe('This is some text');
    });
  });

  describe('buildSearchIndex', () => {
    it('should build search index for user notes', async () => {
      const mockNotes = [
        {
          id: 'note-1',
          title: 'JavaScript Basics',
          content: 'Learn JavaScript fundamentals',
          noteType: 'richtext',
          userId: 'user-123',
          tags: ['javascript', 'programming']
        },
        {
          id: 'note-2',
          title: 'React Components',
          content: 'Building React components',
          noteType: 'markdown',
          userId: 'user-123',
          tags: ['react', 'javascript']
        }
      ];

      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: mockNotes,
        total: 2,
        page: 1,
        limit: 1000
      });

      const index = await searchService.buildSearchIndex('user-123');

      expect(index).toBeDefined();
      expect(index.size).toBe(2);
      expect(Note.findByUserId).toHaveBeenCalledWith('user-123', {
        page: 1,
        limit: 1000,
        includeArchived: false
      });
    });

    it('should handle large number of notes for indexing', async () => {
      const mockNotes = Array.from({ length: 1000 }, (_, i) => ({
        id: `note-${i}`,
        title: `Note ${i}`,
        content: `Content for note ${i}`,
        noteType: 'richtext' as const,
        userId: 'user-123',
        tags: [`tag-${i}`]
      }));

      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: mockNotes,
        total: 1000,
        page: 1,
        limit: 1000
      });

      const startTime = Date.now();
      const index = await searchService.buildSearchIndex('user-123');
      const endTime = Date.now();

      const indexTime = endTime - startTime;
      expect(indexTime).toBeLessThan(2000); // Should complete within 2 seconds
      expect(index.size).toBe(1000);
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      vi.mocked(Note.search).mockRejectedValue(new Error('Database connection failed'));

      await expect(
        searchService.searchNotes('user-123', 'test', { page: 1, limit: 10 })
      ).rejects.toThrow('Database connection failed');
    });

    it('should handle invalid search parameters', async () => {
      await expect(
        searchService.searchNotes('', 'test', { page: 0, limit: -1 })
      ).rejects.toThrow();
    });

    it('should handle malformed search queries', async () => {
      const malformedQueries = [
        null,
        undefined,
        '',
        '   ',
        '<script>alert("xss")</script>'
      ];

      for (const query of malformedQueries) {
        if (query === null || query === undefined) {
          await expect(
            searchService.searchNotes('user-123', query as any, { page: 1, limit: 10 })
          ).rejects.toThrow();
        } else {
          // Should handle gracefully without throwing
          vi.mocked(Note.search).mockResolvedValue({ notes: [], total: 0 });
          const results = await searchService.searchNotes('user-123', query, { page: 1, limit: 10 });
          expect(results.notes).toHaveLength(0);
        }
      }
    });
  });
});
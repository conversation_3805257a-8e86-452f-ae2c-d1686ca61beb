// Simple test to check tag functionality
const axios = require("axios");

async function testTags() {
    try {
        // Test creating a tag
        console.log("Testing tag creation...");
        const response = await axios.post(
            "http://localhost:3001/api/tags",
            {
                name: "test-tag",
            },
            {
                headers: {
                    Authorization: "Bearer your-jwt-token-here",
                },
            }
        );

        console.log("Tag created:", response.data);
    } catch (error) {
        console.error("Error:", error.response?.data || error.message);
    }
}

testTags();

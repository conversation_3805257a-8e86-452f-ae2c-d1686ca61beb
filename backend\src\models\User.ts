import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  themeName?: string; // Custom theme name (e.g., 'darkly', 'flatly', 'cerulean')
  language: string;
  timezone: string;
  autoSaveInterval: number;
  notifications: {
    email: boolean;
    push: boolean;
    mentions: boolean;
  };
}

export interface User {
  id: string;
  email: string;
  password_hash: string;
  display_name: string;
  avatar_url?: string;
  preferences: UserPreferences;
  email_verified: boolean;
  two_fa_secret?: string;
  backup_codes?: string;
  oauth_provider?: string;
  oauth_id?: string;
  admin?: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface CreateUserData {
  email: string;
  password: string;
  display_name: string;
  avatar_url?: string;
  email_verified?: boolean;
  oauth_provider?: string;
  oauth_id?: string;
  admin?: boolean;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export class UserModel {
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  static async comparePassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  static generateId(): string {
    return uuidv4();
  }

  static getDefaultPreferences(): UserPreferences {
    return {
      theme: 'auto',
      themeName: 'default', // Default theme name
      language: 'en',
      timezone: 'UTC',
      autoSaveInterval: 30000, // 30 seconds
      notifications: {
        email: true,
        push: true,
        mentions: true
      }
    };
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static validatePassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/(?=.*[a-z])/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/(?=.*[A-Z])/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/(?=.*\d)/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/(?=.*[@$!%*?&])/.test(password)) {
      errors.push('Password must contain at least one special character (@$!%*?&)');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
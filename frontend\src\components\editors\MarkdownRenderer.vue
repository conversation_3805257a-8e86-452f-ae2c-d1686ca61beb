<template>
  <div class="markdown-renderer" v-html="renderedContent"></div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  content: string;
}

const props = defineProps<Props>();

// Simple markdown renderer - in a real app you'd use a proper markdown library
const renderedContent = computed(() => {
  let html = props.content;
  
  // Basic markdown rendering
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  html = html.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');
  html = html.replace(/\*(.*)\*/gim, '<em>$1</em>');
  html = html.replace(/\n/gim, '<br>');
  
  return html;
});
</script>

<style scoped>
.markdown-renderer {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.markdown-renderer h1,
.markdown-renderer h2,
.markdown-renderer h3 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

.markdown-renderer h1 {
  font-size: 2rem;
  font-weight: 600;
}

.markdown-renderer h2 {
  font-size: 1.5rem;
  font-weight: 600;
}

.markdown-renderer h3 {
  font-size: 1.25rem;
  font-weight: 600;
}
</style>
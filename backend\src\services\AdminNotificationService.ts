import { AdminNotificationRepository } from '../repositories/AdminNotificationRepository'
import { AdminNotification, CreateNotificationData, NotificationFilters } from '../models/AdminNotification'

export class AdminNotificationService {
  constructor(private notificationRepository: AdminNotificationRepository) {}

  async createNotification(data: CreateNotificationData): Promise<AdminNotification> {
    return await this.notificationRepository.create(data)
  }

  async getNotifications(filters: NotificationFilters = {}) {
    const { notifications, total } = await this.notificationRepository.findAll(filters)
    const { page = 1, limit = 20 } = filters
    
    return {
      notifications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  async getNotificationById(id: string): Promise<AdminNotification | null> {
    return await this.notificationRepository.findById(id)
  }

  async markAsRead(id: string): Promise<boolean> {
    return await this.notificationRepository.markAsRead(id)
  }

  async markAllAsRead(): Promise<number> {
    return await this.notificationRepository.markAllAsRead()
  }

  async deleteNotification(id: string): Promise<boolean> {
    return await this.notificationRepository.delete(id)
  }

  async getUnreadCount(): Promise<number> {
    return await this.notificationRepository.getUnreadCount()
  }

  // Helper methods for creating specific types of notifications
  async createSystemAlert(title: string, message: string, actionUrl?: string): Promise<AdminNotification> {
    return this.createNotification({
      type: 'warning',
      category: 'system',
      title,
      message,
      actionUrl
    })
  }

  async createSecurityAlert(title: string, message: string, actionUrl?: string): Promise<AdminNotification> {
    return this.createNotification({
      type: 'critical',
      category: 'security',
      title,
      message,
      actionUrl
    })
  }

  async createContentReport(title: string, message: string, actionUrl?: string): Promise<AdminNotification> {
    return this.createNotification({
      type: 'warning',
      category: 'content_report',
      title,
      message,
      actionUrl
    })
  }

  async createUserActionNotification(title: string, message: string, actionUrl?: string): Promise<AdminNotification> {
    return this.createNotification({
      type: 'info',
      category: 'user_action',
      title,
      message,
      actionUrl
    })
  }
}
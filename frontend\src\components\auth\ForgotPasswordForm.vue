<template>
  <div class="card">
    <div class="card-header">
      <p class="card-header-title">
        <span class="icon">
          <i class="fas fa-key"></i>
        </span>
        <span>Reset Password</span>
      </p>
    </div>
    <div class="card-content">
      <div v-if="!emailSent">
        <p class="has-text-grey mb-4">
          Enter your email address and we'll send you a link to reset your password.
        </p>
        
        <form @submit.prevent="handleSubmit">
          <!-- Email Field -->
          <div class="field">
            <label class="label">Email</label>
            <div class="control has-icons-left">
              <input
                v-model="form.email"
                type="email"
                class="input"
                :class="{ 'is-danger': emailValidation.errors.length > 0 && emailTouched }"
                placeholder="Enter your email"
                @blur="emailTouched = true"
                @input="validateEmail"
                :disabled="authStore.isLoading"
              />
              <span class="icon is-small is-left">
                <i class="fas fa-envelope"></i>
              </span>
            </div>
            <div v-if="emailValidation.errors.length > 0 && emailTouched" class="help is-danger">
              <div v-for="error in emailValidation.errors" :key="error">
                {{ error }}
              </div>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="authStore.error" class="notification is-danger is-light">
            <button class="delete" @click="authStore.error = null"></button>
            {{ authStore.error }}
          </div>

          <!-- Submit Button -->
          <div class="field">
            <div class="control">
              <button
                type="submit"
                class="button is-primary is-fullwidth"
                :class="{ 'is-loading': authStore.isLoading }"
                :disabled="!isFormValid || authStore.isLoading"
              >
                <span class="icon">
                  <i class="fas fa-paper-plane"></i>
                </span>
                <span>Send Reset Link</span>
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Success State -->
      <div v-else class="has-text-centered">
        <div class="icon is-large has-text-success mb-4">
          <i class="fas fa-check-circle fa-3x"></i>
        </div>
        <h3 class="title is-4">Check Your Email</h3>
        <p class="has-text-grey mb-4">
          We've sent a password reset link to <strong>{{ form.email }}</strong>
        </p>
        <p class="has-text-grey-light is-size-7 mb-4">
          Didn't receive the email? Check your spam folder or try again.
        </p>
        <button 
          class="button is-text"
          @click="resetForm"
          :disabled="authStore.isLoading"
        >
          Try a different email
        </button>
      </div>

      <hr />

      <div class="field">
        <div class="has-text-centered">
          <router-link to="/login" class="is-size-7">
            <span class="icon">
              <i class="fas fa-arrow-left"></i>
            </span>
            Back to Sign In
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { validateField, emailRules } from '../../utils/validation'
import type { ValidationResult } from '../../utils/validation'

const authStore = useAuthStore()

const form = reactive({
  email: ''
})

const emailTouched = ref(false)
const emailSent = ref(false)

const emailValidation = ref<ValidationResult>({ isValid: true, errors: [] })

const validateEmail = () => {
  emailValidation.value = validateField(form.email, emailRules)
}

const isFormValid = computed(() => {
  return emailValidation.value.isValid && form.email.trim() !== ''
})

const handleSubmit = async () => {
  emailTouched.value = true
  validateEmail()
  
  if (!isFormValid.value) {
    return
  }

  const result = await authStore.forgotPassword(form.email.trim())

  if (result.success) {
    emailSent.value = true
  }
}

const resetForm = () => {
  emailSent.value = false
  form.email = ''
  emailTouched.value = false
  authStore.error = null
}
</script>

<style scoped>
</style>
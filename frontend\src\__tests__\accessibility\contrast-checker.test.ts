import { describe, it, expect, vi, beforeEach } from 'vitest'

/**
 * WCAG Color Contrast Checker
 * Implements WCAG 2.1 contrast ratio calculations and validation
 */

interface ContrastTestResult {
  foreground: string
  background: string
  ratio: number
  wcagLevel: 'AAA' | 'AA' | 'FAIL'
  passes: {
    normalAA: boolean
    normalAAA: boolean
    largeAA: boolean
    largeAAA: boolean
  }
  recommendations?: string[]
}

interface ThemeContrastReport {
  theme: string
  overallScore: number
  totalTests: number
  passedTests: number
  failedTests: number
  results: ContrastTestResult[]
  issues: ContrastIssue[]
}

interface ContrastIssue {
  severity: 'critical' | 'high' | 'medium' | 'low'
  element: string
  foreground: string
  background: string
  currentRatio: number
  requiredRatio: number
  suggestion: string
}

class WCAGContrastChecker {
  /**
   * Calculate WCAG 2.1 contrast ratio between two colors
   */
  calculateContrastRatio(color1: string, color2: string): number {
    const rgb1 = this.parseColor(color1)
    const rgb2 = this.parseColor(color2)
    
    const l1 = this.getRelativeLuminance(rgb1)
    const l2 = this.getRelativeLuminance(rgb2)
    
    const lighter = Math.max(l1, l2)
    const darker = Math.min(l1, l2)
    
    return (lighter + 0.05) / (darker + 0.05)
  }

  /**
   * Test color combination against WCAG standards
   */
  testColorCombination(
    foreground: string, 
    background: string, 
    isLargeText: boolean = false
  ): ContrastTestResult {
    const ratio = this.calculateContrastRatio(foreground, background)
    
    // WCAG 2.1 requirements
    const normalAAThreshold = 4.5
    const normalAAAThreshold = 7
    const largeAAThreshold = 3
    const largeAAAThreshold = 4.5
    
    const passes = {
      normalAA: ratio >= normalAAThreshold,
      normalAAA: ratio >= normalAAAThreshold,
      largeAA: ratio >= largeAAThreshold,
      largeAAA: ratio >= largeAAAThreshold
    }
    
    let wcagLevel: 'AAA' | 'AA' | 'FAIL'
    if (isLargeText) {
      wcagLevel = passes.largeAAA ? 'AAA' : passes.largeAA ? 'AA' : 'FAIL'
    } else {
      wcagLevel = passes.normalAAA ? 'AAA' : passes.normalAA ? 'AA' : 'FAIL'
    }
    
    const recommendations = this.generateRecommendations(ratio, isLargeText, passes)
    
    return {
      foreground,
      background,
      ratio,
      wcagLevel,
      passes,
      recommendations
    }
  }

  /**
   * Parse color string to RGB values
   */
  private parseColor(color: string): { r: number; g: number; b: number } {
    // Remove whitespace and convert to lowercase
    color = color.trim().toLowerCase()
    
    // Handle rgb() and rgba() format
    const rgbMatch = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
    if (rgbMatch) {
      return {
        r: parseInt(rgbMatch[1]),
        g: parseInt(rgbMatch[2]),
        b: parseInt(rgbMatch[3])
      }
    }
    
    // Handle hex format (#rgb, #rrggbb)
    const hexMatch = color.match(/^#([a-f\d]{3}|[a-f\d]{6})$/i)
    if (hexMatch) {
      const hex = hexMatch[1]
      if (hex.length === 3) {
        // Convert #rgb to #rrggbb
        return {
          r: parseInt(hex[0] + hex[0], 16),
          g: parseInt(hex[1] + hex[1], 16),
          b: parseInt(hex[2] + hex[2], 16)
        }
      } else {
        return {
          r: parseInt(hex.substr(0, 2), 16),
          g: parseInt(hex.substr(2, 2), 16),
          b: parseInt(hex.substr(4, 2), 16)
        }
      }
    }
    
    // Handle hsl() format
    const hslMatch = color.match(/hsla?\((\d+),\s*(\d+)%,\s*(\d+)%(?:,\s*[\d.]+)?\)/)
    if (hslMatch) {
      const h = parseInt(hslMatch[1]) / 360
      const s = parseInt(hslMatch[2]) / 100
      const l = parseInt(hslMatch[3]) / 100
      return this.hslToRgb(h, s, l)
    }
    
    // Handle named colors
    const namedColors: Record<string, { r: number; g: number; b: number }> = {
      white: { r: 255, g: 255, b: 255 },
      black: { r: 0, g: 0, b: 0 },
      red: { r: 255, g: 0, b: 0 },
      green: { r: 0, g: 128, b: 0 },
      blue: { r: 0, g: 0, b: 255 },
      yellow: { r: 255, g: 255, b: 0 },
      cyan: { r: 0, g: 255, b: 255 },
      magenta: { r: 255, g: 0, b: 255 },
      silver: { r: 192, g: 192, b: 192 },
      gray: { r: 128, g: 128, b: 128 },
      maroon: { r: 128, g: 0, b: 0 },
      olive: { r: 128, g: 128, b: 0 },
      lime: { r: 0, g: 255, b: 0 },
      aqua: { r: 0, g: 255, b: 255 },
      teal: { r: 0, g: 128, b: 128 },
      navy: { r: 0, g: 0, b: 128 },
      fuchsia: { r: 255, g: 0, b: 255 },
      purple: { r: 128, g: 0, b: 128 }
    }
    
    return namedColors[color] || { r: 0, g: 0, b: 0 }
  }

  /**
   * Convert HSL to RGB
   */
  private hslToRgb(h: number, s: number, l: number): { r: number; g: number; b: number } {
    let r, g, b
    
    if (s === 0) {
      r = g = b = l // achromatic
    } else {
      const hue2rgb = (p: number, q: number, t: number) => {
        if (t < 0) t += 1
        if (t > 1) t -= 1
        if (t < 1/6) return p + (q - p) * 6 * t
        if (t < 1/2) return q
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
        return p
      }
      
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s
      const p = 2 * l - q
      
      r = hue2rgb(p, q, h + 1/3)
      g = hue2rgb(p, q, h)
      b = hue2rgb(p, q, h - 1/3)
    }
    
    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    }
  }

  /**
   * Calculate relative luminance according to WCAG 2.1
   */
  private getRelativeLuminance(rgb: { r: number; g: number; b: number }): number {
    const { r, g, b } = rgb
    
    // Convert to sRGB
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    })
    
    // Calculate relative luminance
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
  }

  /**
   * Generate recommendations for improving contrast
   */
  private generateRecommendations(
    ratio: number, 
    isLargeText: boolean, 
    passes: ContrastTestResult['passes']
  ): string[] {
    const recommendations: string[] = []
    
    const requiredRatio = isLargeText ? 3 : 4.5
    const preferredRatio = isLargeText ? 4.5 : 7
    
    if (ratio < requiredRatio) {
      recommendations.push(`Critical: Contrast ratio ${ratio.toFixed(2)} is below WCAG AA minimum of ${requiredRatio}`)
      recommendations.push('Increase contrast by darkening text or lightening background')
    } else if (ratio < preferredRatio) {
      recommendations.push(`Good: Meets WCAG AA (${ratio.toFixed(2)}), consider improving to AAA (${preferredRatio})`)
    } else {
      recommendations.push(`Excellent: Exceeds WCAG AAA requirements (${ratio.toFixed(2)})`)
    }
    
    if (isLargeText && !passes.largeAA) {
      recommendations.push('For large text, minimum ratio should be 3:1')
    }
    
    if (!isLargeText && !passes.normalAA) {
      recommendations.push('For normal text, minimum ratio should be 4.5:1')
    }
    
    return recommendations
  }

  /**
   * Test an entire theme's color combinations
   */
  testThemeContrast(theme: {
    name: string
    colors: Record<string, string>
  }): ThemeContrastReport {
    const results: ContrastTestResult[] = []
    const issues: ContrastIssue[] = []
    
    // Define common color combinations to test
    const combinations = [
      { fg: 'text', bg: 'background', element: 'body text', isLarge: false },
      { fg: 'text', bg: 'surface', element: 'card text', isLarge: false },
      { fg: 'background', bg: 'primary', element: 'primary button text', isLarge: false },
      { fg: 'background', bg: 'success', element: 'success button text', isLarge: false },
      { fg: 'background', bg: 'danger', element: 'danger button text', isLarge: false },
      { fg: 'textMuted', bg: 'background', element: 'muted text', isLarge: false },
      { fg: 'text', bg: 'background', element: 'heading text', isLarge: true },
      { fg: 'primary', bg: 'background', element: 'link text', isLarge: false }
    ]
    
    combinations.forEach(combo => {
      const fgColor = theme.colors[combo.fg]
      const bgColor = theme.colors[combo.bg]
      
      if (fgColor && bgColor) {
        const result = this.testColorCombination(fgColor, bgColor, combo.isLarge)
        results.push(result)
        
        // Check for issues
        const requiredRatio = combo.isLarge ? 3 : 4.5
        if (result.ratio < requiredRatio) {
          issues.push({
            severity: result.ratio < requiredRatio * 0.8 ? 'critical' : 'high',
            element: combo.element,
            foreground: fgColor,
            background: bgColor,
            currentRatio: result.ratio,
            requiredRatio,
            suggestion: `Increase contrast to meet WCAG ${combo.isLarge ? 'AA Large' : 'AA'} requirements`
          })
        }
      }
    })
    
    const passedTests = results.filter(r => r.wcagLevel !== 'FAIL').length
    const totalTests = results.length
    const overallScore = totalTests > 0 ? (passedTests / totalTests) * 100 : 0
    
    return {
      theme: theme.name,
      overallScore,
      totalTests,
      passedTests,
      failedTests: totalTests - passedTests,
      results,
      issues
    }
  }

  /**
   * Generate color suggestions to improve contrast
   */
  suggestColorImprovements(
    foreground: string, 
    background: string, 
    targetRatio: number = 4.5
  ): {
    adjustedForeground?: string
    adjustedBackground?: string
    newRatio: number
  } {
    const fgRgb = this.parseColor(foreground)
    const bgRgb = this.parseColor(background)
    
    // Try darkening foreground
    const darkerFg = this.adjustBrightness(fgRgb, -0.2)
    const darkerFgRatio = this.calculateContrastRatio(
      this.rgbToHex(darkerFg), 
      background
    )
    
    // Try lightening background
    const lighterBg = this.adjustBrightness(bgRgb, 0.2)
    const lighterBgRatio = this.calculateContrastRatio(
      foreground, 
      this.rgbToHex(lighterBg)
    )
    
    // Return the better option
    if (darkerFgRatio >= targetRatio && darkerFgRatio >= lighterBgRatio) {
      return {
        adjustedForeground: this.rgbToHex(darkerFg),
        newRatio: darkerFgRatio
      }
    } else if (lighterBgRatio >= targetRatio) {
      return {
        adjustedBackground: this.rgbToHex(lighterBg),
        newRatio: lighterBgRatio
      }
    } else {
      // If neither works, try more aggressive adjustments
      const muchDarkerFg = this.adjustBrightness(fgRgb, -0.5)
      const muchDarkerFgRatio = this.calculateContrastRatio(
        this.rgbToHex(muchDarkerFg), 
        background
      )
      
      return {
        adjustedForeground: this.rgbToHex(muchDarkerFg),
        newRatio: muchDarkerFgRatio
      }
    }
  }

  /**
   * Adjust brightness of RGB color
   */
  private adjustBrightness(
    rgb: { r: number; g: number; b: number }, 
    factor: number
  ): { r: number; g: number; b: number } {
    return {
      r: Math.max(0, Math.min(255, Math.round(rgb.r * (1 + factor)))),
      g: Math.max(0, Math.min(255, Math.round(rgb.g * (1 + factor)))),
      b: Math.max(0, Math.min(255, Math.round(rgb.b * (1 + factor))))
    }
  }

  /**
   * Convert RGB to hex
   */
  private rgbToHex(rgb: { r: number; g: number; b: number }): string {
    const toHex = (n: number) => {
      const hex = n.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }
    
    return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`
  }
}

describe('WCAG Contrast Checker', () => {
  let checker: WCAGContrastChecker

  beforeEach(() => {
    checker = new WCAGContrastChecker()
  })

  describe('Color Parsing', () => {
    it('should parse hex colors correctly', () => {
      const result = checker.calculateContrastRatio('#000000', '#ffffff')
      expect(result).toBeCloseTo(21, 1) // Perfect contrast
    })

    it('should parse RGB colors correctly', () => {
      const result = checker.calculateContrastRatio('rgb(0, 0, 0)', 'rgb(255, 255, 255)')
      expect(result).toBeCloseTo(21, 1)
    })

    it('should parse named colors correctly', () => {
      const result = checker.calculateContrastRatio('black', 'white')
      expect(result).toBeCloseTo(21, 1)
    })

    it('should parse short hex colors correctly', () => {
      const result = checker.calculateContrastRatio('#000', '#fff')
      expect(result).toBeCloseTo(21, 1)
    })

    it('should parse HSL colors correctly', () => {
      const result = checker.calculateContrastRatio('hsl(0, 0%, 0%)', 'hsl(0, 0%, 100%)')
      expect(result).toBeCloseTo(21, 1)
    })
  })

  describe('Contrast Ratio Calculations', () => {
    it('should calculate perfect contrast correctly', () => {
      const result = checker.calculateContrastRatio('#000000', '#ffffff')
      expect(result).toBeCloseTo(21, 1)
    })

    it('should calculate no contrast correctly', () => {
      const result = checker.calculateContrastRatio('#ffffff', '#ffffff')
      expect(result).toBeCloseTo(1, 1)
    })

    it('should calculate medium contrast correctly', () => {
      const result = checker.calculateContrastRatio('#767676', '#ffffff')
      expect(result).toBeCloseTo(4.5, 0.1) // Should be close to AA threshold
    })

    it('should be symmetric (order independent)', () => {
      const result1 = checker.calculateContrastRatio('#000000', '#ffffff')
      const result2 = checker.calculateContrastRatio('#ffffff', '#000000')
      expect(result1).toBeCloseTo(result2, 5)
    })
  })

  describe('WCAG Compliance Testing', () => {
    it('should identify AA compliant combinations', () => {
      const result = checker.testColorCombination('#000000', '#ffffff')
      
      expect(result.wcagLevel).toBe('AAA')
      expect(result.passes.normalAA).toBe(true)
      expect(result.passes.normalAAA).toBe(true)
      expect(result.passes.largeAA).toBe(true)
      expect(result.passes.largeAAA).toBe(true)
    })

    it('should identify failing combinations', () => {
      const result = checker.testColorCombination('#cccccc', '#ffffff')
      
      expect(result.wcagLevel).toBe('FAIL')
      expect(result.passes.normalAA).toBe(false)
      expect(result.passes.normalAAA).toBe(false)
    })

    it('should handle large text requirements correctly', () => {
      // This combination passes for large text but not normal text
      const result = checker.testColorCombination('#949494', '#ffffff', true)
      
      expect(result.passes.largeAA).toBe(true)
      expect(result.passes.normalAA).toBe(false)
    })

    it('should provide appropriate recommendations', () => {
      const result = checker.testColorCombination('#cccccc', '#ffffff')
      
      expect(result.recommendations).toBeDefined()
      expect(result.recommendations!.length).toBeGreaterThan(0)
      expect(result.recommendations![0]).toContain('Critical')
    })
  })

  describe('Theme Testing', () => {
    const testThemes = [
      {
        name: 'light',
        colors: {
          text: '#363636',
          background: '#ffffff',
          surface: '#f8f9fa',
          primary: '#3273dc',
          success: '#23d160',
          danger: '#ff3860',
          textMuted: '#6b7280'
        }
      },
      {
        name: 'dark',
        colors: {
          text: '#e0e0e0',
          background: '#1a1a1a',
          surface: '#2d2d2d',
          primary: '#4f46e5',
          success: '#10b981',
          danger: '#ef4444',
          textMuted: '#9ca3af'
        }
      }
    ]

    it('should test all theme color combinations', () => {
      testThemes.forEach(theme => {
        const report = checker.testThemeContrast(theme)
        
        expect(report.theme).toBe(theme.name)
        expect(report.totalTests).toBeGreaterThan(0)
        expect(report.results.length).toBe(report.totalTests)
        expect(report.overallScore).toBeGreaterThanOrEqual(0)
        expect(report.overallScore).toBeLessThanOrEqual(100)
      })
    })

    it('should identify contrast issues in themes', () => {
      const lightTheme = testThemes[0]
      const report = checker.testThemeContrast(lightTheme)
      
      // Light theme should generally have good contrast
      expect(report.overallScore).toBeGreaterThan(70)
      
      // Should have minimal critical issues
      const criticalIssues = report.issues.filter(issue => issue.severity === 'critical')
      expect(criticalIssues.length).toBeLessThanOrEqual(2)
    })

    it('should generate comprehensive reports', () => {
      const theme = testThemes[0]
      const report = checker.testThemeContrast(theme)
      
      expect(report).toHaveProperty('theme')
      expect(report).toHaveProperty('overallScore')
      expect(report).toHaveProperty('totalTests')
      expect(report).toHaveProperty('passedTests')
      expect(report).toHaveProperty('failedTests')
      expect(report).toHaveProperty('results')
      expect(report).toHaveProperty('issues')
      
      expect(report.passedTests + report.failedTests).toBe(report.totalTests)
    })
  })

  describe('Color Improvement Suggestions', () => {
    it('should suggest improvements for poor contrast', () => {
      const suggestion = checker.suggestColorImprovements('#cccccc', '#ffffff', 4.5)
      
      expect(suggestion.newRatio).toBeGreaterThan(4.5)
      expect(suggestion.adjustedForeground || suggestion.adjustedBackground).toBeDefined()
    })

    it('should maintain color relationships when suggesting improvements', () => {
      const original = checker.calculateContrastRatio('#cccccc', '#ffffff')
      const suggestion = checker.suggestColorImprovements('#cccccc', '#ffffff', 4.5)
      
      expect(suggestion.newRatio).toBeGreaterThan(original)
      expect(suggestion.newRatio).toBeGreaterThanOrEqual(4.5)
    })

    it('should prefer foreground adjustments when possible', () => {
      const suggestion = checker.suggestColorImprovements('#cccccc', '#ffffff', 4.5)
      
      // Should prefer adjusting foreground (text) over background
      if (suggestion.adjustedForeground) {
        expect(suggestion.adjustedBackground).toBeUndefined()
      }
    })
  })

  describe('Edge Cases', () => {
    it('should handle invalid color formats gracefully', () => {
      const result = checker.calculateContrastRatio('invalid-color', '#ffffff')
      expect(result).toBeGreaterThan(0) // Should default to black and still calculate
    })

    it('should handle empty color strings', () => {
      const result = checker.calculateContrastRatio('', '#ffffff')
      expect(result).toBeGreaterThan(0)
    })

    it('should handle case insensitive color names', () => {
      const result1 = checker.calculateContrastRatio('WHITE', 'BLACK')
      const result2 = checker.calculateContrastRatio('white', 'black')
      expect(result1).toBeCloseTo(result2, 5)
    })

    it('should handle colors with extra whitespace', () => {
      const result = checker.calculateContrastRatio('  #ffffff  ', '  #000000  ')
      expect(result).toBeCloseTo(21, 1)
    })
  })

  describe('Performance', () => {
    it('should calculate contrast ratios efficiently', () => {
      const startTime = performance.now()
      
      // Calculate many contrast ratios
      for (let i = 0; i < 1000; i++) {
        checker.calculateContrastRatio('#000000', '#ffffff')
      }
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // Should complete 1000 calculations in reasonable time
      expect(duration).toBeLessThan(100) // Less than 100ms
    })

    it('should handle theme testing efficiently', () => {
      const theme = {
        name: 'test',
        colors: {
          text: '#363636',
          background: '#ffffff',
          surface: '#f8f9fa',
          primary: '#3273dc',
          success: '#23d160',
          danger: '#ff3860',
          textMuted: '#6b7280'
        }
      }
      
      const startTime = performance.now()
      const report = checker.testThemeContrast(theme)
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(50) // Should complete quickly
      expect(report.results.length).toBeGreaterThan(0)
    })
  })
})
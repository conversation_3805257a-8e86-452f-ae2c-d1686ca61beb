<template>
  <div class="card">
    <div class="card-content">
      <div class="media">
        <div class="media-left">
          <span class="icon is-medium">
            <i class="fas fa-share-alt"></i>
          </span>
        </div>
        <div class="media-content">
          <div class="level">
            <div class="level-left">
              <div class="level-item">
                <div>
                  <p class="title is-6">
                    <span class="tag" :class="getAccessLevelColor(share.accessLevel)">
                      {{ getAccessLevelDisplayName(share.accessLevel) }}
                    </span>
                  </p>
                  <p class="subtitle is-7">
                    {{ getAccessLevelDescription(share.accessLevel) }}
                  </p>
                </div>
              </div>
            </div>
            <div class="level-right">
              <div class="level-item">
                <div class="dropdown" :class="{ 'is-active': showDropdown }" ref="dropdown">
                  <div class="dropdown-trigger">
                    <button 
                      class="button is-small" 
                      @click="toggleDropdown"
                      aria-haspopup="true" 
                      aria-controls="dropdown-menu"
                    >
                      <span class="icon">
                        <i class="fas fa-ellipsis-v"></i>
                      </span>
                    </button>
                  </div>
                  <div class="dropdown-menu" role="menu">
                    <div class="dropdown-content">
                      <a class="dropdown-item" @click="copyShareUrl">
                        <span class="icon">
                          <i class="fas fa-copy"></i>
                        </span>
                        <span>Copy Link</span>
                      </a>
                      <a class="dropdown-item" @click="editShare">
                        <span class="icon">
                          <i class="fas fa-edit"></i>
                        </span>
                        <span>Edit</span>
                      </a>
                      <a class="dropdown-item" @click="viewAccessLogs">
                        <span class="icon">
                          <i class="fas fa-chart-line"></i>
                        </span>
                        <span>View Access Logs</span>
                      </a>
                      <hr class="dropdown-divider">
                      <a class="dropdown-item has-text-danger" @click="deleteShare">
                        <span class="icon">
                          <i class="fas fa-trash"></i>
                        </span>
                        <span>Delete</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Permissions -->
          <div class="field is-grouped is-grouped-multiline">
            <div class="control">
              <div class="tags has-addons">
                <span class="tag is-dark">Permissions</span>
                <span 
                  v-for="permission in share.permissions" 
                  :key="permission"
                  class="tag" 
                  :class="getPermissionColor(permission)"
                >
                  {{ getPermissionDisplayName(permission) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Share Details -->
          <div class="content is-small">
            <div class="columns is-mobile">
              <div class="column">
                <p>
                  <strong>Created:</strong> 
                  {{ formatDate(share.createdAt) }}
                </p>
                <p v-if="share.expiresAt">
                  <strong>Expires:</strong> 
                  <span :class="{ 'has-text-danger': isShareExpired(share) }">
                    {{ formatExpirationDate(share.expiresAt) }}
                  </span>
                </p>
                <p v-else>
                  <strong>Expires:</strong> Never
                </p>
              </div>
              <div class="column">
                <p>
                  <strong>Access Count:</strong> 
                  {{ share.accessCount }}
                </p>
                <p v-if="share.lastAccessedAt">
                  <strong>Last Accessed:</strong> 
                  {{ formatDate(share.lastAccessedAt) }}
                </p>
                <p v-else>
                  <strong>Last Accessed:</strong> Never
                </p>
              </div>
            </div>

            <!-- Additional Security Features -->
            <div v-if="hasSecurityFeatures" class="tags">
              <span v-if="share.passwordHash" class="tag is-warning">
                <span class="icon">
                  <i class="fas fa-lock"></i>
                </span>
                <span>Password Protected</span>
              </span>
              <span v-if="share.allowedIps && share.allowedIps.length > 0" class="tag is-info">
                <span class="icon">
                  <i class="fas fa-shield-alt"></i>
                </span>
                <span>IP Restricted ({{ share.allowedIps.length }})</span>
              </span>
              <span v-if="share.watermark" class="tag is-light">
                <span class="icon">
                  <i class="fas fa-stamp"></i>
                </span>
                <span>Watermarked</span>
              </span>
            </div>
          </div>

          <!-- Share URL Display -->
          <div v-if="showUrl" class="field has-addons">
            <div class="control is-expanded">
              <input 
                type="text" 
                class="input is-small" 
                :value="share.shareUrl" 
                readonly
              >
            </div>
            <div class="control">
              <button 
                class="button is-small is-info" 
                @click="copyShareUrl"
                :class="{ 'is-loading': copying }"
              >
                <span class="icon">
                  <i class="fas fa-copy"></i>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Modal -->
    <ShareEditModal 
      v-if="showEditModal"
      :share="share"
      @close="showEditModal = false"
      @updated="handleShareUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { 
  getAccessLevelDisplayName, 
  getAccessLevelDescription,
  getAccessLevelColor,
  getPermissionDisplayName,
  getPermissionColor,
  formatExpirationDate,
  isShareExpired,
  type NoteShare 
} from '../../types/noteShare';
import ShareEditModal from './ShareEditModal.vue';

interface Props {
  share: NoteShare;
  showUrl?: boolean;
}

interface Emits {
  (e: 'update', shareId: string, updateData: any): void;
  (e: 'delete', shareId: string): void;
  (e: 'copy-url', shareUrl: string): void;
  (e: 'view-logs', shareId: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  showUrl: true
});

const emit = defineEmits<Emits>();

// Reactive state
const showDropdown = ref(false);
const showEditModal = ref(false);
const copying = ref(false);
const dropdown = ref<HTMLElement>();

// Computed properties
const hasSecurityFeatures = computed(() => {
  return !!(props.share.passwordHash || 
           (props.share.allowedIps && props.share.allowedIps.length > 0) || 
           props.share.watermark);
});

// Methods
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value;
};

const closeDropdown = (event: Event) => {
  if (dropdown.value && !dropdown.value.contains(event.target as Node)) {
    showDropdown.value = false;
  }
};

const copyShareUrl = async () => {
  if (!props.share.shareUrl) return;
  
  copying.value = true;
  try {
    await navigator.clipboard.writeText(props.share.shareUrl);
    emit('copy-url', props.share.shareUrl);
  } catch (error) {
    // Fallback for browsers that don't support clipboard API
    const textArea = document.createElement('textarea');
    textArea.value = props.share.shareUrl;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      document.execCommand('copy');
      emit('copy-url', props.share.shareUrl);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    } finally {
      document.body.removeChild(textArea);
    }
  } finally {
    copying.value = false;
    showDropdown.value = false;
  }
};

const editShare = () => {
  showEditModal.value = true;
  showDropdown.value = false;
};

const deleteShare = () => {
  emit('delete', props.share.id);
  showDropdown.value = false;
};

const viewAccessLogs = () => {
  emit('view-logs', props.share.id);
  showDropdown.value = false;
};

const handleShareUpdated = (updatedShare: NoteShare) => {
  emit('update', updatedShare.id, updatedShare);
  showEditModal.value = false;
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

// Lifecycle
onMounted(() => {
  document.addEventListener('click', closeDropdown);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown);
});
</script>

<style scoped>
.card {
  margin-bottom: 1rem;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  min-width: 12rem;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tags {
  margin-bottom: 0.5rem;
}

.field.has-addons {
  margin-top: 0.75rem;
}

.content.is-small p {
  margin-bottom: 0.25rem;
}

.columns.is-mobile {
  margin-bottom: 0.5rem;
}
</style>
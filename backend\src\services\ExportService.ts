import puppeteer from 'puppeteer';
import { marked } from 'marked';
import { Note } from '../models/Note';

export interface ExportOptions {
  format: 'pdf' | 'html' | 'markdown';
  includeMetadata?: boolean;
  customStyles?: string;
}

export interface ExportJob {
  id: string;
  userId: string;
  noteIds: string[];
  format: 'pdf' | 'html' | 'markdown';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  downloadUrl?: string;
  createdAt: Date;
  completedAt?: Date;
  error?: string;
}

export class ExportService {
  private static exportJobs: Map<string, ExportJob> = new Map();

  /**
   * Export a single note to the specified format
   */
  static async exportNote(note: Note, options: ExportOptions): Promise<Buffer> {
    switch (options.format) {
      case 'pdf':
        return this.exportToPDF(note, options);
      case 'html':
        return this.exportToHTML(note, options);
      case 'markdown':
        return this.exportToMarkdown(note, options);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * Export multiple notes to a single file
   */
  static async exportMultipleNotes(notes: Note[], options: ExportOptions): Promise<Buffer> {
    if (notes.length === 0) {
      throw new Error('No notes to export');
    }

    if (notes.length === 1) {
      return this.exportNote(notes[0], options);
    }

    // For multiple notes, combine them into a single document
    switch (options.format) {
      case 'pdf':
        return this.exportMultipleNotesToPDF(notes, options);
      case 'html':
        return this.exportMultipleNotesToHTML(notes, options);
      case 'markdown':
        return this.exportMultipleNotesToMarkdown(notes, options);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * Create an export job for large operations
   */
  static createExportJob(userId: string, noteIds: string[], format: 'pdf' | 'html' | 'markdown'): ExportJob {
    const job: ExportJob = {
      id: this.generateJobId(),
      userId,
      noteIds,
      format,
      status: 'pending',
      createdAt: new Date()
    };

    this.exportJobs.set(job.id, job);
    return job;
  }

  /**
   * Get export job status
   */
  static getExportJob(jobId: string): ExportJob | undefined {
    return this.exportJobs.get(jobId);
  }

  /**
   * Get user's export jobs
   */
  static getUserExportJobs(userId: string): ExportJob[] {
    return Array.from(this.exportJobs.values())
      .filter(job => job.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * Process export job asynchronously
   */
  static async processExportJob(jobId: string, notes: Note[]): Promise<void> {
    const job = this.exportJobs.get(jobId);
    if (!job) {
      throw new Error('Export job not found');
    }

    try {
      job.status = 'processing';
      this.exportJobs.set(jobId, job);

      const options: ExportOptions = {
        format: job.format,
        includeMetadata: true
      };

      const exportBuffer = await this.exportMultipleNotes(notes, options);
      
      // In a real implementation, you would save this to a file storage service
      // For now, we'll simulate by storing the download URL
      job.downloadUrl = `/api/exports/${jobId}/download`;
      job.status = 'completed';
      job.completedAt = new Date();
      
      this.exportJobs.set(jobId, job);
    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.completedAt = new Date();
      this.exportJobs.set(jobId, job);
    }
  }

  /**
   * Export note to PDF format
   */
  private static async exportToPDF(note: Note, options: ExportOptions): Promise<Buffer> {
    const html = await this.noteToHTML(note, options);
    
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
      const page = await browser.newPage();
      await page.setContent(html, { waitUntil: 'networkidle0' });
      
      const pdfBuffer = await page.pdf({
        format: 'A4',
        margin: {
          top: '1in',
          right: '1in',
          bottom: '1in',
          left: '1in'
        },
        printBackground: true
      });

      return Buffer.from(pdfBuffer);
    } finally {
      await browser.close();
    }
  }

  /**
   * Export note to HTML format
   */
  private static async exportToHTML(note: Note, options: ExportOptions): Promise<Buffer> {
    const html = await this.noteToHTML(note, options);
    return Buffer.from(html, 'utf-8');
  }

  /**
   * Export note to Markdown format
   */
  private static async exportToMarkdown(note: Note, options: ExportOptions): Promise<Buffer> {
    let markdown = '';

    // Add metadata if requested
    if (options.includeMetadata) {
      markdown += `---\n`;
      markdown += `title: ${note.title}\n`;
      markdown += `created: ${note.createdAt.toISOString()}\n`;
      markdown += `updated: ${note.updatedAt.toISOString()}\n`;
      markdown += `type: ${note.noteType}\n`;
      if (note.metadata.wordCount) {
        markdown += `wordCount: ${note.metadata.wordCount}\n`;
      }
      markdown += `---\n\n`;
    }

    // Add title
    markdown += `# ${note.title}\n\n`;

    // Add content based on note type
    switch (note.noteType) {
      case 'markdown':
        markdown += note.content;
        break;
      case 'richtext':
        // Convert rich text to markdown (simplified)
        markdown += this.richTextToMarkdown(note.content);
        break;
      case 'kanban':
        markdown += this.kanbanToMarkdown(note.content);
        break;
    }

    return Buffer.from(markdown, 'utf-8');
  }

  /**
   * Export multiple notes to PDF
   */
  private static async exportMultipleNotesToPDF(notes: Note[], options: ExportOptions): Promise<Buffer> {
    const combinedHTML = await this.multipleNotesToHTML(notes, options);
    
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
      const page = await browser.newPage();
      await page.setContent(combinedHTML, { waitUntil: 'networkidle0' });
      
      const pdfBuffer = await page.pdf({
        format: 'A4',
        margin: {
          top: '1in',
          right: '1in',
          bottom: '1in',
          left: '1in'
        },
        printBackground: true
      });

      return Buffer.from(pdfBuffer);
    } finally {
      await browser.close();
    }
  }

  /**
   * Export multiple notes to HTML
   */
  private static async exportMultipleNotesToHTML(notes: Note[], options: ExportOptions): Promise<Buffer> {
    const html = await this.multipleNotesToHTML(notes, options);
    return Buffer.from(html, 'utf-8');
  }

  /**
   * Export multiple notes to Markdown
   */
  private static async exportMultipleNotesToMarkdown(notes: Note[], options: ExportOptions): Promise<Buffer> {
    let markdown = '';

    // Add table of contents
    markdown += `# Notes Export\n\n`;
    markdown += `## Table of Contents\n\n`;
    notes.forEach((note, index) => {
      markdown += `${index + 1}. [${note.title}](#${this.slugify(note.title)})\n`;
    });
    markdown += `\n---\n\n`;

    // Add each note
    notes.forEach((note, index) => {
      if (index > 0) {
        markdown += `\n---\n\n`;
      }

      // Add metadata if requested
      if (options.includeMetadata) {
        markdown += `*Created: ${note.createdAt.toLocaleDateString()}*\n`;
        markdown += `*Updated: ${note.updatedAt.toLocaleDateString()}*\n`;
        markdown += `*Type: ${note.noteType}*\n\n`;
      }

      // Add title with anchor
      markdown += `## ${note.title}\n\n`;

      // Add content based on note type
      switch (note.noteType) {
        case 'markdown':
          markdown += note.content;
          break;
        case 'richtext':
          markdown += this.richTextToMarkdown(note.content);
          break;
        case 'kanban':
          markdown += this.kanbanToMarkdown(note.content);
          break;
      }

      markdown += `\n\n`;
    });

    return Buffer.from(markdown, 'utf-8');
  }

  /**
   * Convert note to HTML
   */
  private static async noteToHTML(note: Note, options: ExportOptions): Promise<string> {
    const styles = this.getDefaultStyles() + (options.customStyles || '');
    
    let contentHTML = '';
    
    switch (note.noteType) {
      case 'markdown':
        contentHTML = await marked(note.content);
        break;
      case 'richtext':
        contentHTML = note.content; // Assuming it's already HTML
        break;
      case 'kanban':
        contentHTML = this.kanbanToHTML(note.content);
        break;
    }

    let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>${this.escapeHtml(note.title)}</title>
    <style>${styles}</style>
</head>
<body>
    <div class="note-export">
        <header class="note-header">
            <h1>${this.escapeHtml(note.title)}</h1>`;

    if (options.includeMetadata) {
      html += `
            <div class="note-metadata">
                <p><strong>Created:</strong> ${note.createdAt.toLocaleDateString()}</p>
                <p><strong>Updated:</strong> ${note.updatedAt.toLocaleDateString()}</p>
                <p><strong>Type:</strong> ${note.noteType}</p>
                ${note.metadata.wordCount ? `<p><strong>Word Count:</strong> ${note.metadata.wordCount}</p>` : ''}
            </div>`;
    }

    html += `
        </header>
        <main class="note-content">
            ${contentHTML}
        </main>
    </div>
</body>
</html>`;

    return html;
  }

  /**
   * Convert multiple notes to HTML
   */
  private static async multipleNotesToHTML(notes: Note[], options: ExportOptions): Promise<string> {
    const styles = this.getDefaultStyles() + (options.customStyles || '');
    
    let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Notes Export</title>
    <style>${styles}</style>
</head>
<body>
    <div class="notes-export">
        <header class="export-header">
            <h1>Notes Export</h1>
            <p>Exported on ${new Date().toLocaleDateString()}</p>
        </header>`;

    // Add table of contents
    html += `
        <nav class="table-of-contents">
            <h2>Table of Contents</h2>
            <ol>`;
    
    notes.forEach((note, index) => {
      html += `<li><a href="#note-${index}">${this.escapeHtml(note.title)}</a></li>`;
    });
    
    html += `
            </ol>
        </nav>`;

    // Add each note
    for (let index = 0; index < notes.length; index++) {
      const note = notes[index];
      let contentHTML = '';
      
      switch (note.noteType) {
        case 'markdown':
          contentHTML = await marked(note.content);
          break;
        case 'richtext':
          contentHTML = note.content;
          break;
        case 'kanban':
          contentHTML = this.kanbanToHTML(note.content);
          break;
      }

      html += `
        <article class="note-export" id="note-${index}">
            <header class="note-header">
                <h2>${this.escapeHtml(note.title)}</h2>`;

      if (options.includeMetadata) {
        html += `
                <div class="note-metadata">
                    <p><strong>Created:</strong> ${note.createdAt.toLocaleDateString()}</p>
                    <p><strong>Updated:</strong> ${note.updatedAt.toLocaleDateString()}</p>
                    <p><strong>Type:</strong> ${note.noteType}</p>
                    ${note.metadata.wordCount ? `<p><strong>Word Count:</strong> ${note.metadata.wordCount}</p>` : ''}
                </div>`;
      }

      html += `
            </header>
            <main class="note-content">
                ${contentHTML}
            </main>
        </article>`;
    }

    html += `
    </div>
</body>
</html>`;

    return html;
  }

  /**
   * Convert rich text to markdown (simplified)
   */
  private static richTextToMarkdown(richText: string): string {
    // This is a simplified conversion - in a real implementation,
    // you might want to use a proper HTML to Markdown converter
    return richText
      .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
      .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
      .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
      .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
      .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
      .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
      .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')
      .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<[^>]*>/g, '') // Remove remaining HTML tags
      .replace(/\n{3,}/g, '\n\n'); // Clean up excessive newlines
  }

  /**
   * Convert kanban board to markdown
   */
  private static kanbanToMarkdown(kanbanContent: string): string {
    try {
      const board = JSON.parse(kanbanContent);
      let markdown = '';

      if (board.columns && Array.isArray(board.columns)) {
        board.columns.forEach((column: any) => {
          markdown += `## ${column.title || 'Untitled Column'}\n\n`;
          
          if (column.cards && Array.isArray(column.cards)) {
            column.cards.forEach((card: any) => {
              markdown += `- **${card.title || 'Untitled Card'}**\n`;
              if (card.description) {
                markdown += `  ${card.description}\n`;
              }
              markdown += '\n';
            });
          }
          
          markdown += '\n';
        });
      }

      return markdown;
    } catch (error) {
      return 'Invalid Kanban board data';
    }
  }

  /**
   * Convert kanban board to HTML
   */
  private static kanbanToHTML(kanbanContent: string): string {
    try {
      const board = JSON.parse(kanbanContent);
      let html = '<div class="kanban-board">';

      if (board.columns && Array.isArray(board.columns)) {
        board.columns.forEach((column: any) => {
          html += `<div class="kanban-column">`;
          html += `<h3 class="column-title">${this.escapeHtml(column.title || 'Untitled Column')}</h3>`;
          html += `<div class="column-cards">`;
          
          if (column.cards && Array.isArray(column.cards)) {
            column.cards.forEach((card: any) => {
              html += `<div class="kanban-card">`;
              html += `<h4 class="card-title">${this.escapeHtml(card.title || 'Untitled Card')}</h4>`;
              if (card.description) {
                html += `<p class="card-description">${this.escapeHtml(card.description)}</p>`;
              }
              html += `</div>`;
            });
          }
          
          html += `</div></div>`;
        });
      }

      html += '</div>';
      return html;
    } catch (error) {
      return '<p>Invalid Kanban board data</p>';
    }
  }

  /**
   * Get default CSS styles for exports
   */
  private static getDefaultStyles(): string {
    return `
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      
      .note-export, .notes-export {
        margin-bottom: 40px;
      }
      
      .note-header {
        border-bottom: 2px solid #eee;
        padding-bottom: 20px;
        margin-bottom: 30px;
      }
      
      .note-metadata {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-top: 15px;
      }
      
      .note-metadata p {
        margin: 5px 0;
        font-size: 0.9em;
        color: #666;
      }
      
      .table-of-contents {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      
      .table-of-contents ol {
        margin: 10px 0;
      }
      
      .table-of-contents a {
        text-decoration: none;
        color: #007bff;
      }
      
      .kanban-board {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
      }
      
      .kanban-column {
        flex: 1;
        min-width: 250px;
        background: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
      }
      
      .column-title {
        margin-top: 0;
        color: #495057;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
      }
      
      .kanban-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 3px;
        padding: 10px;
        margin-bottom: 10px;
      }
      
      .card-title {
        margin: 0 0 5px 0;
        font-size: 1em;
        color: #495057;
      }
      
      .card-description {
        margin: 0;
        font-size: 0.9em;
        color: #6c757d;
      }
      
      h1, h2, h3, h4, h5, h6 {
        color: #2c3e50;
      }
      
      code {
        background: #f8f9fa;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Monaco', 'Consolas', monospace;
      }
      
      pre {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
      }
      
      blockquote {
        border-left: 4px solid #007bff;
        margin: 0;
        padding-left: 20px;
        color: #6c757d;
      }
      
      @media print {
        .note-export {
          page-break-after: always;
        }
        
        .note-export:last-child {
          page-break-after: auto;
        }
      }
    `;
  }

  /**
   * Escape HTML characters
   */
  private static escapeHtml(text: string): string {
    const div = { innerHTML: '' } as any;
    div.textContent = text;
    return div.innerHTML || text.replace(/[&<>"']/g, (match: string) => {
      const escapeMap: { [key: string]: string } = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;'
      };
      return escapeMap[match];
    });
  }

  /**
   * Create URL-friendly slug
   */
  private static slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  /**
   * Generate unique job ID
   */
  private static generateJobId(): string {
    return `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clean up old export jobs (should be called periodically)
   */
  static cleanupOldJobs(maxAgeHours: number = 24): void {
    const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
    
    for (const [jobId, job] of this.exportJobs.entries()) {
      if (job.createdAt < cutoffTime) {
        this.exportJobs.delete(jobId);
      }
    }
  }
}
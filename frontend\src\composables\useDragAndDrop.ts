import { ref } from 'vue'
import type { KanbanCard } from '@/types/kanban'

// Global drag state
const draggedCard = ref<KanbanCard | null>(null)
const draggedCardData = ref<any>(null)

export function useDragAndDrop() {
  const startCardDrag = (card: KanbanCard) => {
    console.log('Global: Starting card drag:', card.title)
    draggedCard.value = card
    draggedCardData.value = {
      type: 'card',
      cardId: card.id,
      sourceColumnId: card.columnId,
      currentPosition: card.position
    }
  }

  const endCardDrag = () => {
    console.log('Global: Ending card drag')
    draggedCard.value = null
    draggedCardData.value = null
  }

  const getDraggedCard = () => draggedCard.value
  const getDraggedCardData = () => draggedCardData.value

  return {
    draggedCard,
    draggedCardData,
    startCardDrag,
    endCardDrag,
    getDraggedCard,
    getDraggedCardData
  }
}
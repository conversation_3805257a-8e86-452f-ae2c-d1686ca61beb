// Lazy Chart.js loading for <500ms initialization target
// Only load charts when actually needed, not during app initialization

export interface LazyChartConfig {
  type: 'line' | 'bar' | 'doughnut' | 'pie'
  data: any
  options?: any
}

let chartInstance: any = null
let isLoading = false

// Lazy load Chart.js only when needed
export async function createLazyChart(canvas: HTMLCanvasElement, config: LazyChartConfig): Promise<any> {
  if (isLoading) {
    // Wait for existing load to complete
    while (isLoading) {
      await new Promise(resolve => setTimeout(resolve, 50))
    }
  }

  if (!chartInstance) {
    isLoading = true
    try {
      console.log('📊 Lazy loading Chart.js...')
      
      // Dynamic import Chart.js only when needed
      const [
        { Chart },
        { CategoryScale, LinearScale, PointElement, LineElement, BarElement, ArcElement },
        { Title, Tooltip, Legend }
      ] = await Promise.all([
        import('chart.js/auto'),
        import('chart.js'),
        import('chart.js')
      ])

      // Register minimal components
      Chart.register(
        CategoryScale,
        LinearScale,
        PointElement,
        LineElement,
        BarElement,
        ArcElement,
        Title,
        Tooltip,
        Legend
      )

      chartInstance = Chart
      console.log('✅ Chart.js loaded successfully')
    } catch (error) {
      console.error('❌ Failed to load Chart.js:', error)
      throw error
    } finally {
      isLoading = false
    }
  }

  return new chartInstance(canvas, {
    type: config.type,
    data: config.data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      ...config.options
    }
  })
}

// Preload Chart.js during idle time (not blocking initialization)
export function preloadChartJs(): void {
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      import('chart.js/auto').then(() => {
        console.log('📊 Chart.js preloaded during idle time')
      }).catch(error => {
        console.warn('⚠️ Chart.js preload failed:', error)
      })
    }, { timeout: 5000 })
  }
}

// Fallback chart implementation using Canvas API
export function createFallbackChart(canvas: HTMLCanvasElement, config: LazyChartConfig): void {
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // Simple fallback visualization
  ctx.fillStyle = '#f0f0f0'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  
  ctx.fillStyle = '#666'
  ctx.font = '14px sans-serif'
  ctx.textAlign = 'center'
  ctx.fillText('Chart loading...', canvas.width / 2, canvas.height / 2)
  
  console.log('📊 Using fallback chart rendering')
}
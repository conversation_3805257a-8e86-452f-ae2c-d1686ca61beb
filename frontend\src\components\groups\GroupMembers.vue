<template>
  <div class="group-members">
    <div class="members-list">
      <div v-for="member in group.members" :key="member.userId" class="member-card">
        <div class="member-info">
          <div class="member-avatar">
            <img v-if="member.avatarUrl" :src="member.avatarUrl" :alt="member.displayName" class="avatar-image" />
            <div v-else class="avatar-placeholder">
              {{ getInitials(member.displayName) }}
            </div>
          </div>
          <div class="member-details">
            <h4 class="member-name">{{ member.displayName }}</h4>
            <p class="member-email">{{ member.email }}</p>
            <div class="member-meta">
              <span class="role-badge" :class="getRoleColor(member.role)">
                {{ getRoleDisplayName(member.role) }}
              </span>
              <span v-if="member.userId === group.ownerId" class="owner-badge">
                Owner
              </span>
              <span class="join-date">
                Joined {{ formatDate(member.joinedAt) }}
              </span>
            </div>
          </div>
        </div>
        <div v-if="canManageMembers && member.userId !== group.ownerId" class="member-actions">
          <div class="dropdown" :class="{ 'is-active': activeDropdown === member.userId }">
            <div class="dropdown-trigger">
              <button class="button is-small is-ghost" @click="toggleDropdown(member.userId)">
                <span class="icon">
                  <i class="fas fa-ellipsis-v"></i>
                </span>
              </button>
            </div>
            <div class="dropdown-menu">
              <div class="dropdown-content">
                <a v-if="canChangeRoles && member.role !== 'admin'" class="dropdown-item"
                  @click="changeRole(member, 'admin')">
                  <span class="icon">
                    <i class="fas fa-crown"></i>
                  </span>
                  <span>Make Admin</span>
                </a>
                <a v-if="canChangeRoles && member.role !== 'editor'" class="dropdown-item"
                  @click="changeRole(member, 'editor')">
                  <span class="icon">
                    <i class="fas fa-edit"></i>
                  </span>
                  <span>Make Editor</span>
                </a>
                <a v-if="canChangeRoles && member.role !== 'viewer'" class="dropdown-item"
                  @click="changeRole(member, 'viewer')">
                  <span class="icon">
                    <i class="fas fa-eye"></i>
                  </span>
                  <span>Make Viewer</span>
                </a>
                <hr v-if="canChangeRoles" class="dropdown-divider">
                <a class="dropdown-item has-text-danger" @click="removeMember(member)">
                  <span class="icon">
                    <i class="fas fa-user-minus"></i>
                  </span>
                  <span>Remove Member</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Confirmation Modal -->
    <div v-if="showConfirmModal" class="modal is-active">
      <div class="modal-background" @click="cancelAction"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">{{ confirmAction.title }}</p>
          <button class="delete" @click="cancelAction"></button>
        </header>
        <section class="modal-card-body">
          <p>{{ confirmAction.message }}</p>
        </section>
        <footer class="modal-card-foot">
          <button class="button is-danger" :class="{ 'is-loading': actionLoading }" @click="executeAction">
            {{ confirmAction.confirmText }}
          </button>
          <button class="button" @click="cancelAction">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useGroupsStore } from '../../stores/groups';
import { useAuthStore } from '../../stores/auth';
import { getRoleDisplayName, getRoleColor, getPermissions } from '../../types/group';
import type { GroupWithMembers, GroupMember, UserRole } from '../../types/group';

// Props
const props = defineProps<{
  group: GroupWithMembers;
  userRole: UserRole | null;
}>();

// Emits
const emit = defineEmits<{
  memberRemoved: [];
  roleUpdated: [];
}>();

const groupsStore = useGroupsStore();
const authStore = useAuthStore();

const activeDropdown = ref<string | null>(null);
const showConfirmModal = ref(false);
const actionLoading = ref(false);
const confirmAction = ref<{
  title: string;
  message: string;
  confirmText: string;
  action: () => Promise<void>;
}>({
  title: '',
  message: '',
  confirmText: '',
  action: async () => { }
});

// Computed properties
const permissions = computed(() => {
  return props.userRole ? getPermissions(props.userRole) : null;
});

const canManageMembers = computed(() => {
  return permissions.value?.canRemoveMembers || false;
});

const canChangeRoles = computed(() => {
  return props.userRole === 'admin';
});

// Methods
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

const toggleDropdown = (userId: string) => {
  activeDropdown.value = activeDropdown.value === userId ? null : userId;
};

const changeRole = (member: GroupMember, newRole: UserRole) => {
  activeDropdown.value = null;

  confirmAction.value = {
    title: 'Change Member Role',
    message: `Are you sure you want to change ${member.displayName}'s role to ${getRoleDisplayName(newRole)}?`,
    confirmText: 'Change Role',
    action: async () => {
      try {
        await groupsStore.updateMemberRole(props.group.id, member.userId, { role: newRole });
        emit('roleUpdated');
      } catch (error) {
        console.error('Failed to update member role:', error);
        throw error;
      }
    }
  };

  showConfirmModal.value = true;
};

const removeMember = (member: GroupMember) => {
  activeDropdown.value = null;

  const isCurrentUser = authStore.user?.id === member.userId;

  confirmAction.value = {
    title: isCurrentUser ? 'Leave Group' : 'Remove Member',
    message: isCurrentUser
      ? 'Are you sure you want to leave this group?'
      : `Are you sure you want to remove ${member.displayName} from this group?`,
    confirmText: isCurrentUser ? 'Leave Group' : 'Remove Member',
    action: async () => {
      try {
        await groupsStore.removeMember(props.group.id, member.userId);
        emit('memberRemoved');
      } catch (error) {
        console.error('Failed to remove member:', error);
        throw error;
      }
    }
  };

  showConfirmModal.value = true;
};

const executeAction = async () => {
  try {
    actionLoading.value = true;
    await confirmAction.value.action();
    showConfirmModal.value = false;
  } catch (error) {
    // Error is handled by the store
  } finally {
    actionLoading.value = false;
  }
};

const cancelAction = () => {
  showConfirmModal.value = false;
  activeDropdown.value = null;
};

// Close dropdown when clicking outside
document.addEventListener('click', (event) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.dropdown')) {
    activeDropdown.value = null;
  }
});
</script>

<style scoped>
.group-members {
  padding: 1.5rem;
}

/* Members list */
.members-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Member card */
.member-card {
  background: var(--card-background);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
}

.member-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Member info */
.member-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.member-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.member-details {
  flex: 1;
}

.member-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #363636;
  margin: 0 0 0.25rem 0;
}

.member-email {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
}

.member-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* Role and owner badges */
.role-badge {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  font-weight: 500;
}

.role-badge.is-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.role-badge.is-info {
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.role-badge.is-success {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.owner-badge {
  background: #ffc107;
  color: #212529;
  border: 1px solid #ffc107;
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  font-weight: 500;
}

.join-date {
  color: #6c757d;
  font-size: 0.8rem;
}

/* Member actions */
.member-actions {
  flex-shrink: 0;
}

/* Dropdown styling */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  z-index: 20;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border: 1px solid #e9ecef;
  overflow: hidden;
  min-width: 180px;
}

.dropdown-content {
  padding: 0.5rem 0;
  background: white;
}

.dropdown-item {
  padding: 0.75rem 1rem;
  color: #495057;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #363636;
}

.dropdown-item.has-text-danger {
  color: #dc3545;
}

.dropdown-item.has-text-danger:hover {
  background-color: #f8d7da;
  color: #721c24;
}

.dropdown-divider {
  margin: 0.5rem 0;
  border-color: #e9ecef;
}

.button.is-ghost {
  background: transparent;
  border: none;
  border-radius: 6px;
  padding: 0.5rem;
  transition: all 0.2s ease;
  color: #6c757d;
  cursor: pointer;
}

.button.is-ghost:hover {
  background-color: #f8f9fa;
  color: #495057;
}

/* Modal styling */
.modal-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-card-head {
  background: #007bff;
  color: white;
  border: none;
  padding: 1.5rem 2rem;
}

.modal-card-title {
  color: white;
  font-weight: 600;
}

.modal-card-body {
  padding: 2rem;
  background: white;
}

.modal-card-foot {
  background: #f8f9fa;
  border: none;
  padding: 1.5rem 2rem;
  justify-content: flex-end;
  gap: 1rem;
}

.modal-card-foot .button {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
}

.modal-card-foot .button.is-danger {
  background: #dc3545;
  border: none;
  color: white;
}

.modal-card-foot .button.is-danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .group-members {
    padding: 1rem;
  }

  .member-card {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .member-info {
    width: 100%;
  }

  .member-actions {
    align-self: flex-end;
  }

  .dropdown-menu {
    position: fixed;
    right: 1rem;
    left: 1rem;
    top: auto;
    width: auto;
  }
}

@media (max-width: 480px) {
  .group-members {
    padding: 0.75rem;
  }

  .member-card {
    padding: 0.75rem;
  }

  .member-avatar {
    width: 40px;
    height: 40px;
  }

  .avatar-placeholder {
    font-size: 0.9rem;
  }

  .member-name {
    font-size: 1rem;
  }

  .member-email {
    font-size: 0.8rem;
  }
}
</style>
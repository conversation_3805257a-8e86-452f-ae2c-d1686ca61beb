import { describe, it, expect } from 'vitest';

// Simple validation functions to test
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

const sanitizeInput = (input: string): string => {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

const validateNoteTitle = (title: string): { isValid: boolean; error?: string } => {
  if (!title || title.trim().length === 0) {
    return { isValid: false, error: 'Title is required' };
  }
  
  if (title.length > 200) {
    return { isValid: false, error: 'Title must be 200 characters or less' };
  }
  
  return { isValid: true };
};

const validateNoteType = (noteType: string): boolean => {
  const validTypes = ['richtext', 'markdown', 'kanban'];
  return validTypes.includes(noteType);
};

const validateTags = (tags: string[]): { isValid: boolean; error?: string } => {
  if (tags.length > 20) {
    return { isValid: false, error: 'Maximum 20 tags allowed' };
  }
  
  for (const tag of tags) {
    if (tag.length > 50) {
      return { isValid: false, error: 'Tag must be 50 characters or less' };
    }
    
    if (tag.trim().length === 0) {
      return { isValid: false, error: 'Tags cannot be empty' };
    }
  }
  
  return { isValid: true };
};

describe('Input Validation', () => {
  describe('Email Validation', () => {
    it('should accept valid email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it('should reject invalid email addresses', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'user@',
        'user@.com',
        'user@example',
        ''
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });
  });

  describe('Password Validation', () => {
    it('should accept strong passwords', () => {
      const strongPasswords = [
        'SecurePass123',
        'MyP@ssw0rd',
        'ComplexPassword1',
        'Str0ng!Pass'
      ];

      strongPasswords.forEach(password => {
        const result = validatePassword(password);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should reject weak passwords', () => {
      const weakPasswords = [
        { password: 'short', expectedErrors: ['Password must be at least 8 characters long'] },
        { password: 'nouppercase123', expectedErrors: ['Password must contain at least one uppercase letter'] },
        { password: 'NOLOWERCASE123', expectedErrors: ['Password must contain at least one lowercase letter'] },
        { password: 'NoNumbers', expectedErrors: ['Password must contain at least one number'] },
        { password: '12345678', expectedErrors: ['Password must contain at least one lowercase letter', 'Password must contain at least one uppercase letter'] }
      ];

      weakPasswords.forEach(({ password, expectedErrors }) => {
        const result = validatePassword(password);
        expect(result.isValid).toBe(false);
        expectedErrors.forEach(error => {
          expect(result.errors).toContain(error);
        });
      });
    });
  });

  describe('Input Sanitization', () => {
    it('should sanitize HTML characters', () => {
      const maliciousInputs = [
        { input: '<script>alert("xss")</script>', expected: '&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;' },
        { input: '<img src="x" onerror="alert(1)">', expected: '&lt;img src=&quot;x&quot; onerror=&quot;alert(1)&quot;&gt;' },
        { input: 'Normal text', expected: 'Normal text' },
        { input: 'Text with "quotes" and \'apostrophes\'', expected: 'Text with &quot;quotes&quot; and &#x27;apostrophes&#x27;' }
      ];

      maliciousInputs.forEach(({ input, expected }) => {
        expect(sanitizeInput(input)).toBe(expected);
      });
    });

    it('should handle empty and null inputs', () => {
      expect(sanitizeInput('')).toBe('');
      expect(sanitizeInput('   ')).toBe('   ');
    });
  });

  describe('Note Title Validation', () => {
    it('should accept valid note titles', () => {
      const validTitles = [
        'My Note',
        'A'.repeat(200), // Exactly 200 characters
        'Note with special chars !@#$%',
        'Unicode title 测试 🚀'
      ];

      validTitles.forEach(title => {
        const result = validateNoteTitle(title);
        expect(result.isValid).toBe(true);
        expect(result.error).toBeUndefined();
      });
    });

    it('should reject invalid note titles', () => {
      const invalidTitles = [
        { title: '', expectedError: 'Title is required' },
        { title: '   ', expectedError: 'Title is required' },
        { title: 'A'.repeat(201), expectedError: 'Title must be 200 characters or less' }
      ];

      invalidTitles.forEach(({ title, expectedError }) => {
        const result = validateNoteTitle(title);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe(expectedError);
      });
    });
  });

  describe('Note Type Validation', () => {
    it('should accept valid note types', () => {
      const validTypes = ['richtext', 'markdown', 'kanban'];

      validTypes.forEach(type => {
        expect(validateNoteType(type)).toBe(true);
      });
    });

    it('should reject invalid note types', () => {
      const invalidTypes = ['invalid', 'text', 'html', '', 'RICHTEXT'];

      invalidTypes.forEach(type => {
        expect(validateNoteType(type)).toBe(false);
      });
    });
  });

  describe('Tags Validation', () => {
    it('should accept valid tag arrays', () => {
      const validTagArrays = [
        [],
        ['work'],
        ['work', 'important', 'project'],
        Array.from({ length: 20 }, (_, i) => `tag${i}`) // Exactly 20 tags
      ];

      validTagArrays.forEach(tags => {
        const result = validateTags(tags);
        expect(result.isValid).toBe(true);
        expect(result.error).toBeUndefined();
      });
    });

    it('should reject invalid tag arrays', () => {
      const invalidTagArrays = [
        { tags: Array.from({ length: 21 }, (_, i) => `tag${i}`), expectedError: 'Maximum 20 tags allowed' },
        { tags: ['valid', 'A'.repeat(51)], expectedError: 'Tag must be 50 characters or less' },
        { tags: ['valid', ''], expectedError: 'Tags cannot be empty' },
        { tags: ['valid', '   '], expectedError: 'Tags cannot be empty' }
      ];

      invalidTagArrays.forEach(({ tags, expectedError }) => {
        const result = validateTags(tags);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe(expectedError);
      });
    });
  });

  describe('Security Validation', () => {
    it('should detect SQL injection attempts', () => {
      const sqlInjectionAttempts = [
        "1'; DROP TABLE users; --",
        "1' OR '1'='1",
        "1' UNION SELECT * FROM users --",
        '1"; DELETE FROM notes; --'
      ];

      const containsSqlInjection = (input: string): boolean => {
        const sqlPatterns = [
          /DROP\s+TABLE/i,
          /DELETE\s+FROM/i,
          /UNION\s+SELECT/i,
          /--/,
          /;\s*DROP/i,
          /'\s*OR\s*'/i
        ];
        return sqlPatterns.some(pattern => pattern.test(input));
      };

      sqlInjectionAttempts.forEach(attempt => {
        expect(containsSqlInjection(attempt)).toBe(true);
      });
    });

    it('should allow safe inputs', () => {
      const safeInputs = [
        'user-123',
        'note_456',
        'abc123def',
        '12345',
        'normal text input'
      ];

      const containsSqlInjection = (input: string): boolean => {
        const sqlPatterns = [
          /DROP\s+TABLE/i,
          /DELETE\s+FROM/i,
          /UNION\s+SELECT/i,
          /--/,
          /;\s*DROP/i,
          /'\s*OR\s*'/i
        ];
        return sqlPatterns.some(pattern => pattern.test(input));
      };

      safeInputs.forEach(input => {
        expect(containsSqlInjection(input)).toBe(false);
      });
    });
  });

  describe('Content Size Validation', () => {
    it('should validate content size limits', () => {
      const validateContentSize = (content: string, maxSize: number): boolean => {
        return Buffer.byteLength(content, 'utf8') <= maxSize;
      };

      // Test with 1MB limit
      const oneMB = 1024 * 1024;
      
      expect(validateContentSize('Small content', oneMB)).toBe(true);
      expect(validateContentSize('A'.repeat(oneMB), oneMB)).toBe(true);
      expect(validateContentSize('A'.repeat(oneMB + 1), oneMB)).toBe(false);
    });

    it('should handle Unicode content correctly', () => {
      const validateContentSize = (content: string, maxSize: number): boolean => {
        return Buffer.byteLength(content, 'utf8') <= maxSize;
      };

      const unicodeContent = '测试内容 🚀 émojis';
      const contentSize = Buffer.byteLength(unicodeContent, 'utf8');
      
      expect(validateContentSize(unicodeContent, contentSize)).toBe(true);
      expect(validateContentSize(unicodeContent, contentSize - 1)).toBe(false);
    });
  });
});
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import NoteEditor from '../NoteEditor.vue'
import type { Note } from '../../../services/noteService'

describe('NoteEditor - Kanban Integration', () => {
  let wrapper: any
  const testingPinia = createTestingPinia({ createSpy: vi.fn })

  const mockKanbanNote: Note = {
    id: 'note-1',
    userId: 'user-1',
    title: 'Test Kanban Board',
    content: JSON.stringify({
      id: 'board-1',
      title: 'Test Board',
      columns: [
        {
          id: 'col-1',
          title: 'To Do',
          position: 0,
          boardId: 'board-1',
          cards: [
            {
              id: 'card-1',
              title: 'Test Card',
              description: 'Test Description',
              position: 0,
              columnId: 'col-1',
              createdAt: '2023-01-01T00:00:00Z',
              updatedAt: '2023-01-01T00:00:00Z'
            }
          ],
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        }
      ],
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    }),
    noteType: 'kanban',
    metadata: { wordCount: 2, readingTime: 1 },
    isArchived: false,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    tags: []
  }

  beforeEach(() => {
    wrapper = mount(NoteEditor, {
      props: {
        note: mockKanbanNote
      },
      global: {
        plugins: [testingPinia]
      }
    })
  })

  it('renders KanbanBoardInline for kanban note type', () => {
    expect(wrapper.find('.kanban-board-inline').exists()).toBe(true)
    expect(wrapper.find('.markdown-editor').exists()).toBe(false)
    expect(wrapper.find('.richtext-editor').exists()).toBe(false)
  })

  it('displays kanban board content', () => {
    expect(wrapper.find('.kanban-column').exists()).toBe(true)
    expect(wrapper.find('.column-title').text()).toBe('To Do')
    expect(wrapper.find('.kanban-card').exists()).toBe(true)
    expect(wrapper.find('.card-title').text()).toBe('Test Card')
  })

  it('shows kanban option in note type selector for new notes', () => {
    const newNoteWrapper = mount(NoteEditor, {
      props: {
        note: null
      },
      global: {
        plugins: [testingPinia]
      }
    })

    const select = newNoteWrapper.find('select')
    const options = select.findAll('option')
    
    expect(options.some(option => option.text() === 'Kanban Board')).toBe(true)
  })

  it('can create new kanban note', async () => {
    const newNoteWrapper = mount(NoteEditor, {
      props: {
        note: null
      },
      global: {
        plugins: [testingPinia]
      }
    })

    // Start creating a note
    await newNoteWrapper.vm.startCreating()

    // Select kanban type
    const select = newNoteWrapper.find('select')
    await select.setValue('kanban')

    // Should show kanban board inline
    expect(newNoteWrapper.find('.kanban-board-inline').exists()).toBe(true)
  })

  it('preserves kanban data structure', () => {
    // Check that the kanban board structure is preserved in the DOM
    expect(wrapper.find('.kanban-column').exists()).toBe(true)
    expect(wrapper.find('.kanban-card').exists()).toBe(true)
    
    // Check that the content is properly structured JSON
    const content = wrapper.vm.localContent
    expect(() => JSON.parse(content)).not.toThrow()
    
    const parsedContent = JSON.parse(content)
    expect(parsedContent.columns).toBeDefined()
    expect(Array.isArray(parsedContent.columns)).toBe(true)
    expect(parsedContent.columns.length).toBeGreaterThan(0)
  })
})
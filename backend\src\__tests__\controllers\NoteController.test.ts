import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { NoteController } from '../../controllers/NoteController';
import { Note } from '../../models/Note';
import { authMiddleware } from '../../middleware/auth';

// Mock dependencies
vi.mock('../../models/Note');
vi.mock('../../middleware/auth');

const app = express();
app.use(express.json());

// Mock auth middleware to add user to request
vi.mocked(authMiddleware).mockImplementation((req: any, res, next) => {
  req.user = { id: 'user-123', email: '<EMAIL>' };
  next();
});

// Setup routes
const noteController = new NoteController();
app.get('/notes', authMiddleware, noteController.getNotes.bind(noteController));
app.post('/notes', authMiddleware, noteController.createNote.bind(noteController));
app.get('/notes/:id', authMiddleware, noteController.getNote.bind(noteController));
app.put('/notes/:id', authMiddleware, noteController.updateNote.bind(noteController));
app.delete('/notes/:id', authMiddleware, noteController.deleteNote.bind(noteController));
app.get('/notes/:id/versions', authMiddleware, noteController.getNoteVersions.bind(noteController));

describe('NoteController', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('GET /notes', () => {
    it('should return paginated notes for authenticated user', async () => {
      const mockNotes = [
        {
          id: 'note-1',
          title: 'Test Note 1',
          content: 'Content 1',
          noteType: 'richtext',
          userId: 'user-123',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'note-2',
          title: 'Test Note 2',
          content: 'Content 2',
          noteType: 'markdown',
          userId: 'user-123',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: mockNotes,
        total: 2,
        page: 1,
        limit: 10
      });

      const response = await request(app)
        .get('/notes')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('notes');
      expect(response.body).toHaveProperty('total', 2);
      expect(response.body.notes).toHaveLength(2);
    });

    it('should filter notes by type', async () => {
      const mockNotes = [
        {
          id: 'note-1',
          title: 'Markdown Note',
          content: '# Header',
          noteType: 'markdown',
          userId: 'user-123'
        }
      ];

      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: mockNotes,
        total: 1,
        page: 1,
        limit: 10
      });

      const response = await request(app)
        .get('/notes')
        .query({ type: 'markdown' });

      expect(response.status).toBe(200);
      expect(Note.findByUserId).toHaveBeenCalledWith('user-123', {
        page: 1,
        limit: 10,
        type: 'markdown'
      });
    });

    it('should filter notes by tags', async () => {
      const mockNotes = [
        {
          id: 'note-1',
          title: 'Tagged Note',
          content: 'Content',
          noteType: 'richtext',
          tags: ['work', 'important'],
          userId: 'user-123'
        }
      ];

      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: mockNotes,
        total: 1,
        page: 1,
        limit: 10
      });

      const response = await request(app)
        .get('/notes')
        .query({ tags: 'work,important' });

      expect(response.status).toBe(200);
      expect(Note.findByUserId).toHaveBeenCalledWith('user-123', {
        page: 1,
        limit: 10,
        tags: ['work', 'important']
      });
    });
  });

  describe('POST /notes', () => {
    it('should create a new note successfully', async () => {
      const mockNote = {
        id: 'note-123',
        title: 'New Note',
        content: 'Note content',
        noteType: 'richtext',
        userId: 'user-123',
        tags: ['test'],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.mocked(Note.create).mockResolvedValue(mockNote);

      const response = await request(app)
        .post('/notes')
        .send({
          title: 'New Note',
          content: 'Note content',
          noteType: 'richtext',
          tags: ['test']
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id', 'note-123');
      expect(response.body).toHaveProperty('title', 'New Note');
      expect(Note.create).toHaveBeenCalledWith({
        title: 'New Note',
        content: 'Note content',
        noteType: 'richtext',
        userId: 'user-123',
        tags: ['test']
      });
    });

    it('should return 400 for invalid note type', async () => {
      const response = await request(app)
        .post('/notes')
        .send({
          title: 'New Note',
          content: 'Note content',
          noteType: 'invalid-type'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 400 for missing required fields', async () => {
      const response = await request(app)
        .post('/notes')
        .send({
          content: 'Note content'
          // Missing title and noteType
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /notes/:id', () => {
    it('should return note by id for owner', async () => {
      const mockNote = {
        id: 'note-123',
        title: 'Test Note',
        content: 'Note content',
        noteType: 'richtext',
        userId: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.mocked(Note.findById).mockResolvedValue(mockNote);

      const response = await request(app)
        .get('/notes/note-123');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', 'note-123');
      expect(response.body).toHaveProperty('title', 'Test Note');
    });

    it('should return 404 for non-existent note', async () => {
      vi.mocked(Note.findById).mockResolvedValue(null);

      const response = await request(app)
        .get('/notes/non-existent');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Note not found');
    });

    it('should return 403 for note owned by different user', async () => {
      const mockNote = {
        id: 'note-123',
        title: 'Test Note',
        content: 'Note content',
        noteType: 'richtext',
        userId: 'different-user',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.mocked(Note.findById).mockResolvedValue(mockNote);

      const response = await request(app)
        .get('/notes/note-123');

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error', 'Access denied');
    });
  });

  describe('PUT /notes/:id', () => {
    it('should update note successfully', async () => {
      const mockNote = {
        id: 'note-123',
        title: 'Updated Note',
        content: 'Updated content',
        noteType: 'richtext',
        userId: 'user-123',
        updatedAt: new Date()
      };

      vi.mocked(Note.findById).mockResolvedValue({
        id: 'note-123',
        userId: 'user-123'
      } as any);
      vi.mocked(Note.update).mockResolvedValue(mockNote);

      const response = await request(app)
        .put('/notes/note-123')
        .send({
          title: 'Updated Note',
          content: 'Updated content'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('title', 'Updated Note');
      expect(Note.update).toHaveBeenCalledWith('note-123', {
        title: 'Updated Note',
        content: 'Updated content'
      });
    });

    it('should return 404 for non-existent note', async () => {
      vi.mocked(Note.findById).mockResolvedValue(null);

      const response = await request(app)
        .put('/notes/non-existent')
        .send({
          title: 'Updated Note'
        });

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Note not found');
    });

    it('should return 403 for note owned by different user', async () => {
      vi.mocked(Note.findById).mockResolvedValue({
        id: 'note-123',
        userId: 'different-user'
      } as any);

      const response = await request(app)
        .put('/notes/note-123')
        .send({
          title: 'Updated Note'
        });

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error', 'Access denied');
    });
  });

  describe('DELETE /notes/:id', () => {
    it('should delete note successfully', async () => {
      vi.mocked(Note.findById).mockResolvedValue({
        id: 'note-123',
        userId: 'user-123'
      } as any);
      vi.mocked(Note.delete).mockResolvedValue(true);

      const response = await request(app)
        .delete('/notes/note-123');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Note deleted successfully');
      expect(Note.delete).toHaveBeenCalledWith('note-123');
    });

    it('should return 404 for non-existent note', async () => {
      vi.mocked(Note.findById).mockResolvedValue(null);

      const response = await request(app)
        .delete('/notes/non-existent');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Note not found');
    });

    it('should return 403 for note owned by different user', async () => {
      vi.mocked(Note.findById).mockResolvedValue({
        id: 'note-123',
        userId: 'different-user'
      } as any);

      const response = await request(app)
        .delete('/notes/note-123');

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error', 'Access denied');
    });
  });

  describe('GET /notes/:id/versions', () => {
    it('should return note version history', async () => {
      const mockVersions = [
        {
          id: 'version-1',
          noteId: 'note-123',
          content: 'Version 1 content',
          createdAt: new Date('2023-01-01')
        },
        {
          id: 'version-2',
          noteId: 'note-123',
          content: 'Version 2 content',
          createdAt: new Date('2023-01-02')
        }
      ];

      vi.mocked(Note.findById).mockResolvedValue({
        id: 'note-123',
        userId: 'user-123'
      } as any);
      vi.mocked(Note.getVersions).mockResolvedValue(mockVersions);

      const response = await request(app)
        .get('/notes/note-123/versions');

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
      expect(response.body[0]).toHaveProperty('id', 'version-1');
    });

    it('should return 404 for non-existent note', async () => {
      vi.mocked(Note.findById).mockResolvedValue(null);

      const response = await request(app)
        .get('/notes/non-existent/versions');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Note not found');
    });

    it('should return 403 for note owned by different user', async () => {
      vi.mocked(Note.findById).mockResolvedValue({
        id: 'note-123',
        userId: 'different-user'
      } as any);

      const response = await request(app)
        .get('/notes/note-123/versions');

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error', 'Access denied');
    });
  });
});
export type AccessLevel = 'private' | 'shared' | 'unlisted' | 'public';
export type Permission = 'view' | 'comment' | 'edit';

export interface ShareSettings {
  accessLevel: AccessLevel;
  permissions: Permission[];
  expiresAt?: Date;
  passwordProtected: boolean;
  password?: string;
  allowedIps?: string[];
  watermark?: boolean;
}

export interface NoteShare {
  id: string;
  noteId: string;
  shareToken: string;
  accessLevel: AccessLevel;
  permissions: Permission[];
  expiresAt?: string;
  passwordHash?: string;
  allowedIps?: string[];
  watermark: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  accessCount: number;
  lastAccessedAt?: string;
  shareUrl?: string;
  isExpired?: boolean;
}

export interface ShareAccess {
  id: string;
  shareId: string;
  ipAddress: string;
  userAgent: string;
  accessedAt: string;
  userId?: string;
}

export interface CreateShareData {
  accessLevel: AccessLevel;
  permissions: Permission[];
  expiresAt?: string;
  password?: string;
  allowedIps?: string[];
  watermark?: boolean;
}

export interface UpdateShareData {
  accessLevel?: AccessLevel;
  permissions?: Permission[];
  expiresAt?: string;
  password?: string;
  allowedIps?: string[];
  watermark?: boolean;
}

export interface SharedNoteResponse {
  note: {
    id: string;
    title: string;
    content: string;
    noteType: 'richtext' | 'markdown' | 'kanban';
    metadata: any;
    createdAt: string;
    updatedAt: string;
  };
  share: {
    accessLevel: AccessLevel;
    permissions: Permission[];
    watermark: boolean;
    expiresAt?: string;
  };
  watermark?: {
    text: string;
    timestamp: string;
  };
}

export interface ShareFilters {
  accessLevel?: AccessLevel;
  isExpired?: boolean;
}

// Helper functions
export const getAccessLevelDisplayName = (level: AccessLevel): string => {
  switch (level) {
    case 'private':
      return 'Private';
    case 'shared':
      return 'Shared with specific people';
    case 'unlisted':
      return 'Anyone with the link';
    case 'public':
      return 'Public';
    default:
      return 'Unknown';
  }
};

export const getAccessLevelDescription = (level: AccessLevel): string => {
  switch (level) {
    case 'private':
      return 'Only you can access this note';
    case 'shared':
      return 'Only people you specifically share with can access';
    case 'unlisted':
      return 'Anyone with the link can access, but it won\'t appear in search results';
    case 'public':
      return 'Anyone can find and access this note';
    default:
      return '';
  }
};

export const getPermissionDisplayName = (permission: Permission): string => {
  switch (permission) {
    case 'view':
      return 'View';
    case 'comment':
      return 'Comment';
    case 'edit':
      return 'Edit';
    default:
      return 'Unknown';
  }
};

export const getPermissionDescription = (permission: Permission): string => {
  switch (permission) {
    case 'view':
      return 'Can view the note content';
    case 'comment':
      return 'Can view and add comments';
    case 'edit':
      return 'Can view, comment, and edit the note';
    default:
      return '';
  }
};

export const getAccessLevelColor = (level: AccessLevel): string => {
  switch (level) {
    case 'private':
      return 'is-dark';
    case 'shared':
      return 'is-info';
    case 'unlisted':
      return 'is-warning';
    case 'public':
      return 'is-success';
    default:
      return 'is-light';
  }
};

export const getPermissionColor = (permission: Permission): string => {
  switch (permission) {
    case 'view':
      return 'is-info';
    case 'comment':
      return 'is-warning';
    case 'edit':
      return 'is-danger';
    default:
      return 'is-light';
  }
};

export const isShareExpired = (share: NoteShare): boolean => {
  if (!share.expiresAt) return false;
  return new Date() > new Date(share.expiresAt);
};

export const formatExpirationDate = (expiresAt?: string): string => {
  if (!expiresAt) return 'Never expires';
  
  const date = new Date(expiresAt);
  const now = new Date();
  
  if (date < now) {
    return 'Expired';
  }
  
  const diffMs = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) {
    return 'Expires in 1 day';
  } else if (diffDays < 7) {
    return `Expires in ${diffDays} days`;
  } else if (diffDays < 30) {
    const weeks = Math.ceil(diffDays / 7);
    return `Expires in ${weeks} week${weeks > 1 ? 's' : ''}`;
  } else {
    return date.toLocaleDateString();
  }
};

export const getDefaultShareSettings = (): ShareSettings => {
  return {
    accessLevel: 'private',
    permissions: ['view'],
    passwordProtected: false,
    watermark: false
  };
};
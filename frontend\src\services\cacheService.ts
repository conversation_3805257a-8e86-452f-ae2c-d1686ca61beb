// Frontend Cache Service
// Manages client-side caching, service worker, and offline functionality

export interface CacheConfig {
  enableServiceWorker: boolean;
  cacheTimeout: number;
  maxCacheSize: number;
  enableOfflineMode: boolean;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
  tags?: string[];
}

export class CacheService {
  private static instance: CacheService;
  private cache: Map<string, CacheEntry<any>> = new Map();
  private config: CacheConfig;
  private serviceWorker: ServiceWorker | null = null;
  private _isInitialized: boolean = false;

  private constructor(config: CacheConfig) {
    this.config = config;
    this.initializeServiceWorker();
    this.startCleanupInterval();
  }

  static getInstance(config?: CacheConfig): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService(config || {
        enableServiceWorker: true,
        cacheTimeout: 5 * 60 * 1000, // 5 minutes
        maxCacheSize: 100,
        enableOfflineMode: true
      });
    }
    return CacheService.instance;
  }

  // Service Worker Management
  private async initializeServiceWorker(): Promise<void> {
    if (!this.config.enableServiceWorker || !('serviceWorker' in navigator)) {
      console.log('Service Worker not supported or disabled');
      return;
    }

    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered:', registration);

      // Handle service worker updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New service worker is available
              this.notifyServiceWorkerUpdate();
            }
          });
        }
      });

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', this.handleServiceWorkerMessage.bind(this));

      this.serviceWorker = registration.active;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }

  private handleServiceWorkerMessage(event: MessageEvent): void {
    const { type, payload } = event.data;
    
    switch (type) {
      case 'CACHE_UPDATED':
        console.log('Cache updated by service worker:', payload);
        break;
        
      case 'OFFLINE_MODE':
        this.handleOfflineMode(payload.isOffline);
        break;
        
      default:
        console.log('Unknown service worker message:', type);
    }
  }

  private notifyServiceWorkerUpdate(): void {
    // Notify user about service worker update
    const event = new CustomEvent('sw-update-available');
    window.dispatchEvent(event);
  }

  // Memory Cache Management
  set<T>(key: string, data: T, ttl: number = this.config.cacheTimeout, tags?: string[]): void {
    // Check cache size limit
    if (this.cache.size >= this.config.maxCacheSize) {
      this.evictOldestEntries(Math.floor(this.config.maxCacheSize * 0.1));
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl,
      tags
    };

    this.cache.set(key, entry);
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Tag-based cache invalidation
  invalidateByTags(tags: string[]): number {
    let invalidatedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key);
        invalidatedCount++;
      }
    }

    return invalidatedCount;
  }

  // Cache statistics
  getStats(): any {
    const now = Date.now();
    let expiredCount = 0;
    let totalSize = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        expiredCount++;
      }
      totalSize += JSON.stringify(entry).length;
    }

    return {
      totalEntries: this.cache.size,
      expiredEntries: expiredCount,
      approximateSize: totalSize,
      hitRate: this.calculateHitRate()
    };
  }

  // Specific cache methods for common data types
  cacheNotes(notes: any[], ttl?: number): void {
    this.set('notes-list', notes, ttl, ['notes']);
    
    // Cache individual notes
    notes.forEach(note => {
      this.set(`note-${note.id}`, note, ttl, ['notes', `note-${note.id}`]);
    });
  }

  getCachedNotes(): any[] | null {
    return this.get('notes-list');
  }

  cacheNote(note: any, ttl?: number): void {
    this.set(`note-${note.id}`, note, ttl, ['notes', `note-${note.id}`]);
  }

  getCachedNote(noteId: string): any | null {
    return this.get(`note-${noteId}`);
  }

  cacheSearchResults(query: string, results: any[], ttl?: number): void {
    const key = `search-${this.hashString(query)}`;
    this.set(key, results, ttl, ['search']);
  }

  getCachedSearchResults(query: string): any[] | null {
    const key = `search-${this.hashString(query)}`;
    return this.get(key);
  }

  cacheTags(tags: any[], ttl?: number): void {
    this.set('tags-list', tags, ttl, ['tags']);
  }

  getCachedTags(): any[] | null {
    return this.get('tags-list');
  }

  cacheUserSettings(settings: any, ttl?: number): void {
    this.set('user-settings', settings, ttl, ['user', 'settings']);
  }

  getCachedUserSettings(): any | null {
    return this.get('user-settings');
  }

  // Cache invalidation methods
  invalidateNotesCache(): void {
    this.invalidateByTags(['notes']);
  }

  invalidateNoteCache(noteId: string): void {
    this.invalidateByTags([`note-${noteId}`]);
  }

  invalidateSearchCache(): void {
    this.invalidateByTags(['search']);
  }

  invalidateTagsCache(): void {
    this.invalidateByTags(['tags']);
  }

  invalidateUserCache(): void {
    this.invalidateByTags(['user', 'settings']);
  }

  // Offline functionality
  private handleOfflineMode(isOffline: boolean): void {
    if (!this.config.enableOfflineMode) return;

    const event = new CustomEvent('offline-status-changed', {
      detail: { isOffline }
    });
    window.dispatchEvent(event);

    if (isOffline) {
      console.log('App is now offline - using cached data');
    } else {
      console.log('App is back online - syncing data');
      this.syncOfflineData();
    }
  }

  private async syncOfflineData(): Promise<void> {
    // Trigger background sync if service worker is available
    if (this.serviceWorker && 'sync' in (window as any).ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await (registration as any).sync.register('sync-notes');
        await (registration as any).sync.register('sync-user-actions');
        console.log('Background sync registered');
      } catch (error) {
        console.error('Background sync registration failed:', error);
      }
    }
  }

  // Getter for initialization status
  get isInitialized(): boolean {
    return this._isInitialized;
  }

  // Reset initialization flag
  resetInitialization(): void {
    this._isInitialized = false;
  }

  // Preload critical data
  async preloadCriticalData(): Promise<void> {
    if (this.isInitialized) {
      console.log('Cache service already initialized, skipping...');
      return;
    }
    
    try {
      // This would typically fetch and cache critical data
      console.log('Preloading critical data...');
      
      // Example: preload user settings, recent notes, etc.
      // const [settings, recentNotes] = await Promise.all([
      //   this.fetchUserSettings(),
      //   this.fetchRecentNotes()
      // ]);
      
      // this.cacheUserSettings(settings);
      // this.cacheNotes(recentNotes);
      
      this._isInitialized = true;
    } catch (error) {
      console.error('Failed to preload critical data:', error);
    }
  }

  // Service worker communication
  sendMessageToServiceWorker(type: string, payload?: any): void {
    if (this.serviceWorker) {
      this.serviceWorker.postMessage({ type, payload });
    }
  }

  clearServiceWorkerCache(): void {
    this.sendMessageToServiceWorker('CLEAR_CACHE');
  }

  updateServiceWorker(): void {
    this.sendMessageToServiceWorker('SKIP_WAITING');
  }

  // Private helper methods
  private evictOldestEntries(count: number): void {
    const entries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp)
      .slice(0, count);

    entries.forEach(([key]) => this.cache.delete(key));
  }

  private startCleanupInterval(): void {
    setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of this.cache.entries()) {
        if (now > entry.expiry) {
          this.cache.delete(key);
        }
      }
    }, 60000); // Clean up every minute
  }

  private calculateHitRate(): number {
    // This would require tracking hits and misses
    // For now, return a placeholder
    return 0;
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

// Export singleton instance
export const cacheService = CacheService.getInstance();
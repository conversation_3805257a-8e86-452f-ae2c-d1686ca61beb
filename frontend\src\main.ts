// Ultra-minimal main.ts for <500ms initialization target
// This loads only the absolute minimum needed for first paint

import { mountMinimalApp } from './minimal-app'

// Start performance measurement
const startTime = performance.now()

// Mount minimal app immediately
mountMinimalApp()

// Log minimal app initialization time
console.log(`🚀 Minimal app initialized in ${(performance.now() - startTime).toFixed(2)}ms`)

// Export performance start time for full app
;(window as any).__APP_START_TIME__ = startTime
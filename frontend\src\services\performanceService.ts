// Performance Monitoring Service
// Tracks and reports performance metrics for the application

export interface PerformanceMetrics {
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  timeToInteractive: number;
}

export interface ApiMetrics {
  endpoint: string;
  method: string;
  duration: number;
  status: number;
  timestamp: number;
  cached: boolean;
}

export interface UserInteractionMetrics {
  action: string;
  element: string;
  duration: number;
  timestamp: number;
}

export interface MemoryMetrics {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  timestamp: number;
}

export class PerformanceService {
  private static instance: PerformanceService;
  private apiMetrics: ApiMetrics[] = [];
  private userInteractions: UserInteractionMetrics[] = [];
  private memorySnapshots: MemoryMetrics[] = [];
  private performanceObserver: PerformanceObserver | null = null;
  private isMonitoring = false;

  private constructor() {
    this.initializePerformanceObserver();
    this.startMemoryMonitoring();
  }

  static getInstance(): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService();
    }
    return PerformanceService.instance;
  }

  // Initialize performance observer for Web Vitals
  private initializePerformanceObserver(): void {
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported');
      return;
    }

    try {
      // Observe paint metrics
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.handlePerformanceEntry(entry);
        }
      });

      // Observe different types of performance entries
      this.performanceObserver.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
      
      this.isMonitoring = true;
      console.log('Performance monitoring initialized');
    } catch (error) {
      console.error('Failed to initialize performance observer:', error);
    }
  }

  private handlePerformanceEntry(entry: PerformanceEntry): void {
    switch (entry.entryType) {
      case 'paint':
        this.handlePaintEntry(entry as PerformancePaintTiming);
        break;
      case 'largest-contentful-paint':
        this.handleLCPEntry(entry as any);
        break;
      case 'first-input':
        this.handleFIDEntry(entry as any);
        break;
      case 'layout-shift':
        this.handleCLSEntry(entry as any);
        break;
    }
  }

  private handlePaintEntry(entry: PerformancePaintTiming): void {
    console.log(`${entry.name}: ${entry.startTime}ms`);
  }

  private handleLCPEntry(entry: any): void {
    console.log(`Largest Contentful Paint: ${entry.startTime}ms`);
  }

  private handleFIDEntry(entry: any): void {
    console.log(`First Input Delay: ${entry.processingStart - entry.startTime}ms`);
  }

  private handleCLSEntry(entry: any): void {
    if (!entry.hadRecentInput) {
      console.log(`Cumulative Layout Shift: ${entry.value}`);
    }
  }

  // API Performance Tracking
  trackApiCall(endpoint: string, method: string, startTime: number, status: number, cached: boolean = false): void {
    const duration = performance.now() - startTime;
    
    const metric: ApiMetrics = {
      endpoint,
      method,
      duration,
      status,
      timestamp: Date.now(),
      cached
    };

    this.apiMetrics.push(metric);
    
    // Keep only last 1000 entries
    if (this.apiMetrics.length > 1000) {
      this.apiMetrics = this.apiMetrics.slice(-1000);
    }

    // Log slow API calls
    if (duration > 1000) {
      console.warn(`Slow API call detected: ${method} ${endpoint} took ${duration.toFixed(2)}ms`);
    }
  }

  // User Interaction Tracking
  trackUserInteraction(action: string, element: string, startTime: number): void {
    const duration = performance.now() - startTime;
    
    const metric: UserInteractionMetrics = {
      action,
      element,
      duration,
      timestamp: Date.now()
    };

    this.userInteractions.push(metric);
    
    // Keep only last 500 entries
    if (this.userInteractions.length > 500) {
      this.userInteractions = this.userInteractions.slice(-500);
    }

    // Log slow interactions
    if (duration > 100) {
      console.warn(`Slow interaction detected: ${action} on ${element} took ${duration.toFixed(2)}ms`);
    }
  }

  // Memory Monitoring
  private startMemoryMonitoring(): void {
    if (!('memory' in performance)) {
      console.warn('Memory API not supported');
      return;
    }

    // Take memory snapshot every 30 seconds
    setInterval(() => {
      this.takeMemorySnapshot();
    }, 30000);

    // Take initial snapshot
    this.takeMemorySnapshot();
  }

  private takeMemorySnapshot(): void {
    if (!('memory' in performance)) return;

    const memory = (performance as any).memory;
    
    const snapshot: MemoryMetrics = {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      timestamp: Date.now()
    };

    this.memorySnapshots.push(snapshot);
    
    // Keep only last 100 snapshots (50 minutes of data)
    if (this.memorySnapshots.length > 100) {
      this.memorySnapshots = this.memorySnapshots.slice(-100);
    }

    // Check for memory leaks
    this.checkMemoryUsage(snapshot);
  }

  private checkMemoryUsage(snapshot: MemoryMetrics): void {
    const usagePercentage = (snapshot.usedJSHeapSize / snapshot.jsHeapSizeLimit) * 100;
    
    if (usagePercentage > 80) {
      console.warn(`High memory usage detected: ${usagePercentage.toFixed(2)}%`);
    }

    // Check for memory growth trend
    if (this.memorySnapshots.length >= 10) {
      const recent = this.memorySnapshots.slice(-10);
      const trend = this.calculateMemoryTrend(recent);
      
      if (trend > 1.5) { // 50% increase over 10 snapshots
        console.warn('Potential memory leak detected - memory usage trending upward');
      }
    }
  }

  private calculateMemoryTrend(snapshots: MemoryMetrics[]): number {
    if (snapshots.length < 2) return 1;
    
    const first = snapshots[0].usedJSHeapSize;
    const last = snapshots[snapshots.length - 1].usedJSHeapSize;
    
    return last / first;
  }

  // Performance Metrics Collection
  getPerformanceMetrics(): PerformanceMetrics {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    const fcp = paint.find(entry => entry.name === 'first-contentful-paint');
    const lcp = performance.getEntriesByType('largest-contentful-paint').pop() as any;
    
    return {
      pageLoadTime: navigation ? navigation.loadEventEnd - navigation.fetchStart : 0,
      firstContentfulPaint: fcp ? fcp.startTime : 0,
      largestContentfulPaint: lcp ? lcp.startTime : 0,
      firstInputDelay: this.getFirstInputDelay(),
      cumulativeLayoutShift: this.getCumulativeLayoutShift(),
      timeToInteractive: this.getTimeToInteractive()
    };
  }

  private getFirstInputDelay(): number {
    const fidEntries = performance.getEntriesByType('first-input') as any[];
    if (fidEntries.length === 0) return 0;
    
    const fid = fidEntries[0];
    return fid.processingStart - fid.startTime;
  }

  private getCumulativeLayoutShift(): number {
    const clsEntries = performance.getEntriesByType('layout-shift') as any[];
    let clsValue = 0;
    
    for (const entry of clsEntries) {
      if (!entry.hadRecentInput) {
        clsValue += entry.value;
      }
    }
    
    return clsValue;
  }

  private getTimeToInteractive(): number {
    // Simplified TTI calculation
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    return navigation ? navigation.domInteractive - navigation.fetchStart : 0;
  }

  // API Performance Analytics
  getApiPerformanceStats(): any {
    if (this.apiMetrics.length === 0) return null;

    const totalCalls = this.apiMetrics.length;
    const cachedCalls = this.apiMetrics.filter(m => m.cached).length;
    const averageDuration = this.apiMetrics.reduce((sum, m) => sum + m.duration, 0) / totalCalls;
    const slowCalls = this.apiMetrics.filter(m => m.duration > 1000).length;
    
    // Group by endpoint
    const endpointStats = this.apiMetrics.reduce((acc, metric) => {
      const key = `${metric.method} ${metric.endpoint}`;
      if (!acc[key]) {
        acc[key] = { count: 0, totalDuration: 0, errors: 0 };
      }
      acc[key].count++;
      acc[key].totalDuration += metric.duration;
      if (metric.status >= 400) {
        acc[key].errors++;
      }
      return acc;
    }, {} as any);

    return {
      totalCalls,
      cachedCalls,
      cacheHitRate: (cachedCalls / totalCalls) * 100,
      averageDuration: averageDuration.toFixed(2),
      slowCalls,
      slowCallRate: (slowCalls / totalCalls) * 100,
      endpointStats: Object.entries(endpointStats).map(([endpoint, stats]: [string, any]) => ({
        endpoint,
        count: stats.count,
        averageDuration: (stats.totalDuration / stats.count).toFixed(2),
        errorRate: (stats.errors / stats.count) * 100
      }))
    };
  }

  // Memory Performance Analytics
  getMemoryStats(): any {
    if (this.memorySnapshots.length === 0) return null;

    const latest = this.memorySnapshots[this.memorySnapshots.length - 1];
    const usagePercentage = (latest.usedJSHeapSize / latest.jsHeapSizeLimit) * 100;
    
    return {
      currentUsage: {
        used: this.formatBytes(latest.usedJSHeapSize),
        total: this.formatBytes(latest.totalJSHeapSize),
        limit: this.formatBytes(latest.jsHeapSizeLimit),
        percentage: usagePercentage.toFixed(2)
      },
      trend: this.memorySnapshots.length >= 10 ? this.calculateMemoryTrend(this.memorySnapshots.slice(-10)) : 1,
      snapshots: this.memorySnapshots.length
    };
  }

  // User Interaction Analytics
  getUserInteractionStats(): any {
    if (this.userInteractions.length === 0) return null;

    const totalInteractions = this.userInteractions.length;
    const averageDuration = this.userInteractions.reduce((sum, i) => sum + i.duration, 0) / totalInteractions;
    const slowInteractions = this.userInteractions.filter(i => i.duration > 100).length;

    // Group by action
    const actionStats = this.userInteractions.reduce((acc, interaction) => {
      if (!acc[interaction.action]) {
        acc[interaction.action] = { count: 0, totalDuration: 0 };
      }
      acc[interaction.action].count++;
      acc[interaction.action].totalDuration += interaction.duration;
      return acc;
    }, {} as any);

    return {
      totalInteractions,
      averageDuration: averageDuration.toFixed(2),
      slowInteractions,
      slowInteractionRate: (slowInteractions / totalInteractions) * 100,
      actionStats: Object.entries(actionStats).map(([action, stats]: [string, any]) => ({
        action,
        count: stats.count,
        averageDuration: (stats.totalDuration / stats.count).toFixed(2)
      }))
    };
  }

  // Utility methods
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Export performance data
  exportPerformanceData(): any {
    return {
      timestamp: Date.now(),
      performanceMetrics: this.getPerformanceMetrics(),
      apiStats: this.getApiPerformanceStats(),
      memoryStats: this.getMemoryStats(),
      interactionStats: this.getUserInteractionStats(),
      isMonitoring: this.isMonitoring
    };
  }

  // Clear collected data
  clearData(): void {
    this.apiMetrics = [];
    this.userInteractions = [];
    this.memorySnapshots = [];
    console.log('Performance data cleared');
  }

  // Stop monitoring
  stopMonitoring(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }
    this.isMonitoring = false;
    console.log('Performance monitoring stopped');
  }
}

// Export singleton instance
export const performanceService = PerformanceService.getInstance();
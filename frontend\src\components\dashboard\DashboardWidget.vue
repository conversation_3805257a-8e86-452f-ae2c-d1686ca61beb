<template>
  <div class="dashboard-widget" :class="widgetClasses">
    <div class="widget-header" v-if="title || $slots.header">
      <div class="widget-title">
        <span class="icon" v-if="icon">
          <i :class="icon"></i>
        </span>
        <h3>{{ title }}</h3>
      </div>
      <div class="widget-actions" v-if="$slots.actions">
        <slot name="actions"></slot>
      </div>
    </div>
    
    <div class="widget-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  title?: string
  icon?: string
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'primary' | 'info' | 'warning' | 'danger'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  variant: 'default'
})

const widgetClasses = computed(() => ({
  [`widget-${props.size}`]: true,
  [`widget-${props.variant}`]: props.variant !== 'default'
}))
</script>

<style scoped>
</style>
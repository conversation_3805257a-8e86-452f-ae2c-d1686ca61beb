import { http } from '../utils/http';
import type { 
  GroupWithMembers, 
  CreateGroupData, 
  UpdateGroupData, 
  InviteUserData, 
  UpdateMemberRoleData,
  GroupInvitation 
} from '../types/group';

export interface GroupsResponse {
  groups: GroupWithMembers[];
  count: number;
}

export interface GroupResponse {
  group: GroupWithMembers;
}

export interface CreateGroupResponse {
  message: string;
  group: GroupWithMembers;
}

export interface InvitationResponse {
  message: string;
  invitation: {
    id: string;
    email: string;
    role: string;
    expiresAt: string;
  };
}

export interface InvitationsResponse {
  invitations: GroupInvitation[];
}

export interface MessageResponse {
  message: string;
}

export class GroupService {
  private static readonly BASE_URL = '/groups';

  // Create a new group
  static async createGroup(groupData: CreateGroupData): Promise<GroupWithMembers> {
    const response = await http.post<CreateGroupResponse>(this.BASE_URL, groupData);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!.group;
  }

  // Get user's groups
  static async getUserGroups(): Promise<GroupWithMembers[]> {
    const response = await http.get<GroupsResponse>(this.BASE_URL);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data?.groups || [];
  }

  // Get group by ID
  static async getGroup(id: string): Promise<GroupWithMembers> {
    const response = await http.get<GroupResponse>(`${this.BASE_URL}/${id}`);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!.group;
  }

  // Update group
  static async updateGroup(id: string, updateData: UpdateGroupData): Promise<GroupWithMembers> {
    const response = await http.put<CreateGroupResponse>(`${this.BASE_URL}/${id}`, updateData);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!.group;
  }

  // Delete group
  static async deleteGroup(id: string): Promise<void> {
    const response = await http.delete<MessageResponse>(`${this.BASE_URL}/${id}`);
    if (response.error) {
      throw new Error(response.error);
    }
  }

  // Invite user to group
  static async inviteUser(groupId: string, inviteData: InviteUserData): Promise<void> {
    const response = await http.post<InvitationResponse>(`${this.BASE_URL}/${groupId}/invite`, inviteData);
    if (response.error) {
      throw new Error(response.error);
    }
  }

  // Accept group invitation
  static async acceptInvitation(token: string): Promise<GroupWithMembers> {
    const response = await http.post<GroupResponse>(`${this.BASE_URL}/invitations/${token}/accept`);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!.group;
  }

  // Remove member from group
  static async removeMember(groupId: string, memberId: string): Promise<void> {
    const response = await http.delete<MessageResponse>(`${this.BASE_URL}/${groupId}/members/${memberId}`);
    if (response.error) {
      throw new Error(response.error);
    }
  }

  // Update member role
  static async updateMemberRole(groupId: string, memberId: string, roleData: UpdateMemberRoleData): Promise<void> {
    const response = await http.put<MessageResponse>(`${this.BASE_URL}/${groupId}/members/${memberId}/role`, roleData);
    if (response.error) {
      throw new Error(response.error);
    }
  }

  // Get group invitations (admin only)
  static async getGroupInvitations(groupId: string): Promise<GroupInvitation[]> {
    const response = await http.get<InvitationsResponse>(`${this.BASE_URL}/${groupId}/invitations`);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data?.invitations || [];
  }

  // Helper method to get user's role in a group
  static getUserRole(group: GroupWithMembers, userId: string): string | null {
    const member = group.members.find(m => m.userId === userId);
    return member ? member.role : null;
  }

  // Helper method to check if user is group owner
  static isGroupOwner(group: GroupWithMembers, userId: string): boolean {
    return group.ownerId === userId;
  }

  // Helper method to check if user is group member
  static isGroupMember(group: GroupWithMembers, userId: string): boolean {
    return group.members.some(m => m.userId === userId);
  }
}
import { NoteRepository } from '../repositories/NoteRepository';
import { UserRepository } from '../repositories/UserRepository';
import { CreateNoteData } from '../models/Note';
import { CreateUserData } from '../models/User';

export class SeedDataManager {
  static async seedDevelopmentData(): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      console.log('Skipping seed data in production environment');
      return;
    }

    try {
      console.log('Seeding development data...');

      // Create test user if not exists
      const testEmail = '<EMAIL>';
      let testUser = await UserRepository.findByEmail(testEmail);

      if (!testUser) {
        const userData: CreateUserData = {
          email: testEmail,
          password: 'TestPassword123!',
          display_name: 'Test User',
          email_verified: true
        };

        testUser = await UserRepository.create(userData);
        console.log('Created test user:', testUser.email);
      }

      // Create sample notes
      const sampleNotes: Omit<CreateNoteData, 'userId'>[] = [
        {
          title: 'Welcome to Your Note-Taking App',
          content: `# Welcome!

This is your first note. You can use **rich text formatting**, create lists, and organize your thoughts.

## Features
- Multiple note formats (Rich Text, Markdown, Kanban)
- Real-time collaboration
- Powerful search
- Tag organization

Start creating your notes and stay organized!`,
          noteType: 'markdown'
        },
        {
          title: 'Project Planning',
          content: JSON.stringify({
            columns: [
              {
                id: 'todo',
                title: 'To Do',
                cards: [
                  {
                    id: 'card1',
                    title: 'Research user requirements',
                    description: 'Gather feedback from potential users'
                  },
                  {
                    id: 'card2',
                    title: 'Design wireframes',
                    description: 'Create initial UI mockups'
                  }
                ]
              },
              {
                id: 'inprogress',
                title: 'In Progress',
                cards: [
                  {
                    id: 'card3',
                    title: 'Implement authentication',
                    description: 'Set up user login and registration'
                  }
                ]
              },
              {
                id: 'done',
                title: 'Done',
                cards: [
                  {
                    id: 'card4',
                    title: 'Set up development environment',
                    description: 'Configure tools and dependencies'
                  }
                ]
              }
            ]
          }),
          noteType: 'kanban'
        },
        {
          title: 'Meeting Notes - Team Sync',
          content: `<h2>Team Sync Meeting</h2>
<p><strong>Date:</strong> Today</p>
<p><strong>Attendees:</strong> Team members</p>

<h3>Agenda</h3>
<ul>
<li>Project status updates</li>
<li>Upcoming deadlines</li>
<li>Blockers and solutions</li>
</ul>

<h3>Action Items</h3>
<ol>
<li>Complete feature implementation by Friday</li>
<li>Review and test new functionality</li>
<li>Prepare demo for stakeholders</li>
</ol>

<p><em>Next meeting: Same time next week</em></p>`,
          noteType: 'richtext'
        },
        {
          title: 'Quick Ideas',
          content: `# Random Thoughts

## App Improvements
- Add dark mode toggle
- Implement keyboard shortcuts
- Create mobile app version

## Personal
- Learn new programming language
- Read more technical books
- Contribute to open source

## Work
- Optimize database queries
- Improve error handling
- Add comprehensive tests`,
          noteType: 'markdown'
        }
      ];

      // Create sample tags
      const sampleTags = [
        // Work-related tags
        'work', 'meeting', 'project', 'urgent', 'todo', 'review', 'planning',
        'documentation', 'bug', 'feature', 'deadline', 'client', 'team',
        
        // Personal tags
        'personal', 'ideas', 'notes', 'draft', 'learning', 'goals', 'health',
        'finance', 'travel', 'hobby', 'family', 'friends',
        
        // Content type tags
        'research', 'reference', 'tutorial', 'checklist', 'template', 'archive',
        'important', 'favorite', 'public', 'private'
      ];
      const createdTags = [];

      for (const tagName of sampleTags) {
        try {
          const tag = await NoteRepository.createTag(tagName, testUser.id);
          createdTags.push(tag);
          console.log(`Created tag: ${tagName}`);
        } catch (error) {
          // Tag might already exist, get it
          const existingTags = await NoteRepository.getTagsByUserId(testUser.id);
          const existingTag = existingTags.find(t => t.name === tagName);
          if (existingTag) {
            createdTags.push(existingTag);
          }
        }
      }

      // Create notes and associate with tags
      for (let i = 0; i < sampleNotes.length; i++) {
        const noteData: CreateNoteData = {
          ...sampleNotes[i],
          userId: testUser.id
        };

        try {
          const note = await NoteRepository.create(noteData);
          console.log(`Created note: ${note.title}`);

          // Add tags to notes
          const tagIndices = [
            [0, 2], // work, ideas
            [0, 3], // work, project  
            [0, 3], // work, meeting
            [1, 2]  // personal, ideas
          ];

          if (tagIndices[i]) {
            for (const tagIndex of tagIndices[i]) {
              if (createdTags[tagIndex]) {
                await NoteRepository.addTagToNote(note.id, createdTags[tagIndex].id);
              }
            }
          }
        } catch (error) {
          console.log(`Note "${sampleNotes[i].title}" might already exist`);
        }
      }

      console.log('Development data seeded successfully');
    } catch (error) {
      console.error('Error seeding development data:', error);
    }
  }

  static async clearAllData(): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Cannot clear data in production environment');
    }

    console.log('Clearing all development data...');
    
    // This would be implemented if needed for testing
    // For now, we'll just log the intent
    console.log('Data clearing not implemented - use database reset instead');
  }
}
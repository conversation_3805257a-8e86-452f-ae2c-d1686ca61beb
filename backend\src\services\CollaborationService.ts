import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { UserRepository } from '../repositories/UserRepository';

export interface AuthenticatedSocket extends Socket {
  userId: string;
  userEmail: string;
}

export interface NoteOperation {
  type: 'insert' | 'delete' | 'replace';
  position: number;
  content?: string;
  length?: number;
  author?: string;
  timestamp?: number;
}

export interface CollaborationEvent {
  noteId: string;
  userId: string;
  userEmail?: string;
  timestamp: number;
}

export class CollaborationService {
  private io: SocketIOServer;
  private activeUsers = new Map<string, Set<string>>(); // noteId -> Set of userIds
  private userSockets = new Map<string, string>(); // userId -> socketId

  constructor(io: SocketIOServer) {
    this.io = io;
    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    // Authentication middleware for WebSocket connections
    this.io.use(async (socket: Socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        
        if (!token) {
          return next(new Error('Authentication error: No token provided'));
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any;
        
        // Verify user exists and is active
        const user = await UserRepository.findById(decoded.userId);
        if (!user) {
          return next(new Error('Authentication error: User not found'));
        }

        // Attach user info to socket
        (socket as AuthenticatedSocket).userId = decoded.userId;
        (socket as AuthenticatedSocket).userEmail = decoded.email;

        next();
      } catch (error) {
        console.error('WebSocket authentication error:', error);
        next(new Error('Authentication error: Invalid token'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log(`User ${socket.userEmail} connected with socket ${socket.id}`);
      
      // Store user socket mapping
      this.userSockets.set(socket.userId, socket.id);

      // Handle note joining
      socket.on('note:join', (data: { noteId: string }) => {
        this.handleNoteJoin(socket, data.noteId);
      });

      // Handle note leaving
      socket.on('note:leave', (data: { noteId: string }) => {
        this.handleNoteLeave(socket, data.noteId);
      });

      // Handle note editing
      socket.on('note:edit', (data: { noteId: string; operation: NoteOperation }) => {
        this.handleNoteEdit(socket, data.noteId, data.operation);
      });

      // Handle cursor updates
      socket.on('cursor:update', (data: { noteId: string; position: number; selection?: any }) => {
        this.handleCursorUpdate(socket, data);
      });

      // Handle typing indicators
      socket.on('typing:start', (data: { noteId: string }) => {
        this.handleTypingStart(socket, data.noteId);
      });

      socket.on('typing:stop', (data: { noteId: string }) => {
        this.handleTypingStop(socket, data.noteId);
      });

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        this.handleDisconnect(socket, reason);
      });
    });
  }

  private handleNoteJoin(socket: AuthenticatedSocket, noteId: string) {
    try {
      // Join the socket to the note room
      socket.join(`note:${noteId}`);

      // Track active users for this note
      if (!this.activeUsers.has(noteId)) {
        this.activeUsers.set(noteId, new Set());
      }
      this.activeUsers.get(noteId)!.add(socket.userId);

      // Notify other users in the note
      socket.to(`note:${noteId}`).emit('user:joined', {
        noteId,
        userId: socket.userId,
        userEmail: socket.userEmail,
        timestamp: Date.now()
      });

      console.log(`User ${socket.userEmail} joined note ${noteId}`);
    } catch (error) {
      console.error('Error handling note join:', error);
      socket.emit('error', { message: 'Failed to join note' });
    }
  }

  private handleNoteLeave(socket: AuthenticatedSocket, noteId: string) {
    try {
      // Leave the socket room
      socket.leave(`note:${noteId}`);

      // Remove user from active users
      if (this.activeUsers.has(noteId)) {
        this.activeUsers.get(noteId)!.delete(socket.userId);
        
        // Clean up empty note rooms
        if (this.activeUsers.get(noteId)!.size === 0) {
          this.activeUsers.delete(noteId);
        }
      }

      // Notify other users
      socket.to(`note:${noteId}`).emit('user:left', {
        noteId,
        userId: socket.userId,
        timestamp: Date.now()
      });

      console.log(`User ${socket.userEmail} left note ${noteId}`);
    } catch (error) {
      console.error('Error handling note leave:', error);
    }
  }

  private handleNoteEdit(socket: AuthenticatedSocket, noteId: string, operation: NoteOperation) {
    try {
      // Add metadata to the operation
      const enrichedOperation = {
        ...operation,
        author: socket.userId,
        timestamp: Date.now()
      };

      // Broadcast the operation to other users in the note
      socket.to(`note:${noteId}`).emit('note:operation', {
        noteId,
        operation: enrichedOperation,
        userId: socket.userId,
        timestamp: Date.now()
      });

      console.log(`User ${socket.userEmail} edited note ${noteId}:`, operation.type);
    } catch (error) {
      console.error('Error handling note edit:', error);
      socket.emit('error', { message: 'Failed to process edit operation' });
    }
  }

  private handleCursorUpdate(socket: AuthenticatedSocket, data: { noteId: string; position: number; selection?: any }) {
    try {
      // Broadcast cursor position to other users
      socket.to(`note:${data.noteId}`).emit('cursor:update', {
        noteId: data.noteId,
        userId: socket.userId,
        position: data.position,
        selection: data.selection,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error handling cursor update:', error);
    }
  }

  private handleTypingStart(socket: AuthenticatedSocket, noteId: string) {
    try {
      socket.to(`note:${noteId}`).emit('user:typing', {
        noteId,
        userId: socket.userId,
        isTyping: true,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error handling typing start:', error);
    }
  }

  private handleTypingStop(socket: AuthenticatedSocket, noteId: string) {
    try {
      socket.to(`note:${noteId}`).emit('user:typing', {
        noteId,
        userId: socket.userId,
        isTyping: false,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error handling typing stop:', error);
    }
  }

  private handleDisconnect(socket: AuthenticatedSocket, reason: string) {
    try {
      console.log(`User ${socket.userEmail} disconnected: ${reason}`);

      // Remove user from all active notes
      for (const [noteId, users] of this.activeUsers.entries()) {
        if (users.has(socket.userId)) {
          users.delete(socket.userId);
          
          // Notify other users in the note
          socket.to(`note:${noteId}`).emit('user:left', {
            noteId,
            userId: socket.userId,
            timestamp: Date.now()
          });

          // Clean up empty note rooms
          if (users.size === 0) {
            this.activeUsers.delete(noteId);
          }
        }
      }

      // Remove user socket mapping
      this.userSockets.delete(socket.userId);
    } catch (error) {
      console.error('Error handling disconnect:', error);
    }
  }

  // Public methods for external use
  public getActiveUsers(noteId: string): string[] {
    return Array.from(this.activeUsers.get(noteId) || []);
  }

  public isUserActive(noteId: string, userId: string): boolean {
    return this.activeUsers.get(noteId)?.has(userId) || false;
  }

  public broadcastToNote(noteId: string, event: string, data: any) {
    this.io.to(`note:${noteId}`).emit(event, data);
  }

  public sendToUser(userId: string, event: string, data: any) {
    const socketId = this.userSockets.get(userId);
    if (socketId) {
      this.io.to(socketId).emit(event, data);
    }
  }
}

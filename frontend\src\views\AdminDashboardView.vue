<template>
  <div class="admin-dashboard">
    <div class="admin-container">
      <!-- Header -->
      <div class="admin-header">
        <div class="header-content">
          <div class="header-info">
            <router-link to="/dashboard" class="button is-light home-button" title="Back to Dashboard">
              <span class="icon">
                <i class="fas fa-home"></i>
              </span>
              <span>Home</span>
            </router-link>
            <div class="header-text">
              <h1 class="page-title">Admin Dashboard</h1>
              <p class="page-subtitle">System overview and management</p>
            </div>
          </div>
          <div class="header-actions">
            <AdminNotifications />
            <button class="button is-primary" @click="refreshDashboard" :class="{ 'is-loading': isLoading }">
              <span class="icon">
                <i class="fas fa-sync-alt"></i>
              </span>
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="error-notification">
        <div class="error-content">
          <span class="icon">
            <i class="fas fa-exclamation-circle"></i>
          </span>
          <p>{{ error }}</p>
          <button class="button is-small" @click="error = null">Dismiss</button>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading && !dashboardStats" class="dashboard-card">
        <div class="loading-state">
          <div class="loader"></div>
          <p>Loading dashboard...</p>
        </div>
      </div>

      <!-- Dashboard Content -->
      <div v-else-if="dashboardStats">
        <!-- System Health Alert -->
        <div v-if="dashboardStats.systemHealth.status !== 'healthy'" class="alert-notification" :class="{
          'alert-warning': dashboardStats.systemHealth.status === 'warning',
          'alert-danger': dashboardStats.systemHealth.status === 'critical'
        }">
          <div class="alert-content">
            <span class="icon">
              <i class="fas fa-exclamation-triangle"></i>
            </span>
            <div class="alert-text">
              <strong>System Health Alert:</strong>
              <span v-if="dashboardStats.systemHealth.status === 'warning'">
                System performance is degraded. Average response time: {{
                  Math.round(dashboardStats.systemHealth.avg_response_time) }}ms
              </span>
              <span v-else>
                System is experiencing critical issues. Error rate: {{ dashboardStats.systemHealth.error_rate.toFixed(2)
                }}%
              </span>
            </div>
          </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-grid">
          <!-- User Stats -->
          <div class="stat-card">
            <div class="dashboard-card">
              <div class="card-header">
                <div class="card-icon">
                  <i class="fas fa-users"></i>
                </div>
                <div class="card-title-section">
                  <h3 class="card-title">{{ dashboardStats.userStats.total }}</h3>
                  <p class="card-subtitle">Total Users</p>
                </div>
              </div>
              <div class="card-stats">
                <div class="stat-item">
                  <span class="stat-label">Online</span>
                  <span class="stat-value online">
                    <i class="fas fa-circle"></i>
                    {{ dashboardStats.userStats.online || 0 }}
                  </span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Verified</span>
                  <span class="stat-value">{{ dashboardStats.userStats.verified }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">New This Week</span>
                  <span class="stat-value">{{ dashboardStats.userStats.new_this_week }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Note Stats -->
          <div class="stat-card">
            <div class="dashboard-card">
              <div class="card-header">
                <div class="card-icon info">
                  <i class="fas fa-sticky-note"></i>
                </div>
                <div class="card-title-section">
                  <h3 class="card-title">{{ dashboardStats.noteStats.total }}</h3>
                  <p class="card-subtitle">Total Notes</p>
                </div>
              </div>
              <div class="card-stats">
                <div class="stat-item">
                  <span class="stat-label">Rich Text</span>
                  <span class="stat-value">{{ dashboardStats.noteStats.richtext }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Markdown</span>
                  <span class="stat-value">{{ dashboardStats.noteStats.markdown }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Kanban</span>
                  <span class="stat-value">{{ dashboardStats.noteStats.kanban }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Group Stats -->
          <div class="stat-card">
            <div class="dashboard-card">
              <div class="card-header">
                <div class="card-icon success">
                  <i class="fas fa-layer-group"></i>
                </div>
                <div class="card-title-section">
                  <h3 class="card-title">{{ dashboardStats.groupStats.total }}</h3>
                  <p class="card-subtitle">Total Groups</p>
                </div>
              </div>
              <div class="card-stats">
                <div class="stat-item">
                  <span class="stat-label">Avg Members</span>
                  <span class="stat-value">{{ Math.round(dashboardStats.groupStats.avg_members || 0) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">New This Week</span>
                  <span class="stat-value">{{ dashboardStats.groupStats.created_this_week }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- System Health -->
          <div class="stat-card">
            <div class="dashboard-card">
              <div class="card-header">
                <div class="card-icon" :class="{
                  'success': dashboardStats.systemHealth.status === 'healthy',
                  'warning': dashboardStats.systemHealth.status === 'warning',
                  'danger': dashboardStats.systemHealth.status === 'critical'
                }">
                  <i class="fas fa-heartbeat"></i>
                </div>
                <div class="card-title-section">
                  <h3 class="card-title text-capitalize">{{ dashboardStats.systemHealth.status }}</h3>
                  <p class="card-subtitle">System Health</p>
                </div>
              </div>
              <div class="card-stats">
                <div class="stat-item">
                  <span class="stat-label">Avg Response</span>
                  <span class="stat-value">{{ Math.round(dashboardStats.systemHealth.avg_response_time) }}ms</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Error Rate</span>
                  <span class="stat-value">{{ dashboardStats.systemHealth.error_rate.toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="content-grid">
          <div class="content-main">
            <div class="dashboard-card">
              <div class="card-header-simple">
                <h3 class="card-title">Recent Activity</h3>
              </div>
              <div class="card-content">
                <div v-if="dashboardStats.recentActivity.length === 0" class="empty-state">
                  <div class="empty-content">
                    <span class="icon">
                      <i class="fas fa-clock"></i>
                    </span>
                    <p>No recent activity</p>
                  </div>
                </div>
                <div v-else class="activity-list">
                  <div v-for="activity in dashboardStats.recentActivity.slice(0, 10)"
                    :key="`${activity.action}-${activity.user_id}-${activity.created_at}`" class="activity-item">
                    <div class="activity-icon">
                      <i class="fas" :class="{
                        'fa-user': activity.resource_type === 'user',
                        'fa-sticky-note': activity.resource_type === 'note',
                        'fa-layer-group': activity.resource_type === 'group',
                        'fa-search': activity.resource_type === 'search',
                        'fa-cog': activity.resource_type === 'system'
                      }"></i>
                    </div>
                    <div class="activity-content">
                      <div class="activity-text">
                        <span class="activity-action">{{ formatAction(activity.action) }}</span>
                        <span class="activity-type">{{ activity.resource_type }}</span>
                      </div>
                      <div class="activity-meta">
                        {{ formatDate(activity.created_at) }} • {{ activity.count }} times
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="content-sidebar">
            <div class="dashboard-card">
              <div class="card-header-simple">
                <h3 class="card-title">Quick Actions</h3>
              </div>
              <div class="card-content">
                <div class="action-buttons">
                  <router-link to="/admin/users" class="action-button primary">
                    <span class="icon">
                      <i class="fas fa-users"></i>
                    </span>
                    <span>Manage Users</span>
                  </router-link>

                  <router-link to="/admin/reports" class="action-button warning">
                    <span class="icon">
                      <i class="fas fa-flag"></i>
                    </span>
                    <span>Content Reports</span>
                  </router-link>

                  <router-link to="/admin/system" class="action-button info">
                    <span class="icon">
                      <i class="fas fa-cogs"></i>
                    </span>
                    <span>System Settings</span>
                  </router-link>

                  <router-link to="/admin/metrics" class="action-button link">
                    <span class="icon">
                      <i class="fas fa-chart-line"></i>
                    </span>
                    <span>Performance Metrics</span>
                  </router-link>

                  <router-link to="/admin/performance" class="action-button success">
                    <span class="icon">
                      <i class="fas fa-tachometer-alt"></i>
                    </span>
                    <span>Performance Monitor</span>
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Admin Activity Log -->
        <div class="audit-section">
          <div class="dashboard-card">
            <AdminAuditTrail :show-filters="false" :limit="10" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAdminStore } from '../stores/admin'
import { storeToRefs } from 'pinia'
import AdminNotifications from '../components/admin/AdminNotifications.vue'
import AdminAuditTrail from '../components/admin/AdminAuditTrail.vue'

const adminStore = useAdminStore()
const { dashboardStats, isLoading, error } = storeToRefs(adminStore)

onMounted(() => {
  adminStore.loadDashboard()
})

const refreshDashboard = () => {
  adminStore.loadDashboard()
}

const formatAction = (action: string) => {
  return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMins / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`

  return date.toLocaleDateString()
}
</script>

<style scoped>
.admin-dashboard {
  min-height: 100vh;
  background: var(--color-surface);
  padding: 0;
}

.admin-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

/* Header */
.admin-header {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 2rem;
}

.header-info {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  flex: 1;
}

.home-button {
  background: var(--color-success);
  color: white;
  border-color: var(--color-success);
  border-radius: 6px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.home-button:hover {
  background: var(--color-success-dark);
  border-color: var(--color-success-dark);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text-strong);
  margin: 0 0 0.5rem 0;
}

.page-subtitle {
  color: var(--color-text-muted);
  font-size: 1.125rem;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  flex-shrink: 0;
  align-items: center;
}

.button {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.button.is-primary {
  background: var(--color-link);
  color: white;
  border-color: var(--color-link);
}

.button.is-primary:hover {
  background: var(--color-link-dark);
  border-color: var(--color-link-dark);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Error notification */
.error-notification {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid var(--color-danger);
}

.error-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.error-content .icon {
  color: var(--color-danger);
  font-size: 1.25rem;
}

.error-content p {
  color: var(--color-text-strong);
  margin: 0;
  flex: 1;
}

/* Alert notification */
.alert-notification {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.alert-warning {
  border-left: 4px solid var(--color-warning);
}

.alert-danger {
  border-left: 4px solid var(--color-danger);
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.alert-content .icon {
  font-size: 1.25rem;
}

.alert-warning .icon {
  color: var(--color-warning);
}

.alert-danger .icon {
  color: var(--color-danger);
}

.alert-text {
  color: var(--color-text-strong);
  flex: 1;
}

/* Dashboard card */
.dashboard-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Loading state */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.loader {
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-link);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-state p {
  color: var(--color-text-muted);
  margin: 0;
}

/* Stats grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  flex-direction: column;
}

/* Card styling */
.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border);
}

.card-header-simple {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid var(--color-border);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  background: var(--color-link);
  flex-shrink: 0;
}

.card-icon.info {
  background: var(--color-info);
}

.card-icon.success {
  background: var(--color-success);
}

.card-icon.warning {
  background: var(--color-warning);
  color: var(--color-text-strong);
}

.card-icon.danger {
  background: var(--color-danger);
}

.card-title-section {
  flex: 1;
}

.card-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--color-text-strong);
  margin: 0 0 0.25rem 0;
}

.card-subtitle {
  color: var(--color-text-muted);
  font-size: 1rem;
  margin: 0;
}

.card-stats {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: var(--color-text-muted);
  font-size: 0.9rem;
}

.stat-value {
  color: var(--color-text-strong);
  font-weight: 600;
  font-size: 0.9rem;
}

.stat-value.online {
  color: var(--color-success);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.text-capitalize {
  text-transform: capitalize;
}

/* Content grid */
.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.content-main {
  display: flex;
  flex-direction: column;
}

.content-sidebar {
  display: flex;
  flex-direction: column;
}

.card-content {
  padding: 1.5rem;
}

/* Activity list */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--color-surface);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--color-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-muted);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.activity-action {
  font-weight: 600;
  color: var(--color-text-strong);
}

.activity-type {
  background: var(--color-surface);
  color: var(--color-text-muted);
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  text-transform: capitalize;
}

.activity-meta {
  color: var(--color-text-muted);
  font-size: 0.8rem;
}

/* Empty state */
.empty-state {
  padding: 2rem;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.empty-content .icon {
  color: var(--color-text-muted);
  font-size: 2rem;
}

.empty-content p {
  color: var(--color-text-muted);
  margin: 0;
}

/* Action buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.action-button.primary {
  background: var(--color-link);
  color: white;
  border-color: var(--color-link);
}

.action-button.primary:hover {
  background: var(--color-link-dark);
  border-color: var(--color-link-dark);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.action-button.warning {
  background: var(--color-warning);
  color: var(--color-text-strong);
  border-color: var(--color-warning);
}

.action-button.warning:hover {
  background: var(--color-warning-dark);
  border-color: var(--color-warning-dark);
  color: var(--color-text-strong);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.action-button.info {
  background: var(--color-info);
  color: white;
  border-color: var(--color-info);
}

.action-button.info:hover {
  background: var(--color-info-dark);
  border-color: var(--color-info-dark);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

.action-button.link {
  background: var(--color-purple, #6f42c1);
  color: white;
  border-color: var(--color-purple, #6f42c1);
}

.action-button.link:hover {
  background: var(--color-purple-dark, #5a32a3);
  border-color: var(--color-purple-dark, #512d92);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(111, 66, 193, 0.3);
}

.action-button.success {
  background: var(--color-success);
  color: white;
  border-color: var(--color-success);
}

.action-button.success:hover {
  background: var(--color-success-dark);
  border-color: var(--color-success-dark);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

/* Audit section */
.audit-section {
  margin-top: 2rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .admin-container {
    padding: 1rem;
  }

  .admin-header {
    padding: 1.5rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1.5rem;
  }

  .header-info {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
  }

  .button {
    flex: 1;
    justify-content: center;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .admin-container {
    padding: 0.75rem;
  }

  .admin-header {
    padding: 1rem;
  }

  .page-title {
    font-size: 1.25rem;
  }

  .card-header {
    padding: 1rem;
  }

  .card-content {
    padding: 1rem;
  }

  .card-stats {
    padding: 0.75rem 1rem 1rem 1rem;
  }
}
</style>
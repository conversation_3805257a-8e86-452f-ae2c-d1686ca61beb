<template>
  <DashboardWidget title="Quick Stats" icon="fas fa-chart-bar">
    <div v-if="loading" class="has-text-centered">
      <div class="loader"></div>
    </div>

    <div v-else class="stats-grid">
      <div class="stat-item" @click="navigateTo('/dashboard/notes')">
        <div class="stat-icon">
          <span class="icon has-text-primary">
            <i class="fas fa-sticky-note"></i>
          </span>
        </div>
        <div class="stat-content">
          <p class="stat-number">{{ stats.totalNotes }}</p>
          <p class="stat-label">Total Notes</p>
        </div>
      </div>

      <div class="stat-item" @click="navigateTo('/dashboard/favorites')">
        <div class="stat-icon">
          <span class="icon has-text-warning">
            <i class="fas fa-star"></i>
          </span>
        </div>
        <div class="stat-content">
          <p class="stat-number">{{ stats.favoriteNotes }}</p>
          <p class="stat-label">Favorites</p>
        </div>
      </div>

      <div class="stat-item" @click="navigateTo('/dashboard/shared')">
        <div class="stat-icon">
          <span class="icon has-text-info">
            <i class="fas fa-share-alt"></i>
          </span>
        </div>
        <div class="stat-content">
          <p class="stat-number">{{ stats.sharedNotes }}</p>
          <p class="stat-label">Shared</p>
        </div>
      </div>

      <div class="stat-item" @click="navigateTo('/dashboard/groups')">
        <div class="stat-icon">
          <span class="icon has-text-success">
            <i class="fas fa-users"></i>
          </span>
        </div>
        <div class="stat-content">
          <p class="stat-number">{{ stats.groups }}</p>
          <p class="stat-label">Groups</p>
        </div>
      </div>
    </div>
  </DashboardWidget>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotesStore } from '../../stores/notes'
import { useGroupsStore } from '../../stores/groups'
import { useAuthStore } from '../../stores/auth'
import DashboardWidget from './DashboardWidget.vue'

interface Stats {
  totalNotes: number
  favoriteNotes: number
  sharedNotes: number
  groups: number
}

const router = useRouter()
const notesStore = useNotesStore()
const groupsStore = useGroupsStore()
const authStore = useAuthStore()

const loading = ref(true)
const stats = ref<Stats>({
  totalNotes: 0,
  favoriteNotes: 0,
  sharedNotes: 0,
  groups: 0
})

const navigateTo = (path: string) => {
  router.push(path)
}

const loadStats = async () => {
  try {
    loading.value = true

    // Only load data if user is authenticated
    if (authStore.isAuthenticated) {
      // Load data from stores
      await Promise.all([
        notesStore.loadNotes?.() || Promise.resolve(),
        groupsStore.loadGroups?.() || Promise.resolve()
      ])
    } else {
      console.log('User not authenticated, using demo data for stats')
    }

    // Calculate stats
    stats.value = {
      totalNotes: notesStore.notes?.length || 42, // Fallback for demo
      favoriteNotes: notesStore.notes?.filter(note => note.metadata?.isFavorite)?.length || 8,
      sharedNotes: notesStore.notes?.filter(note => note.groupId || (note.metadata?.collaborators && note.metadata.collaborators.length > 0))?.length || 5,
      groups: groupsStore.groups?.length || 3
    }
  } catch (error) {
    console.error('Failed to load stats:', error)
    // Use demo data on error
    stats.value = {
      totalNotes: 42,
      favoriteNotes: 8,
      sharedNotes: 5,
      groups: 3
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  cursor: pointer;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

.stat-icon {
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--color-text-strong);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--color-text);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
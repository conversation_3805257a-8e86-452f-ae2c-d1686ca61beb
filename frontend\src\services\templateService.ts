import { httpClient } from '../utils/http';

export interface TemplateMetadata {
  category?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime?: number; // in minutes
  author?: string;
  version?: string;
  usageCount?: number;
}

export interface Template {
  id: string;
  userId: string;
  groupId?: string;
  name: string;
  description?: string;
  noteType: 'richtext' | 'markdown' | 'kanban';
  content: string;
  isPublic: boolean;
  tags: string[];
  metadata: TemplateMetadata;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTemplateData {
  name: string;
  description?: string;
  noteType: 'richtext' | 'markdown' | 'kanban';
  content: string;
  isPublic?: boolean;
  tags?: string[];
  metadata?: TemplateMetadata;
  groupId?: string;
}

export interface UpdateTemplateData {
  name?: string;
  description?: string;
  content?: string;
  isPublic?: boolean;
  tags?: string[];
  metadata?: TemplateMetadata;
}

export interface TemplateFilters {
  noteType?: 'richtext' | 'markdown' | 'kanban';
  category?: string;
  isPublic?: boolean;
  tags?: string[];
  search?: string;
  groupId?: string;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: 'created_at' | 'updated_at' | 'name' | 'usage_count';
  sortOrder?: 'asc' | 'desc';
}

export interface TemplatesResponse {
  templates: Template[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class TemplateService {
  /**
   * Get templates with filtering and pagination
   */
  static async getTemplates(filters: TemplateFilters = {}, pagination: PaginationOptions = {}): Promise<TemplatesResponse> {
    const params = new URLSearchParams();
    
    // Add pagination params
    if (pagination.page) params.append('page', pagination.page.toString());
    if (pagination.limit) params.append('limit', pagination.limit.toString());
    if (pagination.sortBy) params.append('sortBy', pagination.sortBy);
    if (pagination.sortOrder) params.append('sortOrder', pagination.sortOrder);
    
    // Add filter params
    if (filters.noteType) params.append('noteType', filters.noteType);
    if (filters.category) params.append('category', filters.category);
    if (filters.isPublic !== undefined) params.append('isPublic', filters.isPublic.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.groupId) params.append('groupId', filters.groupId);
    if (filters.tags && filters.tags.length > 0) params.append('tags', filters.tags.join(','));

    const response = await httpClient.get(`/templates?${params.toString()}`);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data as TemplatesResponse;
  }

  /**
   * Get a specific template by ID
   */
  static async getTemplate(id: string): Promise<Template> {
    const response = await httpClient.get(`/templates/${id}`);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data as Template;
  }

  /**
   * Create a new template
   */
  static async createTemplate(templateData: CreateTemplateData): Promise<Template> {
    const response = await httpClient.post('/templates', templateData);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data as Template;
  }

  /**
   * Update an existing template
   */
  static async updateTemplate(id: string, updateData: UpdateTemplateData): Promise<Template> {
    const response = await httpClient.put(`/templates/${id}`, updateData);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data as Template;
  }

  /**
   * Delete a template
   */
  static async deleteTemplate(id: string): Promise<void> {
    const response = await httpClient.delete(`/templates/${id}`);
    if (response.error) {
      throw new Error(response.error);
    }
  }

  /**
   * Use a template to create a new note
   */
  static async useTemplate(id: string, options: { title?: string; groupId?: string } = {}): Promise<any> {
    const response = await httpClient.post(`/templates/${id}/use`, options);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data;
  }

  /**
   * Get available template categories
   */
  static async getCategories(): Promise<string[]> {
    const response = await httpClient.get('/templates/categories');
    if (response.error) {
      throw new Error(response.error);
    }
    return (response.data as any).categories;
  }

  /**
   * Seed built-in templates
   */
  static async seedBuiltInTemplates(): Promise<{ message: string; templates: Template[] }> {
    const response = await httpClient.post('/templates/seed');
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data as { message: string; templates: Template[] };
  }

  /**
   * Get template display name for note type
   */
  static getNoteTypeDisplayName(noteType: string): string {
    switch (noteType) {
      case 'richtext':
        return 'Rich Text';
      case 'markdown':
        return 'Markdown';
      case 'kanban':
        return 'Kanban Board';
      default:
        return noteType;
    }
  }

  /**
   * Get template icon for note type
   */
  static getNoteTypeIcon(noteType: string): string {
    switch (noteType) {
      case 'richtext':
        return 'fas fa-font';
      case 'markdown':
        return 'fab fa-markdown';
      case 'kanban':
        return 'fas fa-columns';
      default:
        return 'fas fa-file';
    }
  }

  /**
   * Get difficulty color class
   */
  static getDifficultyColor(difficulty?: string): string {
    switch (difficulty) {
      case 'beginner':
        return 'is-success';
      case 'intermediate':
        return 'is-warning';
      case 'advanced':
        return 'is-danger';
      default:
        return 'is-light';
    }
  }

  /**
   * Format estimated time
   */
  static formatEstimatedTime(minutes?: number): string {
    if (!minutes) return '';
    
    if (minutes < 60) {
      return `${minutes} min`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (remainingMinutes === 0) {
      return `${hours}h`;
    }
    
    return `${hours}h ${remainingMinutes}m`;
  }

  /**
   * Create template from existing note
   */
  static createTemplateFromNote(note: any): CreateTemplateData {
    return {
      name: `${note.title} Template`,
      description: `Template created from note: ${note.title}`,
      noteType: note.noteType,
      content: note.content,
      isPublic: false,
      tags: note.tags?.map((tag: any) => typeof tag === 'string' ? tag : tag.name) || [],
      metadata: {
        category: 'Custom',
        difficulty: 'beginner',
        author: 'User'
      }
    };
  }
}
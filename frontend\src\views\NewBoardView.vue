<template>
  <div class="new-board-view">
    <div class="container">
      <div class="columns is-centered">
        <div class="column is-8-tablet is-6-desktop">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon">
                  <i class="fas fa-plus"></i>
                </span>
                <span>Create New Kanban Board</span>
              </p>
            </div>

            <div class="card-content">
              <form @submit.prevent="createBoard">
                <div class="field">
                  <label class="label">Board Title</label>
                  <div class="control">
                    <input v-model="boardData.title" class="input" type="text" placeholder="Enter board title" required
                      ref="titleInput" />
                  </div>
                </div>

                <div class="field">
                  <label class="label">Description (Optional)</label>
                  <div class="control">
                    <textarea v-model="boardData.description" class="textarea"
                      placeholder="Describe what this board is for..." rows="3"></textarea>
                  </div>
                </div>

                <div class="field">
                  <label class="label">Template</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select v-model="selectedTemplate">
                        <option value="">Start from scratch</option>
                        <option value="basic">Basic (To Do, In Progress, Done)</option>
                        <option value="software">Software Development</option>
                        <option value="marketing">Marketing Campaign</option>
                        <option value="personal">Personal Tasks</option>
                      </select>
                    </div>
                  </div>
                  <p class="help">Choose a template to get started quickly</p>
                </div>

                <div class="field">
                  <label class="label">Visibility</label>
                  <div class="control">
                    <label class="radio">
                      <input v-model="boardData.shareSettings!.isPublic" type="radio" :value="false" />
                      Private - Only you and invited members can see this board
                    </label>
                    <br>
                    <label class="radio">
                      <input v-model="boardData.shareSettings!.isPublic" type="radio" :value="true" />
                      Public - Anyone with the link can view this board
                    </label>
                  </div>
                </div>

                <div class="field is-grouped">
                  <div class="control">
                    <button type="submit" class="button is-primary" :class="{ 'is-loading': isCreating }"
                      :disabled="!boardData.title?.trim() || isCreating">
                      <span class="icon">
                        <i class="fas fa-plus"></i>
                      </span>
                      <span>Create Board</span>
                    </button>
                  </div>
                  <div class="control">
                    <button type="button" class="button" @click="goBack" :disabled="isCreating">
                      Cancel
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <!-- Template Preview -->
          <div v-if="selectedTemplate" class="template-preview">
            <div class="card">
              <div class="card-header">
                <p class="card-header-title">Template Preview</p>
              </div>
              <div class="card-content">
                <div class="template-columns">
                  <div v-for="column in getTemplateColumns(selectedTemplate)" :key="column.title"
                    class="template-column">
                    <div class="column-header">
                      <h6 class="title is-6">{{ column.title }}</h6>
                    </div>
                    <div class="column-cards">
                      <div v-for="card in column.sampleCards || []" :key="card" class="sample-card">
                        {{ card }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useKanbanStore } from '@/stores/kanban'
import { useNotificationStore } from '@/stores/notifications'
import type { KanbanBoard } from '@/types/kanban'

const router = useRouter()
const kanbanStore = useKanbanStore()
const notificationStore = useNotificationStore()

// Reactive state
const isCreating = ref(false)
const selectedTemplate = ref('')
const titleInput = ref<HTMLInputElement>()

const boardData = ref<Partial<KanbanBoard>>({
  title: '',
  description: '',
  columns: [],
  labels: [],
  members: [],
  settings: {
    allowComments: true,
    allowAttachments: true,
    cardCoverImages: true,
    votingEnabled: false,
    dueDateReminders: true,
    backgroundColor: '#f8f9fa'
  },
  shareSettings: {
    isPublic: false,
    allowedUsers: [],
    permissions: {}
  }
})

// Template definitions
const templates = {
  basic: {
    name: 'Basic Kanban',
    columns: [
      { title: 'To Do', sampleCards: ['Plan project', 'Research competitors'] },
      { title: 'In Progress', sampleCards: ['Design mockups'] },
      { title: 'Done', sampleCards: ['Set up repository'] }
    ]
  },
  software: {
    name: 'Software Development',
    columns: [
      { title: 'Backlog', sampleCards: ['User authentication', 'API integration'] },
      { title: 'In Development', sampleCards: ['Dashboard UI'] },
      { title: 'Code Review', sampleCards: ['Login component'] },
      { title: 'Testing', sampleCards: ['User registration'] },
      { title: 'Done', sampleCards: ['Project setup'] }
    ]
  },
  marketing: {
    name: 'Marketing Campaign',
    columns: [
      { title: 'Ideas', sampleCards: ['Social media campaign', 'Email newsletter'] },
      { title: 'Planning', sampleCards: ['Content calendar'] },
      { title: 'In Progress', sampleCards: ['Blog post draft'] },
      { title: 'Review', sampleCards: ['Ad copy'] },
      { title: 'Published', sampleCards: ['Launch announcement'] }
    ]
  },
  personal: {
    name: 'Personal Tasks',
    columns: [
      { title: 'Someday', sampleCards: ['Learn Spanish', 'Read more books'] },
      { title: 'This Week', sampleCards: ['Grocery shopping'] },
      { title: 'Today', sampleCards: ['Morning workout'] },
      { title: 'Done', sampleCards: ['Pay bills'] }
    ]
  }
}

// Methods
const getTemplateColumns = (templateKey: string) => {
  return templates[templateKey as keyof typeof templates]?.columns || []
}

const createBoard = async () => {
  if (!boardData.value.title?.trim()) return

  isCreating.value = true

  try {
    // Apply template if selected
    if (selectedTemplate.value) {
      const template = templates[selectedTemplate.value as keyof typeof templates]
      if (template) {
        boardData.value.columns = template.columns.map((col, index) => ({
          id: `col-${index + 1}`,
          title: col.title,
          position: index,
          boardId: '', // Will be set by the API
          cards: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }))
      }
    }

    const newBoard = await kanbanStore.createBoard(boardData.value)

    notificationStore.addNotification({
      type: 'success',
      message: 'Board created successfully!',
      title: '',
      category: 'content_report',
      read: false
    })

    // Navigate to the new board
    router.push(`/boards/${newBoard.id}`)
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to create board',
      read: false
    })
  } finally {
    isCreating.value = false
  }
}

const goBack = () => {
  router.back()
}

// Lifecycle
onMounted(async () => {
  await nextTick()
  titleInput.value?.focus()
})
</script>

<style scoped>
.new-board-view {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem 0;
}

.card {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.template-preview {
  margin-top: 2rem;
}

.template-columns {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding: 1rem 0;
}

.template-column {
  min-width: 200px;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
}

.column-header {
  margin-bottom: 0.75rem;
}

.column-header .title {
  margin: 0;
  color: #2c3e50;
}

.column-cards {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sample-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #6c757d;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.radio {
  display: block;
  margin-bottom: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
  .new-board-view {
    padding: 1rem;
  }

  .template-columns {
    flex-direction: column;
  }

  .template-column {
    min-width: auto;
  }
}
</style>
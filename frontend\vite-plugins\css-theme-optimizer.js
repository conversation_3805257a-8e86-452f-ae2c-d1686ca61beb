/**
 * Vite plugin for optimizing theme CSS files
 * Handles lazy loading, tree shaking, and cache optimization for themes
 */

import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

export function cssThemeOptimizer(options = {}) {
  const {
    themeDir = 'src/styles/themes/bulmaswatch',
    enableTreeShaking = true,
    enableLazyLoading = true,
    cacheOptimization = true,
  } = options;

  return {
    name: 'css-theme-optimizer',

    configResolved(config) {
      this.isProduction = config.command === 'build';
      this.root = config.root;
    },

    buildStart() {
      if (!this.isProduction) return;

      console.log('🎨 Optimizing theme CSS files...');
    },

    generateBundle(options, bundle) {
      if (!this.isProduction) return;

      // Find theme CSS files in the bundle
      const themeFiles = Object.keys(bundle).filter(
        fileName => fileName.includes('theme-') && fileName.endsWith('.css')
      );

      themeFiles.forEach(fileName => {
        const file = bundle[fileName];
        if (file.type === 'asset' && typeof file.source === 'string') {
          // Optimize theme CSS
          let optimizedCSS = file.source;

          if (enableTreeShaking) {
            optimizedCSS = this.removeUnusedThemeStyles(optimizedCSS);
          }

          if (cacheOptimization) {
            optimizedCSS = this.optimizeForCaching(optimizedCSS);
          }

          // Update the bundle
          file.source = optimizedCSS;

          console.log(`✅ Optimized theme file: ${fileName} (${this.getSize(optimizedCSS)})`);
        }
      });

      // Generate theme manifest for lazy loading
      if (enableLazyLoading) {
        this.generateThemeManifest(bundle, themeFiles);
      }
    },

    removeUnusedThemeStyles(css) {
      // Remove unused CSS rules based on common patterns
      let optimized = css;

      // Remove unused utility classes that are rarely used in themes
      const unusedUtilities = [
        /\.is-size-[7-9]\s*{[^}]*}/g,
        /\.has-text-weight-[^{]*{[^}]*}/g,
        /\.is-family-[^{]*{[^}]*}/g,
      ];

      unusedUtilities.forEach(pattern => {
        optimized = optimized.replace(pattern, '');
      });

      // Remove empty rules
      optimized = optimized.replace(/[^{}]*{\s*}/g, '');

      // Remove duplicate rules (simple deduplication)
      const rules = optimized.split('}');
      const uniqueRules = [...new Set(rules)];
      optimized = uniqueRules.join('}');

      return optimized;
    },

    optimizeForCaching(css) {
      // Sort CSS rules for better compression
      const rules = css.split('}').filter(rule => rule.trim());

      // Sort by selector to improve gzip compression
      rules.sort((a, b) => {
        const selectorA = a.split('{')[0]?.trim() || '';
        const selectorB = b.split('{')[0]?.trim() || '';
        return selectorA.localeCompare(selectorB);
      });

      return rules.join('}') + '}';
    },

    generateThemeManifest(bundle, themeFiles) {
      const manifest = {
        version: '1.0.0',
        themes: {},
        preload: ['default'],
        lazy: [],
      };

      themeFiles.forEach(fileName => {
        const themeName = this.extractThemeName(fileName);
        const file = bundle[fileName];

        manifest.themes[themeName] = {
          file: fileName,
          size: this.getSize(file.source),
          preload: themeName === 'default',
          integrity: this.generateIntegrity(file.source),
        };

        if (themeName !== 'default') {
          manifest.lazy.push(themeName);
        }
      });

      // Add manifest to bundle
      this.emitFile({
        type: 'asset',
        fileName: 'theme-manifest.json',
        source: JSON.stringify(manifest, null, 2),
      });

      console.log(`📋 Generated theme manifest with ${Object.keys(manifest.themes).length} themes`);
    },

    extractThemeName(fileName) {
      const match = fileName.match(/theme-([^-]+)/);
      return match ? match[1] : 'unknown';
    },

    getSize(content) {
      const bytes = Buffer.byteLength(content, 'utf8');
      return bytes > 1024 ? `${(bytes / 1024).toFixed(1)}KB` : `${bytes}B`;
    },

    generateIntegrity(content) {
      // Simple hash for integrity checking
      const crypto = require('crypto');
      return crypto.createHash('sha256').update(content).digest('base64');
    },
  };
}

export default cssThemeOptimizer;

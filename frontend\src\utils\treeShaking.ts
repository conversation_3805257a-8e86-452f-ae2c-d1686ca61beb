// Tree shaking configuration and unused code detection

/**
 * Mark functions and modules for tree shaking
 * This helps bundlers identify what can be safely removed
 */

// Mark unused exports for removal
export const UNUSED_EXPORTS = [
  // Add any exports that are not used in production
] as const

/**
 * Dynamic import wrapper that helps with code splitting
 */
export function lazyImport<T>(
  importFn: () => Promise<{ default: T }>,
  fallback?: T
): () => Promise<T> {
  let cached: T | null = null
  
  return async () => {
    if (cached) return cached
    
    try {
      const module = await importFn()
      cached = module.default
      return cached
    } catch (error) {
      console.warn('Failed to load module:', error)
      if (fallback) {
        cached = fallback
        return cached
      }
      throw error
    }
  }
}

/**
 * Conditional imports based on environment
 */
export const conditionalImports = {
  // Development only imports
  dev: {
    devtools: () => import(/* @vite-ignore */ 'vue-devtools').catch(() => ({ default: null as any })),
    performance: () => import('./performance')
  },
  
  // Production only imports
  prod: {
    analytics: () => import('../services/performanceService'),
    monitoring: () => import('../services/performanceApiService')
  }
}

/**
 * Feature flags for conditional code inclusion
 */
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: import.meta.env.PROD,
  ENABLE_DEBUG_TOOLS: import.meta.env.DEV,
  ENABLE_PERFORMANCE_MONITORING: true,
  ENABLE_OFFLINE_SUPPORT: true,
  ENABLE_PWA_FEATURES: import.meta.env.PROD,
  ENABLE_ADVANCED_SEARCH: true,
  ENABLE_COLLABORATION: true,
  ENABLE_ADMIN_PANEL: true
} as const

/**
 * Remove development code in production builds
 */
export function stripDevCode<T>(devCode: () => T, prodCode?: () => T): T | undefined {
  if (import.meta.env.DEV) {
    return devCode()
  } else if (prodCode) {
    return prodCode()
  }
  return undefined
}

/**
 * Conditional execution based on feature flags
 */
export function withFeature<T>(
  flag: keyof typeof FEATURE_FLAGS,
  code: () => T
): T | undefined {
  return FEATURE_FLAGS[flag] ? code() : undefined
}

/**
 * Bundle size optimization hints
 */
export const OPTIMIZATION_HINTS = {
  // Large dependencies that should be code-split
  HEAVY_DEPENDENCIES: [
    '@tiptap/vue-3',
    '@tiptap/starter-kit',
    'chart.js',
    'marked',
    'highlight.js'
  ],
  
  // Components that should be lazy-loaded
  LAZY_COMPONENTS: [
    'AdminDashboard',
    'PerformanceMonitor',

    'RichTextEditor',
    'MarkdownEditor'
  ],
  
  // Routes that should be code-split
  LAZY_ROUTES: [
    '/admin/*',
    '/board/*',
    '/shared/*'
  ]
} as const

/**
 * Dead code elimination markers
 */
export function markAsUnused(reason: string): void {
  if (import.meta.env.DEV) {
    console.warn(`Unused code detected: ${reason}`)
  }
}

/**
 * Production build optimizations
 */
if (import.meta.env.PROD) {
  // Remove console.log statements in production
  const originalLog = console.log
  console.log = () => {}
  
  // Remove console.debug statements
  console.debug = () => {}
  
  // Keep console.warn and console.error for important messages
}

/**
 * Module federation helpers for micro-frontend architecture
 */
export const MODULE_FEDERATION = {
  // Shared dependencies that can be federated
  SHARED_MODULES: [
    'vue',
    'vue-router',
    'pinia'
  ],
  
  // Remote modules that can be loaded dynamically
  REMOTE_MODULES: {
    // Future: Load admin panel as separate micro-frontend
    // admin: () => import('admin-app/AdminModule')
  }
} as const
<template>
  <div class="auth-layout">
    <div class="auth-container">
      <div class="auth-header">
        <div class="auth-logo">
          <img src="/logo_icon_only.png" alt="CF Notes Pro" class="logo-image" />
        </div>
        <div class="brand-name">CF Notes Pro</div>
        <h1 class="auth-title">Welcome Back</h1>
        <p class="auth-subtitle">Sign in to your account to continue</p>
      </div>
      <div class="auth-form-container">
        <LoginForm />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import LoginForm from '../components/auth/LoginForm.vue'
</script>

<style scoped>
</style>
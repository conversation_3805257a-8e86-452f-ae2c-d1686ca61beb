import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import NoteEditor from '../NoteEditor.vue'
import type { Note, Tag } from '../../../services/noteService'

// Mock the composables
vi.mock('../../../composables/useAutoSave', () => ({
  useAutoSave: vi.fn(() => ({
    updateTitle: vi.fn(),
    updateContent: vi.fn(),
    updateTags: vi.fn(),
    forceSave: vi.fn(),
    cancelAutoSave: vi.fn(),
    initialize: vi.fn()
  })),
  useOfflineSync: vi.fn(() => ({
    isOnline: { value: true },
    queuedOperations: { value: 0 }
  }))
}))

describe('NoteEditor - Tags', () => {
  const mockTags: Tag[] = [
    { id: '1', name: 'work', userId: 'user1', createdAt: '2023-01-01' },
    { id: '2', name: 'personal', userId: 'user1', createdAt: '2023-01-01' },
    { id: '3', name: 'ideas', userId: 'user1', createdAt: '2023-01-01' }
  ]

  const mockNote: Note = {
    id: 'note1',
    userId: 'user1',
    title: 'Test Note',
    content: 'Test content',
    noteType: 'markdown',
    metadata: {},
    isArchived: false,
    createdAt: '2023-01-01',
    updatedAt: '2023-01-01',
    tags: [mockTags[0], mockTags[1]]
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('displays existing note tags', () => {
    const wrapper = mount(NoteEditor, {
      props: {
        note: mockNote
      },
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            notes: {
              tags: mockTags,
              isLoading: false
            }
          }
        })]
      }
    })

    // Check if TagInput component is rendered
    const tagInput = wrapper.findComponent({ name: 'TagInput' })
    expect(tagInput.exists()).toBe(true)
  })

  it('handles tag creation', async () => {
    const wrapper = mount(NoteEditor, {
      props: {
        note: mockNote
      },
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            notes: {
              tags: mockTags,
              isLoading: false
            }
          }
        })]
      }
    })

    const tagInput = wrapper.findComponent({ name: 'TagInput' })
    
    // Simulate tag creation
    await tagInput.vm.$emit('tag-created', 'new-tag')

    // Check if the component handled the event
    expect(wrapper.vm).toBeDefined()
  })

  it('handles tag addition', async () => {
    const wrapper = mount(NoteEditor, {
      props: {
        note: mockNote
      },
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            notes: {
              tags: mockTags,
              isLoading: false
            }
          }
        })]
      }
    })

    const tagInput = wrapper.findComponent({ name: 'TagInput' })
    
    // Simulate tag addition
    await tagInput.vm.$emit('tag-added', mockTags[2])

    // Check if the component handled the event
    expect(wrapper.vm).toBeDefined()
  })

  it('handles tag removal', async () => {
    const wrapper = mount(NoteEditor, {
      props: {
        note: mockNote
      },
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            notes: {
              tags: mockTags,
              isLoading: false
            }
          }
        })]
      }
    })

    const tagInput = wrapper.findComponent({ name: 'TagInput' })
    
    // Simulate tag removal
    await tagInput.vm.$emit('tag-removed', mockTags[0], 0)

    // Check if the component handled the event
    expect(wrapper.vm).toBeDefined()
  })

  it('loads available tags on mount', () => {
    const wrapper = mount(NoteEditor, {
      props: {
        note: mockNote
      },
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            notes: {
              tags: mockTags,
              isLoading: false
            }
          }
        })]
      }
    })

    // Check if the component is properly initialized
    expect(wrapper.vm).toBeDefined()
  })
})
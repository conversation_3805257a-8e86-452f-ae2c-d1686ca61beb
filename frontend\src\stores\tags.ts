import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { tagService, type Tag as ApiTag, type CreateTagData, type UpdateTagData } from '../services/tagService'

export interface Tag {
  id: string
  name: string
  icon: string
  color: string
  count: number
  isPredefined: boolean
  createdAt: Date
  updatedAt: Date
}

export interface TagFilter {
  selectedTags: string[]
  filterMode: 'any' | 'all' // any = OR, all = AND
}

// Predefined tags with icons
const PREDEFINED_TAGS: Omit<Tag, 'id' | 'count' | 'createdAt' | 'updatedAt'>[] = [
  { name: 'todo', icon: 'fas fa-check-square', color: '#007bff', isPredefined: true },
  { name: 'personal', icon: 'fas fa-user', color: '#28a745', isPredefined: true },
  { name: 'family', icon: 'fas fa-heart', color: '#dc3545', isPredefined: true },
  { name: 'health', icon: 'fas fa-heartbeat', color: '#fd7e14', isPredefined: true },
  { name: 'blog', icon: 'fas fa-blog', color: '#6f42c1', isPredefined: true },
  { name: 'code', icon: 'fas fa-code', color: '#20c997', isPredefined: true },
  { name: 'recipe', icon: 'fas fa-utensils', color: '#ffc107', isPredefined: true },
  { name: 'science', icon: 'fas fa-flask', color: '#17a2b8', isPredefined: true },
  { name: 'study', icon: 'fas fa-graduation-cap', color: '#6610f2', isPredefined: true },
  { name: 'travel', icon: 'fas fa-plane', color: '#e83e8c', isPredefined: true }
]

export const useTagsStore = defineStore('tags', () => {
  // State
  const tags = ref<Tag[]>([])
  const selectedTags = ref<string[]>([])
  const filterMode = ref<'any' | 'all'>('any')
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const availableTags = computed(() => {
    return tags.value.filter(tag => tag.count > 0).sort((a, b) => b.count - a.count)
  })

  const predefinedTags = computed(() => {
    return tags.value.filter(tag => tag.isPredefined)
  })

  const customTags = computed(() => {
    return tags.value.filter(tag => !tag.isPredefined)
  })

  const activeFilter = computed((): TagFilter => ({
    selectedTags: selectedTags.value,
    filterMode: filterMode.value
  }))

  const hasActiveFilter = computed(() => selectedTags.value.length > 0)

  // Actions
  const initializePredefinedTags = async () => {
    try {
      const response = await tagService.initializePredefinedTags()
      if (!response.success) {
        console.warn('Failed to initialize predefined tags:', response.error)
      }
    } catch (err) {
      console.warn('Error initializing predefined tags:', err)
    }
  }

  const loadTags = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      // Initialize predefined tags first
      await initializePredefinedTags()
      
      // Load tags from API
      const response = await tagService.getTags()
      
      if (response.success && response.data) {
        tags.value = response.data
      } else {
        throw new Error(response.error || 'Failed to load tags')
      }
      
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load tags'
      console.error('Error loading tags:', err)
    } finally {
      isLoading.value = false
    }
  }

  const createTag = async (tagData: { name: string; icon?: string; color?: string }) => {
    try {
      const createData: CreateTagData = {
        name: tagData.name.toLowerCase().trim(),
        icon: tagData.icon || 'fas fa-tag',
        color: tagData.color || '#6c757d'
      }
      
      const response = await tagService.createTag(createData)
      
      if (response.success && response.data) {
        tags.value.push(response.data)
        return response.data
      } else {
        throw new Error(response.error || 'Failed to create tag')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create tag'
      throw err
    }
  }

  const updateTag = async (tagId: string, updates: Partial<Tag>) => {
    try {
      const updateData: UpdateTagData = {}
      if (updates.name !== undefined) updateData.name = updates.name
      if (updates.icon !== undefined) updateData.icon = updates.icon
      if (updates.color !== undefined) updateData.color = updates.color
      
      const response = await tagService.updateTag(tagId, updateData)
      
      if (response.success && response.data) {
        const tagIndex = tags.value.findIndex(t => t.id === tagId)
        if (tagIndex !== -1) {
          tags.value[tagIndex] = response.data
        }
        return response.data
      } else {
        throw new Error(response.error || 'Failed to update tag')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update tag'
      throw err
    }
  }

  const deleteTag = async (tagId: string) => {
    try {
      const response = await tagService.deleteTag(tagId)
      
      if (response.success) {
        const tagIndex = tags.value.findIndex(t => t.id === tagId)
        if (tagIndex !== -1) {
          const tag = tags.value[tagIndex]
          tags.value.splice(tagIndex, 1)
          
          // Remove from selected tags if present
          const selectedIndex = selectedTags.value.indexOf(tag.name)
          if (selectedIndex > -1) {
            selectedTags.value.splice(selectedIndex, 1)
          }
        }
      } else {
        throw new Error(response.error || 'Failed to delete tag')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete tag'
      throw err
    }
  }

  const toggleTag = (tagName: string) => {
    const index = selectedTags.value.indexOf(tagName)
    if (index > -1) {
      selectedTags.value.splice(index, 1)
    } else {
      selectedTags.value.push(tagName)
    }
  }

  const clearSelectedTags = () => {
    selectedTags.value = []
  }

  const setFilterMode = (mode: 'any' | 'all') => {
    filterMode.value = mode
  }

  const getTagByName = (name: string) => {
    return tags.value.find(tag => tag.name === name)
  }

  const updateTagCounts = (tagCounts: Record<string, number>) => {
    Object.entries(tagCounts).forEach(([tagName, count]) => {
      const tag = tags.value.find(t => t.name === tagName)
      if (tag) {
        tag.count = count
        tag.updatedAt = new Date()
      }
    })
  }

  // Initialize store
  const initialize = async () => {
    await loadTags()
  }

  return {
    // State
    tags,
    selectedTags,
    filterMode,
    isLoading,
    error,
    
    // Computed
    availableTags,
    predefinedTags,
    customTags,
    activeFilter,
    hasActiveFilter,
    
    // Actions
    initialize,
    loadTags,
    createTag,
    updateTag,
    deleteTag,
    toggleTag,
    clearSelectedTags,
    setFilterMode,
    getTagByName,
    updateTagCounts,
    initializePredefinedTags
  }
})
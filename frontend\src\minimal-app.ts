// Ultra-minimal app initialization for <500ms target
// This creates the absolute minimum needed for first paint

import { createApp } from 'vue'

// Minimal app component for initial render
const MinimalApp = {
  template: `
    <div id="app-loading">
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading...</div>
      </div>
    </div>
  `
}

// Create minimal app instance
export function createMinimalApp() {
  const app = createApp(MinimalApp)
  return app
}

// Mount minimal app immediately
export function mountMinimalApp() {
  const app = createMinimalApp()
  app.mount('#app')
  
  console.log('🚀 Minimal app mounted in', performance.now().toFixed(2), 'ms')
  
  // Schedule full app loading
  scheduleFullAppLoad()
}

// Schedule full app loading after minimal app is rendered
function scheduleFullAppLoad() {
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      loadFullApp()
    }, { timeout: 100 })
  } else {
    setTimeout(loadFullApp, 50)
  }
}

// Load and mount full app
async function loadFullApp() {
  try {
    console.log('🔄 Loading full application...')
    
    // Dynamic import of full app
    const { mountFullApp } = await import('./main-full')
    
    // Replace minimal app with full app
    await mountFullApp()
    
    console.log('✅ Full application loaded successfully')
    
  } catch (error) {
    console.error('❌ Failed to load full application:', error)
    
    // Show error message in minimal app
    const appElement = document.getElementById('app-loading')
    if (appElement) {
      appElement.innerHTML = `
        <div class="error-container">
          <div class="error-message">Failed to load application</div>
          <button onclick="window.location.reload()" class="retry-button">Retry</button>
        </div>
      `
    }
  }
}

// Add minimal CSS for loading state
const style = document.createElement('style')
style.textContent = `
  #app-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  .loading-container {
    text-align: center;
  }
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3273dc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }
  .loading-text {
    color: #666;
    font-size: 14px;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .error-container {
    text-align: center;
    color: #721c24;
  }
  .error-message {
    margin-bottom: 1rem;
    font-size: 16px;
  }
  .retry-button {
    padding: 0.5rem 1rem;
    background: #3273dc;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
`
document.head.appendChild(style)
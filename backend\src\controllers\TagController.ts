import { Request, Response } from 'express';
import { TagRepository } from '../repositories/TagRepository';
import { TagModel, CreateTagData, UpdateTagData } from '../models/Tag';

export class TagController {
  // GET /api/tags
  static async getTags(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { isPredefined, search } = req.query;

      const filters = {
        userId,
        isPredefined: isPredefined !== undefined ? isPredefined === 'true' : undefined,
        search: search as string
      };

      // Try to initialize predefined tags if user has no tags
      try {
        await TagRepository.initializePredefinedTags(userId);
      } catch (initError) {
        console.warn('Failed to initialize predefined tags:', initError);
      }

      const tags = await TagRepository.findByFilters(filters);
      const tagCounts = await TagRepository.getTagUsageCounts(userId);

      // Add usage counts to tags
      const tagsWithCounts = tags.map(tag => ({
        ...tag,
        count: tagCounts[tag.name] || 0
      }));

      res.json({
        success: true,
        data: tagsWithCounts
      });
    } catch (error) {
      console.error('Error fetching tags:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch tags'
      });
    }
  }

  // POST /api/tags
  static async createTag(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { name, icon, color } = req.body;

      const createData: CreateTagData = {
        userId,
        name,
        icon,
        color,
        isPredefined: false
      };

      // Validate input
      const validation = TagModel.validateCreateData(createData);
      if (!validation.valid) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: validation.errors
        });
        return;
      }

      // Check if tag already exists
      const existingTag = await TagRepository.findByUserIdAndName(userId, name);
      if (existingTag) {
        res.status(409).json({
          success: false,
          error: 'Tag with this name already exists'
        });
        return;
      }

      const tag = await TagRepository.create(createData);

      res.status(201).json({
        success: true,
        data: {
          ...tag,
          count: 0
        }
      });
    } catch (error) {
      console.error('Error creating tag:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create tag'
      });
    }
  }

  // GET /api/tags/:id
  static async getTag(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { id } = req.params;
      const tag = await TagRepository.findById(id);

      if (!tag) {
        res.status(404).json({
          success: false,
          error: 'Tag not found'
        });
        return;
      }

      // Check if user owns this tag
      if (tag.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      const tagCounts = await TagRepository.getTagUsageCounts(userId);

      res.json({
        success: true,
        data: {
          ...tag,
          count: tagCounts[tag.name] || 0
        }
      });
    } catch (error) {
      console.error('Error fetching tag:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch tag'
      });
    }
  }

  // PUT /api/tags/:id
  static async updateTag(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { id } = req.params;
      const { name, icon, color } = req.body;

      // Check if tag exists and user owns it
      const existingTag = await TagRepository.findById(id);
      if (!existingTag) {
        res.status(404).json({
          success: false,
          error: 'Tag not found'
        });
        return;
      }

      if (existingTag.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      const updateData: UpdateTagData = {};
      if (name !== undefined) updateData.name = name;
      if (icon !== undefined) updateData.icon = icon;
      if (color !== undefined) updateData.color = color;

      // Validate input
      const validation = TagModel.validateUpdateData(updateData);
      if (!validation.valid) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: validation.errors
        });
        return;
      }

      // For predefined tags, only allow icon and color updates
      if (existingTag.isPredefined && updateData.name !== undefined) {
        res.status(400).json({
          success: false,
          error: 'Cannot change name of predefined tags'
        });
        return;
      }

      // Check for name conflicts (if name is being updated)
      if (updateData.name && updateData.name !== existingTag.name) {
        const conflictingTag = await TagRepository.findByUserIdAndName(userId, updateData.name);
        if (conflictingTag) {
          res.status(409).json({
            success: false,
            error: 'Tag with this name already exists'
          });
          return;
        }
      }

      const updatedTag = await TagRepository.update(id, updateData);
      if (!updatedTag) {
        res.status(404).json({
          success: false,
          error: 'Tag not found'
        });
        return;
      }

      const tagCounts = await TagRepository.getTagUsageCounts(userId);

      res.json({
        success: true,
        data: {
          ...updatedTag,
          count: tagCounts[updatedTag.name] || 0
        }
      });
    } catch (error) {
      console.error('Error updating tag:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update tag'
      });
    }
  }

  // DELETE /api/tags/:id
  static async deleteTag(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { id } = req.params;

      // Check if tag exists and user owns it
      const existingTag = await TagRepository.findById(id);
      if (!existingTag) {
        res.status(404).json({
          success: false,
          error: 'Tag not found'
        });
        return;
      }

      if (existingTag.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      if (existingTag.isPredefined) {
        res.status(400).json({
          success: false,
          error: 'Cannot delete predefined tags'
        });
        return;
      }

      const deleted = await TagRepository.delete(id);
      if (!deleted) {
        res.status(404).json({
          success: false,
          error: 'Tag not found or cannot be deleted'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Tag deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting tag:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete tag'
      });
    }
  }

  // POST /api/tags/initialize
  static async initializePredefinedTags(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      await TagRepository.initializePredefinedTags(userId);

      res.json({
        success: true,
        message: 'Predefined tags initialized successfully'
      });
    } catch (error) {
      console.error('Error initializing predefined tags:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to initialize predefined tags'
      });
    }
  }
}
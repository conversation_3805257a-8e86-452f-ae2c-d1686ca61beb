import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import { createRouter, createWebHistory } from 'vue-router'
import AdminNotifications from '../AdminNotifications.vue'
import { useNotificationStore } from '../../../stores/notifications'

// Mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/admin/reports', component: { template: '<div>Reports</div>' } }
  ]
})

describe('AdminNotifications', () => {
  let wrapper: any
  let notificationStore: any

  beforeEach(() => {
    const pinia = createTestingPinia({
      createSpy: vi.fn
    })

    wrapper = mount(AdminNotifications, {
      global: {
        plugins: [pinia, router]
      }
    })

    notificationStore = useNotificationStore()
    
    // Mock store methods
    notificationStore.loadNotifications = vi.fn()
    notificationStore.markAsRead = vi.fn()
    notificationStore.markAllAsRead = vi.fn()
    
    // Mock store state
    notificationStore.notifications = [
      {
        id: '1',
        type: 'critical',
        category: 'content_report',
        title: 'Test Notification',
        message: 'Test message',
        read: false,
        actionUrl: '/admin/reports',
        createdAt: new Date().toISOString()
      }
    ]
    notificationStore.unreadCount = 1
    notificationStore.hasUnread = true
    notificationStore.isLoading = false
  })

  it('renders notification bell correctly', () => {
    expect(wrapper.find('.fa-bell').exists()).toBe(true)
  })

  it('shows unread count badge when there are unread notifications', () => {
    const badge = wrapper.find('.notification-badge')
    expect(badge.exists()).toBe(true)
    expect(badge.text()).toBe('1')
  })

  it('shows "Mark all read" button when there are unread notifications', async () => {
    // Open dropdown
    await wrapper.find('.dropdown-trigger button').trigger('click')
    
    const markAllButton = wrapper.find('button:contains("Mark all read")')
    expect(markAllButton.exists()).toBe(true)
  })

  it('calls loadNotifications when dropdown is opened', async () => {
    await wrapper.find('.dropdown-trigger button').trigger('click')
    
    expect(notificationStore.loadNotifications).toHaveBeenCalled()
  })

  it('calls markAllAsRead when "Mark all read" button is clicked', async () => {
    // Open dropdown
    await wrapper.find('.dropdown-trigger button').trigger('click')
    await wrapper.vm.$nextTick()
    
    // Find and click mark all read button
    const markAllButton = wrapper.find('button').filter((button: any) => 
      button.text().includes('Mark all read')
    )[0]
    
    if (markAllButton) {
      await markAllButton.trigger('click')
      expect(notificationStore.markAllAsRead).toHaveBeenCalled()
    }
  })

  it('displays notification items correctly', async () => {
    // Open dropdown
    await wrapper.find('.dropdown-trigger button').trigger('click')
    await wrapper.vm.$nextTick()
    
    const notificationItem = wrapper.find('.notification-item')
    expect(notificationItem.exists()).toBe(true)
    expect(notificationItem.text()).toContain('Test Notification')
    expect(notificationItem.text()).toContain('Test message')
  })

  it('shows "View All Notifications" link', async () => {
    // Open dropdown
    await wrapper.find('.dropdown-trigger button').trigger('click')
    await wrapper.vm.$nextTick()
    
    const viewAllLink = wrapper.find('router-link[to="/admin/notifications"]')
    expect(viewAllLink.exists()).toBe(true)
  })
})
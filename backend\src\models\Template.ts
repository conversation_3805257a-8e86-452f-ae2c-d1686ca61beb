import { v4 as uuidv4 } from 'uuid';

export interface Template {
  id: string;
  userId: string;
  groupId?: string;
  name: string;
  description?: string;
  noteType: 'richtext' | 'markdown' | 'kanban';
  content: string;
  isPublic: boolean;
  tags: string[];
  metadata: TemplateMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface TemplateMetadata {
  category?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime?: number; // in minutes
  author?: string;
  version?: string;
  usageCount?: number;
}

export interface CreateTemplateData {
  userId: string;
  groupId?: string;
  name: string;
  description?: string;
  noteType: 'richtext' | 'markdown' | 'kanban';
  content: string;
  isPublic?: boolean;
  tags?: string[];
  metadata?: TemplateMetadata;
}

export interface UpdateTemplateData {
  name?: string;
  description?: string;
  content?: string;
  isPublic?: boolean;
  tags?: string[];
  metadata?: TemplateMetadata;
}

export interface TemplateFilters {
  userId?: string;
  groupId?: string;
  noteType?: 'richtext' | 'markdown' | 'kanban';
  category?: string;
  isPublic?: boolean;
  tags?: string[];
  search?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: 'created_at' | 'updated_at' | 'name' | 'usage_count';
  sortOrder?: 'asc' | 'desc';
}

export class TemplateModel {
  static generateId(): string {
    return uuidv4();
  }

  static validateNoteType(noteType: string): boolean {
    return ['richtext', 'markdown', 'kanban'].includes(noteType);
  }

  static validateName(name: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!name || name.trim().length === 0) {
      errors.push('Template name is required');
    }
    
    if (name.length > 100) {
      errors.push('Template name must be less than 100 characters');
    }

    if (name.length < 3) {
      errors.push('Template name must be at least 3 characters');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static validateContent(content: string, noteType: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (content === undefined || content === null) {
      errors.push('Template content is required');
    }

    // Content length validation
    if (content && content.length > 1000000) { // 1MB limit
      errors.push('Template content is too large (maximum 1MB)');
    }

    // Type-specific validation
    if (noteType === 'kanban' && content) {
      try {
        const parsed = JSON.parse(content);
        if (!parsed.columns || !Array.isArray(parsed.columns)) {
          errors.push('Kanban template must have a columns array');
        }
      } catch (e) {
        errors.push('Kanban template content must be valid JSON');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static validateDescription(description: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (description && description.length > 500) {
      errors.push('Template description must be less than 500 characters');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static getDefaultMetadata(): TemplateMetadata {
    return {
      usageCount: 0,
      version: '1.0.0'
    };
  }

  static updateMetadata(existingMetadata: TemplateMetadata = {}, updates: TemplateMetadata = {}): TemplateMetadata {
    return {
      ...existingMetadata,
      ...updates
    };
  }

  static incrementUsageCount(metadata: TemplateMetadata): TemplateMetadata {
    return {
      ...metadata,
      usageCount: (metadata.usageCount || 0) + 1
    };
  }

  static getBuiltInTemplates(): CreateTemplateData[] {
    return [
      {
        userId: 'system',
        name: 'Meeting Notes',
        description: 'Template for taking meeting notes with agenda, attendees, and action items',
        noteType: 'markdown',
        content: `# Meeting Notes

## Meeting Details
- **Date:** 
- **Time:** 
- **Location/Platform:** 
- **Duration:** 

## Attendees
- 
- 
- 

## Agenda
1. 
2. 
3. 

## Discussion Points

### Topic 1


### Topic 2


## Action Items
- [ ] **Task 1** - Assigned to: [Name] - Due: [Date]
- [ ] **Task 2** - Assigned to: [Name] - Due: [Date]

## Next Steps


## Notes

`,
        isPublic: true,
        tags: ['meeting', 'work', 'productivity'],
        metadata: {
          category: 'Business',
          difficulty: 'beginner',
          estimatedTime: 5,
          author: 'System'
        }
      },
      {
        userId: 'system',
        name: 'Project Planning',
        description: 'Comprehensive project planning template with goals, timeline, and resources',
        noteType: 'markdown',
        content: `# Project Planning

## Project Overview
**Project Name:** 
**Project Manager:** 
**Start Date:** 
**End Date:** 
**Budget:** 

## Project Goals
### Primary Objectives
1. 
2. 
3. 

### Success Criteria
- 
- 
- 

## Stakeholders
| Name | Role | Contact | Involvement Level |
|------|------|---------|-------------------|
|      |      |         |                   |
|      |      |         |                   |

## Timeline & Milestones
- **Phase 1:** [Date] - 
- **Phase 2:** [Date] - 
- **Phase 3:** [Date] - 

## Resources Required
### Team Members
- 
- 
- 

### Tools & Technology
- 
- 
- 

### Budget Breakdown
| Item | Cost | Notes |
|------|------|-------|
|      |      |       |

## Risk Assessment
| Risk | Probability | Impact | Mitigation Strategy |
|------|-------------|--------|-------------------|
|      |             |        |                   |

## Next Actions
- [ ] 
- [ ] 
- [ ] 
`,
        isPublic: true,
        tags: ['project', 'planning', 'management'],
        metadata: {
          category: 'Business',
          difficulty: 'intermediate',
          estimatedTime: 15,
          author: 'System'
        }
      },
      {
        userId: 'system',
        name: 'Daily Journal',
        description: 'Simple daily journal template for reflection and planning',
        noteType: 'markdown',
        content: `# Daily Journal - [Date]

## Today's Mood
😊 😐 😔 😤 😴 (circle one)

## Gratitude
Three things I'm grateful for today:
1. 
2. 
3. 

## Today's Priorities
- [ ] 
- [ ] 
- [ ] 

## Accomplishments
What did I achieve today?
- 
- 
- 

## Challenges
What challenges did I face?
- 
- 

## Lessons Learned


## Tomorrow's Focus
What are my top 3 priorities for tomorrow?
1. 
2. 
3. 

## Reflection
How do I feel about today overall?


## Notes

`,
        isPublic: true,
        tags: ['journal', 'personal', 'reflection'],
        metadata: {
          category: 'Personal',
          difficulty: 'beginner',
          estimatedTime: 10,
          author: 'System'
        }
      },
      {
        userId: 'system',
        name: 'Simple Kanban Board',
        description: 'Basic kanban board template with To Do, In Progress, and Done columns',
        noteType: 'kanban',
        content: JSON.stringify({
          columns: [
            {
              id: 'todo',
              title: 'To Do',
              cards: [
                {
                  id: 'card-1',
                  title: 'Plan project structure',
                  description: 'Define the overall architecture and components'
                },
                {
                  id: 'card-2',
                  title: 'Research requirements',
                  description: 'Gather and analyze project requirements'
                }
              ]
            },
            {
              id: 'in-progress',
              title: 'In Progress',
              cards: [
                {
                  id: 'card-3',
                  title: 'Design user interface',
                  description: 'Create wireframes and mockups'
                }
              ]
            },
            {
              id: 'done',
              title: 'Done',
              cards: [
                {
                  id: 'card-4',
                  title: 'Set up development environment',
                  description: 'Install tools and configure workspace'
                }
              ]
            }
          ]
        }),
        isPublic: true,
        tags: ['kanban', 'productivity', 'workflow'],
        metadata: {
          category: 'Productivity',
          difficulty: 'beginner',
          estimatedTime: 2,
          author: 'System'
        }
      },
      {
        userId: 'system',
        name: 'Bug Report',
        description: 'Structured template for reporting software bugs',
        noteType: 'markdown',
        content: `# Bug Report

## Bug Summary
**Title:** 
**Severity:** Critical / High / Medium / Low
**Priority:** P1 / P2 / P3 / P4

## Environment
- **OS:** 
- **Browser:** 
- **Version:** 
- **Device:** 

## Steps to Reproduce
1. 
2. 
3. 
4. 

## Expected Behavior
What should happen:


## Actual Behavior
What actually happens:


## Screenshots/Videos
[Attach screenshots or videos if applicable]

## Additional Information
- **Error Messages:** 
- **Console Logs:** 
- **Network Requests:** 

## Workaround
Is there a temporary workaround?


## Impact
How does this affect users?


## Reporter Information
- **Name:** 
- **Email:** 
- **Date:** 
`,
        isPublic: true,
        tags: ['bug', 'development', 'testing'],
        metadata: {
          category: 'Development',
          difficulty: 'beginner',
          estimatedTime: 5,
          author: 'System'
        }
      }
    ];
  }
}
<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">
          <span class="icon">
            <i class="fas fa-tags"></i>
          </span>
          Tag Settings
        </p>
        <button class="delete" @click="$emit('close')"></button>
      </header>
      
      <section class="modal-card-body">
        <!-- Create New Tag Section -->
        <div class="section">
          <h4 class="title is-5">Create New Tag</h4>
          <div class="field">
            <label class="label">Tag Name</label>
            <div class="control">
              <input
                v-model="newTag.name"
                class="input"
                type="text"
                placeholder="Enter tag name..."
                @keyup.enter="createTag"
              />
            </div>
          </div>
          
          <div class="columns">
            <div class="column">
              <div class="field">
                <label class="label">Icon</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="newTag.icon">
                      <option value="fas fa-tag">Default Tag</option>
                      <option value="fas fa-star">Star</option>
                      <option value="fas fa-heart">Heart</option>
                      <option value="fas fa-bookmark">Bookmark</option>
                      <option value="fas fa-flag">Flag</option>
                      <option value="fas fa-lightbulb">Idea</option>
                      <option value="fas fa-exclamation">Important</option>
                      <option value="fas fa-question">Question</option>
                      <option value="fas fa-info">Info</option>
                      <option value="fas fa-cog">Settings</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column">
              <div class="field">
                <label class="label">Color</label>
                <div class="control">
                  <div class="color-picker">
                    <div class="color-options">
                      <div
                        v-for="color in colorOptions"
                        :key="color"
                        class="color-option"
                        :class="{ 'is-selected': newTag.color === color, [getColorClass(color)]: true }"
                        @click="newTag.color = color"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="field">
            <div class="control">
              <button
                class="button is-primary"
                :class="{ 'is-loading': isCreating }"
                :disabled="!newTag.name.trim() || isCreating"
                @click="createTag"
              >
                <span class="icon">
                  <i class="fas fa-plus"></i>
                </span>
                <span>Create Tag</span>
              </button>
            </div>
          </div>
        </div>

        <hr />

        <!-- Predefined Tags Section -->
        <div class="section">
          <h4 class="title is-5">Predefined Tags</h4>
          <p class="subtitle is-6">These tags come with the app and cannot be deleted.</p>
          
          <div class="tags-grid">
            <div
              v-for="tag in predefinedTags"
              :key="tag.id"
              class="tag-item predefined"
            >
              <div class="tag-info">
                <span class="icon" :class="getIconColorClass(tag.color)">
                  <i :class="tag.icon"></i>
                </span>
                <span class="tag-name">{{ tag.name }}</span>
                <span class="tag-count">{{ tag.count }}</span>
              </div>
              <div class="tag-actions">
                <button
                  class="button is-small is-ghost"
                  @click="editTag(tag)"
                  title="Edit color and icon"
                >
                  <span class="icon">
                    <i class="fas fa-edit"></i>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <hr />

        <!-- Custom Tags Section -->
        <div class="section">
          <h4 class="title is-5">Custom Tags</h4>
          <p class="subtitle is-6">Tags you've created. You can edit or delete these.</p>
          
          <div v-if="customTags.length === 0" class="empty-state">
            <p class="has-text-grey">No custom tags yet. Create one above!</p>
          </div>
          
          <div v-else class="tags-grid">
            <div
              v-for="tag in customTags"
              :key="tag.id"
              class="tag-item custom"
            >
              <div class="tag-info">
                <span class="icon" :class="getIconColorClass(tag.color)">
                  <i :class="tag.icon"></i>
                </span>
                <span class="tag-name">{{ tag.name }}</span>
                <span class="tag-count">{{ tag.count }}</span>
              </div>
              <div class="tag-actions">
                <button
                  class="button is-small is-ghost"
                  @click="editTag(tag)"
                  title="Edit tag"
                >
                  <span class="icon">
                    <i class="fas fa-edit"></i>
                  </span>
                </button>
                <button
                  class="button is-small is-ghost has-text-danger"
                  @click="deleteTag(tag)"
                  title="Delete tag"
                >
                  <span class="icon">
                    <i class="fas fa-trash"></i>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <footer class="modal-card-foot">
        <button class="button" @click="$emit('close')">Close</button>
      </footer>
    </div>

    <!-- Edit Tag Modal -->
    <div class="modal" :class="{ 'is-active': editingTag }">
      <div class="modal-background" @click="cancelEdit"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Edit Tag</p>
          <button class="delete" @click="cancelEdit"></button>
        </header>
        
        <section class="modal-card-body" v-if="editingTag">
          <div class="field">
            <label class="label">Tag Name</label>
            <div class="control">
              <input
                v-model="editForm.name"
                class="input"
                type="text"
                :disabled="editingTag.isPredefined"
              />
            </div>
            <p v-if="editingTag.isPredefined" class="help">
              Predefined tag names cannot be changed
            </p>
          </div>
          
          <div class="columns">
            <div class="column">
              <div class="field">
                <label class="label">Icon</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="editForm.icon">
                      <option value="fas fa-tag">Default Tag</option>
                      <option value="fas fa-star">Star</option>
                      <option value="fas fa-heart">Heart</option>
                      <option value="fas fa-bookmark">Bookmark</option>
                      <option value="fas fa-flag">Flag</option>
                      <option value="fas fa-lightbulb">Idea</option>
                      <option value="fas fa-exclamation">Important</option>
                      <option value="fas fa-question">Question</option>
                      <option value="fas fa-info">Info</option>
                      <option value="fas fa-cog">Settings</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column">
              <div class="field">
                <label class="label">Color</label>
                <div class="control">
                  <div class="color-picker">
                    <div class="color-options">
                      <div
                        v-for="color in colorOptions"
                        :key="color"
                        class="color-option"
                        :class="{ 'is-selected': editForm.color === color, [getColorClass(color)]: true }"
                        @click="editForm.color = color"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        <footer class="modal-card-foot">
          <button
            class="button is-primary"
            :class="{ 'is-loading': isUpdating }"
            @click="saveEdit"
          >
            Save Changes
          </button>
          <button class="button" @click="cancelEdit">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useTagsStore, type Tag } from '../../stores/tags'

// Props
interface Props {
  isOpen: boolean
}

defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// Store
const tagsStore = useTagsStore()

// State
const newTag = ref({
  name: '',
  icon: 'fas fa-tag',
  color: '#6c757d'
})

const editingTag = ref<Tag | null>(null)
const editForm = ref({
  name: '',
  icon: '',
  color: ''
})

const isCreating = ref(false)
const isUpdating = ref(false)

// Color options
const colorOptions = [
  '#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8',
  '#6f42c1', '#fd7e14', '#20c997', '#6610f2', '#e83e8c',
  '#6c757d', '#343a40', '#f8f9fa', '#e9ecef', '#dee2e6'
]

// Helper methods for color classes
const getColorClass = (color: string): string => {
  return `color-${color.slice(1)}`
}

const getIconColorClass = (color: string): string => {
  return `icon-color-${color.slice(1)}`
}

// Computed
const predefinedTags = computed(() => tagsStore.predefinedTags)
const customTags = computed(() => tagsStore.customTags)

// Methods
const createTag = async () => {
  if (!newTag.value.name.trim()) return
  
  isCreating.value = true
  try {
    await tagsStore.createTag({
      name: newTag.value.name,
      icon: newTag.value.icon,
      color: newTag.value.color
    })
    
    // Reset form
    newTag.value = {
      name: '',
      icon: 'fas fa-tag',
      color: '#6c757d'
    }
  } catch (error) {
    console.error('Failed to create tag:', error)
    // TODO: Show error notification
  } finally {
    isCreating.value = false
  }
}

const editTag = (tag: Tag) => {
  editingTag.value = tag
  editForm.value = {
    name: tag.name,
    icon: tag.icon,
    color: tag.color
  }
}

const saveEdit = async () => {
  if (!editingTag.value) return
  
  isUpdating.value = true
  try {
    await tagsStore.updateTag(editingTag.value.id, {
      name: editForm.value.name,
      icon: editForm.value.icon,
      color: editForm.value.color
    })
    
    cancelEdit()
  } catch (error) {
    console.error('Failed to update tag:', error)
    // TODO: Show error notification
  } finally {
    isUpdating.value = false
  }
}

const cancelEdit = () => {
  editingTag.value = null
  editForm.value = {
    name: '',
    icon: '',
    color: ''
  }
}

const deleteTag = async (tag: Tag) => {
  if (!confirm(`Are you sure you want to delete the "${tag.name}" tag?`)) {
    return
  }
  
  try {
    await tagsStore.deleteTag(tag.id)
  } catch (error) {
    console.error('Failed to delete tag:', error)
    // TODO: Show error notification
  }
}

// Watch for modal close to reset forms
watch(() => editingTag.value, (newVal) => {
  if (!newVal) {
    editForm.value = {
      name: '',
      icon: '',
      color: ''
    }
  }
})
</script>

<style scoped>
.section {
  padding: 1.5rem 0;
}

.section:first-child {
  padding-top: 0;
}

.tags-grid {
  display: grid;
  gap: 0.75rem;
}

.tag-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: white;
  transition: all 0.2s ease;
}

.tag-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.tag-item.predefined {
  background: #f8f9fa;
}

.tag-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.tag-name {
  font-weight: 500;
  text-transform: capitalize;
}

.tag-count {
  font-size: 0.875rem;
  color: #6c757d;
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 1.5rem;
  text-align: center;
}

.tag-actions {
  display: flex;
  gap: 0.25rem;
}

.color-picker {
  width: 100%;
}

.color-options {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.5rem;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.is-selected {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.empty-state {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.button.is-ghost {
  background: transparent;
  border: none;
}

.button.is-ghost:hover {
  background: #f8f9fa;
}

.button.is-ghost.has-text-danger:hover {
  background: #dc3545;
  color: white !important;
}

/* Modal z-index fix */
.modal {
  z-index: 9999;
}

.modal .modal {
  z-index: 10000;
}

/* Color utility classes */
.color-007bff { background-color: #007bff; }
.color-28a745 { background-color: #28a745; }
.color-dc3545 { background-color: #dc3545; }
.color-ffc107 { background-color: #ffc107; }
.color-17a2b8 { background-color: #17a2b8; }
.color-6f42c1 { background-color: #6f42c1; }
.color-fd7e14 { background-color: #fd7e14; }
.color-20c997 { background-color: #20c997; }
.color-6610f2 { background-color: #6610f2; }
.color-e83e8c { background-color: #e83e8c; }
.color-6c757d { background-color: #6c757d; }
.color-343a40 { background-color: #343a40; }
.color-f8f9fa { background-color: #f8f9fa; }
.color-e9ecef { background-color: #e9ecef; }
.color-dee2e6 { background-color: #dee2e6; }

/* Icon color utility classes */
.icon-color-007bff { color: #007bff; }
.icon-color-28a745 { color: #28a745; }
.icon-color-dc3545 { color: #dc3545; }
.icon-color-ffc107 { color: #ffc107; }
.icon-color-17a2b8 { color: #17a2b8; }
.icon-color-6f42c1 { color: #6f42c1; }
.icon-color-fd7e14 { color: #fd7e14; }
.icon-color-20c997 { color: #20c997; }
.icon-color-6610f2 { color: #6610f2; }
.icon-color-e83e8c { color: #e83e8c; }
.icon-color-6c757d { color: #6c757d; }
.icon-color-343a40 { color: #343a40; }
.icon-color-f8f9fa { color: #f8f9fa; }
.icon-color-e9ecef { color: #e9ecef; }
.icon-color-dee2e6 { color: #dee2e6; }
</style>
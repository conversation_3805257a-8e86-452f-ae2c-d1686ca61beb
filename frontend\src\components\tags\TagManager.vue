<template>
  <div class="tag-manager">
    <div class="card">
      <div class="card-header">
        <div class="card-header-title">
          <span class="icon">
            <i class="fas fa-tags"></i>
          </span>
          <span>Tag Management</span>
        </div>
        <div class="card-header-icon">
          <div class="buttons">
            <button
              class="button is-small is-primary"
              @click="showCreateModal = true"
            >
              <span class="icon">
                <i class="fas fa-plus"></i>
              </span>
              <span>New Tag</span>
            </button>
            <button
              class="button is-small"
              :class="{ 'is-primary': showStats }"
              @click="showStats = !showStats"
            >
              <span class="icon">
                <i class="fas fa-chart-bar"></i>
              </span>
            </button>
          </div>
        </div>
      </div>

      <div class="card-content">
        <!-- Search and Filter Controls -->
        <div class="field has-addons mb-4">
          <div class="control has-icons-left is-expanded">
            <input
              v-model="searchQuery"
              type="text"
              class="input"
              placeholder="Search tags..."
            />
            <span class="icon is-small is-left">
              <i class="fas fa-search"></i>
            </span>
          </div>
          <div class="control">
            <div class="select">
              <select v-model="sortBy">
                <option value="name">Name</option>
                <option value="usage">Usage</option>
                <option value="created">Date Created</option>
              </select>
            </div>
          </div>
          <div class="control">
            <button
              class="button"
              :class="{ 'is-primary': sortOrder === 'desc' }"
              @click="toggleSortOrder"
            >
              <span class="icon">
                <i :class="sortOrder === 'desc' ? 'fas fa-sort-down' : 'fas fa-sort-up'"></i>
              </span>
            </button>
          </div>
        </div>

        <!-- Tag Statistics -->
        <div v-if="showStats" class="tag-stats mb-4">
          <div class="columns">
            <div class="column">
              <div class="has-text-centered">
                <p class="title is-4">{{ totalTags }}</p>
                <p class="subtitle is-6">Total Tags</p>
              </div>
            </div>
            <div class="column">
              <div class="has-text-centered">
                <p class="title is-4">{{ usedTags }}</p>
                <p class="subtitle is-6">Used Tags</p>
              </div>
            </div>
            <div class="column">
              <div class="has-text-centered">
                <p class="title is-4">{{ unusedTags }}</p>
                <p class="subtitle is-6">Unused Tags</p>
              </div>
            </div>
            <div class="column">
              <div class="has-text-centered">
                <p class="title is-4">{{ averageUsage.toFixed(1) }}</p>
                <p class="subtitle is-6">Avg Usage</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Tag List -->
        <div class="tag-list">
          <div
            v-for="tag in filteredAndSortedTags"
            :key="tag.id"
            class="tag-item"
            :class="{ 'is-selected': selectedTags.includes(tag.id) }"
          >
            <div class="tag-item-content">
              <div class="tag-item-main">
                <div class="tag-item-info">
                  <span class="tag-name">{{ tag.name }}</span>
                  <span class="tag-usage">
                    <span class="icon is-small">
                      <i class="fas fa-file-alt"></i>
                    </span>
                    <span>{{ getTagUsage(tag.id) }} notes</span>
                  </span>
                </div>
                <div class="tag-item-meta">
                  <small class="has-text-grey">
                    Created {{ formatDate(tag.createdAt) }}
                  </small>
                </div>
              </div>
              
              <div class="tag-item-actions">
                <div class="buttons are-small">
                  <button
                    class="button is-light"
                    @click="viewTagNotes(tag)"
                    :title="`View notes with tag '${tag.name}'`"
                  >
                    <span class="icon">
                      <i class="fas fa-eye"></i>
                    </span>
                  </button>
                  <button
                    class="button is-light"
                    @click="editTag(tag)"
                    title="Edit tag"
                  >
                    <span class="icon">
                      <i class="fas fa-edit"></i>
                    </span>
                  </button>
                  <button
                    class="button is-light"
                    @click="mergeTag(tag)"
                    title="Merge with another tag"
                  >
                    <span class="icon">
                      <i class="fas fa-code-branch"></i>
                    </span>
                  </button>
                  <button
                    class="button is-danger is-light"
                    @click="deleteTag(tag)"
                    title="Delete tag"
                    :disabled="getTagUsage(tag.id) > 0"
                  >
                    <span class="icon">
                      <i class="fas fa-trash"></i>
                    </span>
                  </button>
                </div>
              </div>
            </div>

            <!-- Tag Usage Bar -->
            <div class="tag-usage-bar">
              <div
                class="tag-usage-fill"
                :style="{ width: getUsagePercentage(tag.id) + '%' }"
              ></div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="filteredAndSortedTags.length === 0" class="empty-state">
            <div class="has-text-centered py-4">
              <span class="icon is-large has-text-grey-light">
                <i class="fas fa-tags fa-2x"></i>
              </span>
              <p class="title is-5 has-text-grey">
                {{ searchQuery ? 'No matching tags found' : 'No tags yet' }}
              </p>
              <p class="subtitle is-6 has-text-grey">
                {{ searchQuery ? 'Try adjusting your search' : 'Create your first tag to get started' }}
              </p>
            </div>
          </div>
        </div>

        <!-- Bulk Actions -->
        <div v-if="selectedTags.length > 0" class="bulk-actions mt-4">
          <div class="field is-grouped">
            <div class="control">
              <span class="tag is-primary">
                {{ selectedTags.length }} selected
              </span>
            </div>
            <div class="control">
              <button class="button is-light" @click="clearSelection">
                Clear Selection
              </button>
            </div>
            <div class="control">
              <button class="button is-danger is-light" @click="bulkDelete">
                <span class="icon">
                  <i class="fas fa-trash"></i>
                </span>
                <span>Delete Selected</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Tag Modal -->
    <div class="modal" :class="{ 'is-active': showCreateModal }">
      <div class="modal-background" @click="showCreateModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Create New Tag</p>
          <button class="delete" @click="showCreateModal = false"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Tag Name</label>
            <div class="control">
              <input
                ref="createTagInput"
                v-model="newTagName"
                type="text"
                class="input"
                placeholder="Enter tag name..."
                @keydown.enter="createTag"
              />
            </div>
            <p class="help">Tag names should be descriptive and unique</p>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-primary"
            @click="createTag"
            :disabled="!newTagName.trim()"
          >
            Create Tag
          </button>
          <button class="button" @click="showCreateModal = false">
            Cancel
          </button>
        </footer>
      </div>
    </div>

    <!-- Edit Tag Modal -->
    <div class="modal" :class="{ 'is-active': showEditModal }">
      <div class="modal-background" @click="showEditModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Edit Tag</p>
          <button class="delete" @click="showEditModal = false"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Tag Name</label>
            <div class="control">
              <input
                v-model="editTagName"
                type="text"
                class="input"
                placeholder="Enter tag name..."
                @keydown.enter="updateTag"
              />
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-primary"
            @click="updateTag"
            :disabled="!editTagName.trim()"
          >
            Update Tag
          </button>
          <button class="button" @click="showEditModal = false">
            Cancel
          </button>
        </footer>
      </div>
    </div>

    <!-- Merge Tag Modal -->
    <div class="modal" :class="{ 'is-active': showMergeModal }">
      <div class="modal-background" @click="showMergeModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Merge Tag</p>
          <button class="delete" @click="showMergeModal = false"></button>
        </header>
        <section class="modal-card-body">
          <p class="mb-4">
            Merge "{{ tagToMerge?.name }}" into another tag. 
            All notes with this tag will be updated to use the target tag.
          </p>
          <div class="field">
            <label class="label">Target Tag</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="mergeTargetId">
                  <option value="">Select target tag...</option>
                  <option
                    v-for="tag in availableTagsForMerge"
                    :key="tag.id"
                    :value="tag.id"
                  >
                    {{ tag.name }} ({{ getTagUsage(tag.id) }} notes)
                  </option>
                </select>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-warning"
            @click="performMerge"
            :disabled="!mergeTargetId"
          >
            Merge Tags
          </button>
          <button class="button" @click="showMergeModal = false">
            Cancel
          </button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotesStore } from '../../stores/notes'
import type { Tag } from '../../services/noteService'

interface Emits {
  (e: 'tag-selected', tag: Tag): void
  (e: 'tag-created', tag: Tag): void
  (e: 'tag-updated', tag: Tag): void
  (e: 'tag-deleted', tagId: string): void
  (e: 'tags-merged', sourceId: string, targetId: string): void
}

const emit = defineEmits<Emits>()

const router = useRouter()
const notesStore = useNotesStore()

// Local state
const searchQuery = ref('')
const sortBy = ref<'name' | 'usage' | 'created'>('name')
const sortOrder = ref<'asc' | 'desc'>('asc')
const showStats = ref(false)
const selectedTags = ref<string[]>([])

// Modal state
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showMergeModal = ref(false)
const newTagName = ref('')
const editTagName = ref('')
const editingTag = ref<Tag | null>(null)
const tagToMerge = ref<Tag | null>(null)
const mergeTargetId = ref('')

// Refs
const createTagInput = ref<HTMLInputElement>()

// Computed
const tags = computed(() => notesStore.tags)
const tagUsageCount = computed(() => notesStore.tagUsageCount)

const totalTags = computed(() => tags.value.length)
const usedTags = computed(() => {
  return tags.value.filter(tag => getTagUsage(tag.id) > 0).length
})
const unusedTags = computed(() => totalTags.value - usedTags.value)
const averageUsage = computed(() => {
  if (totalTags.value === 0) return 0
  const totalUsage = tags.value.reduce((sum, tag) => sum + getTagUsage(tag.id), 0)
  return totalUsage / totalTags.value
})

const maxUsage = computed(() => {
  return Math.max(...tags.value.map(tag => getTagUsage(tag.id)), 1)
})

const filteredAndSortedTags = computed(() => {
  let filtered = tags.value

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(tag =>
      tag.name.toLowerCase().includes(query)
    )
  }

  // Apply sorting
  filtered.sort((a, b) => {
    let comparison = 0

    switch (sortBy.value) {
      case 'name':
        comparison = a.name.localeCompare(b.name)
        break
      case 'usage':
        comparison = getTagUsage(a.id) - getTagUsage(b.id)
        break
      case 'created':
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        break
    }

    return sortOrder.value === 'desc' ? -comparison : comparison
  })

  return filtered
})

const availableTagsForMerge = computed(() => {
  return tags.value.filter(tag => tag.id !== tagToMerge.value?.id)
})

// Methods
const getTagUsage = (tagId: string): number => {
  return tagUsageCount.value.get(tags.value.find(t => t.id === tagId)?.name || '') || 0
}

const getUsagePercentage = (tagId: string): number => {
  const usage = getTagUsage(tagId)
  return maxUsage.value > 0 ? (usage / maxUsage.value) * 100 : 0
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return 'today'
  } else if (diffDays === 1) {
    return 'yesterday'
  } else if (diffDays < 7) {
    return `${diffDays} days ago`
  } else {
    return date.toLocaleDateString()
  }
}

const viewTagNotes = (tag: Tag) => {
  emit('tag-selected', tag)
  router.push({
    name: 'Search',
    query: { tags: tag.name }
  })
}

const editTag = (tag: Tag) => {
  editingTag.value = tag
  editTagName.value = tag.name
  showEditModal.value = true
}

const mergeTag = (tag: Tag) => {
  tagToMerge.value = tag
  mergeTargetId.value = ''
  showMergeModal.value = true
}

const deleteTag = async (tag: Tag) => {
  const usage = getTagUsage(tag.id)
  if (usage > 0) {
    alert(`Cannot delete tag "${tag.name}" because it is used by ${usage} notes.`)
    return
  }

  if (confirm(`Are you sure you want to delete the tag "${tag.name}"?`)) {
    try {
      // TODO: Implement tag deletion in the store
      // await notesStore.deleteTag(tag.id)
      emit('tag-deleted', tag.id)
    } catch (error) {
      console.error('Failed to delete tag:', error)
      alert('Failed to delete tag. Please try again.')
    }
  }
}

const createTag = async () => {
  if (!newTagName.value.trim()) return

  try {
    const tag = await notesStore.createTag(newTagName.value.trim())
    emit('tag-created', tag)
    
    newTagName.value = ''
    showCreateModal.value = false
  } catch (error) {
    console.error('Failed to create tag:', error)
    alert('Failed to create tag. Please try again.')
  }
}

const updateTag = async () => {
  if (!editTagName.value.trim() || !editingTag.value) return

  try {
    // TODO: Implement tag update in the store
    // const updatedTag = await notesStore.updateTag(editingTag.value.id, editTagName.value.trim())
    // emit('tag-updated', updatedTag)
    
    showEditModal.value = false
    editingTag.value = null
    editTagName.value = ''
  } catch (error) {
    console.error('Failed to update tag:', error)
    alert('Failed to update tag. Please try again.')
  }
}

const performMerge = async () => {
  if (!tagToMerge.value || !mergeTargetId.value) return

  const targetTag = tags.value.find(t => t.id === mergeTargetId.value)
  if (!targetTag) return

  if (confirm(
    `Are you sure you want to merge "${tagToMerge.value.name}" into "${targetTag.name}"? ` +
    `This will update all notes and cannot be undone.`
  )) {
    try {
      // TODO: Implement tag merge in the store
      // await notesStore.mergeTags(tagToMerge.value.id, mergeTargetId.value)
      emit('tags-merged', tagToMerge.value.id, mergeTargetId.value)
      
      showMergeModal.value = false
      tagToMerge.value = null
      mergeTargetId.value = ''
    } catch (error) {
      console.error('Failed to merge tags:', error)
      alert('Failed to merge tags. Please try again.')
    }
  }
}

const clearSelection = () => {
  selectedTags.value = []
}

const bulkDelete = async () => {
  const tagsToDelete = tags.value.filter(tag => selectedTags.value.includes(tag.id))
  const usedTags = tagsToDelete.filter(tag => getTagUsage(tag.id) > 0)

  if (usedTags.length > 0) {
    alert(`Cannot delete ${usedTags.length} tags because they are still in use.`)
    return
  }

  if (confirm(`Are you sure you want to delete ${tagsToDelete.length} tags?`)) {
    try {
      for (const tag of tagsToDelete) {
        // TODO: Implement bulk tag deletion
        // await notesStore.deleteTag(tag.id)
        emit('tag-deleted', tag.id)
      }
      clearSelection()
    } catch (error) {
      console.error('Failed to delete tags:', error)
      alert('Failed to delete some tags. Please try again.')
    }
  }
}

// Watch for modal visibility to focus inputs
watch(showCreateModal, (isVisible) => {
  if (isVisible) {
    nextTick(() => {
      createTagInput.value?.focus()
    })
  }
})

// Lifecycle
onMounted(() => {
  // Load tags if not already loaded
  if (tags.value.length === 0) {
    notesStore.loadTags()
  }
})
</script>

<style scoped>
.tag-manager {
  max-width: 100%;
}

.tag-list {
  max-height: 600px;
  overflow-y: auto;
}

.tag-item {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
  overflow: hidden;
}

.tag-item:hover {
  border-color: var(--color-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-item.is-selected {
  border-color: var(--color-primary);
  background-color: var(--color-surface-hover);
}

.tag-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
}

.tag-item-main {
  flex: 1;
}

.tag-item-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.25rem;
}

.tag-name {
  font-weight: 600;
  font-size: 1.1rem;
}

.tag-usage {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-text-muted);
  font-size: 0.9rem;
}

.tag-item-meta {
  color: var(--color-text-muted);
  font-size: 0.8rem;
}

.tag-item-actions {
  flex-shrink: 0;
}

.tag-usage-bar {
  height: 3px;
  background-color: var(--color-surface);
  position: relative;
}

.tag-usage-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-success));
  transition: width 0.3s ease;
}

.tag-stats {
  background-color: var(--color-surface);
  border-radius: 6px;
  padding: 1rem;
}

.bulk-actions {
  background-color: var(--color-surface);
  border-radius: 6px;
  padding: 1rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
}

/* Modal styling */
.modal-card {
  max-width: 500px;
  margin: 0 auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .tag-item-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .tag-item-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .tag-item-actions {
    align-self: center;
  }
  
  .tag-stats .columns {
    display: block;
  }
  
  .tag-stats .column {
    margin-bottom: 1rem;
  }
}

/* Animation */
.tag-item {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>
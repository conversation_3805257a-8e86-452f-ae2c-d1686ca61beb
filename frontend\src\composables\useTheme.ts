import { ref, computed, watch, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { ThemeManager } from '@/utils/ThemeManager'
import { themePreloader, type PreloadProgress } from '@/utils/ThemePreloader'
import type { BulmaswatchTheme, ThemeSettings } from '@/types/theme'

// Global theme manager instance
let themeManager: ThemeManager | null = null

// Reactive state
const currentTheme = ref<string>('default')
const currentMode = ref<'light' | 'dark' | 'auto'>('auto')
const availableThemes = ref<BulmaswatchTheme[]>([])
const isLoading = ref<boolean>(false)
const error = ref<string | null>(null)
const systemPreference = ref<'light' | 'dark'>('light')
const preloadProgress = ref<PreloadProgress | null>(null)
const isPreloading = ref<boolean>(false)

export function useTheme() {
  // Initialize theme manager if not already done
  const initializeThemeManager = async () => {
    if (!themeManager) {
      themeManager = new ThemeManager()
      try {
        isLoading.value = true
        error.value = null
        await themeManager.initialize()
        availableThemes.value = themeManager.getAvailableThemes()
        systemPreference.value = themeManager.getSystemPreference()
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to initialize theme manager'
        console.error('Theme manager initialization failed:', err)
      } finally {
        isLoading.value = false
      }
    }
    return themeManager
  }

  // Set theme mode (light, dark, auto)
  const setThemeMode = async (mode: 'light' | 'dark' | 'auto') => {
    if (!themeManager) {
      await initializeThemeManager()
    }

    try {
      isLoading.value = true
      error.value = null
      currentMode.value = mode
      
      // Apply the appropriate theme based on mode
      await themeManager!.setTheme(mode)
      currentTheme.value = themeManager!.getCurrentTheme()
      
      // Save preference to localStorage
      localStorage.setItem('theme-mode', mode)
      
      // Only save specific theme name if not in auto mode
      if (mode !== 'auto') {
        localStorage.setItem('theme-name', currentTheme.value)
      } else {
        // In auto mode, remove specific theme preference
        localStorage.removeItem('theme-name')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to set theme mode'
      console.error('Failed to set theme mode:', err)
    } finally {
      isLoading.value = false
    }
  }

  // Set specific theme by name
  const setTheme = async (themeName: string) => {
    if (!themeManager) {
      await initializeThemeManager()
    }

    try {
      isLoading.value = true
      error.value = null
      
      await themeManager!.setTheme(themeName)
      currentTheme.value = themeName
      
      // Record theme usage for optimization
      recordThemeUsage(themeName)
      
      // Update mode based on theme (exit auto mode when selecting specific theme)
      const theme = themeManager!.getTheme(themeName)
      if (theme) {
        currentMode.value = theme.isDark ? 'dark' : 'light'
      }
      
      // Save preference to localStorage
      localStorage.setItem('theme-mode', currentMode.value)
      localStorage.setItem('theme-name', themeName)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to set theme'
      console.error('Failed to set theme:', err)
    } finally {
      isLoading.value = false
    }
  }

  // Get theme preview colors
  const getThemePreview = (themeName: string) => {
    return themeManager?.getThemePreview(themeName) || null
  }

  // Load saved theme preference
  const loadSavedTheme = async () => {
    const savedMode = localStorage.getItem('theme-mode') as 'light' | 'dark' | 'auto' | null
    const savedTheme = localStorage.getItem('theme-name')

    // Set default mode if none saved
    if (savedMode) {
      currentMode.value = savedMode
    } else {
      currentMode.value = 'auto' // Default to auto mode
    }

    // Apply theme based on mode
    if (currentMode.value === 'auto') {
      // In auto mode, use system preference
      await setThemeMode('auto')
    } else if (savedTheme && (currentMode.value === 'light' || currentMode.value === 'dark')) {
      // Use specific saved theme
      await setTheme(savedTheme)
    } else if (currentMode.value === 'light' || currentMode.value === 'dark') {
      // Fallback to mode-based theme
      await setThemeMode(currentMode.value)
    }
  }

  // Preload themes for better performance
  const preloadThemes = async (themeNames: string[]) => {
    if (!themeManager) {
      await initializeThemeManager()
    }

    const preloadPromises = themeNames.map(name => 
      themeManager!.preloadTheme(name).catch(err => 
        console.warn(`Failed to preload theme ${name}:`, err)
      )
    )

    await Promise.allSettled(preloadPromises)
  }

  // Execute intelligent preloading strategy
  const executePreloadStrategy = async () => {
    if (!themeManager) {
      await initializeThemeManager()
    }

    try {
      isPreloading.value = true
      const strategy = themePreloader.getPreloadStrategy()
      
      // Set up progress tracking
      themePreloader.onProgress((progress) => {
        preloadProgress.value = progress
      })

      await themePreloader.executePreloadStrategy(strategy, themeManager)
    } catch (err) {
      console.error('Failed to execute preload strategy:', err)
    } finally {
      isPreloading.value = false
      // Clear progress after a delay
      setTimeout(() => {
        preloadProgress.value = null
      }, 2000)
    }
  }

  // Record theme usage for optimization
  const recordThemeUsage = (themeName: string) => {
    themePreloader.recordThemeUsage(themeName)
  }

  // Get preload statistics
  const getPreloadStats = () => {
    return themePreloader.getStats()
  }

  // Get themes by category
  const getThemesByCategory = (category: 'light' | 'dark' | 'colorful') => {
    return computed(() => 
      availableThemes.value.filter(theme => theme.category === category)
    )
  }

  // Get light themes
  const lightThemes = computed(() => 
    availableThemes.value.filter(theme => !theme.isDark)
  )

  // Get dark themes
  const darkThemes = computed(() => 
    availableThemes.value.filter(theme => theme.isDark)
  )

  // Check if current theme is dark
  const isDarkTheme = computed(() => {
    const theme = themeManager?.getTheme(currentTheme.value)
    return theme?.isDark || false
  })

  // Get current theme object
  const getCurrentThemeObject = computed(() => {
    return themeManager?.getTheme(currentTheme.value) || null
  })

  // Watch for system preference changes when in auto mode
  const handleSystemPreferenceChange = (event: MediaQueryListEvent) => {
    const newPreference = event.matches ? 'dark' : 'light'
    systemPreference.value = newPreference
    
    if (currentMode.value === 'auto' && themeManager) {
      // Automatically switch theme when system preference changes
      setThemeMode('auto')
    }
  }

  // Initialize system preference watcher
  const initializeSystemPreferenceWatcher = () => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      // Set initial system preference
      systemPreference.value = mediaQuery.matches ? 'dark' : 'light'
      
      // Add listener for changes
      mediaQuery.addEventListener('change', handleSystemPreferenceChange)
      
      return () => {
        mediaQuery.removeEventListener('change', handleSystemPreferenceChange)
      }
    }
    return () => {}
  }

  // Theme change callback
  const onThemeChange = (callback: (theme: string) => void) => {
    themeManager?.onThemeChange(callback)
  }

  // Remove theme change callback
  const offThemeChange = (callback: (theme: string) => void) => {
    themeManager?.offThemeChange(callback)
  }

  // Clear error
  const clearError = () => {
    error.value = null
  }

  // Reset to default theme
  const resetToDefault = async () => {
    await setTheme('default')
  }

  // Get theme settings object
  const getThemeSettings = (): ThemeSettings => {
    return {
      mode: currentMode.value,
      selectedTheme: currentTheme.value,
      customThemes: availableThemes.value,
      transitionDuration: 300,
      preloadThemes: true
    }
  }

  // Apply theme settings
  const applyThemeSettings = async (settings: Partial<ThemeSettings>) => {
    if (settings.mode) {
      await setThemeMode(settings.mode)
    }
    if (settings.selectedTheme && settings.mode !== 'auto') {
      await setTheme(settings.selectedTheme)
    }
  }

  // System preference cleanup function
  let cleanupSystemWatcher: (() => void) | null = null

  // Manual initialization function for use outside of components
  const initialize = async () => {
    await initializeThemeManager()
    
    // Initialize system preference watcher
    cleanupSystemWatcher = initializeSystemPreferenceWatcher()
    
    await loadSavedTheme()
    
    // Execute intelligent preloading strategy (with error handling)
    executePreloadStrategy().catch(err => {
      console.warn('Theme preloading failed, but theme system will continue to work:', err)
    })
  }

  // Cleanup function for manual cleanup
  const cleanup = () => {
    // Clean up event listeners
    if (cleanupSystemWatcher) {
      cleanupSystemWatcher()
    }
  }

  // Auto-initialize if called within a component context
  if (getCurrentInstance()) {
    onMounted(async () => {
      await initialize()
    })

    onUnmounted(() => {
      cleanup()
    })
  }

      return {
      // State
      currentTheme: readonly(currentTheme),
      currentMode: readonly(currentMode),
      availableThemes: readonly(availableThemes),
      isLoading: readonly(isLoading),
      error: readonly(error),
      systemPreference: readonly(systemPreference),
      preloadProgress: readonly(preloadProgress),
      isPreloading: readonly(isPreloading),
      
      // Computed
      lightThemes,
      darkThemes,
      isDarkTheme,
      getCurrentThemeObject,
      
      // Methods
      setThemeMode,
      setTheme,
      getThemePreview,
      preloadThemes,
      getThemesByCategory,
      onThemeChange,
      offThemeChange,
      clearError,
      resetToDefault,
      getThemeSettings,
      applyThemeSettings,
      executePreloadStrategy,
      recordThemeUsage,
      getPreloadStats,
      
      // Utilities
      initializeThemeManager,
      initialize,
      cleanup
    }
}

// Export readonly to prevent external modification
function readonly<T>(ref: any): T {
  return ref as T
}
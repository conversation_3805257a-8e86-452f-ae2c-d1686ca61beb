import { httpClient } from '../utils/http';
import type {
  NoteShare,
  ShareAccess,
  CreateShareData,
  UpdateShareData,
  SharedNoteResponse,
  ShareFilters
} from '../types/noteShare';

export interface SharesResponse {
  shares: NoteShare[];
}

export interface AccessLogsResponse {
  accessLogs: ShareAccess[];
}

class NoteShareService {
  // Create a new share for a note
  async createShare(noteId: string, shareData: CreateShareData): Promise<NoteShare> {
    const response = await httpClient.post<NoteShare>(`/notes/${noteId}/share`, shareData);
    
    if (response.error) {
      throw new Error(response.error);
    }
    
    return response.data!;
  }

  // Get all shares for a specific note
  async getNoteShares(noteId: string): Promise<NoteShare[]> {
    const response = await httpClient.get<SharesResponse>(`/notes/${noteId}/shares`);
    
    if (response.error) {
      throw new Error(response.error);
    }
    
    return response.data!.shares;
  }

  // Get all shares created by the current user
  async getUserShares(filters?: ShareFilters): Promise<NoteShare[]> {
    const params = new URLSearchParams();
    
    if (filters?.accessLevel) {
      params.append('accessLevel', filters.accessLevel);
    }
    
    if (filters?.isExpired !== undefined) {
      params.append('isExpired', filters.isExpired.toString());
    }

    const queryString = params.toString();
    const url = queryString ? `/shares?${queryString}` : '/shares';
    
    const response = await httpClient.get<SharesResponse>(url);
    
    if (response.error) {
      throw new Error(response.error);
    }
    
    return response.data!.shares;
  }

  // Update an existing share
  async updateShare(shareId: string, updateData: UpdateShareData): Promise<NoteShare> {
    const response = await httpClient.put<NoteShare>(`/shares/${shareId}`, updateData);
    
    if (response.error) {
      throw new Error(response.error);
    }
    
    return response.data!;
  }

  // Delete a share
  async deleteShare(shareId: string): Promise<void> {
    const response = await httpClient.delete(`/shares/${shareId}`);
    
    if (response.error) {
      throw new Error(response.error);
    }
  }

  // Access a shared note using share token
  async accessSharedNote(shareToken: string, password?: string): Promise<SharedNoteResponse> {
    const data = password ? { password } : {};
    const response = await httpClient.post<SharedNoteResponse>(`/shared/${shareToken}`, data);
    
    if (response.error) {
      throw new Error(response.error);
    }
    
    return response.data!;
  }

  // Get access logs for a share
  async getShareAccessLogs(shareId: string, limit: number = 100): Promise<ShareAccess[]> {
    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    
    const response = await httpClient.get<AccessLogsResponse>(`/shares/${shareId}/access-logs?${params.toString()}`);
    
    if (response.error) {
      throw new Error(response.error);
    }
    
    return response.data!.accessLogs;
  }

  // Clean up expired shares (admin function)
  async cleanupExpiredShares(): Promise<{ message: string; deletedCount: number }> {
    const response = await httpClient.post<{ message: string; deletedCount: number }>('/shares/cleanup');
    
    if (response.error) {
      throw new Error(response.error);
    }
    
    return response.data!;
  }

  // Helper methods for share management
  
  // Copy share URL to clipboard
  async copyShareUrl(shareUrl: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(shareUrl);
    } catch (error) {
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = shareUrl;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      try {
        document.execCommand('copy');
      } catch (err) {
        throw new Error('Failed to copy to clipboard');
      } finally {
        document.body.removeChild(textArea);
      }
    }
  }

  // Generate a shareable link with optional password
  generateShareableLink(baseUrl: string, shareToken: string, password?: string): string {
    const url = new URL(`/shared/${shareToken}`, baseUrl);
    
    if (password) {
      // Note: In a real application, you might want to handle password differently
      // This is just for demonstration purposes
      url.searchParams.set('pwd', btoa(password));
    }
    
    return url.toString();
  }

  // Validate share settings
  validateShareSettings(settings: CreateShareData | UpdateShareData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if ('accessLevel' in settings && settings.accessLevel) {
      const validAccessLevels = ['private', 'shared', 'unlisted', 'public'];
      if (!validAccessLevels.includes(settings.accessLevel)) {
        errors.push('Invalid access level');
      }
    }

    if ('permissions' in settings && settings.permissions) {
      const validPermissions = ['view', 'comment', 'edit'];
      if (!Array.isArray(settings.permissions) || settings.permissions.length === 0) {
        errors.push('At least one permission is required');
      } else {
        for (const permission of settings.permissions) {
          if (!validPermissions.includes(permission)) {
            errors.push(`Invalid permission: ${permission}`);
          }
        }
      }
    }

    if (settings.expiresAt) {
      const expirationDate = new Date(settings.expiresAt);
      if (expirationDate <= new Date()) {
        errors.push('Expiration date must be in the future');
      }
    }

    if (settings.password && settings.password.length < 6) {
      errors.push('Password must be at least 6 characters long');
    }

    if (settings.allowedIps && Array.isArray(settings.allowedIps)) {
      for (const ip of settings.allowedIps) {
        if (!this.isValidIpAddress(ip)) {
          errors.push(`Invalid IP address: ${ip}`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // Helper method to validate IP addresses
  private isValidIpAddress(ip: string): boolean {
    // Basic IP validation (IPv4 and IPv6)
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    const ipv4CidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/;
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip) || ipv4CidrRegex.test(ip);
  }

  // Get share statistics
  getShareStatistics(shares: NoteShare[]): {
    total: number;
    byAccessLevel: Record<string, number>;
    expired: number;
    totalAccess: number;
  } {
    const stats = {
      total: shares.length,
      byAccessLevel: {} as Record<string, number>,
      expired: 0,
      totalAccess: 0
    };

    shares.forEach(share => {
      // Count by access level
      stats.byAccessLevel[share.accessLevel] = (stats.byAccessLevel[share.accessLevel] || 0) + 1;
      
      // Count expired shares
      if (share.isExpired) {
        stats.expired++;
      }
      
      // Sum total access count
      stats.totalAccess += share.accessCount;
    });

    return stats;
  }
}

export const noteShareService = new NoteShareService();
export default noteShareService;
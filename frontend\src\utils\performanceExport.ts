// Performance metrics export utility for <500ms validation
// This creates metrics that can be validated by the build process

export interface PerformanceMetrics {
  timestamp: number
  initialization: {
    duration: number
    storeInitTime: number
    domReadyTime: number
    startTime: number
    endTime: number
  }
  coreWebVitals: {
    fcp: number
    lcp: number
    tti: number
    cls: number
    fid: number
  }
  budgetViolations: string[]
  buildInfo: {
    version: string
    environment: string
    buildTime: number
  }
}

export function exportPerformanceMetrics(metrics: PerformanceMetrics): void {
  try {
    // Save to localStorage for development
    localStorage.setItem('performance-metrics', JSON.stringify(metrics))
    
    // Log summary for immediate feedback
    console.group('📊 Performance Export Summary')
    console.log(`⏱️  Initialization: ${metrics.initialization.duration.toFixed(2)}ms`)
    console.log(`🎯 Target: 500ms`)
    console.log(`📈 Status: ${metrics.initialization.duration <= 500 ? '✅ PASS' : '❌ FAIL'}`)
    
    if (metrics.coreWebVitals.fcp > 0) {
      console.log(`🎨 FCP: ${metrics.coreWebVitals.fcp.toFixed(2)}ms`)
      console.log(`🖼️  LCP: ${metrics.coreWebVitals.lcp.toFixed(2)}ms`)
      console.log(`⚡ TTI: ${metrics.coreWebVitals.tti.toFixed(2)}ms`)
    }
    
    if (metrics.budgetViolations.length > 0) {
      console.warn('⚠️  Budget Violations:')
      metrics.budgetViolations.forEach(violation => console.warn(`   • ${violation}`))
    }
    
    console.groupEnd()
    
    // Export to file system in development mode
    if (import.meta.env.DEV) {
      exportToFileSystem(metrics)
    }
    
  } catch (error) {
    console.warn('Failed to export performance metrics:', error)
  }
}

async function exportToFileSystem(metrics: PerformanceMetrics): Promise<void> {
  try {
    // Create a downloadable file with metrics
    const blob = new Blob([JSON.stringify(metrics, null, 2)], { 
      type: 'application/json' 
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-metrics-${Date.now()}.json`
    
    // Auto-download in development for CI/CD integration
    if (import.meta.env.DEV && window.location.search.includes('export-metrics')) {
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
    
  } catch (error) {
    console.warn('Failed to export metrics to file system:', error)
  }
}

// Create baseline metrics for comparison
export function createBaselineMetrics(): PerformanceMetrics {
  const now = performance.now()
  
  return {
    timestamp: Date.now(),
    initialization: {
      duration: now,
      storeInitTime: 0,
      domReadyTime: 0,
      startTime: 0,
      endTime: now
    },
    coreWebVitals: {
      fcp: 0,
      lcp: 0,
      tti: 0,
      cls: 0,
      fid: 0
    },
    budgetViolations: [],
    buildInfo: {
      version: import.meta.env.VITE_APP_VERSION || '1.0.0',
      environment: import.meta.env.MODE,
      buildTime: Date.now()
    }
  }
}

// Validate performance against targets
export function validatePerformanceTargets(metrics: PerformanceMetrics): {
  passed: boolean
  violations: string[]
} {
  const violations: string[] = []
  
  // Check initialization time (500ms target)
  if (metrics.initialization.duration > 500) {
    violations.push(`Initialization time: ${metrics.initialization.duration.toFixed(2)}ms > 500ms`)
  }
  
  // Check store initialization (200ms target)
  if (metrics.initialization.storeInitTime > 200) {
    violations.push(`Store initialization: ${metrics.initialization.storeInitTime.toFixed(2)}ms > 200ms`)
  }
  
  // Check DOM ready time (150ms target)
  if (metrics.initialization.domReadyTime > 150) {
    violations.push(`DOM ready time: ${metrics.initialization.domReadyTime.toFixed(2)}ms > 150ms`)
  }
  
  // Check Core Web Vitals
  if (metrics.coreWebVitals.fcp > 1000) {
    violations.push(`FCP: ${metrics.coreWebVitals.fcp.toFixed(2)}ms > 1000ms`)
  }
  
  if (metrics.coreWebVitals.lcp > 1500) {
    violations.push(`LCP: ${metrics.coreWebVitals.lcp.toFixed(2)}ms > 1500ms`)
  }
  
  if (metrics.coreWebVitals.tti > 2000) {
    violations.push(`TTI: ${metrics.coreWebVitals.tti.toFixed(2)}ms > 2000ms`)
  }
  
  if (metrics.coreWebVitals.cls > 0.05) {
    violations.push(`CLS: ${metrics.coreWebVitals.cls.toFixed(3)} > 0.05`)
  }
  
  if (metrics.coreWebVitals.fid > 100) {
    violations.push(`FID: ${metrics.coreWebVitals.fid.toFixed(2)}ms > 100ms`)
  }
  
  return {
    passed: violations.length === 0,
    violations
  }
}
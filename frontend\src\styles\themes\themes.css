/* Theme System CSS Custom Properties */

/* Base theme variables - Default Light Theme */
:root {
  /* Spacing Scale */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;

  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Border Radius */
  --radius: 0.375rem;
  --radius-sm: 0.25rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-full: 9999px;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;

  /* Primary Colors */
  --color-primary: #00d1b2;
  --color-primary-dark: #009e86;
  --color-primary-light: #00f5d4;
  --color-primary-alpha: rgba(0, 209, 178, 0.1);

  /* Semantic Colors */
  --color-link: #3273dc;
  --color-link-hover: #205bbc;
  --color-link-dark: #205bbc;
  --color-info: #3298dc;
  --color-info-hover: #207dbc;
  --color-info-dark: #207dbc;
  --color-success: #48c774;
  --color-success-hover: #34a85c;
  --color-success-dark: #34a85c;
  --color-warning: #ffdd57;
  --color-warning-hover: #ffd324;
  --color-warning-dark: #e6c200;
  --color-danger: #f14668;
  --color-danger-hover: #ee1742;
  --color-danger-dark: #ee1742;

  /* Background Colors */
  --color-background: #ffffff;
  --color-surface: #f5f5f5;
  /* --color-surface-hover: #eeeeee; */
  --color-card: #ffffff;
  --color-modal: #ffffff;

  /* Text Colors */
  --color-text: #4a4a4a;
  --color-text-strong: #363636;
  --color-text-muted: #7a7a7a;
  --color-text-light: #b5b5b5;
  --color-text-inverse: #ffffff;

  /* Border Colors */
  --color-border: #dbdbdb;
  --color-border-hover: #b5b5b5;
  --color-border-focus: #3273dc;

  /* Component Specific Colors */
  --navbar-background: var(--color-surface);
  --navbar-text: var(--color-text);
  --navbar-border: var(--color-border);
  --navbar-item-hover: var(--color-surface-hover);

  --sidebar-background: var(--color-surface);
  --sidebar-text: var(--color-text);
  --sidebar-border: var(--color-border);
  --sidebar-item-hover: var(--color-surface-hover);

  --dropdown-background: var(--color-background);
  --dropdown-border: var(--color-border);

  --button-background: var(--color-background);
  --button-text: var(--color-text);
  --button-border: var(--color-border);
  --button-hover-background: var(--color-surface);
  --button-hover-border: var(--color-border-hover);
  --button-hover-text: var(--color-text);

  --input-background: var(--color-background);
  --input-text: var(--color-text);
  --input-border: var(--color-border);
  --input-border-hover: var(--color-border-hover);
  --input-focus-border: var(--color-border-focus);
  --input-focus-shadow: rgba(50, 115, 220, 0.25);
  --input-placeholder: var(--color-text-muted);

  --card-background: var(--color-card);
  --card-text: var(--color-text);
  --card-border: var(--color-border);
  --card-header-background: var(--color-surface);
  --card-shadow: rgba(10, 10, 10, 0.1);

  --modal-background: var(--color-modal);
  --modal-text: var(--color-text);
  --modal-overlay: rgba(10, 10, 10, 0.86);

  /* Transitions */
  --transition-theme: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease;
  --transition-medium: all 0.25s ease;
  --transition-normal: all 0.25s ease;
  --transition-slow: all 0.35s ease;

  /* Shadows */
  --shadow: 0 2px 4px rgba(10, 10, 10, 0.1);
  --shadow-sm: 0 1px 2px rgba(10, 10, 10, 0.1);
  --shadow-md: 0 4px 8px rgba(10, 10, 10, 0.1);
  --shadow-lg: 0 8px 16px rgba(10, 10, 10, 0.1);
  --shadow-xl: 0 16px 32px rgba(10, 10, 10, 0.1);
  --shadow-small: 0 2px 4px rgba(10, 10, 10, 0.1);
  --shadow-medium: 0 4px 8px rgba(10, 10, 10, 0.1);
  --shadow-large: 0 8px 16px rgba(10, 10, 10, 0.1);
  --shadow-card: 0 0.5em 1em -0.125em rgba(10, 10, 10, 0.1), 0 0 0 1px rgba(10, 10, 10, 0.02);

  /* Table Colors */
  --table-background: var(--color-background);
  --table-border: var(--color-border);
  --table-header-background: var(--color-surface);
  --table-row-hover: var(--color-surface-hover);

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Notification Colors */
  --notification-success-background: rgba(72, 199, 116, 0.1);
  --notification-success-text: var(--color-success-dark);
  --notification-success-border: var(--color-success);
  --notification-danger-background: rgba(241, 70, 104, 0.1);
  --notification-danger-text: var(--color-danger-dark);
  --notification-danger-border: var(--color-danger);
  --notification-warning-background: rgba(255, 221, 87, 0.1);
  --notification-warning-text: var(--color-warning-dark);
  --notification-warning-border: var(--color-warning);
  --notification-info-background: rgba(50, 152, 220, 0.1);
  --notification-info-text: var(--color-info-dark);
  --notification-info-border: var(--color-info);

  /* Typography */
  --letter-spacing-wide: 0.025em;
}

/* Dark Theme Override */
[data-theme='darkly'] {
  /* Primary Colors */
  --color-primary: #375a7f;
  --color-primary-dark: #2e4a6b;
  --color-primary-light: #4a6b93;
  --color-primary-alpha: rgba(55, 90, 127, 0.1);

  /* Semantic Colors */
  --color-link: #375a7f;
  --color-link-hover: #2e4a6b;
  --color-info: #3298dc;
  --color-info-hover: #207dbc;
  --color-success: #00bc8c;
  --color-success-hover: #009670;
  --color-warning: #f39c12;
  --color-warning-hover: #e08e0b;
  --color-danger: #e74c3c;
  --color-danger-hover: #c0392b;

  /* Background Colors */
  --color-background: #1f2424;
  --color-surface: #303030;
  --color-surface-hover: #404040;
  --color-card: #2d2d2d;
  --color-modal: #2d2d2d;

  /* Text Colors */
  --color-text: #dee2e6;
  --color-text-strong: #ffffff;
  --color-text-muted: #adb5bd;
  --color-text-light: #6c757d;
  --color-text-inverse: #1f2424;

  /* Border Colors */
  --color-border: #495057;
  --color-border-hover: #6c757d;
  --color-border-focus: #375a7f;

  /* Shadows for dark theme */
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.3);
  --shadow-small: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.3);
  --shadow-large: 0 8px 16px rgba(0, 0, 0, 0.3);
  --shadow-card: 0 0.5em 1em -0.125em rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);

  /* Input focus shadow for dark theme */
  --input-focus-shadow: rgba(55, 90, 127, 0.25);

  /* Modal overlay for dark theme */
  --modal-overlay: rgba(0, 0, 0, 0.86);

  /* Dropdown Colors for Dark Theme */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Notification Colors for Dark Theme */
  --notification-success-background: rgba(0, 188, 140, 0.15);
  --notification-success-text: var(--color-success);
  --notification-danger-background: rgba(231, 76, 60, 0.15);
  --notification-danger-text: var(--color-danger);
  --notification-warning-background: rgba(243, 156, 18, 0.15);
  --notification-warning-text: var(--color-warning);
  --notification-info-background: rgba(50, 152, 220, 0.15);
  --notification-info-text: var(--color-info);
}

/* Flatly Theme Override */
[data-theme='flatly'] {
  /* Primary Colors */
  --color-primary: #2c3e50;
  --color-primary-dark: #1a252f;
  --color-primary-light: #34495e;
  --color-primary-alpha: rgba(44, 62, 80, 0.1);

  /* Semantic Colors */
  --color-link: #2c3e50;
  --color-link-hover: #1a252f;
  --color-info: #3498db;
  --color-info-hover: #2980b9;
  --color-success: #18bc9c;
  --color-success-hover: #138f75;
  --color-warning: #f39c12;
  --color-warning-hover: #e67e22;
  --color-danger: #e74c3c;
  --color-danger-hover: #c0392b;

  /* Background Colors */
  --color-background: #ffffff;
  --color-surface: #ecf0f1;
  --color-surface-hover: #d5dbdb;
  --color-card: #ffffff;
  --color-modal: #ffffff;

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Text Colors */
  --color-text: #2c3e50;
  --color-text-strong: #2c3e50;
  --color-text-muted: #7b8a8b;
  --color-text-light: #95a5a6;
  --color-text-inverse: #ffffff;

  /* Border Colors */
  --color-border: #bdc3c7;
  --color-border-hover: #95a5a6;
  --color-border-focus: #2c3e50;

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Input focus shadow for flatly theme */
  --input-focus-shadow: rgba(44, 62, 80, 0.25);
}

/* Cerulean Theme Override */
[data-theme='cerulean'] {
  /* Primary Colors */
  --color-primary: #2fa4e7;
  --color-primary-dark: #1f8dd6;
  --color-primary-light: #52b7ea;
  --color-primary-alpha: rgba(47, 164, 231, 0.1);

  /* Semantic Colors */
  --color-link: #2fa4e7;
  --color-link-hover: #1f8dd6;
  --color-info: #033c73;
  --color-info-hover: #022a52;
  --color-success: #73a839;
  --color-success-hover: #5e8a2e;
  --color-warning: #dd5600;
  --color-warning-hover: #b8460a;
  --color-danger: #c71c22;
  --color-danger-hover: #a01419;

  /* Background Colors */
  --color-background: #ffffff;
  --color-surface: #f8f9fa;
  --color-surface-hover: #e9ecef;
  --color-card: #ffffff;
  --color-modal: #ffffff;

  /* Text Colors */
  --color-text: #333333;
  --color-text-strong: #212529;
  --color-text-muted: #6c757d;
  --color-text-light: #adb5bd;
  --color-text-inverse: #ffffff;

  /* Border Colors */
  --color-border: #dee2e6;
  --color-border-hover: #adb5bd;
  --color-border-focus: #2fa4e7;

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Input focus shadow for cerulean theme */
  --input-focus-shadow: rgba(47, 164, 231, 0.25);
}

/* Solarized Theme Override */
[data-theme='solarized'] {
  /* Primary Colors */
  --color-primary: #2aa198;
  --color-primary-dark: #1f7972;
  --color-primary-light: #35b3aa;
  --color-primary-alpha: rgba(42, 161, 152, 0.1);

  /* Semantic Colors */
  --color-link: #b58900;
  --color-link-hover: #826200;
  --color-info: #268bd2;
  --color-info-hover: #1e6ea7;
  --color-success: #859900;
  --color-success-hover: #596600;
  --color-warning: #cb4b16;
  --color-warning-hover: #9d3a11;
  --color-danger: #d33682;
  --color-danger-hover: #b02669;

  /* Background Colors */
  --color-background: #002b36;
  --color-surface: #073642;
  --color-surface-hover: #0f4a56;
  --color-card: #073642;
  --color-modal: #073642;

  /* Text Colors */
  --color-text: #839496;
  --color-text-strong: #eee8d5;
  --color-text-muted: #75888a;
  --color-text-light: #586e75;
  --color-text-inverse: #002b36;

  /* Border Colors */
  --color-border: #586e75;
  --color-border-hover: #75888a;
  --color-border-focus: #b58900;

  /* Shadows for dark theme */
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.3);
  --shadow-card: 0 0.5em 1em -0.125em rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Input focus shadow */
  --input-focus-shadow: rgba(181, 137, 0, 0.25);

  /* Modal overlay */
  --modal-overlay: rgba(0, 0, 0, 0.86);
}

/* Flatly Dark Theme Override */
[data-theme='flatly-dark'] {
  /* Primary Colors */
  --color-primary: #375a7f;
  --color-primary-dark: #28415b;
  --color-primary-light: #4a6b93;
  --color-primary-alpha: rgba(55, 90, 127, 0.1);

  /* Semantic Colors */
  --color-link: #1abc9c;
  --color-link-hover: #148f77;
  --color-info: #3298dc;
  --color-info-hover: #207dbc;
  --color-success: #2ecc71;
  --color-success-hover: #25a25a;
  --color-warning: #f1b70e;
  --color-warning-hover: #c1920b;
  --color-danger: #e74c3c;
  --color-danger-hover: #d62c1a;

  /* Background Colors */
  --color-background: #1f2424;
  --color-surface: #282f2f;
  --color-surface-hover: #343c3d;
  --color-card: #282f2f;
  --color-modal: #282f2f;

  /* Text Colors */
  --color-text: #ffffff;
  --color-text-strong: #ffffff;
  --color-text-muted: #f2f2f2;
  --color-text-light: #dbdee0;
  --color-text-inverse: #1f2424;

  /* Border Colors */
  --color-border: #4c5759;
  --color-border-hover: #8c9b9d;
  --color-border-focus: #1abc9c;

  /* Shadows for dark theme */
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.3);
  --shadow-card: 0 0.5em 1em -0.125em rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Input focus shadow */
  --input-focus-shadow: rgba(26, 188, 156, 0.25);

  /* Modal overlay */
  --modal-overlay: rgba(0, 0, 0, 0.86);
}

/* Mini Me Theme Override */
[data-theme='mini-me'] {
  /* Primary Colors */
  --color-primary: #d9230f;
  --color-primary-dark: #a91b0c;
  --color-primary-light: #e04a32;
  --color-primary-alpha: rgba(217, 35, 15, 0.1);

  /* Semantic Colors */
  --color-link: #029acf;
  --color-link-hover: #02749c;
  --color-info: #0fc5d9;
  --color-info-hover: #0c9aa9;
  --color-success: #469408;
  --color-success-hover: #2f6405;
  --color-warning: #9b479f;
  --color-warning-hover: #79377c;
  --color-danger: #d9831f;
  --color-danger-hover: #ac6819;

  /* Background Colors */
  --color-background: #ffffff;
  --color-surface: #f5f5f5;
  --color-surface-hover: #e8e8e8;
  --color-card: #ffffff;
  --color-modal: #ffffff;

  /* Text Colors */
  --color-text: #444444;
  --color-text-strong: #373a3c;
  --color-text-muted: #777777;
  --color-text-light: #bbbbbb;
  --color-text-inverse: #ffffff;

  /* Border Colors */
  --color-border: #dddddd;
  --color-border-hover: #bbbbbb;
  --color-border-focus: #029acf;

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Input focus shadow */
  --input-focus-shadow: rgba(2, 154, 207, 0.25);
}

/* The Brave Theme Override */
[data-theme='the-brave'] {
  /* Primary Colors */
  --color-primary: #ff6b35;
  --color-primary-dark: #e55a2b;
  --color-primary-light: #ff7c4f;
  --color-primary-alpha: rgba(255, 107, 53, 0.1);

  /* Semantic Colors */
  --color-link: #3498db;
  --color-link-hover: #2980b9;
  --color-info: #17a2b8;
  --color-info-hover: #138496;
  --color-success: #28a745;
  --color-success-hover: #218838;
  --color-warning: #ffc107;
  --color-warning-hover: #e0a800;
  --color-danger: #dc3545;
  --color-danger-hover: #c82333;

  /* Background Colors */
  --color-background: #ffffff;
  --color-surface: #f8f9fa;
  --color-surface-hover: #e9ecef;
  --color-card: #ffffff;
  --color-modal: #ffffff;

  /* Text Colors */
  --color-text: #2c3e50;
  --color-text-strong: #212529;
  --color-text-muted: #6c757d;
  --color-text-light: #adb5bd;
  --color-text-inverse: #ffffff;

  /* Border Colors */
  --color-border: #dee2e6;
  --color-border-hover: #adb5bd;
  --color-border-focus: #ff6b35;

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Input focus shadow */
  --input-focus-shadow: rgba(255, 107, 53, 0.25);
}

/* Gunmetal Dark Theme Override */
[data-theme='gunmetal-dark'] {
  /* Primary Colors */
  --color-primary: #6c757d;
  --color-primary-dark: #5a6268;
  --color-primary-light: #868e96;
  --color-primary-alpha: rgba(108, 117, 125, 0.1);

  /* Semantic Colors */
  --color-link: #17a2b8;
  --color-link-hover: #138496;
  --color-info: #007bff;
  --color-info-hover: #0056b3;
  --color-success: #28a745;
  --color-success-hover: #218838;
  --color-warning: #ffc107;
  --color-warning-hover: #e0a800;
  --color-danger: #dc3545;
  --color-danger-hover: #c82333;

  /* Background Colors */
  --color-background: #212529;
  --color-surface: #343a40;
  --color-surface-hover: #495057;
  --color-card: #343a40;
  --color-modal: #343a40;

  /* Text Colors */
  --color-text: #f8f9fa;
  --color-text-strong: #ffffff;
  --color-text-muted: #adb5bd;
  --color-text-light: #6c757d;
  --color-text-inverse: #212529;

  /* Border Colors */
  --color-border: #495057;
  --color-border-hover: #6c757d;
  --color-border-focus: #17a2b8;

  /* Shadows for dark theme */
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.3);
  --shadow-card: 0 0.5em 1em -0.125em rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Input focus shadow */
  --input-focus-shadow: rgba(23, 162, 184, 0.25);

  /* Modal overlay */
  --modal-overlay: rgba(0, 0, 0, 0.86);
}

/* Medium Light Theme Override */
[data-theme='medium-light'] {
  /* Primary Colors */
  --color-primary: #5a6c7d;
  --color-primary-dark: #4a5a6b;
  --color-primary-light: #6a7e8f;
  --color-primary-alpha: rgba(90, 108, 125, 0.1);

  /* Semantic Colors */
  --color-link: #3498db;
  --color-link-hover: #2980b9;
  --color-info: #17a2b8;
  --color-info-hover: #138496;
  --color-success: #27ae60;
  --color-success-hover: #229954;
  --color-warning: #f39c12;
  --color-warning-hover: #e67e22;
  --color-danger: #e74c3c;
  --color-danger-hover: #c0392b;

  /* Background Colors */
  --color-background: #f5f7fa;
  --color-surface: #ffffff;
  --color-surface-hover: #ecf0f1;
  --color-card: #ffffff;
  --color-modal: #ffffff;

  /* Text Colors */
  --color-text: #2c3e50;
  --color-text-strong: #2c3e50;
  --color-text-muted: #7f8c8d;
  --color-text-light: #95a5a6;
  --color-text-inverse: #ffffff;

  /* Border Colors */
  --color-border: #bdc3c7;
  --color-border-hover: #95a5a6;
  --color-border-focus: #5a6c7d;

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Input focus shadow */
  --input-focus-shadow: rgba(90, 108, 125, 0.25);
}

/* Jet Black Electric Blue Theme Override */
[data-theme='jet-black-electric-blue'] {
  /* Primary Colors */
  --color-primary: #00d4ff;
  --color-primary-dark: #00a8cc;
  --color-primary-light: #33ddff;
  --color-primary-alpha: rgba(0, 212, 255, 0.1);

  /* Semantic Colors */
  --color-link: #0099cc;
  --color-link-hover: #007399;
  --color-info: #17a2b8;
  --color-info-hover: #138496;
  --color-success: #00ff88;
  --color-success-hover: #00cc6a;
  --color-warning: #ffaa00;
  --color-warning-hover: #cc8800;
  --color-danger: #ff3366;
  --color-danger-hover: #cc1a4d;

  /* Background Colors */
  --color-background: #000000;
  --color-surface: #1a1a1a;
  --color-surface-hover: #2d2d2d;
  --color-card: #1a1a1a;
  --color-modal: #1a1a1a;

  /* Text Colors */
  --color-text: #ffffff;
  --color-text-strong: #ffffff;
  --color-text-muted: #cccccc;
  --color-text-light: #999999;
  --color-text-inverse: #000000;

  /* Border Colors */
  --color-border: #333333;
  --color-border-hover: #555555;
  --color-border-focus: #00d4ff;

  /* Shadows for dark theme */
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.5);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.5);
  --shadow-card: 0 0.5em 1em -0.125em rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(0, 212, 255, 0.1);

  /* Dropdown Colors */
  --dropdown-background: var(--color-surface);
  --dropdown-border: var(--color-border);
  --dropdown-item-hover: var(--color-surface-hover);

  /* Input focus shadow */
  --input-focus-shadow: rgba(0, 212, 255, 0.25);

  /* Modal overlay */
  --modal-overlay: rgba(0, 0, 0, 0.9);
}

/* Theme Transition Animations */
* {
  transition:
    background-color var(--transition-theme),
    color var(--transition-theme),
    border-color var(--transition-theme),
    box-shadow var(--transition-theme);
}

/* Disable transitions during theme switching to prevent flashing */
.theme-switching * {
  transition: none !important;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-theme: none;
    --transition-fast: none;
    --transition-medium: none;
    --transition-slow: none;
  }

  * {
    transition: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-border: #000000;
    --color-text: #000000;
    --color-background: #ffffff;
  }

  [data-theme='darkly'] {
    --color-border: #ffffff;
    --color-text: #ffffff;
    --color-background: #000000;
  }
}

/* Focus indicators that work across all themes */
.focus-visible,
*:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

/* Ensure proper contrast for focus indicators */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
a:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

/* Theme-aware scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}

/* Selection colors */
::selection {
  background: var(--color-primary);
  color: var(--color-text-inverse);
}

::-moz-selection {
  background: var(--color-primary);
  color: var(--color-text-inverse);
}

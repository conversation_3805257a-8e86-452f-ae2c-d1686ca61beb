<template>
  <div class="admin-notifications-view">
    <div class="container is-fluid">
      <!-- Header -->
      <div class="level mb-5">
        <div class="level-left">
          <div class="level-item">
            <div>
              <h1 class="title is-4">
                <span class="icon">
                  <i class="fas fa-bell"></i>
                </span>
                Notifications
              </h1>
              <p class="subtitle is-6">Manage system and admin notifications</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="field is-grouped">
              <div class="control">
                <button 
                  v-if="hasUnread"
                  class="button is-primary"
                  @click="markAllAsRead"
                  :class="{ 'is-loading': isMarkingRead }"
                >
                  <span class="icon">
                    <i class="fas fa-check"></i>
                  </span>
                  <span>Mark All Read</span>
                </button>
              </div>
              <div class="control">
                <button 
                  class="button is-light"
                  @click="loadNotifications"
                  :class="{ 'is-loading': isLoading }"
                >
                  <span class="icon">
                    <i class="fas fa-sync-alt"></i>
                  </span>
                  <span>Refresh</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="notification is-danger mb-5">
        <button class="delete" @click="error = null"></button>
        {{ error }}
      </div>

      <!-- Filters -->
      <div class="card mb-5">
        <div class="card-content">
          <div class="field is-grouped is-grouped-multiline">
            <div class="control">
              <div class="field">
                <label class="label is-small">Type</label>
                <div class="control">
                  <div class="select is-small">
                    <select v-model="filters.type" @change="loadNotifications">
                      <option value="">All Types</option>
                      <option value="critical">Critical</option>
                      <option value="warning">Warning</option>
                      <option value="info">Info</option>
                      <option value="success">Success</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="control">
              <div class="field">
                <label class="label is-small">Category</label>
                <div class="control">
                  <div class="select is-small">
                    <select v-model="filters.category" @change="loadNotifications">
                      <option value="">All Categories</option>
                      <option value="content_report">Content Reports</option>
                      <option value="user_action">User Actions</option>
                      <option value="system">System</option>
                      <option value="security">Security</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="control">
              <div class="field">
                <label class="label is-small">Status</label>
                <div class="control">
                  <div class="select is-small">
                    <select v-model="filters.read" @change="loadNotifications">
                      <option value="">All</option>
                      <option value="false">Unread</option>
                      <option value="true">Read</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Notifications List -->
      <div v-if="isLoading && notifications.length === 0" class="has-text-centered py-6">
        <div class="is-size-4 mb-3">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>Loading notifications...</p>
      </div>

      <div v-else-if="notifications.length === 0" class="has-text-centered py-6">
        <div class="is-size-1 mb-4 has-text-grey-light">
          <i class="fas fa-bell-slash"></i>
        </div>
        <h3 class="title is-5 has-text-grey">No notifications found</h3>
        <p class="has-text-grey">There are no notifications matching your current filters.</p>
      </div>

      <div v-else class="notifications-list">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="card mb-4 notification-card"
          :class="{ 'is-unread': !notification.read }"
        >
          <div class="card-content">
            <div class="media">
              <div class="media-left">
                <span class="icon is-large" :class="{
                  'has-text-danger': notification.type === 'critical',
                  'has-text-warning': notification.type === 'warning',
                  'has-text-info': notification.type === 'info',
                  'has-text-success': notification.type === 'success'
                }">
                  <i class="fas fa-2x" :class="{
                    'fa-exclamation-triangle': notification.type === 'critical',
                    'fa-exclamation-circle': notification.type === 'warning',
                    'fa-info-circle': notification.type === 'info',
                    'fa-check-circle': notification.type === 'success'
                  }"></i>
                </span>
              </div>
              <div class="media-content">
                <div class="content">
                  <div class="level">
                    <div class="level-left">
                      <div class="level-item">
                        <div>
                          <h5 class="title is-5 mb-2">{{ notification.title }}</h5>
                          <p class="mb-3">{{ notification.message }}</p>
                          <div class="tags">
                            <span class="tag" :class="{
                              'is-danger': notification.type === 'critical',
                              'is-warning': notification.type === 'warning',
                              'is-info': notification.type === 'info',
                              'is-success': notification.type === 'success'
                            }">
                              {{ notification.type.toUpperCase() }}
                            </span>
                            <span class="tag is-light">{{ notification.category.replace('_', ' ').toUpperCase() }}</span>
                            <span v-if="!notification.read" class="tag is-primary">NEW</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="level-right">
                      <div class="level-item">
                        <div class="has-text-right">
                          <p class="is-size-7 has-text-grey mb-2">
                            {{ formatDateTime(notification.createdAt) }}
                          </p>
                          <div class="field is-grouped">
                            <div class="control" v-if="!notification.read">
                              <button 
                                class="button is-small is-primary is-outlined"
                                @click="markAsRead(notification.id)"
                                :class="{ 'is-loading': markingRead.includes(notification.id) }"
                              >
                                Mark Read
                              </button>
                            </div>
                            <div class="control" v-if="notification.actionUrl">
                              <button 
                                class="button is-small is-info"
                                @click="handleAction(notification)"
                              >
                                <span class="icon">
                                  <i class="fas fa-external-link-alt"></i>
                                </span>
                                <span>View</span>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <nav v-if="pagination.totalPages > 1" class="pagination is-centered mt-5" role="navigation">
        <button 
          class="pagination-previous" 
          :disabled="pagination.page <= 1"
          @click="changePage(pagination.page - 1)"
        >
          Previous
        </button>
        <button 
          class="pagination-next" 
          :disabled="pagination.page >= pagination.totalPages"
          @click="changePage(pagination.page + 1)"
        >
          Next
        </button>
        <ul class="pagination-list">
          <li v-for="page in visiblePages" :key="page">
            <button 
              v-if="page !== '...'"
              class="pagination-link"
              :class="{ 'is-current': page === pagination.page }"
              @click="changePage(page)"
            >
              {{ page }}
            </button>
            <span v-else class="pagination-ellipsis">&hellip;</span>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '../stores/notifications'
import { storeToRefs } from 'pinia'

const router = useRouter()
const notificationStore = useNotificationStore()

const { 
  notifications, 
  pagination, 
  isLoading, 
  error 
} = storeToRefs(notificationStore)

// Local state
const isMarkingRead = ref(false)
const markingRead = ref<string[]>([])
const filters = ref({
  type: '',
  category: '',
  read: ''
})

// Computed
const hasUnread = computed(() => 
  notifications.value.some(n => !n.read)
)

const visiblePages = computed(() => {
  const current = pagination.value.page
  const total = pagination.value.totalPages
  const pages: (number | string)[] = []
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    pages.push(1)
    if (current > 4) pages.push('...')
    
    const start = Math.max(2, current - 2)
    const end = Math.min(total - 1, current + 2)
    
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
    
    if (current < total - 3) pages.push('...')
    pages.push(total)
  }
  
  return pages
})

// Methods
const loadNotifications = async () => {
  await notificationStore.loadNotifications({
    page: pagination.value.page,
    ...filters.value
  })
}

const markAllAsRead = async () => {
  isMarkingRead.value = true
  try {
    await notificationStore.markAllAsRead()
  } finally {
    isMarkingRead.value = false
  }
}

const markAsRead = async (notificationId: string) => {
  markingRead.value.push(notificationId)
  try {
    await notificationStore.markAsRead(notificationId)
  } finally {
    markingRead.value = markingRead.value.filter(id => id !== notificationId)
  }
}

const handleAction = async (notification: any) => {
  // Mark as read if not already
  if (!notification.read) {
    await markAsRead(notification.id)
  }
  
  // Navigate to action URL
  if (notification.actionUrl) {
    router.push(notification.actionUrl)
  }
}

const changePage = async (page: number | string) => {
  if (typeof page === 'number' && page !== pagination.value.page) {
    await notificationStore.loadNotifications({
      page,
      ...filters.value
    })
  }
}

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString()
}

// Lifecycle
onMounted(() => {
  loadNotifications()
})
</script>

<style scoped>
.notification-card {
  transition: all 0.2s ease;
}

.notification-card.is-unread {
  border-left: 4px solid #3273dc;
  background-color: #f0f8ff;
}

.notification-card:hover {
  box-shadow: 0 8px 16px rgba(10, 10, 10, 0.1);
}

.notifications-list {
  min-height: 400px;
}

.media-left .icon {
  margin-right: 1rem;
}

.tags .tag {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.pagination {
  margin-top: 2rem;
}
</style>
// Critical resource preloading for <500ms initialization
// Preload only essential resources needed for first paint

export function preloadCriticalResources(): void {
  // Skip preloading in development mode - Vite handles this differently
  if (import.meta.env.MODE === 'development') {
    console.log('Skipping asset preloading in development mode')
    return
  }

  // Preload critical chunks that will be needed immediately
  const criticalChunks = [
    'vue-runtime-core',
    'vue-reactivity'
  ]

  criticalChunks.forEach(chunk => {
    const link = document.createElement('link')
    link.rel = 'modulepreload'
    link.href = `/assets/${chunk}.js`
    document.head.appendChild(link)
  })

  // Critical CSS is now inlined in HTML - no need to preload
}

// Defer non-critical resource loading
export function scheduleNonCriticalPreload(): void {
  // Skip preloading in development mode - Vite handles this differently
  if (import.meta.env.MODE === 'development') {
    console.log('Skipping non-critical asset preloading in development mode')
    return
  }

  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      const nonCriticalChunks = [
        'vue-router-lazy',
        'pinia-lazy',
        'charts-lazy'
      ]

      nonCriticalChunks.forEach(chunk => {
        const link = document.createElement('link')
        link.rel = 'modulepreload'
        link.href = `/assets/${chunk}.js`
        document.head.appendChild(link)
      })
    }, { timeout: 3000 })
  }
}

// Initialize critical preloading
export function initializeCriticalPreload(): void {
  // Run immediately for critical resources
  preloadCriticalResources()

  // Schedule non-critical resources
  scheduleNonCriticalPreload()

  console.log('🚀 Critical resource preloading initialized')
}
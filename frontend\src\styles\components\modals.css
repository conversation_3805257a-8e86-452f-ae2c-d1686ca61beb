/* Modal Component Styles */

/* Modal Overlay */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-theme);
  padding: var(--spacing-4);
}

.modal.is-active {
  opacity: 1;
  visibility: visible;
}

/* Modal Background (clickable area to close) */
.modal-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  cursor: pointer;
}

/* Modal Card */
.modal-card {
  background: var(--modal-background);
  border-radius: var(--radius-lg);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  box-shadow: var(--shadow-xl);
  display: flex;
  flex-direction: column;
  transform: scale(0.95);
  transition: var(--transition-theme);
}

.modal.is-active .modal-card {
  transform: scale(1);
}

/* Modal Card Header */
.modal-card-head {
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  background: var(--color-surface);
}

.modal-card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-text-strong);
}

/* Modal Card Body */
.modal-card-body {
  padding: var(--spacing-6);
  overflow-y: auto;
  flex: 1;
  color: var(--color-text);
}

.modal-card-body::-webkit-scrollbar {
  width: 6px;
}

.modal-card-body::-webkit-scrollbar-track {
  background: var(--color-surface);
}

.modal-card-body::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.modal-card-body::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}

/* Modal Card Footer */
.modal-card-foot {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
  flex-shrink: 0;
  background: var(--color-surface);
}

.modal-card-foot.is-centered {
  justify-content: center;
}

.modal-card-foot.is-left {
  justify-content: flex-start;
}

.modal-card-foot.is-spaced {
  justify-content: space-between;
}

/* Close Button */
.delete,
.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  width: 24px;
  height: 24px;
  position: relative;
  border-radius: var(--radius);
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete:hover,
.modal-close:hover {
  background: var(--color-surface-hover);
}

.delete:focus,
.modal-close:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.delete::before,
.delete::after,
.modal-close::before,
.modal-close::after {
  content: '';
  position: absolute;
  width: 14px;
  height: 2px;
  background: var(--color-text-muted);
  border-radius: 1px;
  transition: var(--transition-fast);
}

.delete::before,
.modal-close::before {
  transform: rotate(45deg);
}

.delete::after,
.modal-close::after {
  transform: rotate(-45deg);
}

.delete:hover::before,
.delete:hover::after,
.modal-close:hover::before,
.modal-close:hover::after {
  background: var(--color-text);
}

/* Modal Sizes */
.modal-card.is-small {
  max-width: 400px;
}

.modal-card.is-medium {
  max-width: 600px;
}

.modal-card.is-large {
  max-width: 800px;
}

.modal-card.is-fullscreen {
  max-width: 95vw;
  max-height: 95vh;
  width: 95vw;
  height: 95vh;
}

/* Modal Content (alternative to modal-card) */
.modal-content {
  background: var(--modal-background);
  border-radius: var(--radius-lg);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: auto;
  position: relative;
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-6);
  transform: scale(0.95);
  transition: var(--transition-theme);
}

.modal.is-active .modal-content {
  transform: scale(1);
}

/* Confirmation Modal Styles */
.modal-card.is-confirmation .modal-card-body {
  text-align: center;
  padding: var(--spacing-8) var(--spacing-6);
}

.modal-card.is-confirmation .modal-card-title {
  justify-content: center;
  color: var(--color-danger);
}

.modal-card.is-confirmation .confirmation-icon {
  font-size: var(--font-size-4xl);
  color: var(--color-danger);
  margin-bottom: var(--spacing-4);
}

.modal-card.is-confirmation .confirmation-message {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-6);
  color: var(--color-text);
}

.modal-card.is-confirmation .confirmation-details {
  font-size: var(--font-size-base);
  color: var(--color-text-muted);
  margin-bottom: var(--spacing-6);
}

/* Loading Modal */
.modal-card.is-loading .modal-card-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  min-height: 200px;
}

.modal-card.is-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-surface);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-4);
}

.modal-card.is-loading .loading-message {
  color: var(--color-text-muted);
  text-align: center;
}

/* Modal Animations */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

.modal.is-animating-in .modal-card,
.modal.is-animating-in .modal-content {
  animation: modalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.is-animating-out .modal-card,
.modal.is-animating-out .modal-content {
  animation: modalFadeOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive Modal */
@media screen and (max-width: 768px) {
  .modal {
    padding: var(--spacing-2);
  }

  .modal-card,
  .modal-content {
    width: 100%;
    max-width: none;
    max-height: 100vh;
    border-radius: 0;
  }

  .modal-card.is-fullscreen {
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }

  .modal-card-head,
  .modal-card-foot {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .modal-card-body {
    padding: var(--spacing-4);
  }

  .modal-card-title {
    font-size: var(--font-size-lg);
  }

  .modal-card.is-confirmation .modal-card-body {
    padding: var(--spacing-6) var(--spacing-4);
  }

  .modal-card.is-confirmation .confirmation-icon {
    font-size: var(--font-size-3xl);
  }
}

/* Accessibility */
.modal[aria-hidden='true'] {
  display: none;
}

.modal-card:focus,
.modal-content:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
}

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden;
}

/* Modal backdrop blur effect (optional) */
.modal.has-backdrop-blur {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Nested modals support */
.modal.is-nested {
  z-index: calc(var(--z-modal) + 10);
}

.modal.is-nested .modal-background {
  background: rgba(0, 0, 0, 0.3);
}

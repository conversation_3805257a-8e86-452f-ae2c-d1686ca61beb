import express from 'express';
import { authenticateToken } from '../middleware/auth';
import { generalRateLimit } from '../middleware/rateLimiting';
import { requireAdmin } from '../middleware/adminAuth';
import { queryPerformanceAnalyzer } from '../services/QueryPerformanceAnalyzer';
import { CacheService } from '../services/CacheService';

const router = express.Router();

// Apply authentication and rate limiting
router.use(authenticateToken);
router.use(generalRateLimit);

// Get performance overview
router.get('/overview', requireAdmin, async (req, res) => {
  try {
    const queryStats = queryPerformanceAnalyzer.getQueryStats();
    const cacheStats = await CacheService.getStats();
    
    const overview = {
      database: queryStats,
      cache: cacheStats,
      timestamp: Date.now()
    };

    res.json(overview);
  } catch (error) {
    console.error('Failed to get performance overview:', error);
    res.status(500).json({ error: 'Failed to retrieve performance data' });
  }
});

// Get detailed performance report
router.get('/report', requireAdmin, async (req, res) => {
  try {
    const report = queryPerformanceAnalyzer.generateReport();
    res.json(report);
  } catch (error) {
    console.error('Failed to generate performance report:', error);
    res.status(500).json({ error: 'Failed to generate performance report' });
  }
});

// Get recent slow queries
router.get('/slow-queries', requireAdmin, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    const slowQueries = queryPerformanceAnalyzer.getRecentSlowQueries(limit);
    res.json(slowQueries);
  } catch (error) {
    console.error('Failed to get slow queries:', error);
    res.status(500).json({ error: 'Failed to retrieve slow queries' });
  }
});

// Get cache statistics
router.get('/cache', requireAdmin, async (req, res) => {
  try {
    const stats = await CacheService.getStats();
    res.json(stats);
  } catch (error) {
    console.error('Failed to get cache stats:', error);
    res.status(500).json({ error: 'Failed to retrieve cache statistics' });
  }
});

// Clear cache
router.delete('/cache', requireAdmin, async (req, res) => {
  try {
    await CacheService.clear();
    res.json({ message: 'Cache cleared successfully' });
  } catch (error) {
    console.error('Failed to clear cache:', error);
    res.status(500).json({ error: 'Failed to clear cache' });
  }
});

// Clear performance metrics
router.delete('/metrics', requireAdmin, async (req, res) => {
  try {
    queryPerformanceAnalyzer.clearMetrics();
    res.json({ message: 'Performance metrics cleared successfully' });
  } catch (error) {
    console.error('Failed to clear metrics:', error);
    res.status(500).json({ error: 'Failed to clear performance metrics' });
  }
});

// Export performance data
router.get('/export', requireAdmin, async (req, res) => {
  try {
    const data = queryPerformanceAnalyzer.exportMetrics();
    
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="performance-metrics-${Date.now()}.json"`);
    res.json(data);
  } catch (error) {
    console.error('Failed to export performance data:', error);
    res.status(500).json({ error: 'Failed to export performance data' });
  }
});

// Get system resource usage
router.get('/system', requireAdmin, async (req, res) => {
  try {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const uptime = process.uptime();

    const systemStats = {
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      uptime: uptime,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      timestamp: Date.now()
    };

    res.json(systemStats);
  } catch (error) {
    console.error('Failed to get system stats:', error);
    res.status(500).json({ error: 'Failed to retrieve system statistics' });
  }
});

// Get database statistics
router.get('/database', requireAdmin, async (req, res) => {
  try {
    const { QueryOptimizer } = await import('../utils/queryOptimization');
    const dbStats = await QueryOptimizer.getDatabaseStats();
    res.json(dbStats);
  } catch (error) {
    console.error('Failed to get database stats:', error);
    res.status(500).json({ error: 'Failed to retrieve database statistics' });
  }
});

// Analyze specific query
router.post('/analyze-query', requireAdmin, async (req, res) => {
  try {
    const { query, params = [] } = req.body;
    
    if (!query) {
      return res.status(400).json({ error: 'Query is required' });
    }

    const { QueryOptimizer } = await import('../utils/queryOptimization');
    const analysis = await QueryOptimizer.analyzeQuery(query, params);
    
    return res.json({
      query,
      params,
      analysis,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Failed to analyze query:', error);
    return res.status(500).json({ error: 'Failed to analyze query' });
  }
});

// Performance monitoring configuration
router.get('/config', requireAdmin, async (req, res) => {
  try {
    const config = {
      queryAnalysisEnabled: process.env.ENABLE_QUERY_ANALYSIS === 'true',
      cacheEnabled: true, // Always enabled
      slowQueryThreshold: 100, // ms
      metricsRetention: 1000, // number of metrics to keep
      environment: process.env.NODE_ENV
    };

    res.json(config);
  } catch (error) {
    console.error('Failed to get performance config:', error);
    res.status(500).json({ error: 'Failed to retrieve performance configuration' });
  }
});

// Update performance monitoring configuration
router.put('/config', requireAdmin, async (req, res) => {
  try {
    const { queryAnalysisEnabled } = req.body;
    
    if (typeof queryAnalysisEnabled === 'boolean') {
      queryPerformanceAnalyzer.setEnabled(queryAnalysisEnabled);
    }

    res.json({ message: 'Configuration updated successfully' });
  } catch (error) {
    console.error('Failed to update performance config:', error);
    res.status(500).json({ error: 'Failed to update performance configuration' });
  }
});

export default router;
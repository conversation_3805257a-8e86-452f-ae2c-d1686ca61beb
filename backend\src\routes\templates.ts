import express from 'express';
import { TemplateController } from '../controllers/TemplateController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { generalRateLimit } from '../middleware/rateLimiting';

const router = express.Router();

// Apply rate limiting to all routes
router.use(generalRateLimit);

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Template CRUD routes
router.get('/templates', TemplateController.getTemplates);
router.get('/templates/categories', TemplateController.getCategories);
router.get('/templates/:id', TemplateController.getTemplateById);
router.post('/templates', validateRequest('createTemplate'), TemplateController.createTemplate);
router.put('/templates/:id', validateRequest('updateTemplate'), TemplateController.updateTemplate);
router.delete('/templates/:id', TemplateController.deleteTemplate);

// Template usage
router.post('/templates/:id/use', validateRequest('useTemplate'), TemplateController.useTemplate);

// Admin routes
router.post('/templates/seed', TemplateController.seedBuiltInTemplates);

export default router;
#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Performance budgets (in KB) - Optimized for <500ms initialization
const BUDGETS = {
  'index.html': 50,
  'index.js': 250, // Further reduced for faster initialization
  'vendor.js': 300, // Reduced vendor bundle size
  'vue-core.js': 90, // Vue core framework essentials
  'ui-bulma-core.js': 150, // Core Bulma CSS (optimized)
  'ui-bulma-components.js': 100, // Bulma components
  'ui-bulma-utils.js': 50, // Bulma utilities
  'ui-icons-solid.js': 100, // FontAwesome solid icons
  'ui-icons-regular.js': 50, // FontAwesome regular icons
  'ui-icons-brands.js': 80, // FontAwesome brand icons
  'editor-vue.js': 80, // TipTap Vue integration (optimized)
  'editor-starter.js': 150, // TipTap starter kit
  'editor-extensions-core.js': 80, // Core editor extensions
  'editor-extensions-misc.js': 60, // Misc editor extensions
  'editor-formatting.js': 80, // Editor formatting
  'utils-lodash.js': 150, // Lodash utilities
  'utils-date.js': 40, // Date utilities
  'utils-marked.js': 250, // Marked markdown processor
  'utils-highlight-core.js': 100, // Highlight.js core (optimized)
  'utils-highlight-langs.js': 80, // Highlight.js languages
  'charts-core.js': 80, // Chart.js core
  'charts-adapters.js': 40, // Chart.js adapters
  'auth-google.js': 60, // Google authentication
  'index.css': 80, // Optimized CSS
  total: 1200, // Target: 1.2MB total (further reduced)
  // Route-specific bundle budgets - each route chunk should be under 100KB
  routes: {
    auth: 100, // Authentication routes
    dashboard: 100, // Main dashboard functionality
    search: 100, // Search functionality
    groups: 100, // Group management
    shared: 100, // Shared content
    boards: 100, // Board functionality
    admin: 100, // Admin functionality
  },
};

// Initialization time budgets (in milliseconds) - Optimized for <500ms target
const INIT_TIME_BUDGETS = {
  target: 500, // Target initialization time (reduced from 600ms)
  warning: 600, // Warning threshold (reduced from 800ms)
  critical: 800, // Critical threshold (reduced from 1000ms)
};

function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return Math.round(stats.size / 1024); // Convert to KB
  } catch (error) {
    return 0;
  }
}

function measureInitializationTime() {
  // Check multiple possible locations for performance metrics
  const possiblePaths = [
    path.join(__dirname, '../dist/performance-metrics.json'),
    path.join(__dirname, '../performance-metrics.json'),
    path.join(process.cwd(), 'performance-metrics.json'),
  ];

  let metricsFound = false;
  let metrics = null;

  for (const metricsPath of possiblePaths) {
    if (fs.existsSync(metricsPath)) {
      try {
        metrics = JSON.parse(fs.readFileSync(metricsPath, 'utf8'));
        metricsFound = true;
        console.log(`📊 Found performance metrics at: ${metricsPath}`);
        break;
      } catch (error) {
        console.warn(`⚠️  Could not read metrics from ${metricsPath}:`, error.message);
      }
    }
  }

  console.log('\n⏱️  Initialization Time Analysis\n');
  console.log('Metric'.padEnd(30) + 'Time (ms)'.padEnd(12) + 'Budget (ms)'.padEnd(15) + 'Status');
  console.log('-'.repeat(70));

  if (metricsFound && metrics) {
    const initTime = metrics.initialization?.duration || metrics.initializationTime || 0;
    const storeInitTime = metrics.initialization?.storeInitTime || 0;
    const domReadyTime = metrics.initialization?.domReadyTime || 0;

    // Check app initialization time
    let initStatus = '✅ OK';
    if (initTime > INIT_TIME_BUDGETS.critical) {
      initStatus = '❌ CRITICAL';
    } else if (initTime > INIT_TIME_BUDGETS.warning) {
      initStatus = '⚠️  WARNING';
    }

    console.log(
      'App Initialization'.padEnd(30) +
        initTime.toString().padEnd(12) +
        INIT_TIME_BUDGETS.target.toString().padEnd(15) +
        initStatus
    );

    // Check store initialization time (should be < 300ms)
    if (storeInitTime > 0) {
      const storeStatus = storeInitTime <= 300 ? '✅ OK' : '❌ EXCEEDED';
      console.log(
        'Store Initialization'.padEnd(30) +
          storeInitTime.toString().padEnd(12) +
          '300'.padEnd(15) +
          storeStatus
      );
    }

    // Check DOM ready time (should be < 200ms)
    if (domReadyTime > 0) {
      const domStatus = domReadyTime <= 200 ? '✅ OK' : '❌ EXCEEDED';
      console.log(
        'DOM Ready'.padEnd(30) + domReadyTime.toString().padEnd(12) + '200'.padEnd(15) + domStatus
      );
    }

    // Check Core Web Vitals if available
    if (metrics.coreWebVitals) {
      console.log('\n🌐 Core Web Vitals Analysis\n');
      console.log('Metric'.padEnd(30) + 'Value'.padEnd(12) + 'Budget'.padEnd(15) + 'Status');
      console.log('-'.repeat(70));

      const vitals = metrics.coreWebVitals;
      const vitalsBudgets = {
        fcp: 1000,
        lcp: 1500,
        tti: 2000,
        cls: 0.05,
        fid: 100,
      };

      Object.entries(vitalsBudgets).forEach(([metric, budget]) => {
        const value = vitals[metric] || 0;
        const status = value <= budget ? '✅ OK' : '❌ EXCEEDED';
        const unit = metric === 'cls' ? '' : 'ms';

        console.log(
          metric.toUpperCase().padEnd(30) +
            `${value.toFixed(2)}${unit}`.padEnd(12) +
            `${budget}${unit}`.padEnd(15) +
            status
        );
      });
    }

    return initTime <= INIT_TIME_BUDGETS.target;
  } else {
    console.log(
      'App Initialization'.padEnd(30) +
        'N/A'.padEnd(12) +
        INIT_TIME_BUDGETS.target.toString().padEnd(15) +
        '📝 NO DATA'
    );
    console.log('\n📝 No performance metrics found. To generate metrics:');
    console.log('   1. Run the application in development mode');
    console.log('   2. Performance metrics will be automatically collected');
    console.log('   3. Metrics are saved to localStorage and can be exported');
    console.log('   4. For CI/CD, implement metrics export to file system');
    return true; // Don't fail build if no metrics
  }
}

function checkBudgets() {
  const distPath = path.join(__dirname, '../dist');
  const assetsPath = path.join(distPath, 'assets');

  if (!fs.existsSync(distPath)) {
    console.error('❌ Build directory not found. Run "npm run build" first.');
    process.exit(1);
  }

  console.log('📊 Bundle Size Analysis\n');
  console.log('File'.padEnd(30) + 'Size (KB)'.padEnd(12) + 'Budget (KB)'.padEnd(15) + 'Status');
  console.log('-'.repeat(70));

  let totalSize = 0;
  let budgetExceeded = false;

  // Check HTML files
  const htmlFiles = fs.readdirSync(distPath).filter(file => file.endsWith('.html'));
  htmlFiles.forEach(file => {
    const size = getFileSize(path.join(distPath, file));
    totalSize += size;
    const budget = BUDGETS[file] || BUDGETS['index.html'];
    const status = size <= budget ? '✅ OK' : '❌ EXCEEDED';
    if (size > budget) budgetExceeded = true;

    console.log(
      file.padEnd(30) + size.toString().padEnd(12) + budget.toString().padEnd(15) + status
    );
  });

  // Check JS and CSS files in assets
  if (fs.existsSync(assetsPath)) {
    const assetFiles = fs.readdirSync(assetsPath);

    assetFiles.forEach(file => {
      const size = getFileSize(path.join(assetsPath, file));
      totalSize += size;

      let budget = 100; // Default budget
      let budgetKey = 'default';

      // Determine budget based on file pattern with fallbacks
      if (file.includes('vendor')) {
        budget = BUDGETS['vendor.js'] || 300;
        budgetKey = 'vendor.js';
      } else if (file.includes('vue-core')) {
        budget = BUDGETS['vue-core.js'] || 90;
        budgetKey = 'vue-core.js';
      } else if (file.includes('ui-bulma-core')) {
        budget = BUDGETS['ui-bulma-core.js'] || 150;
        budgetKey = 'ui-bulma-core.js';
      } else if (file.includes('ui-bulma')) {
        budget = BUDGETS['ui-bulma-components.js'] || 100;
        budgetKey = 'ui-bulma-components.js';
      } else if (file.includes('ui-icons')) {
        budget = BUDGETS['ui-icons-solid.js'] || 100;
        budgetKey = 'ui-icons-solid.js';
      } else if (file.includes('editor-vue')) {
        budget = BUDGETS['editor-vue.js'] || 80;
        budgetKey = 'editor-vue.js';
      } else if (file.includes('editor-starter')) {
        budget = BUDGETS['editor-starter.js'] || 150;
        budgetKey = 'editor-starter.js';
      } else if (file.includes('editor-extensions-core')) {
        budget = BUDGETS['editor-extensions-core.js'] || 80;
        budgetKey = 'editor-extensions-core.js';
      } else if (file.includes('editor-extensions')) {
        budget = BUDGETS['editor-extensions-misc.js'] || 60;
        budgetKey = 'editor-extensions-misc.js';
      } else if (file.includes('editor-formatting')) {
        budget = BUDGETS['editor-formatting.js'] || 80;
        budgetKey = 'editor-formatting.js';
      } else if (file.includes('utils-lodash')) {
        budget = BUDGETS['utils-lodash.js'] || 150;
        budgetKey = 'utils-lodash.js';
      } else if (file.includes('utils-date')) {
        budget = BUDGETS['utils-date.js'] || 40;
        budgetKey = 'utils-date.js';
      } else if (file.includes('utils-marked')) {
        budget = BUDGETS['utils-marked.js'] || 250;
        budgetKey = 'utils-marked.js';
      } else if (file.includes('utils-highlight-core')) {
        budget = BUDGETS['utils-highlight-core.js'] || 100;
        budgetKey = 'utils-highlight-core.js';
      } else if (file.includes('utils-highlight')) {
        budget = BUDGETS['utils-highlight-langs.js'] || 80;
        budgetKey = 'utils-highlight-langs.js';
      } else if (file.includes('charts-core')) {
        budget = BUDGETS['charts-core.js'] || 80;
        budgetKey = 'charts-core.js';
      } else if (file.includes('charts')) {
        budget = BUDGETS['charts-adapters.js'] || 40;
        budgetKey = 'charts-adapters.js';
      } else if (file.includes('auth')) {
        budget = BUDGETS['auth-google.js'] || 60;
        budgetKey = 'auth-google.js';
      } else if (file.endsWith('.js')) {
        // Check for route-specific chunks
        let routeFound = false;
        const routeBudgets = BUDGETS.routes || {};
        for (const [routeName, routeBudget] of Object.entries(routeBudgets)) {
          if (file.includes(routeName)) {
            budget = routeBudget;
            budgetKey = `route-${routeName}.js`;
            routeFound = true;
            break;
          }
        }
        if (!routeFound) {
          budget = BUDGETS['index.js'] || 250;
          budgetKey = 'index.js';
        }
      } else if (file.endsWith('.css')) {
        budget = BUDGETS['index.css'] || 80;
        budgetKey = 'index.css';
      }

      const status = size <= budget ? '✅ OK' : '❌ EXCEEDED';
      if (size > budget) budgetExceeded = true;

      console.log(
        file.padEnd(30) + size.toString().padEnd(12) + budget.toString().padEnd(15) + status
      );
    });
  }

  console.log('-'.repeat(70));
  const totalStatus = totalSize <= BUDGETS.total ? '✅ OK' : '❌ EXCEEDED';
  if (totalSize > BUDGETS.total) budgetExceeded = true;

  console.log(
    'TOTAL'.padEnd(30) +
      totalSize.toString().padEnd(12) +
      BUDGETS.total.toString().padEnd(15) +
      totalStatus
  );

  // Check initialization time
  const initTimeOk = measureInitializationTime();

  // Route-specific bundle analysis
  console.log('\n🛣️  Route Bundle Analysis');
  console.log(
    'Route Chunk'.padEnd(30) + 'Size (KB)'.padEnd(12) + 'Budget (KB)'.padEnd(15) + 'Status'
  );
  console.log('-'.repeat(70));

  let routeBudgetExceeded = false;
  if (fs.existsSync(assetsPath)) {
    const assetFiles = fs.readdirSync(assetsPath);

    Object.keys(BUDGETS.routes).forEach(routeName => {
      const routeFiles = assetFiles.filter(
        file => file.includes(routeName) && file.endsWith('.js')
      );
      const routeSize = routeFiles.reduce((total, file) => {
        return total + getFileSize(path.join(assetsPath, file));
      }, 0);

      if (routeSize > 0) {
        const budget = BUDGETS.routes[routeName];
        const status = routeSize <= budget ? '✅ OK' : '❌ EXCEEDED';
        if (routeSize > budget) routeBudgetExceeded = true;

        console.log(
          `${routeName}-route`.padEnd(30) +
            routeSize.toString().padEnd(12) +
            budget.toString().padEnd(15) +
            status
        );
      }
    });
  }

  console.log('\n📈 Performance Recommendations:');

  if (budgetExceeded || routeBudgetExceeded || !initTimeOk) {
    if (budgetExceeded) {
      console.log('❌ Bundle size budget exceeded!');
      console.log('   • Consider code splitting for large components');
      console.log('   • Remove unused dependencies');
      console.log('   • Implement lazy loading for non-critical features');
      console.log('   • Optimize images and assets');
    }

    if (routeBudgetExceeded) {
      console.log('❌ Route bundle size budget exceeded!');
      console.log('   • Split large route components into smaller chunks');
      console.log('   • Move shared functionality to common chunks');
      console.log('   • Implement component-level lazy loading');
      console.log('   • Consider route-level code splitting optimization');
    }

    if (!initTimeOk) {
      console.log('❌ Initialization time budget exceeded!');
      console.log('   • Implement parallel store initialization');
      console.log('   • Defer non-critical service loading');
      console.log('   • Use progressive loading strategies');
      console.log('   • Optimize DOM operations with nextTick');
    }

    process.exit(1);
  } else {
    console.log('✅ All performance budgets are within targets!');
    console.log('   • Continue monitoring bundle sizes and initialization time');
    console.log('   • Consider further optimizations for better performance');
    console.log('   • Route-based code splitting is optimized');
  }

  return { budgetExceeded, initTimeOk };
}

function main() {
  console.log('Starting bundle size check...');
  checkBudgets();
}

// Run if called directly
main();

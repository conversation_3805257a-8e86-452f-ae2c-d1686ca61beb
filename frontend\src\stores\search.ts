import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  searchService, 
  type SearchFilters, 
  type SearchOptions, 
  type SearchResult, 
  type SearchResponse,
  type SearchSuggestions,
  type SearchStats
} from '../services/searchService'

export const useSearchStore = defineStore('search', () => {
  // State
  const searchResults = ref<SearchResult[]>([])
  const searchQuery = ref('')
  const searchFilters = ref<SearchFilters>({})
  const searchOptions = ref<SearchOptions>({ page: 1, limit: 20, sortBy: 'relevance' })
  const isSearching = ref(false)
  const searchError = ref<string | null>(null)
  const searchTime = ref(0)
  const totalResults = ref(0)
  const currentPage = ref(1)
  const totalPages = ref(1)
  const suggestions = ref<SearchSuggestions>({ titles: [], tags: [] })
  const searchStats = ref<SearchStats | null>(null)
  const searchHistory = ref<string[]>([])
  const showAdvancedSearch = ref(false)
  const recentSearches = ref<string[]>([])

  // Advanced search state
  const advancedFilters = ref({
    includeTerms: [] as string[],
    excludeTerms: [] as string[],
    exactPhrases: [] as string[],
    titleSearch: '',
    contentSearch: ''
  })

  // Getters
  const hasResults = computed(() => searchResults.value.length > 0)
  const hasQuery = computed(() => searchQuery.value.trim().length > 0)
  const isFirstPage = computed(() => currentPage.value === 1)
  const isLastPage = computed(() => currentPage.value >= totalPages.value)
  const hasMoreResults = computed(() => currentPage.value < totalPages.value)
  
  const activeFiltersCount = computed(() => {
    let count = 0
    if (searchFilters.value.noteType) count++
    if (searchFilters.value.tags && searchFilters.value.tags.length > 0) count++
    if (searchFilters.value.dateFrom) count++
    if (searchFilters.value.dateTo) count++
    if (searchFilters.value.archived !== undefined) count++
    return count
  })

  const searchSummary = computed(() => {
    if (!hasResults.value) return ''
    
    const start = (currentPage.value - 1) * searchOptions.value.limit + 1
    const end = Math.min(currentPage.value * searchOptions.value.limit, totalResults.value)
    
    return `Showing ${start}-${end} of ${totalResults.value} results (${searchTime.value}ms)`
  })

  // Actions
  const setError = (message: string | null) => {
    searchError.value = message
  }

  const clearError = () => {
    searchError.value = null
  }

  // Perform search
  const search = async (
    query?: string,
    filters?: SearchFilters,
    options?: Partial<SearchOptions>
  ) => {
    isSearching.value = true
    clearError()

    try {
      // Update state
      if (query !== undefined) searchQuery.value = query
      if (filters) searchFilters.value = { ...searchFilters.value, ...filters }
      if (options) searchOptions.value = { ...searchOptions.value, ...options }

      // Parse query for embedded filters
      const { cleanQuery, extractedFilters } = searchService.parseSearchQuery(searchQuery.value)
      
      const finalFilters: SearchFilters = {
        ...searchFilters.value,
        ...extractedFilters,
        query: cleanQuery
      }

      const response = await searchService.searchNotes(finalFilters, searchOptions.value)

      // Update results
      if (searchOptions.value.page === 1) {
        searchResults.value = response.results
      } else {
        // Append results for pagination
        searchResults.value.push(...response.results)
      }

      totalResults.value = response.total
      currentPage.value = response.pagination.page
      totalPages.value = response.pagination.totalPages
      searchTime.value = response.searchTime

      // Add to search history if it's a new search
      if (searchQuery.value.trim() && searchOptions.value.page === 1) {
        searchService.addToSearchHistory(searchQuery.value.trim())
        loadSearchHistory()
      }

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Search failed')
    } finally {
      isSearching.value = false
    }
  }

  // Debounced search for real-time search
  const debouncedSearch = async (
    query?: string,
    filters?: SearchFilters,
    options?: Partial<SearchOptions>
  ) => {
    isSearching.value = true
    clearError()

    try {
      // Update state
      if (query !== undefined) searchQuery.value = query
      if (filters) searchFilters.value = { ...searchFilters.value, ...filters }
      if (options) searchOptions.value = { ...searchOptions.value, ...options }

      // Parse query for embedded filters
      const { cleanQuery, extractedFilters } = searchService.parseSearchQuery(searchQuery.value)
      
      const finalFilters: SearchFilters = {
        ...searchFilters.value,
        ...extractedFilters,
        query: cleanQuery
      }

      const response = await searchService.debouncedSearch(finalFilters, searchOptions.value)

      // Update results
      searchResults.value = response.results
      totalResults.value = response.total
      currentPage.value = response.pagination.page
      totalPages.value = response.pagination.totalPages
      searchTime.value = response.searchTime

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Search failed')
    } finally {
      isSearching.value = false
    }
  }

  // Load more results (pagination)
  const loadMoreResults = async () => {
    if (hasMoreResults.value && !isSearching.value) {
      await search(undefined, undefined, { page: currentPage.value + 1 })
    }
  }

  // Get search suggestions
  const getSuggestions = async (query: string) => {
    try {
      const result = await searchService.getSearchSuggestions(query)
      suggestions.value = result
      return result
    } catch (error) {
      console.error('Failed to get suggestions:', error)
      return { titles: [], tags: [] }
    }
  }

  // Load search statistics
  const loadSearchStats = async () => {
    try {
      const stats = await searchService.getSearchStats()
      searchStats.value = stats
      return stats
    } catch (error) {
      console.error('Failed to load search stats:', error)
      return null
    }
  }

  // Load search history
  const loadSearchHistory = () => {
    searchHistory.value = searchService.getSearchHistory()
    recentSearches.value = searchHistory.value.slice(0, 5)
  }

  // Clear search history
  const clearSearchHistory = () => {
    searchService.clearSearchHistory()
    loadSearchHistory()
  }

  // Set search query
  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  // Set search filters
  const setSearchFilters = (filters: Partial<SearchFilters>) => {
    searchFilters.value = { ...searchFilters.value, ...filters }
  }

  // Clear search filters
  const clearSearchFilters = () => {
    searchFilters.value = {}
  }

  // Set search options
  const setSearchOptions = (options: Partial<SearchOptions>) => {
    searchOptions.value = { ...searchOptions.value, ...options }
  }

  // Clear search results
  const clearSearchResults = () => {
    searchResults.value = []
    totalResults.value = 0
    currentPage.value = 1
    totalPages.value = 1
    searchTime.value = 0
  }

  // Reset search state
  const resetSearch = () => {
    searchQuery.value = ''
    clearSearchFilters()
    clearSearchResults()
    clearError()
    searchOptions.value = { page: 1, limit: 20, sortBy: 'relevance' }
  }

  // Advanced search methods
  const toggleAdvancedSearch = () => {
    showAdvancedSearch.value = !showAdvancedSearch.value
  }

  const buildAdvancedQuery = () => {
    const query = searchService.buildAdvancedQuery({
      include: advancedFilters.value.includeTerms,
      exclude: advancedFilters.value.excludeTerms,
      exact: advancedFilters.value.exactPhrases,
      title: advancedFilters.value.titleSearch,
      content: advancedFilters.value.contentSearch
    })
    
    setSearchQuery(query)
    return query
  }

  const clearAdvancedFilters = () => {
    advancedFilters.value = {
      includeTerms: [],
      excludeTerms: [],
      exactPhrases: [],
      titleSearch: '',
      contentSearch: ''
    }
  }

  // Quick search presets
  const searchByNoteType = async (noteType: 'richtext' | 'markdown' | 'kanban') => {
    await search('', { noteType }, { page: 1 })
  }

  const searchByTag = async (tagName: string) => {
    await search('', { tags: [tagName] }, { page: 1 })
  }

  const searchRecent = async (days: number = 7) => {
    const dateFrom = new Date()
    dateFrom.setDate(dateFrom.getDate() - days)
    
    await search('', { 
      dateFrom: dateFrom.toISOString().split('T')[0] 
    }, { page: 1 })
  }

  const searchArchived = async () => {
    await search('', { archived: true }, { page: 1 })
  }

  // Reindex search data
  const reindexSearch = async () => {
    try {
      const result = await searchService.reindexSearch()
      return result
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to reindex search')
      throw error
    }
  }

  // Initialize search store
  const initialize = async () => {
    loadSearchHistory()
    await loadSearchStats()
  }

  // Cancel any pending searches
  const cancelSearch = () => {
    searchService.cancelDebouncedSearch()
    isSearching.value = false
  }

  return {
    // State
    searchResults,
    searchQuery,
    searchFilters,
    searchOptions,
    isSearching,
    searchError,
    searchTime,
    totalResults,
    currentPage,
    totalPages,
    suggestions,
    searchStats,
    searchHistory,
    recentSearches,
    showAdvancedSearch,
    advancedFilters,

    // Getters
    hasResults,
    hasQuery,
    isFirstPage,
    isLastPage,
    hasMoreResults,
    activeFiltersCount,
    searchSummary,

    // Actions
    search,
    debouncedSearch,
    loadMoreResults,
    getSuggestions,
    loadSearchStats,
    loadSearchHistory,
    clearSearchHistory,
    setSearchQuery,
    setSearchFilters,
    clearSearchFilters,
    setSearchOptions,
    clearSearchResults,
    resetSearch,
    toggleAdvancedSearch,
    buildAdvancedQuery,
    clearAdvancedFilters,
    searchByNoteType,
    searchByTag,
    searchRecent,
    searchArchived,
    reindexSearch,
    initialize,
    cancelSearch,
    setError,
    clearError
  }
})
<template>
  <div class="app-sidebar" :class="{ 'is-collapsed': isCollapsed }">
    <!-- Header -->
    <div class="sidebar-header">
      <div class="header-content">
        <div class="logo-section">
          <img src="/logo_icon_only.png" alt="CF Notes Pro Logo" class="logo-icon" />
          <span v-if="!isCollapsed" class="logo-text">CF Notes Pro</span>
        </div>
        <div class="header-actions">
          <button class="button is-ghost sidebar-toggle is-hidden-mobile" @click="$emit('toggle-collapse')"
            :title="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'">
            <span class="icon">
              <i :class="isCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'"></i>
            </span>
          </button>
          <button class="button is-ghost sidebar-close is-hidden-tablet" @click="$emit('close-mobile')"
            title="Close sidebar">
            <span class="icon">
              <i class="fas fa-times"></i>
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Scrollable Content Container -->
    <div class="sidebar-content">
      <!-- User Profile Section -->
      <div class="sidebar-section user-section">
        <div class="user-profile" :class="{ 'is-collapsed': isCollapsed }">
          <div class="user-avatar">
            <figure class="image is-32x32">
              <img v-if="authStore && authStore.user?.avatarUrl" class="is-rounded" :src="authStore.user.avatarUrl"
                :alt="authStore.user?.displayName || 'User'">
              <span v-else class="icon is-large avatar-placeholder">
                <i class="fas fa-user fa-lg"></i>
              </span>
            </figure>
          </div>
          <div v-if="!isCollapsed" class="user-info">
            <p class="user-name">{{ (authStore && authStore.user?.displayName) || 'User' }}</p>
            <p class="user-email">{{ authStore && authStore.user?.email }}</p>
          </div>
          <div v-if="!isCollapsed" class="user-actions">
            <div class="dropdown is-right" :class="{ 'is-active': showUserDropdown }">
              <div class="dropdown-trigger">
                <button class="button is-ghost is-small" @click="showUserDropdown = !showUserDropdown"
                  title="User menu">
                  <span class="icon">
                    <i class="fas fa-ellipsis-v"></i>
                  </span>
                </button>
              </div>
              <div class="dropdown-menu">
                <div class="dropdown-content">
                  <a class="dropdown-item" @click="openSettings">
                    <span class="icon">
                      <i class="fas fa-cog"></i>
                    </span>
                    <span>Settings</span>
                  </a>
                  <router-link v-if="authStore && authStore.isAdmin" to="/admin" class="dropdown-item">
                    <span class="icon">
                      <i class="fas fa-shield-alt"></i>
                    </span>
                    <span>Admin Panel</span>
                  </router-link>
                  <hr class="dropdown-divider">
                  <a class="dropdown-item" @click="handleLogout">
                    <span class="icon">
                      <i class="fas fa-sign-out-alt"></i>
                    </span>
                    <span>Sign Out</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions Section (Moved to top) -->
      <div class="sidebar-section actions-section">
        <div class="quick-actions">
          <button class="button is-primary is-fullwidth" :class="{ 'is-small': isCollapsed }" @click="createNote"
            :title="isCollapsed ? 'Create Note' : ''">
            <span class="icon">
              <i class="fas fa-plus"></i>
            </span>
            <span v-if="!isCollapsed">New Note</span>
          </button>
        </div>
      </div>

      <!-- Navigation Section -->
      <nav class="sidebar-section nav-section">
        <div class="sidebar-section-header" v-if="!isCollapsed">
          <h3 class="section-title">Navigation</h3>
        </div>
        <ul class="sidebar-menu">
          <li class="menu-item">
            <a href="#" class="menu-link" :class="{ 'is-active': activeSection === 'dashboard' }"
              :title="isCollapsed ? 'Dashboard' : ''" @click.prevent="handleSectionChange('dashboard')">
              <span class="icon">
                <i class="fas fa-tachometer-alt"></i>
              </span>
              <span v-if="!isCollapsed" class="menu-text">Dashboard</span>
            </a>
          </li>

          <li class="menu-item">
            <a href="#" class="menu-link" :class="{ 'is-active': activeSection === 'recent' }"
              :title="isCollapsed ? 'Recent' : ''" @click.prevent="handleSectionChange('recent')">
              <span class="icon">
                <i class="fas fa-clock"></i>
              </span>
              <span v-if="!isCollapsed" class="menu-text">Recent</span>
            </a>
          </li>
          <li class="menu-item">
            <a href="#" class="menu-link"
              :class="{ 'is-active': activeSection === 'favorites' }" :title="isCollapsed ? 'Favorites' : ''"
              @click.prevent="handleSectionChange('favorites')">
              <span class="icon">
                <i class="fas fa-star"></i>
              </span>
              <span v-if="!isCollapsed" class="menu-text">Favorites</span>
            </a>
          </li>
          <li class="menu-item">
            <a href="#" class="menu-link" :class="{ 'is-active': activeSection === 'shared' }"
              :title="isCollapsed ? 'Shared' : ''" @click.prevent="handleSectionChange('shared')">
              <span class="icon">
                <i class="fas fa-share-alt"></i>
              </span>
              <span v-if="!isCollapsed" class="menu-text">Shared</span>
            </a>
          </li>
          <li class="menu-item">
            <router-link to="/dashboard/groups" class="menu-link" :class="{ 'is-active': activeSection === 'groups' }"
              :title="isCollapsed ? 'Groups' : ''">
              <span class="icon">
                <i class="fas fa-users"></i>
              </span>
              <span v-if="!isCollapsed" class="menu-text">Groups</span>
            </router-link>
          </li>
          <li class="menu-item">
            <a href="#" class="menu-link"
              :class="{ 'is-active': activeSection === 'archived' }" :title="isCollapsed ? 'Archived' : ''"
              @click.prevent="handleSectionChange('archived')">
              <span class="icon">
                <i class="fas fa-archive"></i>
              </span>
              <span v-if="!isCollapsed" class="menu-text">Archived</span>
            </a>
          </li>
        </ul>
      </nav>



      <!-- Admin Section -->
      <div v-if="authStore && authStore.isAdmin" class="sidebar-section admin-section">
        <div class="sidebar-section-header" v-if="!isCollapsed">
          <h3 class="section-title">Administration</h3>
        </div>
        <ul class="sidebar-menu">
          <li class="menu-item">
            <router-link to="/admin" class="menu-link" :class="{ 'is-active': activeSection === 'admin-dashboard' }"
              :title="isCollapsed ? 'Admin Dashboard' : ''">
              <span class="icon">
                <i class="fas fa-shield-alt"></i>
              </span>
              <span v-if="!isCollapsed" class="menu-text">Dashboard</span>
            </router-link>
          </li>
          <li class="menu-item">
            <router-link to="/admin/users" class="menu-link" :class="{ 'is-active': activeSection === 'admin-users' }"
              :title="isCollapsed ? 'User Management' : ''">
              <span class="icon">
                <i class="fas fa-users-cog"></i>
              </span>
              <span v-if="!isCollapsed" class="menu-text">Users</span>
            </router-link>
          </li>
        </ul>
      </div>

      <!-- Groups Section -->
      <div class="sidebar-section groups-section">
        <div class="sidebar-section-header" v-if="!isCollapsed">
          <h3 class="section-title">Groups</h3>
          <button class="button is-ghost is-small" @click="createGroup" title="Create Group">
            <span class="icon">
              <i class="fas fa-plus"></i>
            </span>
          </button>
        </div>
        <!-- Collapsed state create button -->
        <div v-if="isCollapsed" class="collapsed-create-button">
          <button class="button is-ghost is-small" @click="createGroup" title="Create Group">
            <span class="icon">
              <i class="fas fa-plus"></i>
            </span>
          </button>
        </div>
        <div v-if="!isCollapsed" class="groups-container">
          <div v-if="groups.length === 0" class="empty-state">
            <p class="has-text-grey">No groups yet</p>
          </div>
          <ul v-else class="sidebar-menu">
            <li v-for="group in groups" :key="group.id" class="menu-item">
              <router-link :to="`/groups/${group.id}`" class="menu-link"
                :class="{ 'is-active': activeSection === 'groups' && route.params.id === group.id }"
                :title="isCollapsed ? group.name : ''">
                <span class="icon">
                  <i class="fas fa-users"></i>
                </span>
                <span v-if="!isCollapsed" class="menu-text">{{ group.name }}</span>
                <span v-if="!isCollapsed" class="menu-count">{{ group.memberCount }}</span>
              </router-link>
            </li>
          </ul>
        </div>
      </div>

      <!-- Tags Section -->
      <div class="sidebar-section tags-section">
        <div class="sidebar-section-header" v-if="!isCollapsed">
          <h3 class="section-title">
            Tags
            <span v-if="hasActiveTagFilter" class="filter-indicator">
              ({{ selectedTags.length }})
            </span>
          </h3>
          <div class="header-actions">
            <button v-if="hasActiveTagFilter" class="button is-ghost is-small" @click="clearTagFilter"
              title="Clear tag filter">
              <span class="icon">
                <i class="fas fa-times"></i>
              </span>
            </button>
            <button class="button is-ghost is-small" @click="manageTags" title="Tag settings">
              <span class="icon">
                <i class="fas fa-cog"></i>
              </span>
            </button>
          </div>
        </div>

        <!-- Collapsed state cog button -->
        <div v-if="isCollapsed" class="collapsed-create-button">
          <button class="button is-ghost is-small" @click="manageTags" title="Tag Settings">
            <span class="icon">
              <i class="fas fa-cog"></i>
            </span>
          </button>
        </div>

        <div v-if="!isCollapsed" class="tags-container">
          <div v-if="availableTags.length === 0" class="empty-state">
            <p class="has-text-grey">No tags yet</p>
            <button class="button is-small is-text" @click="manageTags">
              Create your first tag
            </button>
          </div>
          <div v-else class="tags-wrapper">
            <!-- Filter mode toggle -->
            <div v-if="selectedTags.length > 1" class="filter-mode">
              <div class="field has-addons">
                <div class="control">
                  <button class="button is-small" :class="{ 'is-primary': tagsStore?.filterMode === 'any' }"
                    @click="tagsStore?.setFilterMode('any')" title="Show notes with ANY of the selected tags">
                    ANY
                  </button>
                </div>
                <div class="control">
                  <button class="button is-small" :class="{ 'is-primary': tagsStore?.filterMode === 'all' }"
                    @click="tagsStore?.setFilterMode('all')" title="Show notes with ALL of the selected tags">
                    ALL
                  </button>
                </div>
              </div>
            </div>

            <!-- Tags list -->
            <div class="tags">
              <span v-for="tag in availableTags" :key="tag.name" class="tag is-clickable" :class="{
                'is-primary': selectedTags.includes(tag.name),
                'is-predefined': tag.isPredefined
              }" :style="{
                '--tag-color': tag.color,
                borderColor: selectedTags.includes(tag.name) ? tag.color : undefined,
                backgroundColor: selectedTags.includes(tag.name) ? tag.color : undefined
              }" @click="toggleTag(tag.name)" :title="`${tag.name} (${tag.count} notes)`">
                <span class="icon" v-if="tag.icon">
                  <i :class="tag.icon"></i>
                </span>
                <span class="tag-name">{{ tag.name }}</span>
                <span class="tag-count">{{ tag.count }}</span>
              </span>
            </div>
          </div>
        </div>
      </div>


    </div>

    <!-- Tag Settings Modal -->
    <TagSettingsModal :is-open="showTagSettings" @close="closeTagSettings" />
  </div>
</template>

<script setup lang="ts">
// Components
import { ref, computed, onMounted, onUnmounted, getCurrentInstance, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useGroupsStore } from '../../stores/groups'
import { useTagsStore } from '../../stores/tags'
import TagSettingsModal from '../tags/TagSettingsModal.vue'

// Props
interface Props {
  isCollapsed: boolean
  currentSection?: string // Current active section from parent
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'toggle-collapse': []
  'close-mobile': []
  'create-note': []
  'open-settings': []
  'create-group': []
  'show-dashboard': []
  'filter-by-tags': [{ selectedTags: string[], filterMode: 'any' | 'all' }]
  'update-tags': [string[]] // Emit when navigation changes to update tag list
  'section-change': [string] // Emit when section changes (dashboard, recent, favorites, etc.)
}>()

// Composables with error handling
let router: any = null
let route: any = null
let authStore: any = null

// Initialize composables safely
const initializeComposables = () => {
  try {
    if (!router) {
      router = useRouter()
      console.log('Router initialized in Sidebar:', !!router)
    }
    if (!route) route = useRoute()
    if (!authStore) {
      authStore = useAuthStore()
      console.log('Auth store initialized in Sidebar:', !!authStore)
    }
    return true
  } catch (error) {
    console.warn('Composables initialization error in Sidebar:', error)
    return false
  }
}

// Try initial initialization - defer until component is mounted
// This ensures Vue Router is properly initialized by the time we try to use it
const isRouterAvailable = ref(false)

// Check if we're in a context where Vue Router should be available
const checkRouterAvailability = () => {
  try {
    // Try to access the router through the app context
    const app = getCurrentInstance()?.appContext
    if (app && app.config.globalProperties.$router) {
      router = app.config.globalProperties.$router
      isRouterAvailable.value = true
      return true
    }

    // Try direct initialization
    if (initializeComposables()) {
      isRouterAvailable.value = true
      return true
    }

    return false
  } catch (error) {
    console.warn('Router availability check failed:', error)
    return false
  }
}

// Create a reactive router proxy that handles both scenarios
const routerProxy = {
  push: (path: string) => {
    if (router && typeof router.push === 'function') {
      return router.push(path)
    } else {
      console.warn('Router not available, using fallback navigation to:', path)
      window.location.href = path
    }
  },
  // Add other router methods if needed
}

// Use the proxy for programmatic navigation
router = routerProxy

// Initialize groups store with error handling
let groupsStore: any = null
try {
  groupsStore = useGroupsStore()
} catch (error) {
  console.warn('Groups store initialization failed:', error)
  groupsStore = {
    groups: ref([]),
    loadGroups: () => Promise.resolve()
  }
}

// Initialize tags store with error handling
let tagsStore: any = null
try {
  tagsStore = useTagsStore()
} catch (error) {
  console.warn('Tags store initialization failed:', error)
  tagsStore = {
    availableTags: ref([]),
    selectedTags: ref([]),
    hasActiveFilter: ref(false),
    initialize: () => Promise.resolve(),
    toggleTag: () => { },
    clearSelectedTags: () => { }
  }
}

// Reactive state
const showUserDropdown = ref(false)
const showTagSettings = ref(false)

// Computed active section based on parent prop or current route
const activeSection = computed(() => {
  // Use parent prop if available (prevents sidebar reloading)
  if (props.currentSection) {
    return props.currentSection
  }
  
  // Fallback to route-based detection
  try {
    const routeName = route?.name as string || ''
    const routePath = route?.path || window.location.pathname

    if (routePath.includes('/admin/users')) return 'admin-users'
    if (routePath.includes('/admin')) return 'admin-dashboard'
    if (routePath.includes('/shared')) return 'shared'
    if (routePath.includes('/groups')) return 'groups'
    if (routePath.includes('/settings')) return 'settings'
    if (routePath.includes('/search')) return 'search'
    if (routePath.includes('/recent')) return 'recent'
    if (routePath.includes('/favorites')) return 'favorites'
    if (routePath.includes('/archived')) return 'archived'
    if (routePath.includes('/notes')) return 'all-notes'
    if (routeName === 'Dashboard' || routePath === '/dashboard') return 'dashboard'

    return 'dashboard'
  } catch (error) {
    console.warn('Error computing active section:', error)
    return 'dashboard'
  }
})

// Computed data from stores
const noteCount = ref(42) // This would come from notes store
const groups = computed(() => {
  try {
    return groupsStore?.groups?.value || groupsStore?.groups || []
  } catch (error) {
    console.warn('Error accessing groups:', error)
    return []
  }
})

// Tags computed properties
const availableTags = computed(() => {
  try {
    return tagsStore?.availableTags?.value || tagsStore?.availableTags || []
  } catch (error) {
    console.warn('Error accessing available tags:', error)
    return []
  }
})

const selectedTags = computed(() => {
  try {
    return tagsStore?.selectedTags?.value || tagsStore.selectedTags || []
  } catch (error) {
    console.warn('Error accessing selected tags:', error)
    return []
  }
})

const hasActiveTagFilter = computed(() => {
  try {
    return tagsStore?.hasActiveFilter?.value || tagsStore?.hasActiveFilter || false
  } catch (error) {
    console.warn('Error accessing tag filter state:', error)
    return false
  }
})

// Methods

const toggleTag = (tagName: string) => {
  try {
    if (tagsStore && typeof tagsStore.toggleTag === 'function') {
      tagsStore.toggleTag(tagName)
      // Emit event to parent to apply filter to current navigation
      // Use .value to get the actual array values, not reactive references
      emit('filter-by-tags', {
        selectedTags: [...(tagsStore.selectedTags?.value || tagsStore.selectedTags || [])],
        filterMode: tagsStore.filterMode?.value || tagsStore.filterMode || 'any'
      })
    }
  } catch (error) {
    console.warn('Error toggling tag:', error)
  }
}

const createNote = () => {
  // Emit create note event to parent
  emit('create-note')
}

const createGroup = () => {
  // Emit create group event to parent (AppLayout)
  emit('create-group')
}

const manageTags = () => {
  showTagSettings.value = true
}

const closeTagSettings = () => {
  showTagSettings.value = false
}

const clearTagFilter = () => {
  try {
    if (tagsStore && typeof tagsStore.clearSelectedTags === 'function') {
      tagsStore.clearSelectedTags()
      // Emit event to parent to clear filter
      emit('filter-by-tags', {
        selectedTags: [],
        filterMode: tagsStore.filterMode?.value || tagsStore.filterMode || 'any'
      })
    }
  } catch (error) {
    console.warn('Error clearing tag filter:', error)
  }
}

const openSettings = () => {
  showUserDropdown.value = false
  // Emit settings event to parent
  emit('open-settings')
}

const handleLogout = async () => {
  showUserDropdown.value = false

  // Ensure auth store is properly initialized
  if (!authStore || typeof authStore.logout !== 'function') {
    console.error('Auth store not properly initialized, attempting to initialize...')
    initializeComposables()

    // If still not available, use fallback
    if (!authStore || typeof authStore.logout !== 'function') {
      console.error('Auth store initialization failed, using fallback logout')
      // Clear all auth data manually
      localStorage.removeItem('auth_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('cached_user')
      sessionStorage.removeItem('logout_in_progress')
      // Force navigation to login
      window.location.href = '/login'
      return
    }
  }

  try {
    console.log('Starting logout process...')
    console.log('Auth state before logout:', {
      token: !!authStore.token,
      user: !!authStore.user,
      isAuthenticated: authStore.isAuthenticated,
      isLoggingOut: authStore.isLoggingOut
    })

    await authStore.logout()
    console.log('Auth store logout completed')

    console.log('Auth state after logout:', {
      token: !!authStore.token,
      user: !!authStore.user,
      isAuthenticated: authStore.isAuthenticated,
      isLoggingOut: authStore.isLoggingOut
    })

    // Use nextTick to ensure reactive updates are processed
    await nextTick()

    // Double-check that auth state is cleared
    if (authStore.token || authStore.user || authStore.isAuthenticated) {
      console.warn('Auth state not properly cleared, forcing cleanup...')
      authStore.forceClearAuth()
    }

    // Clear any remaining auth state
    sessionStorage.removeItem('logout_in_progress')

    // Try router navigation first
    console.log('Redirecting to login...')
    try {
      await router.push('/login')
      console.log('Router navigation successful')
    } catch (routerError) {
      console.warn('Router navigation failed, using window.location:', routerError)
      // Force navigation to login page
      window.location.href = '/login'
    }
  } catch (error) {
    console.error('Logout error:', error)
    // Force navigation even if logout fails
    if (authStore && typeof authStore.forceClearAuth === 'function') {
      authStore.forceClearAuth()
    }
    sessionStorage.removeItem('logout_in_progress')
    window.location.href = '/login'
  }
}



const handleDashboardClick = () => {
  console.log('Dashboard click handler called')

  // Emit event to show dashboard in editor panel
  emit('show-dashboard')

  // Force navigation to dashboard and ensure it's the main dashboard
  try {
    // Ensure auth store is properly initialized before navigation
    if (!authStore.isInitialized && authStore.token) {
      console.log('Initializing auth before dashboard navigation...')
      authStore.initializeAuth()
    }

    if (router && router.push) {
      console.log('Using Vue Router to navigate to /dashboard')
      router.push('/dashboard')
    } else {
      console.log('Using window.location fallback to /dashboard')
      window.location.href = '/dashboard'
    }
  } catch (error) {
    console.error('Navigation error:', error)
    // Fallback: reload the page to dashboard
    window.location.href = '/dashboard'
  }
}

const handleRouterLinkClick = (path: string) => {
  console.log('Router-link clicked, path:', path)
  console.log('Router available:', isRouterAvailable.value)

  // If router is not available, use fallback navigation
  if (!isRouterAvailable.value) {
    console.warn('Router not available, using fallback navigation to:', path)
    window.location.href = path
    return false // Prevent default router-link behavior
  }

  // Allow normal router-link behavior
  return true
}

const handleSectionChange = (section: string) => {
  console.log('Handling section change to:', section)
  
  // Emit event to parent (AppLayout) to handle section change locally
  emit('section-change', section)
  
  // Clear tag filter on section change
  emit('filter-by-tags', { selectedTags: [], filterMode: 'any' })
}

// Load groups on mount and retry composable initialization
onMounted(async () => {
  console.log('Sidebar mounted, checking router availability...')

  // Check if router is available now that the app is mounted
  if (checkRouterAvailability()) {
    console.log('Router is available after mount')
    isRouterAvailable.value = true
  } else {
    console.warn('Router still not available after mount, using fallback navigation')
  }

  // Initialize composables if not already done
  if (!authStore || !router) {
    console.log('Initializing composables in Sidebar...')
    initializeComposables()
  }

  // Add global event listeners for auth events
  window.addEventListener('user-logged-out', handleGlobalLogout)
  window.addEventListener('auth-logout-complete', handleAuthLogoutComplete)
  window.addEventListener('auth-expired', handleAuthExpired)

  // Add click outside handler for dropdown
  document.addEventListener('click', handleClickOutside)

  try {
    // Only load groups if user is authenticated
    if (authStore?.isAuthenticated && groupsStore && typeof groupsStore.loadGroups === 'function') {
      await groupsStore.loadGroups()
    } else if (!authStore?.isAuthenticated) {
      console.log('User not authenticated, skipping groups load')
    }
  } catch (error) {
    console.error('Failed to load groups in sidebar:', error)
  }

  try {
    // Initialize tags store
    if (tagsStore && typeof tagsStore.initialize === 'function') {
      await tagsStore.initialize()
    }
  } catch (error) {
    console.error('Failed to initialize tags in sidebar:', error)
  }

  // Also initialize notes store to get tag counts
  try {
    if (authStore?.isAuthenticated) {
      const notesStore = (await import('../../stores/notes')).useNotesStore()
      if (notesStore && typeof notesStore.initialize === 'function') {
        await notesStore.initialize()
      }
    }
  } catch (error) {
    console.error('Failed to initialize notes store for tag counts:', error)
  }
})

// Event handler functions
const handleGlobalLogout = () => {
  console.log('Global logout event received, clearing auth state...')
  if (authStore && typeof authStore.forceClearAuth === 'function') {
    authStore.forceClearAuth()
  }
  sessionStorage.removeItem('logout_in_progress')
}

const handleAuthLogoutComplete = () => {
  console.log('Global auth logout complete event received...')
  sessionStorage.removeItem('logout_in_progress')
}

const handleAuthExpired = (event: CustomEvent) => {
  console.log('Auth expired event received:', event.detail)
  // Clear groups data since user is no longer authenticated
  if (groupsStore && typeof groupsStore.reset === 'function') {
    groupsStore.reset()
  }
  console.log('Groups cleared due to auth expiration')
}

const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.dropdown')) {
    showUserDropdown.value = false
  }
}

onUnmounted(() => {
  // Clean up event listeners
  window.removeEventListener('user-logged-out', handleGlobalLogout)
  window.removeEventListener('auth-logout-complete', handleAuthLogoutComplete)
  window.removeEventListener('auth-expired', handleAuthExpired)
  document.removeEventListener('click', handleClickOutside)
})
</script>
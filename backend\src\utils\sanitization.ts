import crypto from 'crypto';

/**
 * Enhanced HTML sanitization utility
 */
export class SanitizationUtils {
  // Allowed HTML tags for rich text content
  private static readonly ALLOWED_TAGS = [
    'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li', 'blockquote', 'code', 'pre', 'a', 'img', 'table',
    'thead', 'tbody', 'tr', 'th', 'td', 'div', 'span'
  ];

  // Allowed attributes for specific tags
  private static readonly ALLOWED_ATTRIBUTES: { [key: string]: string[] } = {
    'a': ['href', 'title', 'target'],
    'img': ['src', 'alt', 'title', 'width', 'height'],
    'table': ['class'],
    'th': ['colspan', 'rowspan'],
    'td': ['colspan', 'rowspan'],
    'div': ['class'],
    'span': ['class']
  };

  // Dangerous patterns to remove
  private static readonly DANGEROUS_PATTERNS = [
    // Script tags and javascript
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
    /<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi,
    /<input\b[^>]*>/gi,
    /<textarea\b[^<]*(?:(?!<\/textarea>)<[^<]*)*<\/textarea>/gi,
    /<select\b[^<]*(?:(?!<\/select>)<[^<]*)*<\/select>/gi,
    /<button\b[^<]*(?:(?!<\/button>)<[^<]*)*<\/button>/gi,
    
    // Event handlers
    /on\w+\s*=\s*["'][^"']*["']/gi,
    
    // JavaScript and data URLs
    /javascript:/gi,
    /data:(?!image\/)/gi,
    /vbscript:/gi,
    
    // Meta and link tags
    /<meta\b[^>]*>/gi,
    /<link\b[^>]*>/gi,
    /<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi,
    
    // Dangerous attributes
    /\s(style|class)\s*=\s*["'][^"']*["']/gi
  ];

  /**
   * Sanitize HTML content for rich text notes
   */
  static sanitizeHtml(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    let sanitized = input;

    // Remove dangerous patterns
    this.DANGEROUS_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });

    // Remove null bytes and control characters
    sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

    // Normalize whitespace
    sanitized = sanitized.replace(/\s+/g, ' ').trim();

    return sanitized;
  }

  /**
   * Sanitize plain text input
   */
  static sanitizeText(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    // Remove HTML tags
    let sanitized = input.replace(/<[^>]*>/g, '');
    
    // Remove null bytes and control characters
    sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
    
    // Normalize whitespace
    sanitized = sanitized.replace(/\s+/g, ' ').trim();
    
    return sanitized;
  }

  /**
   * Sanitize SQL input to prevent injection
   */
  static sanitizeSql(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    // Remove SQL injection patterns
    const sqlPatterns = [
      /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)/gi,
      /(--|\/\*|\*\/|;)/g,
      /(\b(or|and)\s+\d+\s*=\s*\d+)/gi,
      /(\b(or|and)\s+['"].*['"])/gi
    ];

    let sanitized = input;
    sqlPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });

    return sanitized.trim();
  }

  /**
   * Sanitize file names
   */
  static sanitizeFileName(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    // Remove dangerous characters
    let sanitized = input.replace(/[<>:"/\\|?*\x00-\x1f]/g, '');
    
    // Remove leading/trailing dots and spaces
    sanitized = sanitized.replace(/^[\s.]+|[\s.]+$/g, '');
    
    // Limit length
    if (sanitized.length > 255) {
      sanitized = sanitized.substring(0, 255);
    }
    
    // Ensure it's not empty
    if (sanitized.length === 0) {
      sanitized = 'untitled';
    }
    
    return sanitized;
  }

  /**
   * Sanitize URL input
   */
  static sanitizeUrl(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    // Only allow http and https protocols
    const urlPattern = /^https?:\/\/[^\s<>"{}|\\^`[\]]+$/i;
    
    if (!urlPattern.test(input)) {
      return '';
    }

    return input.trim();
  }

  /**
   * Sanitize email input
   */
  static sanitizeEmail(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    // Basic email pattern
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    
    const sanitized = input.trim().toLowerCase();
    
    if (!emailPattern.test(sanitized)) {
      return '';
    }

    return sanitized;
  }

  /**
   * Generate a secure random token
   */
  static generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Hash sensitive data
   */
  static hashSensitiveData(data: string, salt?: string): string {
    const actualSalt = salt || crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512');
    return `${actualSalt}:${hash.toString('hex')}`;
  }

  /**
   * Verify hashed sensitive data
   */
  static verifySensitiveData(data: string, hashedData: string): boolean {
    try {
      const [salt, hash] = hashedData.split(':');
      const verifyHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512');
      return hash === verifyHash.toString('hex');
    } catch (error) {
      return false;
    }
  }

  /**
   * Sanitize JSON input
   */
  static sanitizeJson(input: any): any {
    if (typeof input === 'string') {
      return this.sanitizeText(input);
    }
    
    if (Array.isArray(input)) {
      return input.map(item => this.sanitizeJson(item));
    }
    
    if (typeof input === 'object' && input !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(input)) {
        const sanitizedKey = this.sanitizeText(key);
        if (sanitizedKey) {
          sanitized[sanitizedKey] = this.sanitizeJson(value);
        }
      }
      return sanitized;
    }
    
    return input;
  }

  /**
   * Rate limiting key sanitization
   */
  static sanitizeRateLimitKey(input: string): string {
    if (typeof input !== 'string') {
      return 'unknown';
    }

    // Remove special characters that could cause issues
    return input.replace(/[^a-zA-Z0-9._:-]/g, '').substring(0, 100);
  }

  /**
   * Sanitize search query
   */
  static sanitizeSearchQuery(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    // Remove special regex characters that could cause issues
    let sanitized = input.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    
    // Remove excessive whitespace
    sanitized = sanitized.replace(/\s+/g, ' ').trim();
    
    // Limit length
    if (sanitized.length > 200) {
      sanitized = sanitized.substring(0, 200);
    }
    
    return sanitized;
  }

  /**
   * Sanitize tag input
   */
  static sanitizeTag(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    // Remove special characters and normalize
    let sanitized = input.replace(/[<>'"&]/g, '').trim().toLowerCase();
    
    // Replace spaces with hyphens
    sanitized = sanitized.replace(/\s+/g, '-');
    
    // Remove multiple consecutive hyphens
    sanitized = sanitized.replace(/-+/g, '-');
    
    // Remove leading/trailing hyphens
    sanitized = sanitized.replace(/^-+|-+$/g, '');
    
    // Limit length
    if (sanitized.length > 50) {
      sanitized = sanitized.substring(0, 50);
    }
    
    return sanitized;
  }
}

// Export convenience functions
export const sanitizeHtml = SanitizationUtils.sanitizeHtml;
export const sanitizeText = SanitizationUtils.sanitizeText;
export const sanitizeSql = SanitizationUtils.sanitizeSql;
export const sanitizeFileName = SanitizationUtils.sanitizeFileName;
export const sanitizeUrl = SanitizationUtils.sanitizeUrl;
export const sanitizeEmail = SanitizationUtils.sanitizeEmail;
export const sanitizeJson = SanitizationUtils.sanitizeJson;
export const sanitizeSearchQuery = SanitizationUtils.sanitizeSearchQuery;
export const sanitizeTag = SanitizationUtils.sanitizeTag;
import { AuditLogRepository } from '../repositories/AuditLogRepository';
import { AuditLog } from '../models/AuditLog';

export interface LogRetentionPolicy {
  retentionDays: number;
  archiveBeforeDelete?: boolean;
  archivePath?: string;
}

export interface SecurityAlert {
  type: 'MULTIPLE_FAILED_LOGINS' | 'SUSPICIOUS_IP' | 'UNUSUAL_ACTIVITY' | 'RATE_LIMIT_ABUSE';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  message: string;
  metadata: Record<string, any>;
  timestamp: Date;
}

export class AuditLogService {
  private static readonly DEFAULT_RETENTION_DAYS = 365; // 1 year
  private static readonly SECURITY_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private static securityCheckTimer?: NodeJS.Timeout;

  /**
   * Initialize the audit log service with automatic cleanup and monitoring
   */
  static initialize(retentionPolicy?: LogRetentionPolicy): void {
    console.log('Initializing Audit Log Service...');
    
    // Start periodic cleanup
    this.startPeriodicCleanup(retentionPolicy);
    
    // Start security monitoring
    this.startSecurityMonitoring();
    
    console.log('Audit Log Service initialized successfully');
  }

  /**
   * Start periodic cleanup of old audit logs
   */
  private static startPeriodicCleanup(policy?: LogRetentionPolicy): void {
    const retentionDays = policy?.retentionDays || this.DEFAULT_RETENTION_DAYS;
    
    // Run cleanup daily at 2 AM
    const cleanupInterval = 24 * 60 * 60 * 1000; // 24 hours
    
    const runCleanup = async () => {
      try {
        console.log(`Running audit log cleanup (retention: ${retentionDays} days)...`);
        const deletedCount = await this.cleanupOldLogs(retentionDays);
        console.log(`Cleaned up ${deletedCount} old audit log entries`);
      } catch (error) {
        console.error('Error during audit log cleanup:', error);
      }
    };

    // Run initial cleanup after 1 minute
    setTimeout(runCleanup, 60 * 1000);
    
    // Then run daily
    setInterval(runCleanup, cleanupInterval);
  }

  /**
   * Start security monitoring for suspicious activities
   */
  private static startSecurityMonitoring(): void {
    const runSecurityCheck = async () => {
      try {
        const alerts = await this.checkForSecurityThreats();
        
        if (alerts.length > 0) {
          console.warn(`Security alerts detected: ${alerts.length} alerts`);
          
          // Log critical alerts
          alerts.forEach(alert => {
            if (alert.severity === 'CRITICAL' || alert.severity === 'HIGH') {
              console.error(`SECURITY ALERT [${alert.severity}]: ${alert.message}`, alert.metadata);
            }
          });
          
          // TODO: Send alerts to monitoring system or admin notifications
          // await this.sendSecurityAlerts(alerts);
        }
      } catch (error) {
        console.error('Error during security monitoring:', error);
      }
    };

    // Run security checks every 5 minutes
    this.securityCheckTimer = setInterval(runSecurityCheck, this.SECURITY_CHECK_INTERVAL);
  }

  /**
   * Stop the audit log service
   */
  static shutdown(): void {
    if (this.securityCheckTimer) {
      clearInterval(this.securityCheckTimer);
      this.securityCheckTimer = undefined;
    }
    console.log('Audit Log Service shutdown complete');
  }

  /**
   * Clean up old audit logs based on retention policy
   */
  static async cleanupOldLogs(retentionDays: number): Promise<number> {
    try {
      const deletedCount = await AuditLogRepository.deleteOldLogs(retentionDays);
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up old audit logs:', error);
      throw error;
    }
  }

  /**
   * Check for security threats and generate alerts
   */
  static async checkForSecurityThreats(): Promise<SecurityAlert[]> {
    const alerts: SecurityAlert[] = [];
    const now = new Date();

    try {
      // Check for multiple failed login attempts from same IP
      const failedAttempts = await AuditLogRepository.findFailedAttempts(1); // Last hour
      const failedLoginsByIp = new Map<string, AuditLog[]>();
      
      failedAttempts.forEach(attempt => {
        if (attempt.action === 'AUTH_LOGIN' && attempt.response_status === 401) {
          const ip = attempt.ip_address;
          if (!failedLoginsByIp.has(ip)) {
            failedLoginsByIp.set(ip, []);
          }
          failedLoginsByIp.get(ip)!.push(attempt);
        }
      });

      // Alert on multiple failed logins from same IP
      failedLoginsByIp.forEach((attempts, ip) => {
        if (attempts.length >= 5) {
          alerts.push({
            type: 'MULTIPLE_FAILED_LOGINS',
            severity: attempts.length >= 10 ? 'CRITICAL' : 'HIGH',
            message: `Multiple failed login attempts detected from IP ${ip}`,
            metadata: {
              ip_address: ip,
              attempt_count: attempts.length,
              time_window: '1 hour',
              latest_attempt: attempts[0].created_at
            },
            timestamp: now
          });
        }
      });

      // Check for rate limit abuse
      const rateLimitViolations = failedAttempts.filter(
        attempt => attempt.response_status === 429
      );
      
      const rateLimitByIp = new Map<string, number>();
      rateLimitViolations.forEach(violation => {
        const ip = violation.ip_address;
        rateLimitByIp.set(ip, (rateLimitByIp.get(ip) || 0) + 1);
      });

      rateLimitByIp.forEach((count, ip) => {
        if (count >= 10) {
          alerts.push({
            type: 'RATE_LIMIT_ABUSE',
            severity: count >= 50 ? 'CRITICAL' : 'HIGH',
            message: `Excessive rate limit violations from IP ${ip}`,
            metadata: {
              ip_address: ip,
              violation_count: count,
              time_window: '1 hour'
            },
            timestamp: now
          });
        }
      });

      // Check for unusual activity patterns
      const recentLogs = await AuditLogRepository.findByFilter({
        start_date: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        limit: 1000
      });

      // Detect unusual IP addresses for users
      const userIpMap = new Map<string, Set<string>>();
      recentLogs.logs.forEach(log => {
        if (log.user_id) {
          if (!userIpMap.has(log.user_id)) {
            userIpMap.set(log.user_id, new Set());
          }
          userIpMap.get(log.user_id)!.add(log.ip_address);
        }
      });

      userIpMap.forEach((ips, userId) => {
        if (ips.size >= 5) { // User accessed from 5+ different IPs in 24h
          alerts.push({
            type: 'UNUSUAL_ACTIVITY',
            severity: 'MEDIUM',
            message: `User accessed from multiple IP addresses`,
            metadata: {
              user_id: userId,
              ip_count: ips.size,
              ip_addresses: Array.from(ips),
              time_window: '24 hours'
            },
            timestamp: now
          });
        }
      });

    } catch (error) {
      console.error('Error checking for security threats:', error);
    }

    return alerts;
  }

  /**
   * Get audit log statistics for monitoring dashboard
   */
  static async getMonitoringStats(): Promise<{
    totalLogs: number;
    recentErrors: number;
    securityEvents: number;
    topActions: Array<{ action: string; count: number }>;
    topIps: Array<{ ip: string; count: number }>;
  }> {
    try {
      const [stats, securityEvents, failedAttempts] = await Promise.all([
        AuditLogRepository.getStatistics(7), // Last 7 days
        AuditLogRepository.findSecurityEvents(24), // Last 24 hours
        AuditLogRepository.findFailedAttempts(24) // Last 24 hours
      ]);

      return {
        totalLogs: stats.totalLogs,
        recentErrors: failedAttempts.length,
        securityEvents: securityEvents.length,
        topActions: stats.topActions.slice(0, 5),
        topIps: stats.topIpAddresses.slice(0, 5).map(item => ({
          ip: item.ip_address,
          count: item.count
        }))
      };
    } catch (error) {
      console.error('Error getting monitoring stats:', error);
      throw error;
    }
  }

  /**
   * Generate compliance report for audit purposes
   */
  static async generateComplianceReport(
    startDate: Date,
    endDate: Date,
    userId?: string
  ): Promise<{
    reportId: string;
    generatedAt: Date;
    period: { start: Date; end: Date };
    summary: {
      totalLogs: number;
      uniqueUsers: number;
      securityEvents: number;
      errorRate: number;
    };
    details: {
      topActions: Array<{ action: string; count: number }>;
      securityEvents: AuditLog[];
      errorSummary: Array<{ status: number; count: number }>;
    };
  }> {
    try {
      const reportId = `audit-report-${Date.now()}`;
      
      const filter = {
        start_date: startDate,
        end_date: endDate,
        user_id: userId,
        limit: 10000
      };

      const [logs, securityEvents] = await Promise.all([
        AuditLogRepository.findByFilter(filter),
        AuditLogRepository.findSecurityEvents(
          Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60))
        )
      ]);

      // Calculate statistics
      const uniqueUsers = new Set(
        logs.logs.filter(log => log.user_id).map(log => log.user_id)
      ).size;

      const errorLogs = logs.logs.filter(log => log.response_status >= 400);
      const errorRate = logs.logs.length > 0 ? (errorLogs.length / logs.logs.length) * 100 : 0;

      // Group actions
      const actionCounts = new Map<string, number>();
      logs.logs.forEach(log => {
        actionCounts.set(log.action, (actionCounts.get(log.action) || 0) + 1);
      });

      const topActions = Array.from(actionCounts.entries())
        .map(([action, count]) => ({ action, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // Group errors by status code
      const errorCounts = new Map<number, number>();
      errorLogs.forEach(log => {
        errorCounts.set(log.response_status, (errorCounts.get(log.response_status) || 0) + 1);
      });

      const errorSummary = Array.from(errorCounts.entries())
        .map(([status, count]) => ({ status, count }))
        .sort((a, b) => b.count - a.count);

      return {
        reportId,
        generatedAt: new Date(),
        period: { start: startDate, end: endDate },
        summary: {
          totalLogs: logs.total,
          uniqueUsers,
          securityEvents: securityEvents.length,
          errorRate: Math.round(errorRate * 100) / 100
        },
        details: {
          topActions,
          securityEvents: securityEvents.slice(0, 100), // Limit to 100 most recent
          errorSummary
        }
      };
    } catch (error) {
      console.error('Error generating compliance report:', error);
      throw error;
    }
  }
}
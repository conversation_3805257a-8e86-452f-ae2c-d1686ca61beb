import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import KeyboardShortcuts from '../../../components/navigation/KeyboardShortcuts.vue'

describe('KeyboardShortcuts', () => {
  let router: any
  let wrapper: any

  beforeEach(() => {
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/dashboard', component: { template: '<div>Dashboard</div>' } }
      ]
    })

    wrapper = mount(KeyboardShortcuts, {
      global: {
        plugins: [router]
      }
    })
  })

  it('renders without visible content', () => {
    expect(wrapper.find('.keyboard-shortcuts-handler').exists()).toBe(true)
    expect(wrapper.find('.keyboard-shortcuts-handler').isVisible()).toBe(false)
  })

  it('emits open-search on Ctrl+F', async () => {
    const event = new KeyboardEvent('keydown', {
      key: 'f',
      ctrlKey: true,
      bubbles: true
    })

    document.dispatchEvent(event)
    
    // Wait for next tick to allow event processing
    await wrapper.vm.$nextTick()
    
    expect(wrapper.emitted('open-search')).toBeTruthy()
  })

  it('emits create-note on Ctrl+N', async () => {
    const event = new KeyboardEvent('keydown', {
      key: 'n',
      ctrlKey: true,
      bubbles: true
    })

    document.dispatchEvent(event)
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.emitted('create-note')).toBeTruthy()
  })

  it('emits save-note on Ctrl+S', async () => {
    const event = new KeyboardEvent('keydown', {
      key: 's',
      ctrlKey: true,
      bubbles: true
    })

    document.dispatchEvent(event)
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.emitted('save-note')).toBeTruthy()
  })

  it('handles Mac Cmd key shortcuts', async () => {
    const event = new KeyboardEvent('keydown', {
      key: 'f',
      metaKey: true, // Cmd key on Mac
      bubbles: true
    })

    document.dispatchEvent(event)
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.emitted('open-search')).toBeTruthy()
  })

  it('handles escape key events', async () => {
    const event = new KeyboardEvent('keydown', {
      key: 'Escape',
      bubbles: true
    })

    document.dispatchEvent(event)
    
    await wrapper.vm.$nextTick()
    
    // Escape handling is internal, so we just verify no errors occur
    expect(wrapper.exists()).toBe(true)
  })

  it('exposes showKeyboardShortcuts method', () => {
    expect(typeof wrapper.vm.showKeyboardShortcuts).toBe('function')
  })
})
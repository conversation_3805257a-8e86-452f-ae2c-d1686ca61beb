<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Header Test</title>
    <style>
        /* Test CSS for mobile header */
        .mobile-header.mobile-only {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 9999 !important;
            background: #ff0000 !important;
            min-height: 3.25rem !important;
            border: 3px solid #00ff00 !important;
        }

        .mobile-header.mobile-only .navbar-burger {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            color: white !important;
            cursor: pointer !important;
            height: 3.25rem !important;
            position: relative !important;
            width: 3.25rem !important;
            margin-left: auto !important;
            background: #0000ff !important;
            border: 2px solid #ffff00 !important;
        }

        .mobile-header.mobile-only .navbar-burger span {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            background: white !important;
            height: 1px !important;
            left: calc(50% - 8px) !important;
            position: absolute !important;
            transform-origin: center !important;
            transition-duration: 86ms !important;
            transition-property: background-color, opacity, transform !important;
            transition-timing-function: ease-out !important;
            width: 16px !important;
        }

        .mobile-header.mobile-only .navbar-burger span:nth-child(1) {
            top: calc(50% - 6px) !important;
        }

        .mobile-header.mobile-only .navbar-burger span:nth-child(2) {
            top: calc(50% - 1px) !important;
        }

        .mobile-header.mobile-only .navbar-burger span:nth-child(3) {
            top: calc(50% + 4px) !important;
        }

        /* Mobile-only utility class */
        .mobile-only {
            display: block !important;
        }

        @media (min-width: 769px) {
            .mobile-only {
                display: none !important;
            }
        }

        /* Test content */
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }

        .test-content {
            padding: 4rem 1rem 1rem;
            text-align: center;
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }

        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <header class="mobile-header mobile-only">
        <nav class="navbar is-primary">
            <div class="navbar-brand">
                <div class="navbar-item is-clickable">
                    <h1 class="title is-5 has-text-white">Notes</h1>
                </div>
                <div class="navbar-burger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <div class="test-content">
        <h1>Mobile Header Test</h1>
        <p>This page tests the mobile header visibility and hamburger button.</p>
        <p>On mobile devices (width &lt; 769px), you should see:</p>
        <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
            <li>A red header bar at the top</li>
            <li>A blue hamburger button with yellow border</li>
            <li>Three white horizontal lines in the hamburger button</li>
        </ul>
        <p>On desktop devices (width ≥ 769px), the header should be hidden.</p>
        
        <div style="margin-top: 2rem;">
            <button class="test-button" onclick="toggleHeader()">Toggle Header</button>
            <button class="test-button" onclick="checkVisibility()">Check Visibility</button>
        </div>

        <div id="debug-info" style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 5px; text-align: left; max-width: 600px; margin-left: auto; margin-right: auto;">
            <h3>Debug Information:</h3>
            <div id="debug-content"></div>
        </div>
    </div>

    <script>
        function toggleHeader() {
            const header = document.querySelector('.mobile-header');
            if (header.style.display === 'none') {
                header.style.display = 'block';
            } else {
                header.style.display = 'none';
            }
            checkVisibility();
        }

        function checkVisibility() {
            const header = document.querySelector('.mobile-header');
            const burger = document.querySelector('.navbar-burger');
            const spans = document.querySelectorAll('.navbar-burger span');
            
            const debugInfo = {
                'Header Display': getComputedStyle(header).display,
                'Header Visibility': getComputedStyle(header).visibility,
                'Header Opacity': getComputedStyle(header).opacity,
                'Header Z-Index': getComputedStyle(header).zIndex,
                'Burger Display': getComputedStyle(burger).display,
                'Burger Visibility': getComputedStyle(burger).visibility,
                'Burger Opacity': getComputedStyle(burger).opacity,
                'Screen Width': window.innerWidth,
                'Is Mobile': window.innerWidth < 769
            };

            const debugContent = document.getElementById('debug-content');
            debugContent.innerHTML = Object.entries(debugInfo)
                .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                .join('<br>');
        }

        // Check visibility on load
        window.addEventListener('load', checkVisibility);
        window.addEventListener('resize', checkVisibility);
    </script>
</body>
</html>

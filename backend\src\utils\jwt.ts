import * as jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';

export interface JWTPayload {
  userId: string;
  email: string;
  jti: string; // JWT ID for token revocation
  iat?: number; // Issued at time
  exp?: number; // Expiration time
  sessionId?: string; // Session identifier
  deviceId?: string; // Device identifier
  ipAddress?: string; // IP address when token was issued
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export interface TokenBlacklistEntry {
  jti: string;
  expiresAt: number;
}

export class JWTUtils {
  private static tokenBlacklist = new Map<string, TokenBlacklistEntry>();
  private static cleanupInterval: NodeJS.Timeout;

  static {
    // Clean up expired blacklisted tokens every hour
    this.cleanupInterval = setInterval(() => {
      this.cleanupBlacklist();
    }, 60 * 60 * 1000);
  }

  private static getSecret(): string {
    const secret = process.env.JWT_SECRET;
    if (!secret || secret.length < 32) {
      throw new Error('JWT_SECRET environment variable must be at least 32 characters long');
    }
    return secret;
  }

  private static getRefreshSecret(): string {
    const secret = process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET;
    if (!secret || secret.length < 32) {
      throw new Error('JWT_REFRESH_SECRET or JWT_SECRET environment variable must be at least 32 characters long');
    }
    return secret + '_refresh';
  }

  private static generateSessionId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  private static generateDeviceId(userAgent?: string, ip?: string): string {
    const data = `${userAgent || 'unknown'}-${ip || 'unknown'}-${Date.now()}`;
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 16);
  }

  static generateTokenPair(
    userId: string, 
    email: string, 
    options?: {
      userAgent?: string;
      ipAddress?: string;
      sessionId?: string;
    }
  ): TokenPair {
    const jti = uuidv4();
    const sessionId = options?.sessionId || this.generateSessionId();
    const deviceId = this.generateDeviceId(options?.userAgent, options?.ipAddress);
    
    const payload: JWTPayload = { 
      userId, 
      email, 
      jti,
      sessionId,
      deviceId,
      ipAddress: options?.ipAddress
    };

    const expiresIn = process.env.JWT_EXPIRES_IN || '15m'; // Shorter access token lifetime
    const refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

    const accessToken = jwt.sign(payload as any, this.getSecret(), {
      expiresIn,
      issuer: 'note-app',
      audience: 'note-app-users',
      algorithm: 'HS256'
    } as jwt.SignOptions);

    const refreshToken = jwt.sign(payload as any, this.getRefreshSecret(), {
      expiresIn: refreshExpiresIn,
      issuer: 'note-app',
      audience: 'note-app-users',
      algorithm: 'HS256'
    } as jwt.SignOptions);

    // Convert expires in to seconds
    const expiresInSeconds = this.parseExpiresIn(expiresIn);

    return { 
      accessToken, 
      refreshToken, 
      expiresIn: expiresInSeconds,
      tokenType: 'Bearer'
    };
  }

  private static parseExpiresIn(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) return 900; // Default 15 minutes
    
    const value = parseInt(match[1]);
    const unit = match[2];
    
    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 24 * 60 * 60;
      default: return 900;
    }
  }

  static verifyAccessToken(token: string, options?: { ipAddress?: string }): JWTPayload {
    try {
      const payload = jwt.verify(token, this.getSecret(), {
        issuer: 'note-app',
        audience: 'note-app-users',
        algorithms: ['HS256']
      }) as JWTPayload;

      // Check if token is blacklisted
      if (this.isTokenBlacklisted(payload.jti)) {
        throw new Error('Token has been revoked');
      }

      // Optional IP address validation
      if (options?.ipAddress && payload.ipAddress && payload.ipAddress !== options.ipAddress) {
        // Log suspicious activity but don't reject (IP can change legitimately)
        console.warn(`Token IP mismatch: expected ${payload.ipAddress}, got ${options.ipAddress}`);
      }

      return payload;
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid access token');
      } else if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Access token expired');
      } else {
        throw error;
      }
    }
  }

  static verifyRefreshToken(token: string, options?: { ipAddress?: string }): JWTPayload {
    try {
      const payload = jwt.verify(token, this.getRefreshSecret(), {
        issuer: 'note-app',
        audience: 'note-app-users',
        algorithms: ['HS256']
      }) as JWTPayload;

      // Check if token is blacklisted
      if (this.isTokenBlacklisted(payload.jti)) {
        throw new Error('Token has been revoked');
      }

      // Optional IP address validation
      if (options?.ipAddress && payload.ipAddress && payload.ipAddress !== options.ipAddress) {
        console.warn(`Refresh token IP mismatch: expected ${payload.ipAddress}, got ${options.ipAddress}`);
      }

      return payload;
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid refresh token');
      } else if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Refresh token expired');
      } else {
        throw error;
      }
    }
  }

  static generatePasswordResetToken(userId: string, email: string): string {
    const payload = { userId, email, type: 'password_reset' };
    return jwt.sign(payload as any, this.getSecret(), {
      expiresIn: '1h',
      issuer: 'note-app',
      audience: 'note-app-users'
    } as jwt.SignOptions);
  }

  static verifyPasswordResetToken(token: string): { userId: string; email: string } {
    try {
      const payload = jwt.verify(token, this.getSecret(), {
        issuer: 'note-app',
        audience: 'note-app-users'
      }) as any;

      if (payload.type !== 'password_reset') {
        throw new Error('Invalid token type');
      }

      return { userId: payload.userId, email: payload.email };
    } catch (error) {
      throw new Error('Invalid or expired password reset token');
    }
  }

  static generateEmailVerificationToken(userId: string, email: string): string {
    const payload = { userId, email, type: 'email_verification' };
    return jwt.sign(payload as any, this.getSecret(), {
      expiresIn: '24h',
      issuer: 'note-app',
      audience: 'note-app-users'
    } as jwt.SignOptions);
  }

  static verifyEmailVerificationToken(token: string): { userId: string; email: string } {
    try {
      const payload = jwt.verify(token, this.getSecret(), {
        issuer: 'note-app',
        audience: 'note-app-users',
        algorithms: ['HS256']
      }) as any;

      if (payload.type !== 'email_verification') {
        throw new Error('Invalid token type');
      }

      return { userId: payload.userId, email: payload.email };
    } catch (error) {
      throw new Error('Invalid or expired email verification token');
    }
  }

  // Token blacklist management
  static blacklistToken(jti: string, expiresAt?: number): void {
    const expiry = expiresAt || (Date.now() + (24 * 60 * 60 * 1000)); // Default 24 hours
    this.tokenBlacklist.set(jti, { jti, expiresAt: expiry });
  }

  static isTokenBlacklisted(jti: string): boolean {
    const entry = this.tokenBlacklist.get(jti);
    if (!entry) return false;
    
    if (entry.expiresAt < Date.now()) {
      this.tokenBlacklist.delete(jti);
      return false;
    }
    
    return true;
  }

  static revokeToken(token: string): void {
    try {
      const payload = jwt.decode(token) as JWTPayload;
      if (payload && payload.jti) {
        this.blacklistToken(payload.jti, payload.exp ? payload.exp * 1000 : undefined);
      }
    } catch (error) {
      // Token is invalid, but we'll add it to blacklist anyway for safety
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
      this.blacklistToken(tokenHash);
    }
  }

  static revokeAllUserTokens(userId: string): void {
    // In a production environment, this would query a database
    // For now, we'll just log the action
    console.log(`Revoking all tokens for user: ${userId}`);
  }

  private static cleanupBlacklist(): void {
    const now = Date.now();
    for (const [jti, entry] of this.tokenBlacklist.entries()) {
      if (entry.expiresAt < now) {
        this.tokenBlacklist.delete(jti);
      }
    }
  }

  // Session management
  static generateSessionToken(userId: string, sessionId: string): string {
    const payload = {
      userId,
      sessionId,
      type: 'session',
      iat: Math.floor(Date.now() / 1000)
    };

    return jwt.sign(payload, this.getSecret(), {
      expiresIn: '24h',
      issuer: 'note-app',
      audience: 'note-app-users',
      algorithm: 'HS256'
    });
  }

  static verifySessionToken(token: string): { userId: string; sessionId: string } {
    try {
      const payload = jwt.verify(token, this.getSecret(), {
        issuer: 'note-app',
        audience: 'note-app-users',
        algorithms: ['HS256']
      }) as any;

      if (payload.type !== 'session') {
        throw new Error('Invalid token type');
      }

      return { userId: payload.userId, sessionId: payload.sessionId };
    } catch (error) {
      throw new Error('Invalid or expired session token');
    }
  }

  // Cleanup on shutdown
  static cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
}
import { Request, Response, NextFunction } from 'express';
import { AuditLogRepository } from '../repositories/AuditLogRepository';
import { AuditLogModel } from '../models/AuditLog';

// Extend Request interface to include audit data
declare global {
  namespace Express {
    interface Request {
      auditData?: {
        startTime: number;
        sessionId?: string;
      };
    }
  }
}

export interface AuditLoggingOptions {
  excludePaths?: string[];
  excludeMethods?: string[];
  logRequestBody?: boolean;
  sensitiveFields?: string[];
}

const defaultOptions: AuditLoggingOptions = {
  excludePaths: ['/health', '/favicon.ico'],
  excludeMethods: ['OPTIONS'],
  logRequestBody: true,
  sensitiveFields: []
};

export function auditLoggingMiddleware(options: AuditLoggingOptions = {}) {
  const config = { ...defaultOptions, ...options };

  return (req: Request, res: Response, next: NextFunction) => {
    // Skip logging for excluded paths and methods
    if (!AuditLogModel.shouldLogRequest(req.path, req.method)) {
      return next();
    }

    if (config.excludePaths?.some(path => req.path.startsWith(path))) {
      return next();
    }

    if (config.excludeMethods?.includes(req.method.toUpperCase())) {
      return next();
    }

    // Record start time for response time calculation
    const startTime = Date.now();
    req.auditData = {
      startTime,
      sessionId: generateSessionId(req)
    };

    // Override res.end to capture response data
    const originalEnd = res.end.bind(res);
    res.end = function(chunk?: any, encoding?: any): any {
      // Calculate response time
      const responseTime = Date.now() - startTime;

      // Extract audit information
      const auditData = {
        user_id: req.user?.id,
        session_id: req.auditData?.sessionId,
        action: AuditLogModel.getActionFromRequest(req.method, req.path),
        resource_type: AuditLogModel.getResourceTypeFromPath(req.path),
        resource_id: AuditLogModel.extractResourceId(req.path),
        ip_address: getClientIpAddress(req),
        user_agent: req.get('User-Agent'),
        request_method: req.method,
        request_path: req.path,
        request_body: config.logRequestBody 
          ? AuditLogModel.sanitizeRequestBody(req.body, config.sensitiveFields)
          : undefined,
        response_status: res.statusCode,
        response_time_ms: responseTime,
        metadata: extractMetadata(req, res)
      };

      // Log audit data asynchronously to avoid blocking response
      setImmediate(async () => {
        try {
          await AuditLogRepository.create(auditData);
        } catch (error) {
          console.error('Failed to create audit log:', error);
          // Don't throw error to avoid affecting the response
        }
      });

      // Call original end method
      return originalEnd(chunk, encoding);
    };

    next();
  };
}

function generateSessionId(req: Request): string {
  // Try to extract session ID from JWT token
  if (req.user?.jti) {
    return req.user.jti;
  }

  // Fallback to a combination of IP and User-Agent hash
  const identifier = `${getClientIpAddress(req)}-${req.get('User-Agent') || 'unknown'}`;
  return Buffer.from(identifier).toString('base64').substring(0, 32);
}

function getClientIpAddress(req: Request): string {
  // Check for forwarded IP addresses (when behind proxy/load balancer)
  const forwarded = req.get('X-Forwarded-For');
  if (forwarded) {
    // X-Forwarded-For can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim();
  }

  // Check for real IP (some proxies use this)
  const realIp = req.get('X-Real-IP');
  if (realIp) {
    return realIp;
  }

  // Fallback to connection remote address
  return req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
}

function extractMetadata(req: Request, res: Response): Record<string, any> {
  const metadata: Record<string, any> = {};

  // Add query parameters if present
  if (Object.keys(req.query).length > 0) {
    metadata.query_params = req.query;
  }

  // Add route parameters if present
  if (Object.keys(req.params).length > 0) {
    metadata.route_params = req.params;
  }

  // Add content type if present
  const contentType = req.get('Content-Type');
  if (contentType) {
    metadata.content_type = contentType;
  }

  // Add response content type if present
  const responseContentType = res.get('Content-Type');
  if (responseContentType) {
    metadata.response_content_type = responseContentType;
  }

  // Add referer if present
  const referer = req.get('Referer');
  if (referer) {
    metadata.referer = referer;
  }

  // Add origin if present
  const origin = req.get('Origin');
  if (origin) {
    metadata.origin = origin;
  }

  return metadata;
}

// Security monitoring middleware for failed authentication attempts
export function securityMonitoringMiddleware() {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Override res.json to capture authentication failures
    const originalJson = res.json;
    res.json = function(body: any) {
      // Check for authentication/authorization failures
      if (res.statusCode === 401 || res.statusCode === 403) {
        const isAuthEndpoint = req.path.includes('/auth/');
        
        if (isAuthEndpoint || res.statusCode === 401) {
          // Log security event asynchronously
          setImmediate(async () => {
            try {
              await AuditLogRepository.create({
                user_id: req.user?.id,
                session_id: req.auditData?.sessionId,
                action: 'SECURITY_FAILURE',
                resource_type: 'security',
                ip_address: getClientIpAddress(req),
                user_agent: req.get('User-Agent'),
                request_method: req.method,
                request_path: req.path,
                response_status: res.statusCode,
                response_time_ms: req.auditData ? Date.now() - req.auditData.startTime : 0,
                metadata: {
                  failure_type: res.statusCode === 401 ? 'authentication' : 'authorization',
                  error_message: body?.error || 'Unknown error',
                  ...extractMetadata(req, res)
                }
              });
            } catch (error) {
              console.error('Failed to log security event:', error);
            }
          });
        }
      }

      return originalJson.call(this, body);
    };

    next();
  };
}

// Rate limiting monitoring middleware
export function rateLimitMonitoringMiddleware() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Override res.status to capture rate limit violations
    const originalStatus = res.status;
    res.status = function(code: number) {
      if (code === 429) {
        // Log rate limit violation asynchronously
        setImmediate(async () => {
          try {
            await AuditLogRepository.create({
              user_id: req.user?.id,
              session_id: req.auditData?.sessionId,
              action: 'RATE_LIMIT_EXCEEDED',
              resource_type: 'security',
              ip_address: getClientIpAddress(req),
              user_agent: req.get('User-Agent'),
              request_method: req.method,
              request_path: req.path,
              response_status: 429,
              response_time_ms: req.auditData ? Date.now() - req.auditData.startTime : 0,
              metadata: {
                violation_type: 'rate_limit',
                ...extractMetadata(req, res)
              }
            });
          } catch (error) {
            console.error('Failed to log rate limit violation:', error);
          }
        });
      }

      return originalStatus.call(this, code);
    };

    next();
  };
}
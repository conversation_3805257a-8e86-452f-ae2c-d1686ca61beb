# WebSocket Setup and Configuration

## Problem Solved

The original error was caused by two issues:

1. **Vite HMR WebSocket trying to connect to localhost** when users accessed the app via IP/domain
2. **Missing WebSocket implementation** for real-time collaboration features

## Solution Implemented

### 1. Fixed Vite Development Server Configuration

Updated `frontend/vite.config.ts`:
- Changed `host` to `'0.0.0.0'` to allow external connections
- Configured HMR to use `clientPort` instead of hardcoded localhost

### 2. Added Complete WebSocket Support

#### Backend Changes:
- **Installed Socket.IO server** (already in package.json)
- **Created `CollaborationService`** (`backend/src/services/CollaborationService.ts`)
- **Integrated WebSocket server** into main Express server (`backend/src/index.ts`)
- **Added JWT authentication** for WebSocket connections

#### Frontend Changes:
- **Installed `socket.io-client`** package
- **Created `WebSocketService`** (`frontend/src/services/websocketService.ts`)
- **Created `useWebSocket` composable** (`frontend/src/composables/useWebSocket.ts`)
- **Added WebSocket status component** (`frontend/src/components/common/WebSocketStatus.vue`)

### 3. Environment Configuration

#### Frontend `.env`:
```env
VITE_API_BASE_URL=http://localhost:3001/api
VITE_WEBSOCKET_URL=http://localhost:3001
```

#### Backend `.env`:
```env
FRONTEND_URL=http://localhost:5173
```

## Usage

### For Development (localhost):
1. Start backend: `cd backend && npm run dev`
2. Start frontend: `cd frontend && npm run dev`
3. Access via `http://localhost:5173`

### For Production/External Access:
1. Update environment variables:
   - Frontend: `VITE_WEBSOCKET_URL=http://your-server-ip:3001`
   - Backend: `FRONTEND_URL=http://your-server-ip:5173`
2. Start servers
3. Access via `http://your-server-ip:5173`

## WebSocket Features Implemented

### Real-time Collaboration:
- **Note joining/leaving**: Users can join collaborative editing sessions
- **Live editing**: Real-time synchronization of note changes
- **Cursor tracking**: See other users' cursor positions
- **Typing indicators**: Show when other users are typing
- **User presence**: Track who's currently editing a note

### Authentication:
- **JWT-based auth**: WebSocket connections require valid JWT tokens
- **User identification**: Each socket is associated with a user account
- **Secure connections**: Token validation on every connection

## API Usage Examples

### In Vue Components:

```typescript
import { useWebSocket, useNoteCollaboration } from '@/composables/useWebSocket'

// Basic WebSocket connection
const { isConnected, connect, disconnect } = useWebSocket()

// Note collaboration
const { 
  joinNote, 
  leaveNote, 
  sendOperation, 
  onNoteOperation 
} = useNoteCollaboration('note-id')

// Join a note for collaboration
joinNote()

// Send an edit operation
sendOperation({
  type: 'insert',
  position: 10,
  content: 'Hello World'
})

// Listen for operations from other users
onNoteOperation((data) => {
  console.log('Received operation:', data.operation)
})
```

### WebSocket Events:

#### Client → Server:
- `note:join` - Join a note for collaboration
- `note:leave` - Leave a note
- `note:edit` - Send edit operation
- `cursor:update` - Update cursor position
- `typing:start` - Start typing indicator
- `typing:stop` - Stop typing indicator

#### Server → Client:
- `user:joined` - User joined the note
- `user:left` - User left the note
- `note:operation` - Received edit operation
- `cursor:update` - User cursor update
- `user:typing` - User typing status change

## Testing the Setup

1. **Start both servers**
2. **Open the app in multiple browser tabs/windows**
3. **Login with different accounts**
4. **Navigate to the same note**
5. **Test real-time editing**

You should see:
- Users joining/leaving notifications
- Real-time text synchronization
- Cursor position updates
- Typing indicators

## Troubleshooting

### WebSocket Connection Issues:
1. Check that backend server is running on port 3001
2. Verify CORS configuration in backend
3. Ensure JWT token is valid and not expired
4. Check browser console for connection errors

### Development vs Production:
- **Development**: Uses localhost URLs
- **Production**: Update environment variables to use actual server IP/domain
- **CORS**: Make sure backend CORS allows your frontend domain

## Security Considerations

- **JWT Authentication**: All WebSocket connections require valid JWT tokens
- **User Validation**: Tokens are verified against the database
- **CORS Protection**: Only allowed origins can connect
- **Rate Limiting**: Consider adding rate limiting for WebSocket events
- **Input Validation**: All incoming data should be validated

## Next Steps

1. **Add operational transformation** for conflict-free collaborative editing
2. **Implement presence indicators** in the UI
3. **Add typing indicators** to note editors
4. **Create user avatars** for collaboration
5. **Add notification system** for collaboration events

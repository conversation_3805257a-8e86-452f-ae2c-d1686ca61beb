<template>
  <div class="lazy-image-container" :class="containerClass">
    <img
      ref="imageRef"
      :class="imageClass"
      :alt="alt"
      :loading="loading"
      @load="onLoad"
      @error="onError"
    />
    <div v-if="isLoading" class="lazy-image-placeholder">
      <div class="lazy-image-spinner">
        <span class="icon">
          <i class="fas fa-spinner fa-pulse"></i>
        </span>
      </div>
    </div>
    <div v-if="hasError" class="lazy-image-error">
      <span class="icon">
        <i class="fas fa-image"></i>
      </span>
      <span>Failed to load image</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { createIntersectionObserver } from '../../utils/performance'

interface Props {
  src: string
  alt: string
  placeholder?: string
  loading?: 'lazy' | 'eager'
  containerClass?: string
  imageClass?: string
  rootMargin?: string
  threshold?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: 'lazy',
  containerClass: '',
  imageClass: '',
  rootMargin: '50px',
  threshold: 0.1
})

const imageRef = ref<HTMLImageElement>()
const isLoading = ref(true)
const hasError = ref(false)
const isIntersecting = ref(false)

let observer: IntersectionObserver | null = null

const onLoad = () => {
  isLoading.value = false
  hasError.value = false
}

const onError = () => {
  isLoading.value = false
  hasError.value = true
}

const loadImage = () => {
  if (imageRef.value && !imageRef.value.src) {
    if (props.placeholder) {
      imageRef.value.src = props.placeholder
    }
    
    // Use intersection observer for lazy loading
    if (props.loading === 'lazy') {
      observer = createIntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting && imageRef.value) {
              isIntersecting.value = true
              imageRef.value.src = props.src
              observer?.unobserve(imageRef.value)
            }
          })
        },
        {
          rootMargin: props.rootMargin,
          threshold: props.threshold
        }
      )
      
      if (imageRef.value) {
        observer.observe(imageRef.value)
      }
    } else {
      // Load immediately for eager loading
      imageRef.value.src = props.src
    }
  }
}

onMounted(() => {
  loadImage()
})

onUnmounted(() => {
  if (observer && imageRef.value) {
    observer.unobserve(imageRef.value)
  }
})
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.lazy-image-placeholder,
.lazy-image-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #999;
}

.lazy-image-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.lazy-image-error {
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.875rem;
}

img {
  display: block;
  max-width: 100%;
  height: auto;
  transition: opacity 0.3s ease;
}

img:not([src]) {
  opacity: 0;
}

img.loaded {
  opacity: 1;
}
</style>
<template>
  <div class="modal is-active">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">Group Settings</p>
        <button class="delete" @click="$emit('close')"></button>
      </header>

      <section class="modal-card-body">
        <form @submit.prevent="handleSubmit">
          <!-- Group Name -->
          <div class="field">
            <label class="label">Group Name *</label>
            <div class="control">
              <input v-model="form.name" class="input" :class="{ 'is-danger': errors.name }" type="text"
                placeholder="Enter group name" maxlength="100" required />
            </div>
            <p v-if="errors.name" class="help is-danger">{{ errors.name }}</p>
          </div>

          <!-- Description -->
          <div class="field">
            <label class="label">Description</label>
            <div class="control">
              <textarea v-model="form.description" class="textarea" :class="{ 'is-danger': errors.description }"
                placeholder="Describe your group (optional)" maxlength="500" rows="3"></textarea>
            </div>
            <p v-if="errors.description" class="help is-danger">{{ errors.description }}</p>
          </div>

          <!-- Settings -->
          <div class="field">
            <label class="label">Group Settings</label>

            <!-- Allow Member Invites -->
            <div class="field">
              <div class="control">
                <label class="checkbox">
                  <input v-model="form.settings.allowMemberInvites" type="checkbox" />
                  Allow members to invite others
                </label>
              </div>
              <p class="help">When enabled, editors and viewers can invite new members to the group.</p>
            </div>

            <!-- Default Note Permissions -->
            <div class="field">
              <label class="label">Default Note Permissions</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="form.settings.defaultNotePermissions">
                    <option value="view">View Only</option>
                    <option value="edit">View and Edit</option>
                  </select>
                </div>
              </div>
              <p class="help">Default permission level for new members when accessing group notes.</p>
            </div>

            <!-- Max Members -->
            <div class="field">
              <label class="label">Maximum Members</label>
              <div class="control">
                <input v-model.number="form.settings.maxMembers" class="input"
                  :class="{ 'is-danger': errors.maxMembers }" type="number" min="1" max="1000" placeholder="50" />
              </div>
              <p v-if="errors.maxMembers" class="help is-danger">{{ errors.maxMembers }}</p>
              <p class="help">Maximum number of members allowed in this group (1-1000).</p>
            </div>

            <!-- Require Approval -->
            <div class="field">
              <div class="control">
                <label class="checkbox">
                  <input v-model="form.settings.requireApprovalForJoin" type="checkbox" />
                  Require approval for new members
                </label>
              </div>
              <p class="help">When enabled, new member requests must be approved by an admin.</p>
            </div>
          </div>

          <!-- Danger Zone -->
          <div class="field">
            <label class="label has-text-danger">Danger Zone</label>
            <div class="box has-background-danger-light">
              <div class="field">
                <div class="control">
                  <button type="button" class="button is-danger is-outlined" @click="showDeleteConfirm = true">
                    <span class="icon">
                      <i class="fas fa-trash"></i>
                    </span>
                    <span>Delete Group</span>
                  </button>
                </div>
                <p class="help has-text-danger">
                  This action cannot be undone. All group data will be permanently deleted.
                </p>
              </div>
            </div>
          </div>

          <!-- Error Display -->
          <div v-if="submitError" class="notification is-danger">
            {{ submitError }}
          </div>
        </form>
      </section>

      <footer class="modal-card-foot">
        <button class="button is-primary" :class="{ 'is-loading': loading }" :disabled="loading || !isFormValid"
          @click="handleSubmit">
          Save Changes
        </button>
        <button class="button" @click="$emit('close')">Cancel</button>
      </footer>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteConfirm" class="modal is-active">
      <div class="modal-background" @click="showDeleteConfirm = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title has-text-danger">Delete Group</p>
          <button class="delete" @click="showDeleteConfirm = false"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to delete <strong>{{ group.name }}</strong>?</p>
          <p class="has-text-danger mt-2">
            This action cannot be undone. All group data, including notes and member information, will be permanently
            deleted.
          </p>
          <div class="field mt-4">
            <label class="label">Type the group name to confirm:</label>
            <div class="control">
              <input v-model="deleteConfirmText" class="input" type="text" :placeholder="group.name" />
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button is-danger" :class="{ 'is-loading': deleteLoading }"
            :disabled="deleteLoading || deleteConfirmText !== group.name" @click="handleDelete">
            Delete Group
          </button>
          <button class="button" @click="showDeleteConfirm = false">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useGroupsStore } from '../../stores/groups';
import type { GroupWithMembers, UpdateGroupData } from '../../types/group';

// Props
const props = defineProps<{
  group: GroupWithMembers;
}>();

// Emits
const emit = defineEmits<{
  close: [];
  updated: [group: GroupWithMembers];
}>();

const router = useRouter();
const groupsStore = useGroupsStore();

// Form data
const form = reactive<UpdateGroupData & { settings: any }>({
  name: '',
  description: '',
  settings: {
    allowMemberInvites: true,
    defaultNotePermissions: 'view',
    requireApprovalForJoin: false,
    maxMembers: 50
  }
});

// Form state
const loading = ref(false);
const submitError = ref<string | null>(null);
const showDeleteConfirm = ref(false);
const deleteLoading = ref(false);
const deleteConfirmText = ref('');
const errors = reactive({
  name: '',
  description: '',
  maxMembers: ''
});

// Computed
const isFormValid = computed(() => {
  return (form.name ?? '').trim().length >= 3 &&
    (form.name ?? '').trim().length <= 100 &&
    (!form.description || form.description.length <= 500) &&
    form.settings.maxMembers >= 1 &&
    form.settings.maxMembers <= 1000;
});

// Methods
const validateForm = (): boolean => {
  // Reset errors
  errors.name = '';
  errors.description = '';
  errors.maxMembers = '';

  let isValid = true;

  // Validate name
  if (!(form.name ?? '').trim()) {
    errors.name = 'Group name is required';
    isValid = false;
  } else if ((form.name ?? '').trim().length < 3) {
    errors.name = 'Group name must be at least 3 characters';
    isValid = false;
  } else if ((form.name ?? '').trim().length > 100) {
    errors.name = 'Group name must be less than 100 characters';
    isValid = false;
  }

  // Validate description
  if (form.description && form.description.length > 500) {
    errors.description = 'Description must be less than 500 characters';
    isValid = false;
  }

  // Validate max members
  if (form.settings.maxMembers < 1) {
    errors.maxMembers = 'Maximum members must be at least 1';
    isValid = false;
  } else if (form.settings.maxMembers > 1000) {
    errors.maxMembers = 'Maximum members cannot exceed 1000';
    isValid = false;
  } else if (form.settings.maxMembers < props.group.memberCount) {
    errors.maxMembers = `Maximum members cannot be less than current member count (${props.group.memberCount})`;
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    loading.value = true;
    submitError.value = null;

    const updateData: UpdateGroupData = {
      name: (form.name ?? '').trim(),
      description: form.description?.trim() || undefined,
      settings: form.settings
    };

    const updatedGroup = await groupsStore.updateGroup(props.group.id, updateData);
    emit('updated', updatedGroup);
  } catch (error: any) {
    console.error('Error updating group:', error);
    submitError.value = error.message || 'Failed to update group';
  } finally {
    loading.value = false;
  }
};

const handleDelete = async () => {
  try {
    deleteLoading.value = true;
    await groupsStore.deleteGroup(props.group.id);

    // Navigate back to groups list
    router.push('/groups');
  } catch (error: any) {
    console.error('Error deleting group:', error);
    submitError.value = error.message || 'Failed to delete group';
    showDeleteConfirm.value = false;
  } finally {
    deleteLoading.value = false;
  }
};

// Initialize form with current group data
onMounted(() => {
  form.name = props.group.name;
  form.description = props.group.description || '';
  form.settings = { ...props.group.settings };
});
</script>

<style scoped>
.modal-card {
  width: 90%;
  max-width: 650px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-height: 90vh;
}

.modal-card-head {
  background: #007bff;
  color: white;
  border: none;
  padding: 1.5rem 2rem;
}

.modal-card-title {
  color: white;
  font-weight: 600;
  font-size: 1.25rem;
}

.delete {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.delete:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modal-card-body {
  padding: 2rem;
  background: var(--card-background);
  overflow-y: auto;
  max-height: calc(90vh - 200px);
}

.modal-card-foot {
  background: #f8f9fa;
  border: none;
  padding: 1.5rem 2rem;
  justify-content: flex-end;
  gap: 1rem;
}

/* Form styling */
.field {
  margin-bottom: 1.75rem;
}

.field:last-child {
  margin-bottom: 0;
}

.label {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.label.has-text-danger {
  color: #dc3545;
}

.control {
  position: relative;
}

.input,
.textarea,
.select select {
  border: 2px solid var(--color-border);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: var(--input-background);
}

.input:focus,
.textarea:focus,
.select select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  outline: none;
}

.input.is-danger,
.textarea.is-danger {
  border-color: #dc3545;
}

.input.is-danger:focus,
.textarea.is-danger:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

.select {
  width: 100%;
}

.select select {
  width: 100%;
  cursor: pointer;
}

.select:not(.is-multiple):not(.is-loading)::after {
  border-color: #007bff;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

/* Checkbox styling */
.checkbox {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem 0;
  transition: all 0.2s ease;
}

.checkbox:hover {
  color: #2c3e50;
}

.checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
  accent-color: #007bff;
}

/* Help text styling */
.help {
  font-size: 0.8rem;
  margin-top: 0.5rem;
  line-height: 1.4;
}

.help.is-danger {
  color: #dc3545;
  font-weight: 500;
}

/* Settings sections */
.field .field {
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.field .field:last-child {
  margin-bottom: 0;
}

/* Danger zone styling */
.box.has-background-danger-light {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border: 2px solid #feb2b2;
  border-radius: 8px;
  padding: 1.5rem;
}

.box .field {
  margin-bottom: 0;
}

.box .help.has-text-danger {
  color: #c53030;
  font-weight: 500;
  margin-top: 0.75rem;
}

/* Button styling */
.button {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.button.is-primary {
  background: #007bff;
  border: 1px solid #007bff;
  color: white;
}

.button.is-primary:hover:not(:disabled) {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.button.is-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button.is-primary.is-loading {
  color: transparent;
}

.button.is-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  border: none;
  color: white;
}

.button.is-danger:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.button.is-danger.is-outlined {
  background: transparent;
  border: 2px solid #dc3545;
  color: #dc3545;
}

.button.is-danger.is-outlined:hover {
  background: #dc3545;
  color: white;
  transform: translateY(-1px);
}

.button:not(.is-primary):not(.is-danger) {
  background: var(--button-background);
  border-color: var(--button-border);
  color: var(--button-text);
}

.button:not(.is-primary):not(.is-danger):hover {
  background: #f8f9fa;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

/* Notification styling */
.notification {
  border-radius: 8px;
  border: 1px solid transparent;
  margin-bottom: 1rem;
}

.notification.is-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

/* Delete confirmation modal */
.modal .modal-card {
  max-width: 500px;
}

.modal .modal-card-head.has-text-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.modal .modal-card-body strong {
  color: #2c3e50;
  font-weight: 600;
}

.modal .modal-card-body .has-text-danger {
  color: #dc3545;
  font-weight: 500;
  margin-top: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-card {
    width: 95%;
    margin: 1rem;
    max-height: 95vh;
  }

  .modal-card-head,
  .modal-card-body,
  .modal-card-foot {
    padding: 1.25rem;
  }

  .modal-card-body {
    max-height: calc(95vh - 180px);
  }

  .modal-card-title {
    font-size: 1.1rem;
  }

  .input,
  .textarea,
  .select select {
    font-size: 0.9rem;
  }

  .field .field {
    padding: 0.75rem;
  }

  .box.has-background-danger-light {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .modal-card-foot {
    flex-direction: column;
  }

  .modal-card-foot .button {
    width: 100%;
    justify-content: center;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .modal-card-body {
    background: #2d3748;
    color: #e2e8f0;
  }

  .modal-card-foot {
    background: #4a5568;
  }

  .label {
    color: #f7fafc;
  }

  .input,
  .textarea,
  .select select {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }

  .input:focus,
  .textarea:focus,
  .select select:focus {
    border-color: #667eea;
    background: #4a5568;
  }

  .field .field {
    background: #4a5568;
    border-color: #718096;
  }

  .box.has-background-danger-light {
    background: linear-gradient(135deg, #4a1a1a 0%, #742a2a 100%);
    border-color: #c53030;
  }

  .box .help.has-text-danger {
    color: #feb2b2;
  }

  .checkbox:hover {
    color: #f7fafc;
  }

  .button:not(.is-primary):not(.is-danger) {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }

  .button:not(.is-primary):not(.is-danger):hover {
    background: #718096;
    border-color: #a0aec0;
  }

  .button.is-danger.is-outlined {
    border-color: #feb2b2;
    color: #feb2b2;
  }

  .button.is-danger.is-outlined:hover {
    background: #dc3545;
    color: white;
  }

  .modal .modal-card-body strong {
    color: #f7fafc;
  }

  .modal .modal-card-body .has-text-danger {
    color: #feb2b2;
  }
}
</style>
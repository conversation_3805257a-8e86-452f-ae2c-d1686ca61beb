# Note Taking App

A comprehensive note-taking application built with Vue.js 3 and Node.js, supporting multiple note formats (Rich Text, Markdown, Kanban), real-time collaboration, and secure sharing.

## Project Structure

```
├── frontend/          # Vue.js 3 + TypeScript frontend
│   ├── src/
│   │   ├── components/
│   │   ├── views/
│   │   ├── stores/
│   │   └── main.ts
│   └── package.json
├── backend/           # Node.js + Express + TypeScript backend
│   ├── src/
│   │   ├── config/
│   │   ├── routes/
│   │   ├── models/
│   │   ├── services/
│   │   └── index.ts
│   └── package.json
└── README.md
```

## Development Setup

### Prerequisites
- Node.js (v22.11.0 or higher)
- npm

### Backend Setup
```bash
cd backend
npm install
npm run dev
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

## Features

- **Multi-format Notes**: Rich Text, Markdown with live preview, Kanban boards
- **Real-time Collaboration**: WebSocket-based collaborative editing
- **Secure Sharing**: Multiple access levels and permission controls
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Full-text Search**: Fast search across all note types
- **User Management**: Authentication, groups, and role-based access
- **Export Options**: PDF, Markdown, and HTML export

## Technology Stack

### Frontend
- Vue.js 3 with Composition API
- TypeScript
- Pinia for state management
- Vue Router for routing
- Bulma CSS for styling
- Vite for build tooling

### Backend
- Node.js with Express
- TypeScript
- SQLite (development) / MariaDB (production)
- JWT for authentication
- WebSockets for real-time features

## Development Commands

### Frontend
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run test:unit` - Run unit tests

### Backend
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build TypeScript to JavaScript
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
import { getDatabase } from '../config/database';
import { 
  Note, 
  NoteVersion, 
  CreateNoteData, 
  UpdateNoteData, 
  NoteFilters, 
  PaginationOptions,
  NoteModel 
} from '../models/Note';
import { Tag, TagModel } from '../models/Tag';

export class NoteRepository {
  private static getDb() {
    return getDatabase();
  }

  static async create(noteData: CreateNoteData): Promise<Note> {
    const id = NoteModel.generateId();
    const now = new Date().toISOString();
    const metadata = NoteModel.updateMetadata(noteData.content, noteData.noteType, noteData.metadata);

    const query = `
      INSERT INTO notes (
        id, user_id, group_id, title, content, note_type, metadata, is_archived, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      noteData.userId,
      noteData.groupId || null,
      noteData.title,
      noteData.content,
      noteData.noteType,
      JSON.stringify(metadata),
      false,
      now,
      now
    ];

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().run(query, params, function(err) {
        if (err) {
          reject(err);
          return;
        }

        // Create initial version
        NoteRepository.createVersion(id, noteData.content, 1, noteData.userId)
          .then(() => NoteRepository.findById(id))
          .then(note => {
            if (!note) {
              reject(new Error('Failed to create note'));
              return;
            }
            resolve(note);
          })
          .catch(reject);
      });
    });
  }

  static async findById(id: string): Promise<Note | null> {
    const query = 'SELECT * FROM notes WHERE id = ?';
    
    return new Promise((resolve, reject) => {
      NoteRepository.getDb().get(query, [id], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        
        if (!row) {
          resolve(null);
          return;
        }

        resolve(this.mapRowToNote(row));
      });
    });
  }

  static async findByUserId(
    filters: NoteFilters, 
    pagination: PaginationOptions
  ): Promise<{ notes: Note[]; total: number }> {
    let whereClause = 'WHERE user_id = ?';
    const params: any[] = [filters.userId];

    // Add filters
    if (filters.groupId) {
      whereClause += ' AND group_id = ?';
      params.push(filters.groupId);
    }

    if (filters.noteType) {
      whereClause += ' AND note_type = ?';
      params.push(filters.noteType);
    }

    if (filters.isArchived !== undefined) {
      whereClause += ' AND is_archived = ?';
      params.push(filters.isArchived);
    }

    if (filters.search) {
      whereClause += ' AND (title LIKE ? OR content LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm);
    }

    // Handle tag filtering
    if (filters.tags && filters.tags.length > 0) {
      const tagPlaceholders = filters.tags.map(() => '?').join(',');
      whereClause += ` AND id IN (
        SELECT DISTINCT nt.note_id 
        FROM note_tags nt 
        JOIN tags t ON nt.tag_id = t.id 
        WHERE t.name IN (${tagPlaceholders}) AND t.user_id = ?
      )`;
      params.push(...filters.tags, filters.userId);
    }

    // Count query
    const countQuery = `SELECT COUNT(*) as total FROM notes ${whereClause}`;
    
    // Main query with pagination and sorting
    const sortBy = pagination.sortBy || 'updated_at';
    const sortOrder = pagination.sortOrder || 'desc';
    const offset = (pagination.page - 1) * pagination.limit;

    const mainQuery = `
      SELECT * FROM notes 
      ${whereClause} 
      ORDER BY ${sortBy} ${sortOrder.toUpperCase()} 
      LIMIT ? OFFSET ?
    `;

    const mainParams = [...params, pagination.limit, offset];

    return new Promise((resolve, reject) => {
      // Get total count
      NoteRepository.getDb().get(countQuery, params, (err, countRow) => {
        if (err) {
          reject(err);
          return;
        }

        const total = (countRow as any).total;

        // Get notes
        NoteRepository.getDb().all(mainQuery, mainParams, (err, rows) => {
          if (err) {
            reject(err);
            return;
          }

          const notes = rows.map(row => this.mapRowToNote(row));
          resolve({ notes, total });
        });
      });
    });
  }

  static async update(id: string, updateData: UpdateNoteData, userId: string): Promise<Note> {
    const fields: string[] = [];
    const params: any[] = [];

    if (updateData.title !== undefined) {
      fields.push('title = ?');
      params.push(updateData.title);
    }

    if (updateData.content !== undefined) {
      fields.push('content = ?');
      params.push(updateData.content);
      
      // Update metadata when content changes
      const currentNote = await this.findById(id);
      if (currentNote) {
        try {
          const updatedMetadata = NoteModel.updateMetadata(
            updateData.content, 
            currentNote.noteType, 
            { ...currentNote.metadata, ...updateData.metadata }
          );
          fields.push('metadata = ?');
          params.push(JSON.stringify(updatedMetadata));
        } catch (error) {
          console.error('Error updating metadata:', error);
          // Continue without metadata update if there's an error
        }

        // Create new version when content changes
        try {
          const latestVersion = await this.getLatestVersion(id);
          const nextVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;
          await this.createVersion(id, updateData.content, nextVersionNumber, userId);
        } catch (error) {
          console.error('Error creating note version:', error);
          // Continue without version creation if there's an error
        }
      }
    } else if (updateData.metadata !== undefined) {
      const currentNote = await this.findById(id);
      if (currentNote) {
        const updatedMetadata = { ...currentNote.metadata, ...updateData.metadata };
        fields.push('metadata = ?');
        params.push(JSON.stringify(updatedMetadata));
      }
    }

    if (updateData.isArchived !== undefined) {
      fields.push('is_archived = ?');
      params.push(updateData.isArchived);
    }

    if (fields.length === 0) {
      // Nothing to update, return current note
      const note = await this.findById(id);
      if (!note) {
        throw new Error('Note not found');
      }
      return note;
    }

    fields.push('updated_at = ?');
    params.push(new Date().toISOString());
    params.push(id);

    const query = `UPDATE notes SET ${fields.join(', ')} WHERE id = ?`;

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().run(query, params, function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('Note not found'));
          return;
        }

        NoteRepository.findById(id)
          .then(note => {
            if (!note) {
              reject(new Error('Note not found after update'));
              return;
            }
            resolve(note);
          })
          .catch(reject);
      });
    });
  }

  static async delete(id: string): Promise<void> {
    // Soft delete - mark as archived
    const query = 'UPDATE notes SET is_archived = ?, updated_at = ? WHERE id = ?';
    const now = new Date().toISOString();

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().run(query, [true, now, id], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('Note not found'));
          return;
        }

        resolve();
      });
    });
  }

  static async hardDelete(id: string): Promise<void> {
    // Permanent delete
    const query = 'DELETE FROM notes WHERE id = ?';

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().run(query, [id], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('Note not found'));
          return;
        }

        resolve();
      });
    });
  }

  // Version management
  static async createVersion(noteId: string, content: string, versionNumber: number, userId: string): Promise<NoteVersion> {
    const id = NoteModel.generateId();
    const now = new Date().toISOString();

    const query = `
      INSERT INTO note_versions (id, note_id, content, version_number, created_at, created_by)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const params = [id, noteId, content, versionNumber, now, userId];

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().run(query, params, function(err) {
        if (err) {
          reject(err);
          return;
        }

        resolve({
          id,
          noteId,
          content,
          versionNumber,
          createdAt: new Date(now),
          createdBy: userId
        });
      });
    });
  }

  static async getVersions(noteId: string): Promise<NoteVersion[]> {
    const query = `
      SELECT * FROM note_versions 
      WHERE note_id = ? 
      ORDER BY version_number DESC
    `;

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().all(query, [noteId], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }

        const versions = (rows as any[]).map((row: any) => ({
          id: row.id,
          noteId: row.note_id,
          content: row.content,
          versionNumber: row.version_number,
          createdAt: new Date(row.created_at),
          createdBy: row.created_by
        }));

        resolve(versions);
      });
    });
  }

  static async getLatestVersion(noteId: string): Promise<NoteVersion | null> {
    const query = `
      SELECT * FROM note_versions 
      WHERE note_id = ? 
      ORDER BY version_number DESC 
      LIMIT 1
    `;

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().get(query, [noteId], (err, row) => {
        if (err) {
          reject(err);
          return;
        }

        if (!row) {
          resolve(null);
          return;
        }

        resolve({
          id: (row as any).id,
          noteId: (row as any).note_id,
          content: (row as any).content,
          versionNumber: (row as any).version_number,
          createdAt: new Date((row as any).created_at),
          createdBy: (row as any).created_by
        });
      });
    });
  }

  // Tag management
  static async createTag(name: string, userId: string, icon?: string, color?: string, isPredefined?: boolean): Promise<Tag> {
    const id = TagModel.generateId();
    const now = new Date().toISOString();

    const query = `
      INSERT INTO tags (id, name, user_id, icon, color, is_predefined, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const tagIcon = icon || 'fas fa-tag';
    const tagColor = color || '#6c757d';
    const predefined = isPredefined || false;

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().run(query, [id, name, userId, tagIcon, tagColor, predefined, now, now], function(err) {
        if (err) {
          if (err.message.includes('UNIQUE constraint failed')) {
            reject(new Error('Tag already exists'));
          } else {
            reject(err);
          }
          return;
        }

        resolve({
          id,
          name,
          userId,
          icon: tagIcon,
          color: tagColor,
          isPredefined: predefined,
          createdAt: new Date(now),
          updatedAt: new Date(now)
        });
      });
    });
  }

  static async getTagsByUserId(userId: string): Promise<Tag[]> {
    const query = 'SELECT * FROM tags WHERE user_id = ? ORDER BY name';

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().all(query, [userId], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }

        const tags = (rows as any[]).map((row: any) => ({
          id: row.id,
          name: row.name,
          userId: row.user_id,
          icon: row.icon || 'fas fa-tag',
          color: row.color || '#6c757d',
          isPredefined: Boolean(row.is_predefined),
          createdAt: new Date(row.created_at),
          updatedAt: new Date(row.updated_at)
        }));

        resolve(tags);
      });
    });
  }

  static async addTagToNote(noteId: string, tagId: string): Promise<void> {
    const query = 'INSERT INTO note_tags (note_id, tag_id) VALUES (?, ?)';

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().run(query, [noteId, tagId], async function(err) {
        if (err) {
          if (err.message.includes('UNIQUE constraint failed')) {
            resolve(); // Tag already associated with note
          } else {
            reject(err);
          }
          return;
        }

        // Update FTS table with new tags
        try {
          const { SearchService } = await import('../services/SearchService');
          await SearchService.updateNoteTags(noteId);
        } catch (error) {
          console.error('Failed to update FTS tags:', error);
          // Don't fail the operation if FTS update fails
        }

        resolve();
      });
    });
  }

  static async removeTagFromNote(noteId: string, tagId: string): Promise<void> {
    const query = 'DELETE FROM note_tags WHERE note_id = ? AND tag_id = ?';

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().run(query, [noteId, tagId], async function(err) {
        if (err) {
          reject(err);
          return;
        }

        // Update FTS table with updated tags
        try {
          const { SearchService } = await import('../services/SearchService');
          await SearchService.updateNoteTags(noteId);
        } catch (error) {
          console.error('Failed to update FTS tags:', error);
          // Don't fail the operation if FTS update fails
        }

        resolve();
      });
    });
  }

  static async getTagsForNote(noteId: string): Promise<Tag[]> {
    const query = `
      SELECT t.* FROM tags t
      JOIN note_tags nt ON t.id = nt.tag_id
      WHERE nt.note_id = ?
      ORDER BY t.name
    `;

    return new Promise((resolve, reject) => {
      NoteRepository.getDb().all(query, [noteId], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }

        const tags = (rows as any[]).map((row: any) => ({
          id: row.id,
          name: row.name,
          userId: row.user_id,
          icon: row.icon || 'fas fa-tag',
          color: row.color || '#6c757d',
          isPredefined: Boolean(row.is_predefined),
          createdAt: new Date(row.created_at),
          updatedAt: new Date(row.updated_at)
        }));

        resolve(tags);
      });
    });
  }

  private static mapRowToNote(row: any): Note {
    return {
      id: row.id,
      userId: row.user_id,
      groupId: row.group_id,
      title: row.title,
      content: row.content,
      noteType: row.note_type,
      metadata: JSON.parse(row.metadata || '{}'),
      isArchived: Boolean(row.is_archived),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}
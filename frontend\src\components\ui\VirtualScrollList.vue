<template>
  <div 
    ref="containerRef" 
    class="virtual-scroll-container"
    :style="{ height: containerHeight + 'px' }"
    @scroll="handleScroll"
  >
    <!-- Spacer for items before visible range -->
    <div :style="{ height: offsetY + 'px' }"></div>
    
    <!-- Visible items -->
    <div
      v-for="item in visibleItems"
      :key="getItemKey(item)"
      :style="{ height: itemHeight + 'px' }"
      class="virtual-scroll-item"
    >
      <slot :item="item" :index="item.index"></slot>
    </div>
    
    <!-- Spacer for items after visible range -->
    <div :style="{ height: (totalHeight - offsetY - visibleHeight) + 'px' }"></div>
    
    <!-- Loading indicator -->
    <div v-if="isLoading" class="virtual-scroll-loading">
      <div class="has-text-centered p-4">
        <div class="button is-loading is-ghost"></div>
        <p class="mt-2">Loading more items...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

interface Props {
  items: any[]
  itemHeight: number
  containerHeight: number
  buffer?: number
  keyField?: string
  loadMore?: () => Promise<void>
  hasMore?: boolean
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  buffer: 5,
  keyField: 'id',
  hasMore: false,
  isLoading: false
})

const emit = defineEmits<{
  scroll: [{ scrollTop: number; scrollLeft: number }]
  loadMore: []
}>()

// Refs
const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)

// Computed properties
const totalHeight = computed(() => props.items.length * props.itemHeight)

const visibleStart = computed(() => {
  const start = Math.floor(scrollTop.value / props.itemHeight) - props.buffer
  return Math.max(0, start)
})

const visibleEnd = computed(() => {
  const itemsInView = Math.ceil(props.containerHeight / props.itemHeight)
  const end = visibleStart.value + itemsInView + props.buffer * 2
  return Math.min(props.items.length, end)
})

const visibleItems = computed(() => {
  return props.items.slice(visibleStart.value, visibleEnd.value).map((item, index) => ({
    ...item,
    index: visibleStart.value + index
  }))
})

const offsetY = computed(() => visibleStart.value * props.itemHeight)
const visibleHeight = computed(() => (visibleEnd.value - visibleStart.value) * props.itemHeight)

// Methods
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
  
  emit('scroll', {
    scrollTop: target.scrollTop,
    scrollLeft: target.scrollLeft
  })
  
  // Check if we need to load more items
  if (props.loadMore && props.hasMore && !props.isLoading) {
    const scrollPercentage = (target.scrollTop + target.clientHeight) / target.scrollHeight
    if (scrollPercentage > 0.8) { // Load more when 80% scrolled
      emit('loadMore')
      props.loadMore()
    }
  }
}

const getItemKey = (item: any) => {
  return item[props.keyField] || item.index
}

const scrollToIndex = (index: number, behavior: ScrollBehavior = 'smooth') => {
  if (!containerRef.value) return
  
  const targetScrollTop = index * props.itemHeight
  containerRef.value.scrollTo({
    top: targetScrollTop,
    behavior
  })
}

const scrollToTop = (behavior: ScrollBehavior = 'smooth') => {
  scrollToIndex(0, behavior)
}

const scrollToBottom = (behavior: ScrollBehavior = 'smooth') => {
  if (!containerRef.value) return
  
  containerRef.value.scrollTo({
    top: totalHeight.value,
    behavior
  })
}

// Expose methods to parent
defineExpose({
  scrollToIndex,
  scrollToTop,
  scrollToBottom,
  getVisibleRange: () => ({ start: visibleStart.value, end: visibleEnd.value })
})

// Watch for items changes and maintain scroll position
let previousItemsLength = 0
watch(() => props.items.length, (newLength) => {
  if (newLength > previousItemsLength && containerRef.value) {
    // Items were added, maintain relative scroll position
    nextTick(() => {
      if (containerRef.value) {
        const scrollPercentage = scrollTop.value / (previousItemsLength * props.itemHeight)
        const newScrollTop = scrollPercentage * totalHeight.value
        containerRef.value.scrollTop = newScrollTop
      }
    })
  }
  previousItemsLength = newLength
})

// Performance optimization: throttle scroll events
let scrollTimeout: number | null = null
const throttledHandleScroll = (event: Event) => {
  if (scrollTimeout) return
  
  scrollTimeout = window.setTimeout(() => {
    handleScroll(event)
    scrollTimeout = null
  }, 16) // ~60fps
}

onMounted(() => {
  if (containerRef.value) {
    containerRef.value.addEventListener('scroll', throttledHandleScroll, { passive: true })
  }
})

onUnmounted(() => {
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', throttledHandleScroll)
  }
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
})
</script>

<style scoped>
.virtual-scroll-container {
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  /* Ensure dropdowns can escape the container */
  z-index: 1;
}

.virtual-scroll-item {
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.virtual-scroll-loading {
  position: sticky;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  border-top: 1px solid #e0e0e0;
}

/* Smooth scrolling for better UX */
.virtual-scroll-container {
  scroll-behavior: smooth;
}

/* Ensure dropdowns can escape the virtual scroll container */
.virtual-scroll-container .dropdown {
  position: relative;
  z-index: 10000;
}

.virtual-scroll-container .dropdown-menu {
  z-index: 10000 !important;
  position: absolute !important;
}

/* Custom scrollbar styling */
.virtual-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.virtual-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
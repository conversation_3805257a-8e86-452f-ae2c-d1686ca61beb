name: Performance Check

on:
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'frontend/**'
      - 'package*.json'
  push:
    branches: [ main ]
    paths:
      - 'frontend/**'
      - 'package*.json'

jobs:
  performance-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch full history for baseline comparison

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: |
          package-lock.json
          frontend/package-lock.json

    - name: Install dependencies
      run: |
        npm ci
        cd frontend && npm ci

    - name: Cache performance baselines
      uses: actions/cache@v4
      with:
        path: frontend/baselines/
        key: performance-baselines-${{ github.base_ref || 'main' }}-${{ hashFiles('frontend/package-lock.json') }}
        restore-keys: |
          performance-baselines-${{ github.base_ref || 'main' }}-
          performance-baselines-main-

    - name: Build application
      run: |
        cd frontend
        npm run build
      env:
        NODE_ENV: production
        VITE_APP_VERSION: ${{ github.sha }}

    - name: Run bundle size check
      run: |
        cd frontend
        npm run perf-budget

    - name: Run performance regression check
      id: regression-check
      run: |
        cd frontend
        node scripts/performance-regression-check.js --no-fail
      continue-on-error: true

    - name: Generate performance report
      if: always()
      run: |
        cd frontend
        
        # Create performance report
        echo "# Performance Report" > performance-report.md
        echo "" >> performance-report.md
        echo "**Build:** ${{ github.sha }}" >> performance-report.md
        echo "**Branch:** ${{ github.ref_name }}" >> performance-report.md
        echo "**Timestamp:** $(date -u)" >> performance-report.md
        echo "" >> performance-report.md
        
        # Add bundle size analysis
        echo "## Bundle Size Analysis" >> performance-report.md
        echo "\`\`\`" >> performance-report.md
        npm run perf-budget 2>&1 || true >> performance-report.md
        echo "\`\`\`" >> performance-report.md
        echo "" >> performance-report.md
        
        # Add regression analysis
        echo "## Regression Analysis" >> performance-report.md
        echo "\`\`\`" >> performance-report.md
        node scripts/performance-regression-check.js --no-fail 2>&1 || true >> performance-report.md
        echo "\`\`\`" >> performance-report.md

    - name: Upload performance artifacts
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: performance-report-${{ github.sha }}
        path: |
          frontend/performance-report.md
          frontend/dist/stats.html
          frontend/baselines/
        retention-days: 30

    - name: Comment PR with performance results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          try {
            const reportPath = path.join('frontend', 'performance-report.md');
            if (fs.existsSync(reportPath)) {
              const report = fs.readFileSync(reportPath, 'utf8');
              
              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: report
              });
            }
          } catch (error) {
            console.log('Could not post performance report:', error);
          }

    - name: Update baseline on main branch
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      run: |
        cd frontend
        
        # Update baselines with current metrics
        node scripts/performance-regression-check.js --update-baseline
        
        # Commit updated baselines back to repository
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
        if [ -n "$(git status --porcelain frontend/baselines/)" ]; then
          git add frontend/baselines/
          git commit -m "Update performance baselines [skip ci]"
          git push
        fi

    - name: Fail on critical regressions
      if: steps.regression-check.outcome == 'failure'
      run: |
        echo "❌ Critical performance regressions detected!"
        echo "Check the performance report for details."
        exit 1

  lighthouse-audit:
    runs-on: ubuntu-latest
    needs: performance-check
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: |
          package-lock.json
          frontend/package-lock.json

    - name: Install dependencies
      run: |
        npm ci
        cd frontend && npm ci

    - name: Build application
      run: |
        cd frontend
        npm run build
      env:
        NODE_ENV: production

    - name: Serve application
      run: |
        cd frontend
        npx serve -s dist -p 3000 &
        sleep 5  # Wait for server to start
      
    - name: Run Lighthouse audit
      uses: treosh/lighthouse-ci-action@v12
      with:
        configPath: './frontend/.lighthouserc.json'
        uploadArtifacts: true
        temporaryPublicStorage: true
        runs: 3  # Run 3 times and average results

    - name: Upload Lighthouse results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: lighthouse-results-${{ github.sha }}
        path: |
          .lighthouseci/
        retention-days: 30
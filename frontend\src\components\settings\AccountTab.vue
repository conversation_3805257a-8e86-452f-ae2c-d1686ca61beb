<template>
  <div class="account-tab">
    <div class="notification is-info is-light" v-if="successMessage">
      <button class="delete" @click="successMessage = ''"></button>
      {{ successMessage }}
    </div>

    <div class="notification is-danger is-light" v-if="errorMessage">
      <button class="delete" @click="errorMessage = ''"></button>
      {{ errorMessage }}
    </div>

    <!-- Security Settings -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-shield-alt"></i>
        </span>
        Security
      </h3>

      <div class="field">
        <label class="label">Password</label>
        <div class="control">
          <button 
            class="button is-outlined"
            @click="showPasswordModal = true"
            :disabled="isLoading"
          >
            <span class="icon">
              <i class="fas fa-key"></i>
            </span>
            <span>Change Password</span>
          </button>
        </div>
        <p class="help">Update your account password</p>
      </div>

      <div class="field" v-if="user?.oauth_provider">
        <label class="label">Connected Accounts</label>
        <div class="control">
          <div class="tags">
            <span class="tag is-info">
              <span class="icon">
                <i class="fab fa-google" v-if="user.oauth_provider === 'google'"></i>
                <i class="fas fa-user" v-else></i>
              </span>
              <span>{{ user.oauth_provider === 'google' ? 'Google' : user.oauth_provider }}</span>
            </span>
          </div>
        </div>
        <p class="help">Your account is connected via {{ user.oauth_provider }}</p>
      </div>

      <div class="field">
        <label class="label">Two-Factor Authentication</label>
        <div class="control">
          <button 
            class="button is-outlined"
            @click="show2FAModal = true"
            :disabled="isLoading"
          >
            <span class="icon">
              <i class="fas fa-mobile-alt"></i>
            </span>
            <span>{{ user?.two_fa_secret ? 'Manage 2FA' : 'Enable 2FA' }}</span>
          </button>
        </div>
        <p class="help">Add an extra layer of security to your account</p>
      </div>
    </div>

    <!-- Data Management -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-database"></i>
        </span>
        Data Management
      </h3>

      <div class="field">
        <label class="label">Export Data</label>
        <div class="control">
          <button 
            class="button is-outlined"
            @click="exportUserData"
            :disabled="isLoading"
            :class="{ 'is-loading': isExporting }"
          >
            <span class="icon">
              <i class="fas fa-download"></i>
            </span>
            <span>Download My Data</span>
          </button>
        </div>
        <p class="help">Download all your account data and notes in JSON format</p>
      </div>

      <div class="field">
        <label class="label">Data Portability</label>
        <div class="control">
          <div class="content">
            <p>You have the right to:</p>
            <ul>
              <li>Export your data at any time</li>
              <li>Request data deletion (contact support)</li>
              <li>Access your data processing information</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Account Actions -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-exclamation-triangle"></i>
        </span>
        Account Actions
      </h3>

      <div class="field">
        <label class="label">Deactivate Account</label>
        <div class="control">
          <button 
            class="button is-outlined is-warning"
            @click="showDeactivateModal = true"
            :disabled="isLoading"
          >
            <span class="icon">
              <i class="fas fa-pause"></i>
            </span>
            <span>Deactivate Account</span>
          </button>
        </div>
        <p class="help">Temporarily disable your account (can be reactivated)</p>
      </div>

      <div class="field">
        <label class="label">Delete Account</label>
        <div class="control">
          <button 
            class="button is-outlined is-danger"
            @click="showDeleteModal = true"
            :disabled="isLoading"
          >
            <span class="icon">
              <i class="fas fa-trash"></i>
            </span>
            <span>Delete Account</span>
          </button>
        </div>
        <p class="help">Permanently delete your account and all data (cannot be undone)</p>
      </div>
    </div>

    <!-- Modals -->
    <div class="modal" :class="{ 'is-active': showPasswordModal }">
      <div class="modal-background" @click="showPasswordModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Change Password</p>
          <button class="delete" @click="showPasswordModal = false"></button>
        </header>
        <section class="modal-card-body">
          <p class="has-text-info">
            Password change functionality will be implemented in a future update.
            For now, please use the "Forgot Password" feature on the login page.
          </p>
        </section>
        <footer class="modal-card-foot">
          <button class="button" @click="showPasswordModal = false">Close</button>
        </footer>
      </div>
    </div>

    <div class="modal" :class="{ 'is-active': show2FAModal }">
      <div class="modal-background" @click="show2FAModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Two-Factor Authentication</p>
          <button class="delete" @click="show2FAModal = false"></button>
        </header>
        <section class="modal-card-body">
          <p class="has-text-info">
            Two-factor authentication setup will be implemented in a future update.
            This will provide an additional layer of security for your account.
          </p>
        </section>
        <footer class="modal-card-foot">
          <button class="button" @click="show2FAModal = false">Close</button>
        </footer>
      </div>
    </div>

    <div class="modal" :class="{ 'is-active': showDeactivateModal }">
      <div class="modal-background" @click="showDeactivateModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Deactivate Account</p>
          <button class="delete" @click="showDeactivateModal = false"></button>
        </header>
        <section class="modal-card-body">
          <p class="has-text-warning">
            Account deactivation functionality will be implemented in a future update.
            Please contact support if you need to deactivate your account.
          </p>
        </section>
        <footer class="modal-card-foot">
          <button class="button" @click="showDeactivateModal = false">Close</button>
        </footer>
      </div>
    </div>

    <div class="modal" :class="{ 'is-active': showDeleteModal }">
      <div class="modal-background" @click="showDeleteModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Delete Account</p>
          <button class="delete" @click="showDeleteModal = false"></button>
        </header>
        <section class="modal-card-body">
          <p class="has-text-danger">
            Account deletion functionality will be implemented in a future update.
            Please contact support if you need to delete your account.
          </p>
          <p class="has-text-grey">
            <strong>Warning:</strong> Account deletion is permanent and cannot be undone.
            All your notes and data will be permanently removed.
          </p>
        </section>
        <footer class="modal-card-foot">
          <button class="button" @click="showDeleteModal = false">Close</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { userService } from '../../services/userService'

const authStore = useAuthStore()
const user = computed(() => authStore.user)

const isLoading = ref(false)
const isExporting = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const showPasswordModal = ref(false)
const show2FAModal = ref(false)
const showDeactivateModal = ref(false)
const showDeleteModal = ref(false)

const showSuccess = (message: string) => {
  successMessage.value = message
  errorMessage.value = ''
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
}

const showError = (message: string) => {
  errorMessage.value = message
  successMessage.value = ''
}

const exportUserData = async () => {
  isExporting.value = true
  try {
    const result = await userService.exportData()
    
    if (result.success && result.data) {
      const timestamp = new Date().toISOString().split('T')[0]
      const filename = `user-data-${timestamp}.json`
      userService.downloadExportedData(result.data, filename)
      showSuccess('Data exported successfully')
    } else {
      showError(result.error || 'Failed to export data')
    }
  } catch (error) {
    showError('Failed to export data')
  } finally {
    isExporting.value = false
  }
}
</script>

<style scoped>
.account-tab {
  max-width: 600px;
}

.field-group {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e5e5;
}

.field-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.subtitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #363636;
}

.field {
  margin-bottom: 1rem;
}

.label {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.help {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.tags {
  display: flex;
  gap: 0.5rem;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.content ul {
  margin-left: 1rem;
}

.notification {
  margin-bottom: 1rem;
}

/* Dark mode styles */
:global(.dark) .field-group {
  border-bottom-color: #404040;
}

:global(.dark) .subtitle {
  color: #e5e5e5;
}

:global(.dark) .help {
  color: #9ca3af;
}

:global(.dark) .label {
  color: #e5e5e5;
}

:global(.dark) .content {
  color: #e5e5e5;
}

:global(.dark) .modal-card {
  background: #2b2b2b;
  color: #e5e5e5;
}

:global(.dark) .modal-card-head {
  background: #1e1e1e;
  border-bottom-color: #404040;
}

:global(.dark) .modal-card-foot {
  background: #1e1e1e;
  border-top-color: #404040;
}

:global(.dark) .modal-card-title {
  color: #e5e5e5;
}
</style>
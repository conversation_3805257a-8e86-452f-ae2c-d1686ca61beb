import { Response } from 'express';
import { UserRepository } from '../repositories/UserRepository';
import { UserPreferences } from '../models/User';
import { AuthenticatedRequest } from '../middleware/auth';

export class UserController {
  static async getSettings(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id;

      // Get user with current preferences
      const user = await UserRepository.findById(userId);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      res.json({
        preferences: user.preferences
      });
    } catch (error) {
      console.error('Get settings error:', error);
      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async updateSettings(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id;
      const preferences: Partial<UserPreferences> = req.body;

      // Validate preferences structure
      const validationResult = UserController.validatePreferences(preferences);
      if (!validationResult.valid) {
        res.status(400).json({
          error: 'Invalid preferences',
          code: 'INVALID_PREFERENCES',
          details: validationResult.errors
        });
        return;
      }

      // Update preferences
      await UserRepository.updatePreferences(userId, preferences);

      // Get updated user data
      const user = await UserRepository.findById(userId);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      res.json({
        message: 'Settings updated successfully',
        preferences: user.preferences
      });
    } catch (error) {
      console.error('Update settings error:', error);
      
      if (error instanceof Error && error.message === 'User not found') {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async updateProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id;
      const { display_name, avatar_url } = req.body;

      // Input is already validated and sanitized by middleware
      // Update profile
      await UserRepository.updateProfile(userId, {
        display_name,
        avatar_url
      });

      // Get updated user data
      const user = await UserRepository.findById(userId);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      // Return user data (without password hash)
      const { password_hash, ...userWithoutPassword } = user;

      res.json({
        message: 'Profile updated successfully',
        user: userWithoutPassword
      });
    } catch (error) {
      console.error('Update profile error:', error);
      
      if (error instanceof Error && error.message === 'User not found') {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async exportData(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id;

      // Get user data
      const user = await UserRepository.findById(userId);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      // TODO: In a future task, we'll implement note export functionality
      // For now, we'll just export user profile and preferences
      const { password_hash, two_fa_secret, ...exportableUser } = user;

      const exportData = {
        user: exportableUser,
        exportedAt: new Date().toISOString(),
        version: '1.0'
      };

      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="user-data-${userId}.json"`);
      res.json(exportData);
    } catch (error) {
      console.error('Export data error:', error);
      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  private static validatePreferences(preferences: Partial<UserPreferences>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (preferences.theme !== undefined) {
      if (!['light', 'dark', 'auto'].includes(preferences.theme)) {
        errors.push('Theme must be one of: light, dark, auto');
      }
    }

    if (preferences.themeName !== undefined) {
      if (typeof preferences.themeName !== 'string' || preferences.themeName.length === 0) {
        errors.push('Theme name must be a non-empty string');
      }
      // Validate against known theme names
      const validThemeNames = ['default', 'darkly', 'flatly', 'cerulean', 'cosmo', 'cyborg', 'journal', 'litera', 'lumen', 'minty', 'pulse', 'sandstone', 'simplex', 'sketchy', 'slate', 'solar', 'spacelab', 'superhero', 'united', 'yeti'];
      if (!validThemeNames.includes(preferences.themeName)) {
        errors.push(`Theme name must be one of: ${validThemeNames.join(', ')}`);
      }
    }

    if (preferences.language !== undefined) {
      if (typeof preferences.language !== 'string' || preferences.language.length === 0) {
        errors.push('Language must be a non-empty string');
      }
    }

    if (preferences.timezone !== undefined) {
      if (typeof preferences.timezone !== 'string' || preferences.timezone.length === 0) {
        errors.push('Timezone must be a non-empty string');
      }
    }

    if (preferences.autoSaveInterval !== undefined) {
      if (typeof preferences.autoSaveInterval !== 'number' || preferences.autoSaveInterval < 1000 || preferences.autoSaveInterval > 300000) {
        errors.push('Auto-save interval must be a number between 1000ms (1s) and 300000ms (5min)');
      }
    }

    if (preferences.notifications !== undefined) {
      const { notifications } = preferences;
      
      if (typeof notifications !== 'object' || notifications === null) {
        errors.push('Notifications must be an object');
      } else {
        if (notifications.email !== undefined && typeof notifications.email !== 'boolean') {
          errors.push('Email notifications setting must be a boolean');
        }
        
        if (notifications.push !== undefined && typeof notifications.push !== 'boolean') {
          errors.push('Push notifications setting must be a boolean');
        }
        
        if (notifications.mentions !== undefined && typeof notifications.mentions !== 'boolean') {
          errors.push('Mentions notifications setting must be a boolean');
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
<template>
  <div class="recent-group-activity-widget">
    <div class="widget-header">
      <h3 class="widget-title">
        <span class="icon">
          <i class="fas fa-users"></i>
        </span>
        Group Activity
      </h3>
      <router-link to="/dashboard/groups" class="view-all-link">
        View All Groups
        <span class="icon">
          <i class="fas fa-arrow-right"></i>
        </span>
      </router-link>
    </div>

    <div class="widget-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-state">
        <div class="loader"></div>
        <p>Loading group activity...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="groupActivities.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-users-slash"></i>
        </div>
        <p>No group activity yet</p>
        <button class="button is-primary is-small" @click="$emit('create-group')">
          Create Your First Group
        </button>
      </div>

      <!-- Group Activity List -->
      <div v-else class="group-activity-list">
        <div v-for="activity in groupActivities" :key="activity.id" class="group-activity-item">
          <div class="group-info">
            <div class="group-avatar">
              <i class="fas fa-users"></i>
            </div>
            <div class="group-details">
              <h4 class="group-name">{{ activity.groupName }}</h4>
              <p class="activity-description">{{ activity.description }}</p>
            </div>
          </div>
          <div class="activity-meta">
            <span class="activity-time">{{ formatTime(activity.timestamp) }}</span>
            <div class="activity-badges">
              <span v-if="activity.isNew" class="badge new">New</span>
              <span v-if="activity.memberCount" class="badge members">
                {{ activity.memberCount }} members
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useGroupsStore } from '../../stores/groups'

const emit = defineEmits<{
  'create-group': []
}>()

const groupsStore = useGroupsStore()
const loading = computed(() => groupsStore.loading)

// Mock group activity data - replace with actual group activity store
const groupActivities = ref([
  {
    id: 1,
    groupName: 'Development Team',
    description: 'Sarah added 3 new notes to the group',
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    memberCount: 8,
    isNew: false
  },
  {
    id: 2,
    groupName: 'Project Alpha',
    description: 'New member joined the group',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    memberCount: 5,
    isNew: false
  },
  {
    id: 3,
    groupName: 'Design Review',
    description: 'Group was created',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
    memberCount: 3,
    isNew: true
  }
])

// Methods
const formatTime = (date: Date): string => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`

  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays}d ago`
}
</script>

<style scoped>
.recent-group-activity-widget {
  background: var(--card-background);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border);
  background: var(--card-header-background);
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text-strong);
  margin: 0;
}

.widget-title .icon {
  color: var(--color-success);
}

.view-all-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-primary);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.view-all-link:hover {
  color: var(--color-primary-dark);
}

.widget-content {
  max-height: 400px;
  overflow-y: auto;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--color-text-muted);
}

.loader {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #28a745;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.empty-icon {
  font-size: 2rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.group-activity-list {
  padding: 1rem 0;
}

.group-activity-item {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--color-border);
  transition: background-color 0.2s ease;
}

.group-activity-item:hover {
  background: var(--color-surface-hover);
}

.group-activity-item:last-child {
  border-bottom: none;
}

.group-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.group-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-success);
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
}

.group-details {
  flex: 1;
  min-width: 0;
}

.group-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-strong);
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.activity-description {
  font-size: 0.75rem;
  color: var(--color-text-muted);
  margin: 0;
  line-height: 1.4;
}

.activity-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--color-text-light);
}

.activity-badges {
  display: flex;
  gap: 0.5rem;
}

.badge {
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge.new {
  background: #d4edda;
  color: #155724;
}

.badge.members {
  background: #e9ecef;
  color: #6c757d;
}
</style>
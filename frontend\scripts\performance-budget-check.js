#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Performance budgets for CI/CD enforcement
const PERFORMANCE_BUDGETS = {
  initialization: {
    target: 600, // Target initialization time (ms)
    warning: 800, // Warning threshold (ms)
    critical: 1000, // Critical threshold (ms)
  },
  coreWebVitals: {
    fcp: 1000, // First Contentful Paint (ms)
    lcp: 1500, // Largest Contentful Paint (ms)
    tti: 2000, // Time to Interactive (ms)
    cls: 0.05, // Cumulative Layout Shift
    fid: 100, // First Input Delay (ms)
  },
};

/**
 * Check if performance metrics meet budget requirements
 */
function checkPerformanceBudgets() {
  console.log('🔍 Checking Performance Budgets...\n');

  // Look for performance metrics in multiple locations
  const possiblePaths = [
    path.join(__dirname, '../dist/performance-metrics.json'),
    path.join(__dirname, '../performance-metrics.json'),
    path.join(process.cwd(), 'performance-metrics.json'),
    path.join(process.cwd(), 'frontend/performance-metrics.json'),
  ];

  let metricsFound = false;
  let metrics = null;

  for (const metricsPath of possiblePaths) {
    if (fs.existsSync(metricsPath)) {
      try {
        const fileContent = fs.readFileSync(metricsPath, 'utf8');
        metrics = JSON.parse(fileContent);
        metricsFound = true;
        console.log(`📊 Found performance metrics at: ${metricsPath}`);
        break;
      } catch (error) {
        console.warn(`⚠️  Could not read metrics from ${metricsPath}:`, error.message);
      }
    }
  }

  if (!metricsFound) {
    console.log('📝 No performance metrics found.');
    console.log('   This is expected for the first build or if metrics collection is disabled.');
    console.log('   Performance budgets will be enforced once metrics are available.\n');
    return { passed: true, reason: 'no-metrics' };
  }

  console.log('📈 Performance Budget Analysis\n');
  console.log('Metric'.padEnd(35) + 'Value'.padEnd(15) + 'Budget'.padEnd(15) + 'Status');
  console.log('-'.repeat(80));

  let budgetsPassed = true;
  const violations = [];

  // Check initialization time
  const initTime = metrics.initialization?.duration || 0;
  if (initTime > 0) {
    let initStatus = '✅ PASS';
    if (initTime > PERFORMANCE_BUDGETS.initialization.critical) {
      initStatus = '❌ CRITICAL';
      budgetsPassed = false;
      violations.push(
        `Initialization time: ${initTime}ms > ${PERFORMANCE_BUDGETS.initialization.critical}ms (critical)`
      );
    } else if (initTime > PERFORMANCE_BUDGETS.initialization.warning) {
      initStatus = '⚠️  WARNING';
      violations.push(
        `Initialization time: ${initTime}ms > ${PERFORMANCE_BUDGETS.initialization.warning}ms (warning)`
      );
    }

    console.log(
      'App Initialization'.padEnd(35) +
        `${initTime.toFixed(2)}ms`.padEnd(15) +
        `${PERFORMANCE_BUDGETS.initialization.target}ms`.padEnd(15) +
        initStatus
    );
  }

  // Check store initialization time
  const storeInitTime = metrics.initialization?.storeInitTime || 0;
  if (storeInitTime > 0) {
    const storeStatus = storeInitTime <= 300 ? '✅ PASS' : '❌ FAIL';
    if (storeInitTime > 300) {
      budgetsPassed = false;
      violations.push(`Store initialization: ${storeInitTime}ms > 300ms`);
    }

    console.log(
      'Store Initialization'.padEnd(35) +
        `${storeInitTime.toFixed(2)}ms`.padEnd(15) +
        '300ms'.padEnd(15) +
        storeStatus
    );
  }

  // Check DOM ready time
  const domReadyTime = metrics.initialization?.domReadyTime || 0;
  if (domReadyTime > 0) {
    const domStatus = domReadyTime <= 200 ? '✅ PASS' : '❌ FAIL';
    if (domReadyTime > 200) {
      budgetsPassed = false;
      violations.push(`DOM ready time: ${domReadyTime}ms > 200ms`);
    }

    console.log(
      'DOM Ready'.padEnd(35) +
        `${domReadyTime.toFixed(2)}ms`.padEnd(15) +
        '200ms'.padEnd(15) +
        domStatus
    );
  }

  // Check Core Web Vitals
  if (metrics.coreWebVitals) {
    console.log('\n🌐 Core Web Vitals Budget Analysis\n');
    console.log('Metric'.padEnd(35) + 'Value'.padEnd(15) + 'Budget'.padEnd(15) + 'Status');
    console.log('-'.repeat(80));

    const vitals = metrics.coreWebVitals;

    Object.entries(PERFORMANCE_BUDGETS.coreWebVitals).forEach(([metric, budget]) => {
      const value = vitals[metric] || 0;
      if (value > 0) {
        const status = value <= budget ? '✅ PASS' : '❌ FAIL';
        const unit = metric === 'cls' ? '' : 'ms';

        if (value > budget) {
          budgetsPassed = false;
          violations.push(`${metric.toUpperCase()}: ${value.toFixed(2)}${unit} > ${budget}${unit}`);
        }

        console.log(
          metric.toUpperCase().padEnd(35) +
            `${value.toFixed(2)}${unit}`.padEnd(15) +
            `${budget}${unit}`.padEnd(15) +
            status
        );
      }
    });
  }

  // Check for existing budget violations from the app
  if (metrics.budgetViolations && metrics.budgetViolations.length > 0) {
    console.log('\n⚠️  Additional Budget Violations from App:');
    metrics.budgetViolations.forEach(violation => {
      console.log(`   • ${violation}`);
      violations.push(violation);
    });
    budgetsPassed = false;
  }

  console.log('\n' + '='.repeat(80));

  if (budgetsPassed) {
    console.log('✅ All performance budgets PASSED!');
    console.log('   Application meets all performance targets.');
    return { passed: true, violations: [] };
  } else {
    console.log('❌ Performance budget violations detected!');
    console.log(`   ${violations.length} violation(s) found:`);
    violations.forEach(violation => {
      console.log(`   • ${violation}`);
    });
    return { passed: false, violations };
  }
}

/**
 * Generate performance recommendations
 */
function generateRecommendations(violations) {
  if (violations.length === 0) return;

  console.log('\n💡 Performance Optimization Recommendations:\n');

  violations.forEach(violation => {
    if (violation.includes('Initialization time')) {
      console.log('🚀 Initialization Time Optimization:');
      console.log('   • Implement parallel store initialization');
      console.log('   • Use progressive loading (critical → secondary → background)');
      console.log('   • Defer service worker registration');
      console.log('   • Move non-critical DOM operations to nextTick');
    } else if (violation.includes('Store initialization')) {
      console.log('🏪 Store Initialization Optimization:');
      console.log('   • Use Promise.all for parallel store loading');
      console.log('   • Implement timeout handling for auth store');
      console.log('   • Lazy load non-critical stores');
    } else if (violation.includes('FCP') || violation.includes('LCP')) {
      console.log('🎨 Paint Metrics Optimization:');
      console.log('   • Optimize critical rendering path');
      console.log('   • Reduce bundle size and implement code splitting');
      console.log('   • Preload critical resources');
      console.log('   • Optimize images and fonts');
    } else if (violation.includes('TTI')) {
      console.log('⚡ Time to Interactive Optimization:');
      console.log('   • Reduce JavaScript execution time');
      console.log('   • Implement progressive enhancement');
      console.log('   • Defer non-critical JavaScript');
    } else if (violation.includes('CLS')) {
      console.log('📐 Layout Shift Optimization:');
      console.log('   • Reserve space for dynamic content');
      console.log('   • Use CSS aspect-ratio for images');
      console.log('   • Avoid inserting content above existing content');
    } else if (violation.includes('FID')) {
      console.log('👆 Input Delay Optimization:');
      console.log('   • Break up long-running JavaScript tasks');
      console.log('   • Use web workers for heavy computations');
      console.log('   • Implement code splitting and lazy loading');
    }
    console.log('');
  });
}

/**
 * Main execution
 */
function main() {
  const result = checkPerformanceBudgets();

  if (!result.passed && result.violations) {
    generateRecommendations(result.violations);

    // Exit with error code for CI/CD
    console.log('❌ Performance budget check FAILED');
    process.exit(1);
  } else {
    console.log('✅ Performance budget check PASSED');
    process.exit(0);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { checkPerformanceBudgets, generateRecommendations };

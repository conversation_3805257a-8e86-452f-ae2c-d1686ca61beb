import { describe, it, expect } from 'vitest'
import { createRouter, createWebHistory } from 'vue-router'
import router from '../index'

describe('Board Routes', () => {
  it('should have board view route', () => {
    const routes = router.getRoutes()
    const boardRoute = routes.find(route => route.name === 'BoardView')
    
    expect(boardRoute).toBeDefined()
    expect(boardRoute?.path).toBe('/board/:id')
  })

  it('should have shared board view route', () => {
    const routes = router.getRoutes()
    const sharedBoardRoute = routes.find(route => route.name === 'SharedBoardView')
    
    expect(sharedBoardRoute).toBeDefined()
    expect(sharedBoardRoute?.path).toBe('/board/:id/share/:token')
  })

  it('should match board URLs correctly', async () => {
    const testRouter = createRouter({
      history: createWebHistory(),
      routes: router.getRoutes()
    })

    // Test board view route
    const boardMatch = testRouter.resolve('/board/board-1')
    expect(boardMatch.name).toBe('BoardView')
    expect(boardMatch.params.id).toBe('board-1')

    // Test shared board route
    const sharedBoardMatch = testRouter.resolve('/board/board-1/share/vlznbo4hbo')
    expect(sharedBoardMatch.name).toBe('SharedBoardView')
    expect(sharedBoardMatch.params.id).toBe('board-1')
    expect(sharedBoardMatch.params.token).toBe('vlznbo4hbo')
  })
})
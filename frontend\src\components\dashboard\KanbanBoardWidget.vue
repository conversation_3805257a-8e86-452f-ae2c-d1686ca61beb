<template>
  <div class="kanban-board-widget">
    <div class="widget-header">
      <div class="widget-title">
        <span class="icon">
          <i class="fas fa-columns"></i>
        </span>
        <span>Kanban Boards</span>
      </div>
      <div class="widget-actions">
        <button class="button is-small is-primary" @click="createNewBoard">
          <span class="icon is-small">
            <i class="fas fa-plus"></i>
          </span>
          <span>New Board</span>
        </button>
      </div>
    </div>

    <div class="widget-content">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <div class="spinner"></div>
        <p>Loading boards...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="boards.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-columns"></i>
        </div>
        <h3>No Kanban Boards Yet</h3>
        <p>Create your first board to organize tasks and projects visually.</p>
        <button class="button is-primary" @click="createNewBoard">
          <span class="icon">
            <i class="fas fa-plus"></i>
          </span>
          <span>Create Your First Board</span>
        </button>
      </div>

      <!-- Boards List -->
      <div v-else class="boards-list">
        <div v-for="board in recentBoards" :key="board.id" class="board-card" @click="openBoard(board.id)">
          <div class="board-header">
            <h4 class="board-title">{{ board.title }}</h4>
            <div class="board-actions">
              <button class="button is-small is-ghost" @click.stop="showBoardMenu(board)" title="Board options">
                <i class="fas fa-ellipsis-h"></i>
              </button>
            </div>
          </div>

          <p v-if="board.description" class="board-description">
            {{ truncateText(board.description, 80) }}
          </p>

          <div class="board-stats">
            <div class="stat-item">
              <span class="icon is-small">
                <i class="fas fa-columns"></i>
              </span>
              <span>{{ board.columns.length }} columns</span>
            </div>
            <div class="stat-item">
              <span class="icon is-small">
                <i class="fas fa-sticky-note"></i>
              </span>
              <span>{{ getTotalCards(board) }} cards</span>
            </div>
            <div class="stat-item">
              <span class="icon is-small">
                <i class="fas fa-users"></i>
              </span>
              <span>{{ board.members.length }} members</span>
            </div>
          </div>

          <div class="board-footer">
            <div class="board-members">
              <div v-for="member in board.members.slice(0, 3)" :key="member.id" class="member-avatar"
                :title="member.name">
                <img v-if="member.avatar" :src="member.avatar" :alt="member.name" />
                <span v-else>{{ member.name.charAt(0).toUpperCase() }}</span>
              </div>
              <div v-if="board.members.length > 3" class="extra-members">
                +{{ board.members.length - 3 }}
              </div>
            </div>
            <div class="board-updated">
              {{ formatDate(board.updatedAt) }}
            </div>
          </div>
        </div>

        <!-- View All Boards Link -->
        <div v-if="boards.length > 3" class="view-all-link">
          <router-link to="/boards" class="button is-text">
            View all {{ boards.length }} boards
            <span class="icon is-small">
              <i class="fas fa-arrow-right"></i>
            </span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- Board Menu Dropdown -->
    <div v-if="selectedBoard && showMenu" class="board-menu-overlay" @click="closeMenu">
      <div class="board-menu" @click.stop>
        <div class="menu-item" @click="openBoard(selectedBoard.id)">
          <i class="fas fa-eye"></i>
          Open Board
        </div>
        <div class="menu-item" @click="editBoard(selectedBoard)">
          <i class="fas fa-edit"></i>
          Edit Board
        </div>
        <div class="menu-item" @click="shareBoard(selectedBoard)">
          <i class="fas fa-share"></i>
          Share Board
        </div>
        <hr class="menu-divider">
        <div class="menu-item is-danger" @click="deleteBoard(selectedBoard)">
          <i class="fas fa-trash"></i>
          Delete Board
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useKanbanStore } from '@/stores/kanban'
import { useNotificationStore } from '@/stores/notifications'
import type { KanbanBoard } from '@/types/kanban'

const router = useRouter()
const kanbanStore = useKanbanStore()
const notificationStore = useNotificationStore()

// Reactive state
const isLoading = ref(false)
const selectedBoard = ref<KanbanBoard | null>(null)
const showMenu = ref(false)

// Computed
const boards = computed(() => kanbanStore.boards)
const recentBoards = computed(() => boards.value.slice(0, 3))

// Methods
const loadBoards = async () => {
  isLoading.value = true
  try {
    await kanbanStore.fetchBoards()
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to load kanban boards',
      read: false
    })
  } finally {
    isLoading.value = false
  }
}

const createNewBoard = () => {
  router.push('/boards/new')
}

const openBoard = (boardId: string) => {
  router.push(`/boards/${boardId}`)
  closeMenu()
}

const showBoardMenu = (board: KanbanBoard) => {
  selectedBoard.value = board
  showMenu.value = true
}

const closeMenu = () => {
  selectedBoard.value = null
  showMenu.value = false
}

const editBoard = (board: KanbanBoard) => {
  router.push(`/boards/${board.id}/settings`)
  closeMenu()
}

const shareBoard = (board: KanbanBoard) => {
  // TODO: Implement share functionality
  notificationStore.addNotification({
    type: 'info',
    category: 'system',
    title: 'Info',
    message: 'Share functionality coming soon!',
    read: false
  })
  closeMenu()
}

const deleteBoard = async (board: KanbanBoard) => {
  if (confirm(`Are you sure you want to delete "${board.title}"? This action cannot be undone.`)) {
    try {
      await kanbanStore.deleteBoard(board.id)
      notificationStore.addNotification({
        type: 'success',
        category: 'system',
        title: 'Success',
        message: 'Board deleted successfully',
        read: false
      })
    } catch (error) {
      notificationStore.addNotification({
        type: 'critical',
        category: 'system',
        title: 'Error',
        message: 'Failed to delete board',
        read: false
      })
    }
  }
  closeMenu()
}

const getTotalCards = (board: KanbanBoard) => {
  return board.columns.reduce((total, column) => total + column.cards.length, 0)
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) return 'Yesterday'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`

  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
  })
}

// Lifecycle
onMounted(() => {
  loadBoards()
})
</script>

<style scoped>
.kanban-board-widget {
  background: var(--card-background);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  box-shadow: var(--shadow);
  overflow: hidden;
  position: relative;
}

.widget-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--color-border);
  background: var(--card-header-background);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.widget-title .icon {
  color: #007bff;
}

.widget-content {
  padding: 1.5rem;
  min-height: 200px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6c757d;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.empty-state {
  text-align: center;
  padding: 2rem;
}

.empty-icon {
  font-size: 3rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: #6c757d;
  margin-bottom: 1.5rem;
}

.boards-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.board-card {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.board-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
  transform: translateY(-1px);
}

.board-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.board-title {
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  flex: 1;
}

.board-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.board-card:hover .board-actions {
  opacity: 1;
}

.board-description {
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.board-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #6c757d;
}

.board-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.board-members {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.member-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  overflow: hidden;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.extra-members {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #6c757d;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.6rem;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.board-updated {
  font-size: 0.75rem;
  color: #6c757d;
}

.view-all-link {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #f1f3f4;
}

.board-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.board-menu {
  background: white;
  border-radius: 6px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  min-width: 200px;
  overflow: hidden;
}

.menu-item {
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background: #f8f9fa;
}

.menu-item.is-danger {
  color: #dc3545;
}

.menu-item.is-danger:hover {
  background: #f8d7da;
}

.menu-divider {
  margin: 0;
  border: none;
  border-top: 1px solid #e9ecef;
}

/* Responsive */
@media (max-width: 768px) {
  .widget-header {
    padding: 1rem;
  }

  .widget-content {
    padding: 1rem;
  }

  .board-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .board-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>
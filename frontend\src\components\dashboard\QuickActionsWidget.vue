<template>
  <div class="quick-actions-widget">
    <div class="widget-content">
      <div class="actions-grid">
        <!-- Create Note -->
        <button class="action-button primary" @click="$emit('create-note')">
          <div class="action-icon">
            <i class="fas fa-plus"></i>
          </div>
          <div class="action-content">
            <h4 class="action-title">New Note</h4>
            <p class="action-description">Create a new note</p>
          </div>
        </button>

        <!-- Create Group -->
        <button class="action-button success" @click="$emit('create-group')">
          <div class="action-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="action-content">
            <h4 class="action-title">New Group</h4>
            <p class="action-description">Create a collaboration group</p>
          </div>
        </button>

        <!-- Search -->
        <button class="action-button info" @click="$emit('open-search')">
          <div class="action-icon">
            <i class="fas fa-search"></i>
          </div>
          <div class="action-content">
            <h4 class="action-title">Search</h4>
            <p class="action-description">Find notes and content</p>
          </div>
        </button>

        <!-- Settings -->
        <button class="action-button secondary" @click="$emit('open-settings')">
          <div class="action-icon">
            <i class="fas fa-cog"></i>
          </div>
          <div class="action-content">
            <h4 class="action-title">Settings</h4>
            <p class="action-description">Customize your experience</p>
          </div>
        </button>
      </div>


    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits<{
  'create-note': []
  'create-group': []
  'open-search': []
  'open-settings': []
}>()
</script>

<style scoped>
.quick-actions-widget {
  background: var(--card-background);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.widget-content {
  padding: 1.5rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  background: var(--color-surface);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  background: var(--color-surface-hover);
}

.action-button.primary {
  border-color: #007bff;
}

.action-button.primary:hover {
  background: #f0f8ff;
  border-color: #0056b3;
}

.action-button.success {
  border-color: #28a745;
}

.action-button.success:hover {
  background: #f0fff4;
  border-color: #1e7e34;
}

.action-button.info {
  border-color: #17a2b8;
}

.action-button.info:hover {
  background: #f0fdff;
  border-color: #117a8b;
}

.action-button.secondary {
  border-color: #6c757d;
}

.action-button.secondary:hover {
  background: #f8f9fa;
  border-color: #545b62;
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.action-button.primary .action-icon {
  background: #007bff;
  color: white;
}

.action-button.success .action-icon {
  background: #28a745;
  color: white;
}

.action-button.info .action-icon {
  background: #17a2b8;
  color: white;
}

.action-button.secondary .action-icon {
  background: #6c757d;
  color: white;
}

.action-content {
  flex: 1;
  min-width: 0;
}

.action-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-strong);
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.action-description {
  font-size: 0.75rem;
  color: var(--color-text);
  margin: 0;
  line-height: 1.3;
}



/* Responsive */
@media (max-width: 768px) {
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-button {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .action-icon {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }


}

@media (max-width: 480px) {
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
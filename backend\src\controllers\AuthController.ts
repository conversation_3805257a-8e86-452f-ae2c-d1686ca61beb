import { Request, Response } from 'express';
import { OAuth2Client } from 'google-auth-library';
import { UserRepository } from '../repositories/UserRepository';
import { UserModel, CreateUserData, LoginCredentials } from '../models/User';
import { JWTUtils } from '../utils/jwt';
import { AuthenticatedRequest } from '../middleware/auth';

export class AuthController {
  private static googleClient = new OAuth2Client(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_REDIRECT_URI
  );
  static async register(req: Request, res: Response): Promise<void> {
    try {
      const { email, password, display_name, avatar_url } = req.body;

      // Check if user already exists
      const existingUser = await UserRepository.findByEmail(email);
      if (existingUser) {
        res.status(409).json({
          error: 'User already exists',
          code: 'USER_EXISTS'
        });
        return;
      }

      // Create user data
      const userData: CreateUserData = {
        email,
        password,
        display_name,
        avatar_url
      };

      // Create user
      const user = await UserRepository.create(userData);

      // Generate tokens
      const tokens = JWTUtils.generateTokenPair(user.id, user.email);

      // Generate email verification token
      const emailVerificationToken = JWTUtils.generateEmailVerificationToken(user.id, user.email);

      // TODO: Send verification email (will be implemented in future tasks)
      console.log(`Email verification token for ${user.email}: ${emailVerificationToken}`);

      // Return user data (without password hash) and tokens
      const { password_hash, ...userWithoutPassword } = user;
      
      res.status(201).json({
        message: 'User registered successfully',
        user: userWithoutPassword,
        tokens,
        emailVerificationRequired: true
      });
    } catch (error) {
      console.error('Registration error:', error);
      
      if (error instanceof Error && error.message === 'Email already exists') {
        res.status(409).json({
          error: 'Email already exists',
          code: 'EMAIL_EXISTS'
        });
        return;
      }

      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { email, password }: LoginCredentials = req.body;

      // Find user by email
      const user = await UserRepository.findByEmail(email);
      if (!user) {
        res.status(401).json({
          error: 'Invalid credentials',
          code: 'INVALID_CREDENTIALS'
        });
        return;
      }

      // Verify password
      const isPasswordValid = await UserModel.comparePassword(password, user.password_hash);
      if (!isPasswordValid) {
        res.status(401).json({
          error: 'Invalid credentials',
          code: 'INVALID_CREDENTIALS'
        });
        return;
      }

      // Generate tokens
      const tokens = JWTUtils.generateTokenPair(user.id, user.email);

      // Return user data (without password hash) and tokens
      const { password_hash, ...userWithoutPassword } = user;
      
      res.json({
        message: 'Login successful',
        user: userWithoutPassword,
        tokens,
        emailVerificationRequired: !user.email_verified
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;

      // Verify refresh token
      const payload = JWTUtils.verifyRefreshToken(refreshToken);

      // Verify user still exists
      const user = await UserRepository.findById(payload.userId);
      if (!user) {
        res.status(401).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      // Generate new token pair
      const tokens = JWTUtils.generateTokenPair(user.id, user.email);

      res.json({
        message: 'Token refreshed successfully',
        tokens
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(401).json({
        error: 'Invalid or expired refresh token',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }
  }

  static async requestPasswordReset(req: Request, res: Response): Promise<void> {
    try {
      const { email } = req.body;

      // Find user by email
      const user = await UserRepository.findByEmail(email);
      if (!user) {
        // Don't reveal if email exists or not for security
        res.json({
          message: 'If the email exists, a password reset link has been sent'
        });
        return;
      }

      // Generate password reset token
      const resetToken = JWTUtils.generatePasswordResetToken(user.id, user.email);

      // TODO: Send password reset email (will be implemented in future tasks)
      console.log(`Password reset token for ${user.email}: ${resetToken}`);

      res.json({
        message: 'If the email exists, a password reset link has been sent'
      });
    } catch (error) {
      console.error('Password reset request error:', error);
      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async resetPassword(req: Request, res: Response): Promise<void> {
    try {
      const { token, password } = req.body;

      // Verify reset token
      const { userId, email } = JWTUtils.verifyPasswordResetToken(token);

      // Verify user still exists
      const user = await UserRepository.findById(userId);
      if (!user || user.email !== email) {
        res.status(400).json({
          error: 'Invalid or expired reset token',
          code: 'INVALID_RESET_TOKEN'
        });
        return;
      }

      // Update password
      await UserRepository.updatePassword(userId, password);

      res.json({
        message: 'Password reset successfully'
      });
    } catch (error) {
      console.error('Password reset error:', error);
      
      if (error instanceof Error && error.message.includes('Invalid or expired')) {
        res.status(400).json({
          error: 'Invalid or expired reset token',
          code: 'INVALID_RESET_TOKEN'
        });
        return;
      }

      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async verifyEmail(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.params;

      // Verify email verification token
      const { userId, email } = JWTUtils.verifyEmailVerificationToken(token);

      // Verify user still exists
      const user = await UserRepository.findById(userId);
      if (!user || user.email !== email) {
        res.status(400).json({
          error: 'Invalid or expired verification token',
          code: 'INVALID_VERIFICATION_TOKEN'
        });
        return;
      }

      // Check if already verified
      if (user.email_verified) {
        res.json({
          message: 'Email already verified'
        });
        return;
      }

      // Verify email
      await UserRepository.verifyEmail(userId);

      res.json({
        message: 'Email verified successfully'
      });
    } catch (error) {
      console.error('Email verification error:', error);
      
      if (error instanceof Error && error.message.includes('Invalid or expired')) {
        res.status(400).json({
          error: 'Invalid or expired verification token',
          code: 'INVALID_VERIFICATION_TOKEN'
        });
        return;
      }

      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async resendVerificationEmail(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id;

      // Get user
      const user = await UserRepository.findById(userId);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      // Check if already verified
      if (user.email_verified) {
        res.json({
          message: 'Email already verified'
        });
        return;
      }

      // Generate new verification token
      const emailVerificationToken = JWTUtils.generateEmailVerificationToken(user.id, user.email);

      // TODO: Send verification email (will be implemented in future tasks)
      console.log(`Email verification token for ${user.email}: ${emailVerificationToken}`);

      res.json({
        message: 'Verification email sent'
      });
    } catch (error) {
      console.error('Resend verification email error:', error);
      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async getProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id;

      // Get user
      const user = await UserRepository.findById(userId);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      // Return user data (without password hash)
      const { password_hash, ...userWithoutPassword } = user;
      
      res.json({
        user: userWithoutPassword
      });
    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async logout(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // In a production app, you might want to blacklist the JWT token
      // For now, we'll just return a success message
      // The client should remove the token from storage
      
      res.json({
        message: 'Logged out successfully'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async googleAuth(req: Request, res: Response): Promise<void> {
    try {
      const { credential } = req.body;

      if (!credential) {
        res.status(400).json({
          error: 'Google credential is required',
          code: 'MISSING_CREDENTIAL'
        });
        return;
      }

      // Verify the Google ID token
      const ticket = await AuthController.googleClient.verifyIdToken({
        idToken: credential,
        audience: process.env.GOOGLE_CLIENT_ID
      });

      const payload = ticket.getPayload();
      if (!payload) {
        res.status(400).json({
          error: 'Invalid Google credential',
          code: 'INVALID_CREDENTIAL'
        });
        return;
      }

      const { email, name, picture, email_verified } = payload;

      if (!email) {
        res.status(400).json({
          error: 'Email not provided by Google',
          code: 'MISSING_EMAIL'
        });
        return;
      }

      // Check if user already exists
      let user = await UserRepository.findByEmail(email);

      if (user) {
        // User exists, log them in
        const tokens = JWTUtils.generateTokenPair(user.id, user.email);

        const { password_hash, ...userWithoutPassword } = user;
        
        res.json({
          message: 'Google login successful',
          user: userWithoutPassword,
          tokens,
          emailVerificationRequired: false // Google emails are pre-verified
        });
      } else {
        // Create new user
        const userData: CreateUserData = {
          email,
          password: '', // No password for OAuth users
          display_name: name || email.split('@')[0],
          avatar_url: picture,
          email_verified: email_verified || false,
          oauth_provider: 'google',
          oauth_id: payload.sub
        };

        user = await UserRepository.create(userData);

        // Generate tokens
        const tokens = JWTUtils.generateTokenPair(user.id, user.email);

        const { password_hash, ...userWithoutPassword } = user;
        
        res.status(201).json({
          message: 'Google account created and logged in successfully',
          user: userWithoutPassword,
          tokens,
          emailVerificationRequired: false
        });
      }
    } catch (error) {
      console.error('Google auth error:', error);
      
      if (error instanceof Error && error.message.includes('Token used too late')) {
        res.status(400).json({
          error: 'Google token expired',
          code: 'TOKEN_EXPIRED'
        });
        return;
      }

      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async googleAuthUrl(req: Request, res: Response): Promise<void> {
    try {
      const authUrl = AuthController.googleClient.generateAuthUrl({
        access_type: 'offline',
        scope: ['profile', 'email'],
        prompt: 'consent'
      });

      res.json({
        authUrl
      });
    } catch (error) {
      console.error('Google auth URL generation error:', error);
      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  static async googleCallback(req: Request, res: Response): Promise<void> {
    try {
      const { code } = req.query;

      if (!code || typeof code !== 'string') {
        res.status(400).json({
          error: 'Authorization code is required',
          code: 'MISSING_CODE'
        });
        return;
      }

      // Exchange authorization code for tokens
      const { tokens } = await AuthController.googleClient.getToken(code);
      AuthController.googleClient.setCredentials(tokens);

      // Get user info from Google
      const userInfoResponse = await AuthController.googleClient.request({
        url: 'https://www.googleapis.com/oauth2/v2/userinfo'
      });

      const userInfo = userInfoResponse.data as any;
      const { email, name, picture, verified_email } = userInfo;

      if (!email) {
        res.status(400).json({
          error: 'Email not provided by Google',
          code: 'MISSING_EMAIL'
        });
        return;
      }

      // Check if user already exists
      let user = await UserRepository.findByEmail(email);

      if (user) {
        // User exists, log them in
        const jwtTokens = JWTUtils.generateTokenPair(user.id, user.email);

        // Redirect to frontend with tokens
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
        res.redirect(`${frontendUrl}/auth/callback?token=${jwtTokens.accessToken}&refresh=${jwtTokens.refreshToken}`);
      } else {
        // Create new user
        const userData: CreateUserData = {
          email,
          password: '', // No password for OAuth users
          display_name: name || email.split('@')[0],
          avatar_url: picture,
          email_verified: verified_email || false,
          oauth_provider: 'google',
          oauth_id: userInfo.id
        };

        user = await UserRepository.create(userData);

        // Generate tokens
        const jwtTokens = JWTUtils.generateTokenPair(user.id, user.email);

        // Redirect to frontend with tokens
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
        res.redirect(`${frontendUrl}/auth/callback?token=${jwtTokens.accessToken}&refresh=${jwtTokens.refreshToken}`);
      }
    } catch (error) {
      console.error('Google callback error:', error);
      
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      res.redirect(`${frontendUrl}/auth/error?message=Authentication failed`);
    }
  }
}
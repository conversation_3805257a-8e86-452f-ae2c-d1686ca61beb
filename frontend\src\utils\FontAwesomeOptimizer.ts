/**
 * FontAwesome Optimization Utility
 * Handles dynamic loading and theme integration for FontAwesome icons
 */

export interface FontAwesomeConfig {
  preloadFonts: boolean
  lazyLoadBrands: boolean
  enableAnimations: boolean
  respectReducedMotion: boolean
}

export class FontAwesomeOptimizer {
  private config: FontAwesomeConfig
  private loadedFonts = new Set<string>()
  private preloadPromises = new Map<string, Promise<void>>()

  constructor(config: Partial<FontAwesomeConfig> = {}) {
    this.config = {
      preloadFonts: true,
      lazyLoadBrands: true,
      enableAnimations: true,
      respectReducedMotion: true,
      ...config
    }
  }

  /**
   * Initialize FontAwesome optimization
   */
  async initialize(): Promise<void> {
    // Preload critical fonts
    if (this.config.preloadFonts) {
      await this.preloadCriticalFonts()
    }

    // Set up reduced motion handling
    if (this.config.respectReducedMotion) {
      this.setupReducedMotionHandling()
    }

    // Initialize theme integration
    this.setupThemeIntegration()
  }

  /**
   * Preload critical FontAwesome fonts
   */
  private async preloadCriticalFonts(): Promise<void> {
    const criticalFonts = [
      '/node_modules/@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff2',
      '/node_modules/@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff2'
    ]

    const preloadPromises = criticalFonts.map(fontUrl => this.preloadFont(fontUrl))
    await Promise.allSettled(preloadPromises)
  }

  /**
   * Preload a specific font file
   */
  private async preloadFont(fontUrl: string): Promise<void> {
    if (this.loadedFonts.has(fontUrl)) {
      return
    }

    if (this.preloadPromises.has(fontUrl)) {
      return this.preloadPromises.get(fontUrl)!
    }

    const preloadPromise = this.loadFontFile(fontUrl)
    this.preloadPromises.set(fontUrl, preloadPromise)

    try {
      await preloadPromise
      this.loadedFonts.add(fontUrl)
    } finally {
      this.preloadPromises.delete(fontUrl)
    }
  }

  /**
   * Load font file using Font Loading API or fallback
   */
  private async loadFontFile(fontUrl: string): Promise<void> {
    try {
      // Use Font Loading API if available
      if ('fonts' in document) {
        const fontFace = new FontFace('Font Awesome 6 Free', `url(${fontUrl})`)
        await fontFace.load()
        document.fonts.add(fontFace)
        return
      }

      // Fallback: create link element for preloading
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = fontUrl
      link.as = 'font'
      link.type = 'font/woff2'
      link.crossOrigin = 'anonymous'

      return new Promise((resolve, reject) => {
        link.onload = () => resolve()
        link.onerror = () => reject(new Error(`Failed to preload font: ${fontUrl}`))
        document.head.appendChild(link)
      })
    } catch (error) {
      console.warn(`Failed to preload FontAwesome font ${fontUrl}:`, error)
    }
  }

  /**
   * Lazy load brand fonts when needed
   */
  async loadBrandFonts(): Promise<void> {
    if (!this.config.lazyLoadBrands) {
      return
    }

    const brandFontUrl = '/node_modules/@fortawesome/fontawesome-free/webfonts/fa-brands-400.woff2'
    await this.preloadFont(brandFontUrl)
  }

  /**
   * Set up reduced motion handling
   */
  private setupReducedMotionHandling(): void {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    
    const handleReducedMotion = (e: MediaQueryListEvent | MediaQueryList) => {
      const shouldReduceMotion = e.matches
      
      // Update CSS custom property
      document.documentElement.style.setProperty(
        '--fa-animation-duration',
        shouldReduceMotion ? '0.01ms' : '2s'
      )

      // Disable animations if reduced motion is preferred
      if (shouldReduceMotion) {
        this.disableAnimations()
      } else if (this.config.enableAnimations) {
        this.enableAnimations()
      }
    }

    // Initial check
    handleReducedMotion(mediaQuery)
    
    // Listen for changes
    mediaQuery.addEventListener('change', handleReducedMotion)
  }

  /**
   * Disable FontAwesome animations
   */
  private disableAnimations(): void {
    const style = document.createElement('style')
    style.id = 'fa-reduced-motion'
    style.textContent = `
      .fa-spin, .fa-pulse {
        animation: none !important;
      }
    `
    document.head.appendChild(style)
  }

  /**
   * Enable FontAwesome animations
   */
  private enableAnimations(): void {
    const existingStyle = document.getElementById('fa-reduced-motion')
    if (existingStyle) {
      existingStyle.remove()
    }
  }

  /**
   * Set up theme integration for FontAwesome
   */
  private setupThemeIntegration(): void {
    // Watch for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
          this.updateIconColorsForTheme()
        }
      })
    })

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    })

    // Initial theme setup
    this.updateIconColorsForTheme()
  }

  /**
   * Update icon colors based on current theme
   */
  private updateIconColorsForTheme(): void {
    const currentTheme = document.documentElement.getAttribute('data-theme')
    
    if (!currentTheme) {
      return
    }

    // Get computed theme colors
    const computedStyle = getComputedStyle(document.documentElement)
    
    // Update icon color custom properties
    const iconColorMappings = {
      '--icon-color-default': '--color-text',
      '--icon-color-muted': '--color-text-muted',
      '--icon-color-primary': '--color-primary',
      '--icon-color-success': '--color-success',
      '--icon-color-danger': '--color-danger',
      '--icon-color-warning': '--color-warning-dark',
      '--icon-color-info': '--color-info'
    }

    Object.entries(iconColorMappings).forEach(([iconProp, themeProp]) => {
      const themeColor = computedStyle.getPropertyValue(themeProp).trim()
      if (themeColor) {
        document.documentElement.style.setProperty(iconProp, themeColor)
      }
    })
  }

  /**
   * Optimize icon rendering performance
   */
  optimizeIconRendering(): void {
    // Use CSS containment for better performance
    const style = document.createElement('style')
    style.textContent = `
      .fas, .far, .fab {
        contain: layout style;
        will-change: color;
      }
      
      .fa-spin {
        contain: layout style;
        will-change: transform;
      }
      
      .fa-pulse {
        contain: layout style;
        will-change: opacity;
      }
    `
    document.head.appendChild(style)
  }

  /**
   * Get icon usage statistics for optimization
   */
  getIconUsageStats(): Record<string, number> {
    const iconElements = document.querySelectorAll('.fas, .far, .fab')
    const usage: Record<string, number> = {}

    iconElements.forEach(element => {
      const classes = Array.from(element.classList)
      const iconClass = classes.find(cls => cls.startsWith('fa-') && cls !== 'fas' && cls !== 'far' && cls !== 'fab')
      
      if (iconClass) {
        usage[iconClass] = (usage[iconClass] || 0) + 1
      }
    })

    return usage
  }

  /**
   * Generate optimized FontAwesome CSS based on usage
   */
  generateOptimizedCSS(usageStats: Record<string, number>): string {
    const usedIcons = Object.keys(usageStats)
    
    // Generate CSS with only used icons
    let css = `
      /* Optimized FontAwesome CSS - Generated based on usage */
      .fas, .far, .fab {
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        display: inline-block;
        font-style: normal;
        font-variant: normal;
        text-rendering: auto;
        line-height: 1;
        color: var(--icon-color-default);
        transition: var(--icon-transition);
      }
      
      .fas { font-family: 'Font Awesome 6 Free'; font-weight: 900; }
      .far { font-family: 'Font Awesome 6 Free'; font-weight: 400; }
      .fab { font-family: 'Font Awesome 6 Brands'; font-weight: 400; }
    `

    // Add only used icon definitions
    const iconDefinitions: Record<string, string> = {
      'fa-plus': '\\f067',
      'fa-search': '\\f002',
      'fa-times': '\\f00d',
      'fa-edit': '\\f044',
      'fa-trash': '\\f1f8',
      'fa-save': '\\f0c7',
      'fa-home': '\\f015',
      'fa-user': '\\f007',
      'fa-cog': '\\f013',
      // Add more as needed
    }

    usedIcons.forEach(iconClass => {
      if (iconDefinitions[iconClass]) {
        css += `.${iconClass}:before { content: '${iconDefinitions[iconClass]}'; }\n`
      }
    })

    return css
  }

  /**
   * Clean up unused FontAwesome resources
   */
  cleanup(): void {
    // Remove unused preload links
    const preloadLinks = document.querySelectorAll('link[rel="preload"][as="font"]')
    preloadLinks.forEach(link => {
      const href = (link as HTMLLinkElement).href
      if (href.includes('fontawesome') && !this.loadedFonts.has(href)) {
        link.remove()
      }
    })

    // Clear caches
    this.loadedFonts.clear()
    this.preloadPromises.clear()
  }

  /**
   * Get optimization statistics
   */
  getStats(): {
    loadedFonts: number
    preloadingFonts: number
    config: FontAwesomeConfig
  } {
    return {
      loadedFonts: this.loadedFonts.size,
      preloadingFonts: this.preloadPromises.size,
      config: { ...this.config }
    }
  }
}

// Export singleton instance
export const fontAwesomeOptimizer = new FontAwesomeOptimizer()
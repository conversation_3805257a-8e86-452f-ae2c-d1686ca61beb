import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import AdminPerformanceView from '../AdminPerformanceView.vue'
import { performanceApiService } from '../../services/performanceApiService'

// Mock the performance API service
vi.mock('../../services/performanceApiService', () => ({
  performanceApiService: {
    getOverview: vi.fn(),
    getSlowQueries: vi.fn(),
    getSystemStats: vi.fn(),
    getReport: vi.fn(),
    generateRecommendations: vi.fn(),
    formatBytes: vi.fn((bytes: number) => `${bytes} B`),
    formatUptime: vi.fn((seconds: number) => `${seconds}s`),
    clearMetrics: vi.fn(),
    exportData: vi.fn()
  }
}))

const mockOverview = {
  database: {
    totalQueries: 1000,
    cachedQueries: 800,
    slowQueries: 5,
    cacheHitRate: 80,
    averageExecutionTime: 45,
    maxExecutionTime: 200,
    minExecutionTime: 1
  },
  cache: {
    type: 'redis',
    connected: true,
    memory: '128MB',
    keyspace: 'notes',
    size: 500
  },
  timestamp: Date.now()
}

const mockSystemStats = {
  memory: {
    rss: 100000000,
    heapTotal: 80000000,
    heapUsed: 60000000,
    external: 5000000,
    arrayBuffers: 1000000
  },
  cpu: {
    user: 1000,
    system: 500
  },
  uptime: 3600,
  nodeVersion: 'v18.0.0',
  platform: 'linux',
  arch: 'x64',
  timestamp: Date.now()
}

const mockReport = {
  totalQueries: 1000,
  averageExecutionTime: 45,
  slowQueries: [],
  mostFrequentQueries: [],
  cacheHitRate: 80,
  recommendations: ['Consider adding indexes for slow queries']
}

describe('AdminPerformanceView', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default mock returns
    vi.mocked(performanceApiService.getOverview).mockResolvedValue(mockOverview)
    vi.mocked(performanceApiService.getSystemStats).mockResolvedValue(mockSystemStats)
    vi.mocked(performanceApiService.getSlowQueries).mockResolvedValue([])
    vi.mocked(performanceApiService.getReport).mockResolvedValue(mockReport)
    vi.mocked(performanceApiService.generateRecommendations).mockReturnValue(['Test recommendation'])
  })

  it('renders the performance monitoring header', async () => {
    const wrapper = mount(AdminPerformanceView, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    expect(wrapper.find('h1').text()).toBe('Performance Monitoring')
    expect(wrapper.find('.performance-header').exists()).toBe(true)
  })

  it('displays action buttons in header', async () => {
    const wrapper = mount(AdminPerformanceView, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    const buttons = wrapper.findAll('.header-actions button')
    expect(buttons).toHaveLength(3)
    
    // Check button texts
    expect(buttons[0].text()).toContain('Refresh')
    expect(buttons[1].text()).toContain('Export Data')
    expect(buttons[2].text()).toContain('Clear Metrics')
  })

  it('loads performance data on mount', async () => {
    mount(AdminPerformanceView, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    // Wait for component to mount and load data
    await new Promise(resolve => setTimeout(resolve, 0))

    expect(performanceApiService.getOverview).toHaveBeenCalled()
    expect(performanceApiService.getSystemStats).toHaveBeenCalled()
    expect(performanceApiService.getSlowQueries).toHaveBeenCalledWith(10)
    expect(performanceApiService.getReport).toHaveBeenCalled()
  })

  it('displays database performance metrics when data is loaded', async () => {
    const wrapper = mount(AdminPerformanceView, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    // Wait for data to load
    await new Promise(resolve => setTimeout(resolve, 0))
    await wrapper.vm.$nextTick()

    const databaseCard = wrapper.find('.metric-card')
    expect(databaseCard.exists()).toBe(true)
    expect(databaseCard.text()).toContain('Database Performance')
    expect(databaseCard.text()).toMatch(/1[\s,]?000/) // Total queries (formatted number)
    expect(databaseCard.text()).toContain('80.0%') // Cache hit rate
    expect(databaseCard.text()).toContain('45.00ms') // Avg execution time
  })

  it('displays cache status correctly', async () => {
    const wrapper = mount(AdminPerformanceView, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    // Wait for data to load
    await new Promise(resolve => setTimeout(resolve, 0))
    await wrapper.vm.$nextTick()

    const cacheCard = wrapper.findAll('.metric-card')[1]
    expect(cacheCard.text()).toContain('Cache Status')
    expect(cacheCard.text()).toContain('Connected')
    expect(cacheCard.text()).toContain('redis')
    expect(cacheCard.text()).toContain('128MB')
  })

  it('displays system resources information', async () => {
    const wrapper = mount(AdminPerformanceView, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    // Wait for data to load
    await new Promise(resolve => setTimeout(resolve, 0))
    await wrapper.vm.$nextTick()

    const systemCard = wrapper.findAll('.metric-card')[2]
    expect(systemCard.text()).toContain('System Resources')
    expect(systemCard.text()).toContain('v18.0.0') // Node version
    expect(systemCard.text()).toContain('linux') // Platform
  })

  it('handles refresh button click', async () => {
    const wrapper = mount(AdminPerformanceView, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    const refreshButton = wrapper.find('.btn-primary')
    await refreshButton.trigger('click')

    // Should call the API methods again
    expect(performanceApiService.getOverview).toHaveBeenCalledTimes(2) // Once on mount, once on refresh
  })

  it('handles export data button click', async () => {
    const mockBlob = new Blob(['test data'], { type: 'application/json' })
    vi.mocked(performanceApiService.exportData).mockResolvedValue(mockBlob)

    const wrapper = mount(AdminPerformanceView, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    const exportButton = wrapper.findAll('.btn')[1] // Second button is export
    await exportButton.trigger('click')

    expect(performanceApiService.exportData).toHaveBeenCalled()
  })

  it('shows error message when API calls fail', async () => {
    vi.mocked(performanceApiService.getOverview).mockRejectedValue(new Error('API Error'))

    const wrapper = mount(AdminPerformanceView, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    // Wait for error to be set
    await new Promise(resolve => setTimeout(resolve, 100))
    await wrapper.vm.$nextTick()

    // Check if error handling works (component should handle errors gracefully)
    expect(performanceApiService.getOverview).toHaveBeenCalled()
  })

  it('applies correct status classes based on performance metrics', async () => {
    // Test with poor performance metrics
    const poorOverview = {
      ...mockOverview,
      database: {
        ...mockOverview.database,
        averageExecutionTime: 250, // High execution time
        cacheHitRate: 30, // Low cache hit rate
        slowQueries: 100, // Many slow queries
        totalQueries: 1000
      }
    }
    
    vi.mocked(performanceApiService.getOverview).mockResolvedValue(poorOverview)

    const wrapper = mount(AdminPerformanceView, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    // Wait for data to load
    await new Promise(resolve => setTimeout(resolve, 100))
    await wrapper.vm.$nextTick()

    // Check that the component handles different performance metrics
    expect(performanceApiService.getOverview).toHaveBeenCalled()
  })
})
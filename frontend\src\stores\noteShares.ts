import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { noteShareService } from '../services/noteShareService';
import type {
  NoteShare,
  ShareAccess,
  CreateShareData,
  UpdateShareData,
  SharedNoteResponse,
  ShareFilters
} from '../types/noteShare';

export const useNoteSharesStore = defineStore('noteShares', () => {
  // State
  const shares = ref<NoteShare[]>([]);
  const currentSharedNote = ref<SharedNoteResponse | null>(null);
  const shareAccessLogs = ref<ShareAccess[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // Getters
  const sharesByNote = computed(() => {
    const byNote: Record<string, NoteShare[]> = {};
    shares.value.forEach(share => {
      if (!byNote[share.noteId]) {
        byNote[share.noteId] = [];
      }
      byNote[share.noteId].push(share);
    });
    return byNote;
  });

  const activeShares = computed(() => {
    return shares.value.filter(share => !share.isExpired);
  });

  const expiredShares = computed(() => {
    return shares.value.filter(share => share.isExpired);
  });

  const sharesByAccessLevel = computed(() => {
    const byLevel: Record<string, NoteShare[]> = {};
    shares.value.forEach(share => {
      if (!byLevel[share.accessLevel]) {
        byLevel[share.accessLevel] = [];
      }
      byLevel[share.accessLevel].push(share);
    });
    return byLevel;
  });

  const shareStatistics = computed(() => {
    return noteShareService.getShareStatistics(shares.value);
  });

  // Actions
  const setError = (message: string | null) => {
    error.value = message;
  };

  const clearError = () => {
    error.value = null;
  };

  // Create a new share for a note
  const createShare = async (noteId: string, shareData: CreateShareData) => {
    isLoading.value = true;
    clearError();

    try {
      // Validate share settings
      const validation = noteShareService.validateShareSettings(shareData);
      if (!validation.valid) {
        throw new Error(validation.errors.join(', '));
      }

      const newShare = await noteShareService.createShare(noteId, shareData);
      
      // Add to shares array
      shares.value.unshift(newShare);
      
      return newShare;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create share');
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Load shares for a specific note
  const loadNoteShares = async (noteId: string) => {
    isLoading.value = true;
    clearError();

    try {
      const noteShares = await noteShareService.getNoteShares(noteId);
      
      // Update shares array with note shares
      const existingShareIds = new Set(shares.value.map(s => s.id));
      const newShares = noteShares.filter(share => !existingShareIds.has(share.id));
      
      shares.value.push(...newShares);
      
      return noteShares;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load note shares');
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Load all user shares
  const loadUserShares = async (filters?: ShareFilters) => {
    isLoading.value = true;
    clearError();

    try {
      const userShares = await noteShareService.getUserShares(filters);
      shares.value = userShares;
      
      return userShares;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load user shares');
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Update an existing share
  const updateShare = async (shareId: string, updateData: UpdateShareData) => {
    clearError();

    try {
      // Validate share settings
      const validation = noteShareService.validateShareSettings(updateData);
      if (!validation.valid) {
        throw new Error(validation.errors.join(', '));
      }

      const updatedShare = await noteShareService.updateShare(shareId, updateData);
      
      // Update in shares array
      const index = shares.value.findIndex(share => share.id === shareId);
      if (index !== -1) {
        shares.value[index] = updatedShare;
      }
      
      return updatedShare;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update share');
      throw err;
    }
  };

  // Delete a share
  const deleteShare = async (shareId: string) => {
    clearError();

    try {
      await noteShareService.deleteShare(shareId);
      
      // Remove from shares array
      shares.value = shares.value.filter(share => share.id !== shareId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete share');
      throw err;
    }
  };

  // Access a shared note
  const accessSharedNote = async (shareToken: string, password?: string) => {
    isLoading.value = true;
    clearError();

    try {
      const sharedNote = await noteShareService.accessSharedNote(shareToken, password);
      currentSharedNote.value = sharedNote;
      
      return sharedNote;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to access shared note');
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Load access logs for a share
  const loadShareAccessLogs = async (shareId: string, limit: number = 100) => {
    isLoading.value = true;
    clearError();

    try {
      const accessLogs = await noteShareService.getShareAccessLogs(shareId, limit);
      shareAccessLogs.value = accessLogs;
      
      return accessLogs;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load access logs');
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Copy share URL to clipboard
  const copyShareUrl = async (shareUrl: string) => {
    try {
      await noteShareService.copyShareUrl(shareUrl);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to copy share URL');
      return false;
    }
  };

  // Clean up expired shares
  const cleanupExpiredShares = async () => {
    isLoading.value = true;
    clearError();

    try {
      const result = await noteShareService.cleanupExpiredShares();
      
      // Remove expired shares from local state
      shares.value = shares.value.filter(share => !share.isExpired);
      
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cleanup expired shares');
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Get shares for a specific note
  const getSharesForNote = (noteId: string): NoteShare[] => {
    return shares.value.filter(share => share.noteId === noteId);
  };

  // Check if a note has any active shares
  const hasActiveShares = (noteId: string): boolean => {
    return shares.value.some(share => share.noteId === noteId && !share.isExpired);
  };

  // Get the most permissive share for a note
  const getMostPermissiveShare = (noteId: string): NoteShare | null => {
    const noteShares = getSharesForNote(noteId).filter(share => !share.isExpired);
    
    if (noteShares.length === 0) return null;
    
    // Sort by access level permissiveness: public > unlisted > shared > private
    const accessLevelOrder = { public: 4, unlisted: 3, shared: 2, private: 1 };
    
    return noteShares.sort((a, b) => {
      const aLevel = accessLevelOrder[a.accessLevel] || 0;
      const bLevel = accessLevelOrder[b.accessLevel] || 0;
      return bLevel - aLevel;
    })[0];
  };

  // Optimistic updates for better UX
  const optimisticUpdateShare = (shareId: string, updateData: Partial<NoteShare>) => {
    const index = shares.value.findIndex(share => share.id === shareId);
    if (index !== -1) {
      shares.value[index] = { ...shares.value[index], ...updateData };
    }
  };

  // Reset store state
  const reset = () => {
    shares.value = [];
    currentSharedNote.value = null;
    shareAccessLogs.value = [];
    error.value = null;
    isLoading.value = false;
  };

  return {
    // State
    shares,
    currentSharedNote,
    shareAccessLogs,
    isLoading,
    error,

    // Getters
    sharesByNote,
    activeShares,
    expiredShares,
    sharesByAccessLevel,
    shareStatistics,

    // Actions
    createShare,
    loadNoteShares,
    loadUserShares,
    updateShare,
    deleteShare,
    accessSharedNote,
    loadShareAccessLogs,
    copyShareUrl,
    cleanupExpiredShares,
    getSharesForNote,
    hasActiveShares,
    getMostPermissiveShare,
    optimisticUpdateShare,
    reset,
    setError,
    clearError
  };
});
<template>
  <div class="admin-reports">
    <div class="container is-fluid">
      <!-- Header -->
      <div class="level mb-6">
        <div class="level-left">
          <div class="level-item">
            <div>
              <h1 class="title is-3">Content Reports</h1>
              <p class="subtitle is-6">Manage content moderation reports</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="field is-grouped">
              <div class="control">
                <button 
                  class="button is-primary" 
                  @click="loadReports"
                  :class="{ 'is-loading': isLoading }"
                >
                  <span class="icon">
                    <i class="fas fa-sync-alt"></i>
                  </span>
                  <span>Refresh</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="card mb-5">
        <div class="card-content">
          <div class="columns">
            <div class="column is-3">
              <div class="field">
                <label class="label">Status</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="statusFilter" @change="loadReports">
                      <option value="">All Status</option>
                      <option value="pending">Pending</option>
                      <option value="under_review">Under Review</option>
                      <option value="resolved">Resolved</option>
                      <option value="dismissed">Dismissed</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column is-3">
              <div class="field">
                <label class="label">Type</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="typeFilter" @change="loadReports">
                      <option value="">All Types</option>
                      <option value="note">Note</option>
                      <option value="user">User</option>
                      <option value="group">Group</option>
                      <option value="comment">Comment</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column is-3">
              <div class="field">
                <label class="label">Priority</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="priorityFilter" @change="loadReports">
                      <option value="">All Priorities</option>
                      <option value="critical">Critical</option>
                      <option value="high">High</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="notification is-danger mb-5">
        <button class="delete" @click="error = null"></button>
        {{ error }}
      </div>

      <!-- Reports Table -->
      <div class="card">
        <div class="card-content">
          <div v-if="isLoading && contentReports.length === 0" class="has-text-centered py-6">
            <div class="is-size-4 mb-3">
              <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>Loading reports...</p>
          </div>

          <div v-else-if="contentReports.length === 0" class="has-text-centered py-6">
            <div class="is-size-4 mb-3">
              <i class="fas fa-flag"></i>
            </div>
            <p class="has-text-grey">No reports found</p>
          </div>

          <div v-else class="table-container">
            <table class="table is-fullwidth is-hoverable">
              <thead>
                <tr>
                  <th>Report</th>
                  <th>Status</th>
                  <th>Priority</th>
                  <th>Reported</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="report in contentReports" :key="report.id">
                  <td>
                    <div class="content">
                      <p>
                        <strong>{{ formatReportType(report.type) }}</strong>
                        <span class="tag is-light is-small ml-2">{{ formatReason(report.reason) }}</span>
                      </p>
                      <p class="is-size-7 has-text-grey">
                        {{ report.description.substring(0, 100) }}{{ report.description.length > 100 ? '...' : '' }}
                      </p>
                    </div>
                  </td>
                  <td>
                    <span class="tag" :class="{
                      'is-warning': report.status === 'pending',
                      'is-info': report.status === 'under_review',
                      'is-success': report.status === 'resolved',
                      'is-light': report.status === 'dismissed'
                    }">
                      {{ formatStatus(report.status) }}
                    </span>
                  </td>
                  <td>
                    <span class="tag" :class="{
                      'is-danger': report.priority === 'critical',
                      'is-warning': report.priority === 'high',
                      'is-info': report.priority === 'medium',
                      'is-light': report.priority === 'low'
                    }">
                      {{ report.priority }}
                    </span>
                  </td>
                  <td>
                    <div class="content">
                      <p class="is-size-7">
                        {{ formatDate(report.createdAt) }}
                        <br>
                        <span class="has-text-grey">{{ formatRelativeDate(report.createdAt) }}</span>
                      </p>
                    </div>
                  </td>
                  <td>
                    <div class="buttons are-small">
                      <button 
                        v-if="report.status === 'pending'"
                        class="button is-info is-small"
                        @click="updateReportStatus(report.id, 'under_review')"
                      >
                        Review
                      </button>
                      <button 
                        v-if="report.status !== 'resolved'"
                        class="button is-success is-small"
                        @click="showResolveModal(report)"
                      >
                        Resolve
                      </button>
                      <button 
                        v-if="report.status !== 'dismissed'"
                        class="button is-light is-small"
                        @click="updateReportStatus(report.id, 'dismissed')"
                      >
                        Dismiss
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <nav v-if="reportsPagination.totalPages > 1" class="pagination is-centered mt-5" role="navigation">
            <button 
              class="pagination-previous" 
              :disabled="reportsPagination.page <= 1"
              @click="changePage(reportsPagination.page - 1)"
            >
              Previous
            </button>
            <button 
              class="pagination-next" 
              :disabled="reportsPagination.page >= reportsPagination.totalPages"
              @click="changePage(reportsPagination.page + 1)"
            >
              Next
            </button>
            <ul class="pagination-list">
              <li v-for="page in visiblePages" :key="page">
                <button 
                  v-if="page !== '...'"
                  class="pagination-link" 
                  :class="{ 'is-current': page === reportsPagination.page }"
                  @click="changePage(page as number)"
                >
                  {{ page }}
                </button>
                <span v-else class="pagination-ellipsis">&hellip;</span>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>

    <!-- Resolve Modal -->
    <div class="modal" :class="{ 'is-active': showResolveReportModal }">
      <div class="modal-background" @click="closeResolveModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Resolve Report</p>
          <button class="delete" @click="closeResolveModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="content">
            <p>
              Resolving report for <strong>{{ resolveModalData.report?.type }}</strong>
            </p>
            <div class="field">
              <label class="label">Resolution Notes</label>
              <div class="control">
                <textarea 
                  v-model="resolveModalData.resolution" 
                  class="textarea" 
                  placeholder="Enter resolution details..."
                  rows="4"
                ></textarea>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-success" 
            @click="confirmResolve"
            :class="{ 'is-loading': isLoading }"
          >
            Resolve Report
          </button>
          <button class="button" @click="closeResolveModal">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAdminStore } from '../stores/admin'
import { storeToRefs } from 'pinia'

const adminStore = useAdminStore()
const { contentReports, reportsPagination, isLoading, error } = storeToRefs(adminStore)

// Filters
const statusFilter = ref('')
const typeFilter = ref('')
const priorityFilter = ref('')

// UI state
const showResolveReportModal = ref(false)
const resolveModalData = ref({
  report: null as any,
  resolution: ''
})

const loadReports = () => {
  adminStore.loadContentReports({
    page: reportsPagination.value.page,
    limit: reportsPagination.value.limit,
    status: statusFilter.value,
    type: typeFilter.value,
    priority: priorityFilter.value
  })
}

const changePage = (page: number) => {
  adminStore.loadContentReports({
    page,
    limit: reportsPagination.value.limit,
    status: statusFilter.value,
    type: typeFilter.value,
    priority: priorityFilter.value
  })
}

const visiblePages = computed(() => {
  const current = reportsPagination.value.page
  const total = reportsPagination.value.totalPages
  const pages: (number | string)[] = []
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    pages.push(1)
    if (current > 4) pages.push('...')
    
    const start = Math.max(2, current - 2)
    const end = Math.min(total - 1, current + 2)
    
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
    
    if (current < total - 3) pages.push('...')
    pages.push(total)
  }
  
  return pages
})

const updateReportStatus = async (reportId: string, status: string) => {
  // This would call the admin store method to update report status
  console.log('Update report status:', reportId, status)
  // For now, just reload reports
  loadReports()
}

const showResolveModal = (report: any) => {
  resolveModalData.value = {
    report,
    resolution: ''
  }
  showResolveReportModal.value = true
}

const closeResolveModal = () => {
  showResolveReportModal.value = false
  resolveModalData.value = {
    report: null,
    resolution: ''
  }
}

const confirmResolve = async () => {
  if (!resolveModalData.value.report) return
  
  await updateReportStatus(resolveModalData.value.report.id, 'resolved')
  closeResolveModal()
}

const formatReportType = (type: string) => {
  return type.charAt(0).toUpperCase() + type.slice(1)
}

const formatReason = (reason: string) => {
  return reason.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatStatus = (status: string) => {
  return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const formatRelativeDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Yesterday'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`
  return `${Math.floor(diffDays / 365)} years ago`
}

onMounted(() => {
  loadReports()
})
</script>

<style scoped>
.admin-reports {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem 0;
}

.card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.table-container {
  overflow-x: auto;
}

.buttons.are-small .button {
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}
</style>
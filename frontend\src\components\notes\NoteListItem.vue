<template>
  <div 
    class="note-list-item"
    :class="{ 
      'is-selected': isSelected,
      'is-hover': isHovered 
    }"
    @click="handleClick"
    @contextmenu="handleContextMenu"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <!-- Selection checkbox -->
    <div class="note-item-checkbox">
      <label class="checkbox">
        <input 
          type="checkbox" 
          :checked="isSelected"
          @change="handleSelect"
          @click.stop
        >
      </label>
    </div>

    <!-- Note content -->
    <div class="note-item-content">
      <!-- Header with title and metadata -->
      <div class="note-item-header">
        <h3 class="note-item-title" :title="note.title">
          <span v-html="highlightSearchTerm(note.title)"></span>
        </h3>
        <div class="note-item-meta">
          <span class="note-type-badge" :class="`is-${note.note_type}`">
            <i :class="getNoteTypeIcon(note.note_type)"></i>
            {{ getNoteTypeLabel(note.note_type) }}
          </span>
          <span class="note-date" :title="formatFullDate(note.updated_at)">
            {{ formatRelativeDate(note.updated_at) }}
          </span>
        </div>
      </div>

      <!-- Preview content -->
      <div class="note-item-preview">
        <p v-html="highlightSearchTerm(getPreviewText())"></p>
      </div>

      <!-- Tags -->
      <div v-if="note.tags && note.tags.length > 0" class="note-item-tags">
        <span 
          v-for="tag in note.tags.slice(0, 3)" 
          :key="tag"
          class="tag is-small"
          :class="getTagColor(tag)"
        >
          {{ tag }}
        </span>
        <span v-if="note.tags.length > 3" class="tag is-small is-light">
          +{{ note.tags.length - 3 }}
        </span>
      </div>

      <!-- Footer with actions -->
      <div class="note-item-footer">
        <div class="note-item-stats">
          <span class="stat-item" :title="`${getWordCount()} words`">
            <i class="fas fa-file-word"></i>
            {{ getWordCount() }}
          </span>
          <span v-if="note.metadata?.readTime" class="stat-item" :title="`${note.metadata.readTime} min read`">
            <i class="fas fa-clock"></i>
            {{ note.metadata.readTime }}m
          </span>
        </div>
        
        <div class="note-item-actions">
          <button 
            class="button is-small is-ghost"
            :title="isBookmarked ? 'Remove bookmark' : 'Add bookmark'"
            @click.stop="toggleBookmark"
          >
            <i :class="isBookmarked ? 'fas fa-bookmark' : 'far fa-bookmark'"></i>
          </button>
          <button 
            class="button is-small is-ghost"
            title="Share note"
            @click.stop="shareNote"
          >
            <i class="fas fa-share"></i>
          </button>
          <button 
            class="button is-small is-ghost"
            title="More options"
            @click.stop="showMoreOptions"
          >
            <i class="fas fa-ellipsis-v"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading overlay -->
    <div v-if="isLoading" class="note-item-loading">
      <div class="button is-loading is-ghost"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { formatDistanceToNow, format } from 'date-fns'

interface Note {
  id: string
  title: string
  content: string
  note_type: string
  created_at: string
  updated_at: string
  tags: string[]
  metadata: any
}

interface Props {
  note: Note
  index: number
  isSelected: boolean
  searchQuery?: string
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  searchQuery: '',
  isLoading: false
})

const emit = defineEmits<{
  click: [note: Note]
  select: [noteId: string, selected: boolean]
  'context-menu': [event: MouseEvent, noteId: string]
  bookmark: [noteId: string, bookmarked: boolean]
  share: [noteId: string]
  'more-options': [noteId: string, event: MouseEvent]
}>()

// State
const isHovered = ref(false)
const isBookmarked = ref(false) // This would come from props or store

// Computed properties
const previewLength = computed(() => {
  // Adjust preview length based on screen size
  return window.innerWidth < 768 ? 100 : 150
})

// Methods
const handleClick = () => {
  emit('click', props.note)
}

const handleSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('select', props.note.id, target.checked)
}

const handleContextMenu = (event: MouseEvent) => {
  emit('context-menu', event, props.note.id)
}

const toggleBookmark = () => {
  isBookmarked.value = !isBookmarked.value
  emit('bookmark', props.note.id, isBookmarked.value)
}

const shareNote = () => {
  emit('share', props.note.id)
}

const showMoreOptions = (event: MouseEvent) => {
  emit('more-options', props.note.id, event)
}

const getNoteTypeIcon = (type: string): string => {
  switch (type) {
    case 'richtext':
      return 'fas fa-align-left'
    case 'markdown':
      return 'fab fa-markdown'
    case 'kanban':
      return 'fas fa-columns'
    default:
      return 'fas fa-file-alt'
  }
}

const getNoteTypeLabel = (type: string): string => {
  switch (type) {
    case 'richtext':
      return 'Rich Text'
    case 'markdown':
      return 'Markdown'
    case 'kanban':
      return 'Kanban'
    default:
      return 'Note'
  }
}

const getPreviewText = (): string => {
  let content = props.note.content
  
  // Strip HTML tags for rich text notes
  if (props.note.note_type === 'richtext') {
    content = content.replace(/<[^>]*>/g, '')
  }
  
  // Strip markdown formatting
  if (props.note.note_type === 'markdown') {
    content = content
      .replace(/#{1,6}\s/g, '') // Headers
      .replace(/\*\*(.*?)\*\*/g, '$1') // Bold
      .replace(/\*(.*?)\*/g, '$1') // Italic
      .replace(/`(.*?)`/g, '$1') // Code
      .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Links
  }
  
  // Truncate to preview length
  return content.length > previewLength.value 
    ? content.substring(0, previewLength.value) + '...'
    : content
}

const getWordCount = (): number => {
  const text = getPreviewText().replace(/\s+/g, ' ').trim()
  return text ? text.split(' ').length : 0
}

const formatRelativeDate = (dateString: string): string => {
  try {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true })
  } catch {
    return 'Unknown'
  }
}

const formatFullDate = (dateString: string): string => {
  try {
    return format(new Date(dateString), 'PPpp')
  } catch {
    return 'Unknown date'
  }
}

const highlightSearchTerm = (text: string): string => {
  if (!props.searchQuery.trim()) return text
  
  const regex = new RegExp(`(${props.searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const getTagColor = (tag: string): string => {
  // Generate consistent color based on tag name
  const colors = ['is-primary', 'is-info', 'is-success', 'is-warning', 'is-danger']
  const hash = tag.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0)
    return a & a
  }, 0)
  return colors[Math.abs(hash) % colors.length]
}
</script>

<style scoped>
.note-list-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: var(--color-background);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.note-list-item:hover,
.note-list-item.is-hover {
  border-color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(50, 115, 220, 0.1);
  transform: translateY(-1px);
}

.note-list-item.is-selected {
  border-color: var(--color-primary);
  background: var(--color-surface-hover);
}

.note-item-checkbox {
  flex-shrink: 0;
  margin-right: 1rem;
  margin-top: 0.25rem;
}

.note-item-content {
  flex: 1;
  min-width: 0; /* Prevent flex item from overflowing */
}

.note-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.note-item-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--color-text-strong);
  flex: 1;
  margin-right: 1rem;
  
  /* Truncate long titles */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.note-item-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.note-type-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.note-type-badge.is-richtext {
  background: var(--color-surface-hover);
  color: var(--color-primary);
}

.note-type-badge.is-markdown {
  background: var(--color-surface);
  color: var(--color-text-strong);
}

.note-type-badge.is-kanban {
  background: var(--color-success-light);
  color: var(--color-success);
}

.note-date {
  font-size: 0.75rem;
  color: var(--color-text-muted);
}

.note-item-preview {
  margin-bottom: 0.75rem;
  color: var(--color-text);
  line-height: 1.4;
}

.note-item-preview p {
  margin: 0;
}

.note-item-preview :deep(mark) {
  background: var(--color-warning);
  padding: 0.1em 0.2em;
  border-radius: 2px;
}

.note-item-tags {
  margin-bottom: 0.75rem;
}

.note-item-tags .tag {
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}

.note-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.note-item-stats {
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--color-text-muted);
}

.note-item-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.note-list-item:hover .note-item-actions,
.note-list-item.is-hover .note-item-actions {
  opacity: 1;
}

.note-item-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--color-background-rgb, 255, 255, 255), 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .note-list-item {
    padding: 0.75rem;
  }
  
  .note-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .note-item-title {
    margin-right: 0;
  }
  
  .note-item-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .note-item-actions {
    opacity: 1; /* Always show on mobile */
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .note-list-item {
    background: var(--color-surface);
    border-color: var(--color-border);
    color: var(--color-text);
  }
  
  .note-list-item:hover,
  .note-list-item.is-hover {
    border-color: var(--color-primary);
    background: var(--color-surface-hover);
  }
  
  .note-list-item.is-selected {
    background: var(--color-surface-hover);
  }
  
  .note-item-title {
    color: var(--color-text);
  }
  
  .note-item-preview {
    color: var(--color-text);
  }
}
</style>
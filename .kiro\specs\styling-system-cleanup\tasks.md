# Implementation Plan

- [x] 1. Set up new CSS architecture and file structure





  - Create the new directory structure under `frontend/src/styles/`
  - Move and reorganize existing CSS from `minimal.css` into component-specific files
  - Create base CSS files with CSS custom properties for theming
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 1.1 Create base CSS architecture


  - Create `frontend/src/styles/base/reset.css` with modern CSS reset
  - Create `frontend/src/styles/base/typography.css` with font definitions
  - Create `frontend/src/styles/base/variables.css` with CSS custom properties for theming
  - _Requirements: 1.1, 1.4_

- [x] 1.2 Split existing minimal.css into component files


  - Create `frontend/src/styles/components/buttons.css` with button styles from minimal.css
  - Create `frontend/src/styles/components/forms.css` with form styles from minimal.css
  - Create `frontend/src/styles/components/navigation.css` with navbar and menu styles
  - Create `frontend/src/styles/components/modals.css` with modal styles from minimal.css
  - Create `frontend/src/styles/components/cards.css` with card styles from minimal.css
  - Create `frontend/src/styles/components/layout.css` with layout and grid styles
  - _Requirements: 1.1, 1.2, 1.4_

- [x] 1.3 Create utility CSS files


  - Create `frontend/src/styles/utilities/spacing.css` with margin and padding utilities
  - Create `frontend/src/styles/utilities/colors.css` with color utility classes
  - Create `frontend/src/styles/utilities/typography.css` with text utility classes
  - Create `frontend/src/styles/utilities/responsive.css` with responsive utility classes
  - _Requirements: 1.1, 1.4_

- [x] 1.4 Create main CSS entry point


  - Create `frontend/src/styles/main.css` that imports all CSS files in correct order
  - Update `frontend/src/main.ts` to import the new main.css instead of minimal.css
  - Verify all existing styles still work after reorganization
  - _Requirements: 1.1, 1.3_

- [x] 2. Implement Bulmaswatch theme integration system




  - Download and process selected Bulmaswatch themes
  - Create theme configuration and loading system
  - Implement CSS custom properties for theme switching
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 2.1 Download and prepare Bulmaswatch themes


  - Create `frontend/src/styles/themes/bulmaswatch/` directory
  - Download CSS files for default, darkly, flatly, and cerulean themes from Bulmaswatch
  - Process theme files to extract color variables and create CSS custom property versions
  - Create theme manifest files with metadata for each theme
  - _Requirements: 3.1, 3.2, 3.4_

- [x] 2.2 Create theme management system


  - Create `frontend/src/composables/useTheme.ts` composable for theme management
  - Implement ThemeManager class with theme loading and switching logic
  - Create theme configuration interfaces and types
  - Add theme validation and error handling
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 2.3 Implement CSS custom properties for theming


  - Create `frontend/src/styles/themes/themes.css` with theme switching logic
  - Convert all component CSS files to use CSS custom properties
  - Create light and dark theme variable definitions
  - Add smooth transition animations for theme switching
  - _Requirements: 2.1, 2.2, 4.1, 4.2, 4.3_

- [x] 3. Enhance theme settings UI and user experience





  - Update settings modal with theme selection interface
  - Add theme preview functionality
  - Implement auto mode for system preference detection
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 3.1 Update PreferencesTab component for theme selection


  - Enhance `frontend/src/components/settings/PreferencesTab.vue` with theme selector UI
  - Add theme preview cards showing color schemes
  - Implement theme selection with immediate preview
  - Add accessibility labels and keyboard navigation support
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 3.2 Implement system preference detection and auto mode


  - Add system theme preference detection using `prefers-color-scheme` media query
  - Implement auto mode that follows system preference changes
  - Add event listeners for system theme changes
  - Update theme persistence logic to handle auto mode
  - _Requirements: 2.3, 2.4_

- [x] 3.3 Create theme preview and selection components


  - Create `frontend/src/components/settings/ThemePreview.vue` component
  - Implement theme preview cards with color swatches
  - Add theme selection with visual feedback
  - Create theme switching animations and transitions
  - _Requirements: 6.2, 6.3, 4.1, 4.2, 4.3_

- [x] 4. Integrate theme system with existing stores and components





  - Update auth store theme preference methods
  - Integrate theme system with settings store
  - Update AppLayout component to apply themes
  - _Requirements: 2.5, 5.1, 5.2, 5.3, 5.4_

- [x] 4.1 Update auth store for enhanced theme management


  - Enhance `frontend/src/stores/auth.ts` theme preference methods
  - Add support for Bulmaswatch theme names in addition to light/dark/auto
  - Update theme persistence to store both mode and specific theme
  - Add theme validation and fallback logic
  - _Requirements: 2.5, 5.1_

- [x] 4.2 Integrate with settings store


  - Update `frontend/src/stores/settings.ts` to include theme management
  - Add methods for loading available themes and updating theme preferences
  - Implement theme settings synchronization with backend
  - Add theme-related error handling and user feedback
  - _Requirements: 5.1, 5.2_

- [x] 4.3 Update AppLayout component for theme application


  - Modify `frontend/src/components/layout/AppLayout.vue` to use new theme system
  - Replace existing theme application logic with new ThemeManager
  - Add theme loading states and error handling
  - Ensure theme changes apply immediately across all components
  - _Requirements: 2.1, 2.2, 4.1, 4.2, 4.3_

- [x] 5. Optimize performance and bundle size





  - Implement CSS optimization and code splitting
  - Add theme preloading and caching
  - Optimize FontAwesome integration
  - _Requirements: 3.4, 5.1, 5.2, 5.3, 5.4_

- [x] 5.1 Implement CSS optimization and code splitting


  - Configure Vite to split theme CSS into separate chunks
  - Implement lazy loading for non-default themes
  - Add CSS purging to remove unused styles
  - Optimize CSS custom property usage for better performance
  - _Requirements: 3.4, 5.1, 5.2_

- [x] 5.2 Add theme preloading and caching strategies


  - Implement theme preloading for user's preferred theme
  - Add localStorage caching for downloaded themes
  - Create service worker integration for offline theme availability
  - Add theme loading progress indicators
  - _Requirements: 5.1, 5.2, 5.3_

- [x] 5.3 Optimize FontAwesome integration with themes


  - Update `frontend/src/styles/vendor/fontawesome.css` to work with theme system
  - Ensure icon colors inherit properly from theme variables
  - Optimize FontAwesome loading for better performance
  - Test icon visibility across all themes
  - _Requirements: 3.2, 5.1, 5.4_

- [x] 6. Add comprehensive testing and validation





  - Create unit tests for theme management system
  - Add visual regression tests for theme switching
  - Implement accessibility testing for all themes
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 6.4, 6.5_

- [x] 6.1 Create unit tests for theme management


  - Write tests for `useTheme.ts` composable functionality
  - Test theme loading, switching, and persistence logic
  - Add tests for error handling and fallback scenarios
  - Test system preference detection and auto mode
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 6.2 Implement visual regression testing


  - Create screenshot tests for each theme across key components
  - Test theme transition animations and smoothness
  - Verify component appearance consistency across themes
  - Add responsive design testing for all themes
  - _Requirements: 4.1, 4.2, 4.3_

- [x] 6.3 Add accessibility testing and validation


  - Test color contrast ratios for all themes using automated tools
  - Verify keyboard navigation works properly in theme selector
  - Test screen reader compatibility with theme changes
  - Ensure focus indicators are visible across all themes
  - _Requirements: 6.4, 6.5_

- [x] 7. Final integration and cleanup





  - Remove old CSS files and update imports
  - Update build configuration for production
  - Add documentation for theme system
  - _Requirements: 1.3, 5.1, 5.2, 5.3, 5.4_

- [x] 7.1 Clean up old CSS files and update imports


  - Remove `frontend/src/styles/minimal.css` after verifying all styles are migrated
  - Update all component imports to use new CSS structure
  - Remove unused CSS classes and consolidate duplicates
  - Verify no broken styles or missing imports
  - _Requirements: 1.3, 5.3_

- [x] 7.2 Update build configuration for production optimization


  - Configure Vite build settings for optimal CSS bundling
  - Add CSS minification and optimization for production builds
  - Implement CSS tree shaking to remove unused theme styles
  - Add bundle size monitoring and performance budgets
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [x] 7.3 Create documentation and developer guidelines


  - Write documentation for the new CSS architecture and theme system
  - Create developer guidelines for adding new themes and components
  - Document theme customization and extension procedures
  - Add troubleshooting guide for common theme-related issues
  - _Requirements: 5.1, 5.2, 5.3, 5.4_
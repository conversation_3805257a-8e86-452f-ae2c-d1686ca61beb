import { getDatabase } from '../config/database';
import { CacheService } from '../services/CacheService';

export interface QueryOptions {
  cache?: boolean;
  cacheTTL?: number;
  cacheKey?: string;
  tags?: string[];
}

export interface PaginationOptions {
  page: number;
  limit: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
}

export class QueryOptimizer {
  // Execute optimized query with caching
  static async executeQuery<T>(
    sql: string, 
    params: any[] = [], 
    options: QueryOptions = {}
  ): Promise<T[]> {
    const db = getDatabase();
    
    // Check cache first if enabled
    if (options.cache && options.cacheKey) {
      const cached = await CacheService.get<T[]>(options.cacheKey);
      if (cached) {
        return cached;
      }
    }

    // Execute query
    const result = await new Promise<T[]>((resolve, reject) => {
      db.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows as T[]);
      });
    });

    // Cache result if enabled
    if (options.cache && options.cacheKey) {
      await CacheService.set(options.cacheKey, result, {
        ttl: options.cacheTTL || 300,
        tags: options.tags
      });
    }

    return result;
  }

  // Execute single row query with caching
  static async executeQuerySingle<T>(
    sql: string, 
    params: any[] = [], 
    options: QueryOptions = {}
  ): Promise<T | null> {
    const db = getDatabase();
    
    // Check cache first if enabled
    if (options.cache && options.cacheKey) {
      const cached = await CacheService.get<T>(options.cacheKey);
      if (cached) {
        return cached;
      }
    }

    // Execute query
    const result = await new Promise<T | null>((resolve, reject) => {
      db.get(sql, params, (err, row) => {
        if (err) reject(err);
        else resolve(row as T || null);
      });
    });

    // Cache result if enabled
    if (options.cache && options.cacheKey && result) {
      await CacheService.set(options.cacheKey, result, {
        ttl: options.cacheTTL || 300,
        tags: options.tags
      });
    }

    return result;
  }

  // Build optimized pagination query
  static buildPaginationQuery(
    baseQuery: string,
    options: PaginationOptions,
    countQuery?: string
  ): { query: string; countQuery: string; offset: number } {
    const offset = (options.page - 1) * options.limit;
    const orderBy = options.orderBy || 'created_at';
    const direction = options.orderDirection || 'DESC';

    const query = `
      ${baseQuery}
      ORDER BY ${orderBy} ${direction}
      LIMIT ${options.limit} OFFSET ${offset}
    `;

    const finalCountQuery = countQuery || `
      SELECT COUNT(*) as total 
      FROM (${baseQuery.replace(/SELECT.*?FROM/i, 'SELECT 1 FROM')}) as count_query
    `;

    return { query, countQuery: finalCountQuery, offset };
  }

  // Optimized note search with full-text search
  static async searchNotes(
    userId: string,
    searchQuery: string,
    filters: {
      type?: string;
      tags?: string[];
      dateFrom?: string;
      dateTo?: string;
    } = {},
    pagination: PaginationOptions
  ): Promise<{ notes: any[]; total: number }> {
    const cacheKey = `search:${userId}:${Buffer.from(searchQuery).toString('base64')}:${JSON.stringify(filters)}:${pagination.page}:${pagination.limit}`;
    
    // Check cache first
    const cached = await CacheService.get<{ notes: any[]; total: number }>(cacheKey);
    if (cached) {
      return cached;
    }

    let whereConditions = ['n.user_id = ?'];
    let params = [userId];

    // Add search condition
    if (searchQuery.trim()) {
      whereConditions.push('(n.id IN (SELECT rowid FROM notes_fts WHERE notes_fts MATCH ?) OR n.title LIKE ?)');
      params.push(searchQuery, `%${searchQuery}%`);
    }

    // Add type filter
    if (filters.type) {
      whereConditions.push('n.note_type = ?');
      params.push(filters.type);
    }

    // Add date filters
    if (filters.dateFrom) {
      whereConditions.push('n.created_at >= ?');
      params.push(filters.dateFrom);
    }

    if (filters.dateTo) {
      whereConditions.push('n.created_at <= ?');
      params.push(filters.dateTo);
    }

    // Add tag filter
    if (filters.tags && filters.tags.length > 0) {
      const tagPlaceholders = filters.tags.map(() => '?').join(',');
      whereConditions.push(`n.id IN (
        SELECT nt.note_id 
        FROM note_tags nt 
        JOIN tags t ON nt.tag_id = t.id 
        WHERE t.name IN (${tagPlaceholders})
        GROUP BY nt.note_id 
        HAVING COUNT(DISTINCT t.name) = ?
      )`);
      params.push(...filters.tags, filters.tags.length.toString());
    }

    const whereClause = whereConditions.join(' AND ');

    const baseQuery = `
      SELECT DISTINCT n.*, 
             GROUP_CONCAT(t.name) as tags,
             CASE 
               WHEN n.id IN (SELECT rowid FROM notes_fts WHERE notes_fts MATCH ?) 
               THEN 1 ELSE 0 
             END as relevance_score
      FROM notes n
      LEFT JOIN note_tags nt ON n.id = nt.note_id
      LEFT JOIN tags t ON nt.tag_id = t.id
      WHERE ${whereClause}
      GROUP BY n.id
    `;

    // Add search query parameter for relevance scoring
    const searchParams = searchQuery.trim() ? [searchQuery, ...params] : params;

    const { query, countQuery } = this.buildPaginationQuery(
      baseQuery,
      pagination,
      `SELECT COUNT(DISTINCT n.id) as total FROM notes n 
       LEFT JOIN note_tags nt ON n.id = nt.note_id
       LEFT JOIN tags t ON nt.tag_id = t.id
       WHERE ${whereClause}`
    );

    // Execute queries
    const [notes, countResult] = await Promise.all([
      this.executeQuery(query, searchParams),
      this.executeQuerySingle<{ total: number }>(countQuery, params)
    ]);

    const result = {
      notes: notes.map((note: any) => ({
        ...note,
        tags: note.tags ? note.tags.split(',') : [],
        metadata: JSON.parse(note.metadata || '{}')
      })),
      total: countResult?.total || 0
    };

    // Cache the result
    await CacheService.set(cacheKey, result, {
      ttl: 600, // 10 minutes
      tags: ['search', `user:${userId}`]
    });

    return result;
  }

  // Optimized note list with eager loading
  static async getNotesList(
    userId: string,
    pagination: PaginationOptions,
    filters: {
      type?: string;
      tags?: string[];
      archived?: boolean;
      groupId?: string;
    } = {}
  ): Promise<{ notes: any[]; total: number }> {
    const cacheKey = `notes_list:${userId}:${JSON.stringify(filters)}:${pagination.page}:${pagination.limit}`;
    
    // Check cache first
    const cached = await CacheService.get<{ notes: any[]; total: number }>(cacheKey);
    if (cached) {
      return cached;
    }

    let whereConditions = ['n.user_id = ?'];
    let params = [userId];

    // Add filters
    if (filters.type) {
      whereConditions.push('n.note_type = ?');
      params.push(filters.type);
    }

    if (filters.archived !== undefined) {
      whereConditions.push('n.is_archived = ?');
      params.push((filters.archived ? 1 : 0).toString());
    }

    if (filters.groupId) {
      whereConditions.push('n.group_id = ?');
      params.push(filters.groupId);
    }

    if (filters.tags && filters.tags.length > 0) {
      const tagPlaceholders = filters.tags.map(() => '?').join(',');
      whereConditions.push(`n.id IN (
        SELECT nt.note_id 
        FROM note_tags nt 
        JOIN tags t ON nt.tag_id = t.id 
        WHERE t.name IN (${tagPlaceholders})
        GROUP BY nt.note_id 
        HAVING COUNT(DISTINCT t.name) = ?
      )`);
      params.push(...filters.tags, filters.tags.length.toString());
    }

    const whereClause = whereConditions.join(' AND ');

    const baseQuery = `
      SELECT n.*, 
             GROUP_CONCAT(t.name) as tags,
             g.name as group_name
      FROM notes n
      LEFT JOIN note_tags nt ON n.id = nt.note_id
      LEFT JOIN tags t ON nt.tag_id = t.id
      LEFT JOIN groups g ON n.group_id = g.id
      WHERE ${whereClause}
      GROUP BY n.id
    `;

    const { query, countQuery } = this.buildPaginationQuery(
      baseQuery,
      pagination,
      `SELECT COUNT(DISTINCT n.id) as total FROM notes n 
       LEFT JOIN note_tags nt ON n.id = nt.note_id
       LEFT JOIN tags t ON nt.tag_id = t.id
       WHERE ${whereClause}`
    );

    // Execute queries
    const [notes, countResult] = await Promise.all([
      this.executeQuery(query, params),
      this.executeQuerySingle<{ total: number }>(countQuery, params)
    ]);

    const result = {
      notes: notes.map((note: any) => ({
        ...note,
        tags: note.tags ? note.tags.split(',') : [],
        metadata: JSON.parse(note.metadata || '{}')
      })),
      total: countResult?.total || 0
    };

    // Cache the result
    await CacheService.set(cacheKey, result, {
      ttl: 300, // 5 minutes
      tags: ['notes', `user:${userId}`]
    });

    return result;
  }

  // Batch operations for better performance
  static async batchInsert(table: string, records: any[]): Promise<void> {
    if (records.length === 0) return;

    const db = getDatabase();
    const columns = Object.keys(records[0]);
    const placeholders = columns.map(() => '?').join(',');
    const sql = `INSERT INTO ${table} (${columns.join(',')}) VALUES (${placeholders})`;

    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');
        
        const stmt = db.prepare(sql);
        
        for (const record of records) {
          const values = columns.map(col => record[col]);
          stmt.run(values);
        }
        
        stmt.finalize((err) => {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
          } else {
            db.run('COMMIT', (commitErr) => {
              if (commitErr) reject(commitErr);
              else resolve();
            });
          }
        });
      });
    });
  }

  // Database performance analysis
  static async analyzeQuery(sql: string, params: any[] = []): Promise<any> {
    const db = getDatabase();
    
    const explainSql = `EXPLAIN QUERY PLAN ${sql}`;
    
    return new Promise((resolve, reject) => {
      db.all(explainSql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  // Get database statistics
  static async getDatabaseStats(): Promise<any> {
    const db = getDatabase();
    
    const queries = [
      'SELECT COUNT(*) as total_notes FROM notes',
      'SELECT COUNT(*) as total_users FROM users',
      'SELECT COUNT(*) as total_tags FROM tags',
      'SELECT COUNT(*) as total_groups FROM groups',
      'SELECT name, sql FROM sqlite_master WHERE type="index"',
      'PRAGMA table_info(notes)',
      'PRAGMA index_list(notes)'
    ];

    const results = await Promise.all(
      queries.map(query => 
        new Promise((resolve, reject) => {
          db.all(query, (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          });
        })
      )
    );

    return {
      totalNotes: results[0],
      totalUsers: results[1],
      totalTags: results[2],
      totalGroups: results[3],
      indexes: results[4],
      notesTableInfo: results[5],
      notesIndexes: results[6]
    };
  }
}
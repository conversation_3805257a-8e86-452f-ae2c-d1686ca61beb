import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAuthStore } from './auth'

interface DashboardStats {
  userStats: {
    total: number
    verified: number
    oauth_users: number
    new_this_week: number
    new_this_month: number
    online: number
  }
  noteStats: {
    total: number
    richtext: number
    markdown: number
    kanban: number
    archived: number
    created_this_week: number
  }
  groupStats: {
    total: number
    avg_members: number
    created_this_week: number
  }
  recentActivity: Array<{
    action: string
    resource_type: string
    user_id: string
    created_at: string
    count: number
  }>
  systemHealth: {
    avg_response_time: number
    error_count: number
    total_requests: number
    error_rate: number
    status: 'healthy' | 'warning' | 'critical'
  }
}

interface AdminUser {
  id: string
  email: string
  display_name: string
  avatar_url?: string
  preferences: any
  email_verified: boolean
  oauth_provider?: string
  created_at: string
  updated_at: string
  note_count: number
  group_count: number
  status: 'active' | 'suspended' | 'banned'
  isAdmin: boolean
  lastLogin?: string
}

interface ContentReport {
  id: string
  type: 'note' | 'user' | 'group' | 'comment'
  resourceId: string
  reportedBy: string
  reportedUser?: string
  reason: 'spam' | 'inappropriate_content' | 'harassment' | 'copyright' | 'other'
  description: string
  status: 'pending' | 'under_review' | 'resolved' | 'dismissed'
  priority: 'low' | 'medium' | 'high' | 'critical'
  assignedTo?: string
  resolution?: string
  createdAt: string
  updatedAt: string
}

interface SystemConfig {
  features: {
    registration: boolean
    googleAuth: boolean
    twoFactor: boolean
    emailVerification: boolean
    maintenance: boolean
  }
  limits: {
    maxNotesPerUser: number
    maxFileSize: number
    rateLimit: {
      general: number
      auth: number
    }
  }
  maintenance: {
    mode: boolean
    message: string
    scheduledStart?: string
    scheduledEnd?: string
  }
  notifications: {
    emailEnabled: boolean
    adminAlerts: boolean
    securityAlerts: boolean
  }
}

export const useAdminStore = defineStore('admin', () => {
  const authStore = useAuthStore()
  
  const dashboardStats = ref<DashboardStats | null>(null)
  const users = ref<AdminUser[]>([])
  const usersPagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })
  const contentReports = ref<ContentReport[]>([])
  const reportsPagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })
  const systemConfig = ref<SystemConfig | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const isAdmin = computed(() => {
    return authStore.isAdmin
  })

  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    const response = await fetch(`/api/admin${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    return response.json()
  }

  const loadDashboard = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      const data = await apiCall('/dashboard')
      dashboardStats.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load dashboard'
      console.error('Admin dashboard error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const loadUsers = async (options: {
    page?: number
    limit?: number
    search?: string
    status?: string
    sortBy?: string
    sortOrder?: string
  } = {}) => {
    isLoading.value = true
    error.value = null
    
    try {
      const params = new URLSearchParams()
      if (options.page) params.append('page', options.page.toString())
      if (options.limit) params.append('limit', options.limit.toString())
      if (options.search) params.append('search', options.search)
      if (options.status) params.append('status', options.status)
      if (options.sortBy) params.append('sortBy', options.sortBy)
      if (options.sortOrder) params.append('sortOrder', options.sortOrder)

      const data = await apiCall(`/users?${params.toString()}`)
      users.value = data.users
      usersPagination.value = data.pagination
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load users'
      console.error('Admin users error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const updateUserStatus = async (userId: string, status: string, reason?: string) => {
    try {
      await apiCall(`/users/${userId}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status, reason })
      })
      
      // Update local user data
      const userIndex = users.value.findIndex(u => u.id === userId)
      if (userIndex !== -1) {
        users.value[userIndex].status = status as any
      }
      
      return { success: true }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to update user status'
      error.value = message
      return { success: false, error: message }
    }
  }

  const toggleAdminStatus = async (userId: string, isAdmin: boolean) => {
    try {
      await apiCall(`/users/${userId}/admin`, {
        method: 'PUT',
        body: JSON.stringify({ isAdmin })
      })
      
      // Update local user data
      const userIndex = users.value.findIndex(u => u.id === userId)
      if (userIndex !== -1) {
        users.value[userIndex].isAdmin = isAdmin
      }
      
      return { success: true }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to update admin status'
      error.value = message
      return { success: false, error: message }
    }
  }

  const loadContentReports = async (options: {
    page?: number
    limit?: number
    status?: string
    type?: string
    priority?: string
  } = {}) => {
    isLoading.value = true
    error.value = null
    
    try {
      const params = new URLSearchParams()
      if (options.page) params.append('page', options.page.toString())
      if (options.limit) params.append('limit', options.limit.toString())
      if (options.status) params.append('status', options.status)
      if (options.type) params.append('type', options.type)
      if (options.priority) params.append('priority', options.priority)

      const data = await apiCall(`/content/reports?${params.toString()}`)
      contentReports.value = data.reports
      reportsPagination.value = data.pagination
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load content reports'
      console.error('Admin content reports error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const loadSystemConfig = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      const data = await apiCall('/system/config')
      systemConfig.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load system config'
      console.error('Admin system config error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const updateSystemConfig = async (updates: Partial<SystemConfig>) => {
    try {
      await apiCall('/system/config', {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      
      // Update local config
      if (systemConfig.value) {
        systemConfig.value = { ...systemConfig.value, ...updates }
      }
      
      return { success: true }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to update system config'
      error.value = message
      return { success: false, error: message }
    }
  }

  const toggleMaintenanceMode = async (enabled: boolean, message?: string) => {
    try {
      await apiCall('/system/maintenance', {
        method: 'POST',
        body: JSON.stringify({ enabled, message })
      })
      
      // Update local config
      if (systemConfig.value) {
        systemConfig.value.maintenance.mode = enabled
        if (message) {
          systemConfig.value.maintenance.message = message
        }
      }
      
      return { success: true }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to toggle maintenance mode'
      error.value = message
      return { success: false, error: message }
    }
  }

  const getSystemMetrics = async (hours: number = 24) => {
    try {
      const data = await apiCall(`/system/metrics?hours=${hours}`)
      return { success: true, data }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to load system metrics'
      error.value = message
      return { success: false, error: message }
    }
  }

  const updateReportStatus = async (reportId: string, status: string, resolution?: string) => {
    try {
      await apiCall(`/content/reports/${reportId}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status, resolution })
      })
      
      // Update local report data
      const reportIndex = contentReports.value.findIndex(r => r.id === reportId)
      if (reportIndex !== -1) {
        contentReports.value[reportIndex].status = status as any
        if (resolution) {
          contentReports.value[reportIndex].resolution = resolution
        }
      }
      
      return { success: true }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to update report status'
      error.value = message
      return { success: false, error: message }
    }
  }

  const createModerationAction = async (reportId: string, actionData: any) => {
    try {
      await apiCall(`/content/reports/${reportId}/action`, {
        method: 'POST',
        body: JSON.stringify(actionData)
      })
      
      return { success: true }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to create moderation action'
      error.value = message
      return { success: false, error: message }
    }
  }

  return {
    dashboardStats,
    users,
    usersPagination,
    contentReports,
    reportsPagination,
    systemConfig,
    isLoading,
    error,
    isAdmin,
    loadDashboard,
    loadUsers,
    updateUserStatus,
    toggleAdminStatus,
    loadContentReports,
    loadSystemConfig,
    updateSystemConfig,
    toggleMaintenanceMode,
    getSystemMetrics,
    updateReportStatus,
    createModerationAction
  }
})
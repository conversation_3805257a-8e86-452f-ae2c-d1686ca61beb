import { Request, Response } from 'express';
import { NoteRepository } from '../repositories/NoteRepository';
import { NoteModel, CreateNoteData, UpdateNoteData, NoteFilters, PaginationOptions } from '../models/Note';

export class NoteController {
  // GET /api/notes - List notes with pagination and filtering
  static async getNotes(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Parse query parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100); // Max 100 per page
      const sortBy = (req.query.sortBy as string) || 'updated_at';
      const sortOrder = (req.query.sortOrder as string) || 'desc';
      const noteType = req.query.noteType as string;
      // By default, exclude archived notes unless specifically requested
      const isArchived = req.query.isArchived === 'true' ? true : req.query.isArchived === 'false' ? false : false;
      const search = req.query.search as string;
      const tags = req.query.tags ? (req.query.tags as string).split(',') : undefined;
      const groupId = req.query.groupId as string;

      const filters: NoteFilters = {
        userId,
        groupId,
        noteType: noteType as any,
        tags,
        isArchived,
        search
      };

      const pagination: PaginationOptions = {
        page,
        limit,
        sortBy: sortBy as any,
        sortOrder: sortOrder as any
      };

      const result = await NoteRepository.findByUserId(filters, pagination);

      // Add tags to each note
      const notesWithTags = await Promise.all(
        result.notes.map(async (note) => {
          const tags = await NoteRepository.getTagsForNote(note.id);
          return { ...note, tags };
        })
      );

      res.json({
        notes: notesWithTags,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: Math.ceil(result.total / limit)
        }
      });
    } catch (error) {
      console.error('Error fetching notes:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // GET /api/notes/:id - Get specific note
  static async getNoteById(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const noteId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const note = await NoteRepository.findById(noteId);

      if (!note) {
        res.status(404).json({ error: 'Note not found' });
        return;
      }

      // Check if user owns the note or has access through group
      if (note.userId !== userId) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      // Add tags to the note
      const tags = await NoteRepository.getTagsForNote(note.id);
      const noteWithTags = { ...note, tags };

      res.json(noteWithTags);
    } catch (error) {
      console.error('Error fetching note:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // POST /api/notes - Create new note
  static async createNote(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { title, content, noteType, groupId, tags } = req.body;

      // Validate required fields
      if (!title || !content || !noteType) {
        res.status(400).json({ error: 'Title, content, and noteType are required' });
        return;
      }

      // Validate note type
      if (!NoteModel.validateNoteType(noteType)) {
        res.status(400).json({ error: 'Invalid note type. Must be richtext, markdown, or kanban' });
        return;
      }

      // Validate title
      const titleValidation = NoteModel.validateTitle(title);
      if (!titleValidation.valid) {
        res.status(400).json({ error: 'Invalid title', details: titleValidation.errors });
        return;
      }

      // Validate content
      const contentValidation = NoteModel.validateContent(content, noteType);
      if (!contentValidation.valid) {
        res.status(400).json({ error: 'Invalid content', details: contentValidation.errors });
        return;
      }

      const noteData: CreateNoteData = {
        userId,
        groupId,
        title: title.trim(),
        content,
        noteType
      };

      const note = await NoteRepository.create(noteData);

      // Add tags if provided
      if (tags && Array.isArray(tags)) {
        for (const tagName of tags) {
          try {
            // Create tag if it doesn't exist
            let tag;
            try {
              tag = await NoteRepository.createTag(tagName.trim(), userId);
            } catch (error) {
              // Tag might already exist, get existing tags and find it
              const existingTags = await NoteRepository.getTagsByUserId(userId);
              tag = existingTags.find(t => t.name === tagName.trim());
            }

            if (tag) {
              await NoteRepository.addTagToNote(note.id, tag.id);
            }
          } catch (error) {
            console.error('Error adding tag to note:', error);
            // Continue with other tags
          }
        }
      }

      // Fetch the created note with tags
      const noteTags = await NoteRepository.getTagsForNote(note.id);
      const noteWithTags = { ...note, tags: noteTags };

      res.status(201).json(noteWithTags);
    } catch (error) {
      console.error('Error creating note:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // PUT /api/notes/:id - Update note
  static async updateNote(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const noteId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if note exists and user has access
      const existingNote = await NoteRepository.findById(noteId);
      if (!existingNote) {
        res.status(404).json({ error: 'Note not found' });
        return;
      }

      if (existingNote.userId !== userId) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const { title, content, isArchived, tags } = req.body;
      const updateData: UpdateNoteData = {};

      // Validate and set title if provided
      if (title !== undefined) {
        const titleValidation = NoteModel.validateTitle(title);
        if (!titleValidation.valid) {
          res.status(400).json({ error: 'Invalid title', details: titleValidation.errors });
          return;
        }
        updateData.title = title.trim();
      }

      // Validate and set content if provided
      if (content !== undefined) {
        const contentValidation = NoteModel.validateContent(content, existingNote.noteType);
        if (!contentValidation.valid) {
          res.status(400).json({ error: 'Invalid content', details: contentValidation.errors });
          return;
        }
        updateData.content = content;
      }

      // Set archived status if provided
      if (isArchived !== undefined) {
        updateData.isArchived = Boolean(isArchived);
      }

      const updatedNote = await NoteRepository.update(noteId, updateData, userId);

      // Update tags if provided
      if (tags && Array.isArray(tags)) {
        // Get current tags
        const currentTags = await NoteRepository.getTagsForNote(noteId);
        const currentTagNames = currentTags.map(t => t.name);
        const newTagNames = tags.map((t: string) => t.trim());

        // Remove tags that are no longer needed
        for (const currentTag of currentTags) {
          if (!newTagNames.includes(currentTag.name)) {
            await NoteRepository.removeTagFromNote(noteId, currentTag.id);
          }
        }

        // Add new tags
        for (const tagName of newTagNames) {
          if (!currentTagNames.includes(tagName)) {
            try {
              // Create tag if it doesn't exist
              let tag;
              try {
                tag = await NoteRepository.createTag(tagName, userId);
              } catch (error) {
                // Tag might already exist
                const existingTags = await NoteRepository.getTagsByUserId(userId);
                tag = existingTags.find(t => t.name === tagName);
              }

              if (tag) {
                await NoteRepository.addTagToNote(noteId, tag.id);
              }
            } catch (error) {
              console.error('Error adding tag to note:', error);
            }
          }
        }
      }

      // Fetch the updated note with tags
      const noteTags = await NoteRepository.getTagsForNote(noteId);
      const noteWithTags = { ...updatedNote, tags: noteTags };

      res.json(noteWithTags);
    } catch (error) {
      console.error('Error updating note:', error);
      if (error instanceof Error) {
        res.status(500).json({ error: 'Internal server error', details: error.message });
      } else {
        res.status(500).json({ error: 'Internal server error' });
      }
    }
  }

  // DELETE /api/notes/:id - Soft delete note (mark as archived)
  static async deleteNote(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const noteId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if note exists and user has access
      const existingNote = await NoteRepository.findById(noteId);
      if (!existingNote) {
        res.status(404).json({ error: 'Note not found' });
        return;
      }

      if (existingNote.userId !== userId) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      await NoteRepository.delete(noteId);

      res.json({ message: 'Note archived successfully' });
    } catch (error) {
      console.error('Error archiving note:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // GET /api/notes/:id/versions - Get note version history
  static async getNoteVersions(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const noteId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if note exists and user has access
      const note = await NoteRepository.findById(noteId);
      if (!note) {
        res.status(404).json({ error: 'Note not found' });
        return;
      }

      if (note.userId !== userId) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const versions = await NoteRepository.getVersions(noteId);

      res.json({ versions });
    } catch (error) {
      console.error('Error fetching note versions:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // GET /api/tags - Get user's tags
  static async getTags(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const tags = await NoteRepository.getTagsByUserId(userId);

      res.json({ tags });
    } catch (error) {
      console.error('Error fetching tags:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // POST /api/tags - Create new tag
  static async createTag(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { name } = req.body;

      if (!name || typeof name !== 'string' || name.trim().length === 0) {
        res.status(400).json({ error: 'Tag name is required' });
        return;
      }

      if (name.length > 50) {
        res.status(400).json({ error: 'Tag name must be less than 50 characters' });
        return;
      }

      const tag = await NoteRepository.createTag(name.trim(), userId);

      res.status(201).json(tag);
    } catch (error) {
      if (error instanceof Error && error.message === 'Tag already exists') {
        res.status(409).json({ error: 'Tag already exists' });
        return;
      }

      console.error('Error creating tag:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
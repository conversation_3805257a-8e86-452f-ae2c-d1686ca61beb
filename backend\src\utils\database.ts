import { Database } from 'sqlite3';
import { getDatabase } from '../config/database';

export class DatabaseManager {
  private static instance: Database;

  static getInstance(): Database {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = getDatabase();
    }
    return DatabaseManager.instance;
  }

  static setInstance(db: Database): void {
    DatabaseManager.instance = db;
  }
}
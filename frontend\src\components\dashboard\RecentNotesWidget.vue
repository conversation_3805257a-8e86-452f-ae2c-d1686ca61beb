<template>
  <DashboardWidget title="Recent Notes" icon="fas fa-clock">
    <template #actions>
      <router-link to="/dashboard/recent" class="button is-ghost is-small">
        <span>View All</span>
        <span class="icon">
          <i class="fas fa-arrow-right"></i>
        </span>
      </router-link>
    </template>

    <div v-if="loading" class="has-text-centered">
      <div class="loader"></div>
    </div>

    <div v-else-if="recentNotes.length === 0" class="empty-state">
      <div class="icon is-large has-text-grey-light">
        <i class="fas fa-sticky-note fa-2x"></i>
      </div>
      <p class="has-text-grey">No recent notes</p>
      <button class="button is-primary is-small mt-2" @click="$emit('create-note')">
        Create Your First Note
      </button>
    </div>

    <div v-else class="recent-notes-list">
      <div v-for="note in recentNotes" :key="note.id" class="note-item" @click="openNote(note)">
        <div class="note-icon">
          <span class="icon">
            <i :class="getNoteIcon(note.noteType)"></i>
          </span>
        </div>

        <div class="note-content">
          <p class="note-title">{{ note.title || 'Untitled Note' }}</p>
          <p class="note-preview">{{ getPreview(note.content) }}</p>
          <div class="note-meta">
            <span class="tag is-small" :class="getTypeClass(note.noteType)">
              {{ note.noteType }}
            </span>
            <span class="has-text-grey is-size-7">
              {{ formatDate(note.updatedAt) }}
            </span>
          </div>
        </div>

        <div class="note-actions">
          <button class="button is-ghost is-small" @click.stop="toggleFavorite(note)"
            :title="note.metadata?.isFavorite ? 'Remove from favorites' : 'Add to favorites'">
            <span class="icon">
              <i class="fas fa-star" :class="{ 'has-text-warning': note.metadata?.isFavorite }"></i>
            </span>
          </button>
        </div>
      </div>
    </div>
  </DashboardWidget>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotesStore } from '../../stores/notes'
import DashboardWidget from './DashboardWidget.vue'
import type { Note as ServiceNote } from '../../services/noteService'

interface Note {
  id: string
  title: string
  content: string
  noteType: string
  isFavorite: boolean
  updatedAt: string
  metadata?: {
    isFavorite?: boolean
    collaborators?: string[]
  }
  groupId?: string
}

const emit = defineEmits<{
  'create-note': []
}>()

const router = useRouter()
const notesStore = useNotesStore()

const loading = ref(true)

// Computed
const recentNotes = computed(() => {
  // Get recent notes from store or use mock data
  let notes: Note[] = [];
  
  if (notesStore.notes && notesStore.notes.length > 0) {
    // Convert service notes to component notes
    notes = notesStore.notes.map(note => ({
      id: note.id,
      title: note.title,
      content: note.content,
      noteType: note.noteType,
      isFavorite: note.metadata?.isFavorite || false,
      updatedAt: note.updatedAt
    }));
  } else {
    // Use mock data
    notes = mockNotes.value;
  }
  
  return notes
    .slice()
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 5) // Show only 5 most recent
})

// Mock data for demo
const mockNotes = ref<Note[]>([
  {
    id: '1',
    title: 'Project Planning Meeting',
    content: 'Discussed the upcoming project milestones and resource allocation...',
    noteType: 'meeting',
    isFavorite: true,
    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
  },
  {
    id: '2',
    title: 'Marketing Ideas',
    content: 'Brainstorming session for Q4 marketing campaign. Key ideas include...',
    noteType: 'idea',
    isFavorite: false,
    updatedAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString() // 5 hours ago
  },
  {
    id: '3',
    title: 'Technical Documentation',
    content: 'API documentation for the new user authentication system...',
    noteType: 'documentation',
    isFavorite: false,
    updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
  }
])

// Methods
const getNoteIcon = (type: string) => {
  switch (type) {
    case 'meeting': return 'fas fa-users'
    case 'idea': return 'fas fa-lightbulb'
    case 'documentation': return 'fas fa-file-alt'
    case 'task': return 'fas fa-check-square'
    default: return 'fas fa-sticky-note'
  }
}

const getTypeClass = (type: string) => {
  switch (type) {
    case 'meeting': return 'is-info'
    case 'idea': return 'is-warning'
    case 'documentation': return 'is-primary'
    case 'task': return 'is-success'
    default: return 'is-light'
  }
}

const getPreview = (content: string) => {
  if (!content) return 'No content'
  return content.length > 80 ? content.substring(0, 80) + '...' : content
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60)

  if (diffHours < 1) {
    return 'Just now'
  } else if (diffHours < 24) {
    return `${Math.round(diffHours)}h ago`
  } else if (diffHours < 48) {
    return 'Yesterday'
  } else {
    return date.toLocaleDateString()
  }
}

const openNote = (note: Note) => {
  router.push(`/dashboard/notes/${note.id}`)
}

const toggleFavorite = async (note: Note) => {
  try {
    // TODO: Implement API call
    note.isFavorite = !note.isFavorite
    console.log('Toggled favorite for note:', note.id)
  } catch (error) {
    console.error('Failed to toggle favorite:', error)
  }
}

const loadRecentNotes = async () => {
  try {
    loading.value = true

    // Try to load from store
    if (notesStore.loadNotes) {
      await notesStore.loadNotes()
    }

    // Simulate loading delay for demo
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    console.error('Failed to load recent notes:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadRecentNotes()
})
</script>

<style scoped>
.empty-state {
  text-align: center;
  padding: 2rem 0;
}

.recent-notes-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.note-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: var(--transition-fast);
}

.note-item:hover {
  background: var(--color-surface);
  border-color: var(--color-primary);
}

.note-icon {
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.note-content {
  flex: 1;
  min-width: 0;
}

.note-title {
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--color-text);
}

.note-preview {
  font-size: 0.875rem;
  color: var(--color-text-muted);
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.note-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.note-actions {
  flex-shrink: 0;
}
</style>
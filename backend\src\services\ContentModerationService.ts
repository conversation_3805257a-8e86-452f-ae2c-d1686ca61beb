import { getDatabase } from '../config/database';
import { v4 as uuidv4 } from 'uuid';
import { AuditLogRepository } from '../repositories/AuditLogRepository';

export interface ContentReport {
  id: string;
  type: 'note' | 'user' | 'group' | 'comment';
  resourceId: string;
  reportedBy: string;
  reportedUser?: string;
  reason: 'spam' | 'inappropriate_content' | 'harassment' | 'copyright' | 'other';
  description: string;
  status: 'pending' | 'under_review' | 'resolved' | 'dismissed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTo?: string;
  resolution?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ModerationAction {
  id: string;
  reportId: string;
  actionType: 'warn' | 'suspend' | 'ban' | 'delete_content' | 'no_action';
  duration?: number; // in hours for temporary actions
  reason: string;
  performedBy: string;
  createdAt: Date;
}

export class ContentModerationService {
  /**
   * Initialize content moderation tables
   */
  static async initialize(): Promise<void> {
    const db = getDatabase();

    // Create content reports table
    const reportsQuery = `
      CREATE TABLE IF NOT EXISTS content_reports (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL CHECK (type IN ('note', 'user', 'group', 'comment')),
        resource_id TEXT NOT NULL,
        reported_by TEXT NOT NULL,
        reported_user TEXT,
        reason TEXT NOT NULL CHECK (reason IN ('spam', 'inappropriate_content', 'harassment', 'copyright', 'other')),
        description TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'resolved', 'dismissed')),
        priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
        assigned_to TEXT,
        resolution TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (reported_by) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (reported_user) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_to) REFERENCES users (id) ON DELETE SET NULL
      )
    `;

    // Create moderation actions table
    const actionsQuery = `
      CREATE TABLE IF NOT EXISTS moderation_actions (
        id TEXT PRIMARY KEY,
        report_id TEXT NOT NULL,
        action_type TEXT NOT NULL CHECK (action_type IN ('warn', 'suspend', 'ban', 'delete_content', 'no_action')),
        duration INTEGER, -- in hours
        reason TEXT NOT NULL,
        performed_by TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (report_id) REFERENCES content_reports (id) ON DELETE CASCADE,
        FOREIGN KEY (performed_by) REFERENCES users (id) ON DELETE CASCADE
      )
    `;

    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.run(reportsQuery, (err) => {
          if (err) {
            reject(err);
            return;
          }
        });

        db.run(actionsQuery, (err) => {
          if (err) {
            reject(err);
            return;
          }
        });

        // Create indexes
        db.run('CREATE INDEX IF NOT EXISTS idx_content_reports_status ON content_reports (status)');
        db.run('CREATE INDEX IF NOT EXISTS idx_content_reports_type ON content_reports (type)');
        db.run('CREATE INDEX IF NOT EXISTS idx_content_reports_priority ON content_reports (priority)');
        db.run('CREATE INDEX IF NOT EXISTS idx_content_reports_assigned ON content_reports (assigned_to)');
        db.run('CREATE INDEX IF NOT EXISTS idx_moderation_actions_report ON moderation_actions (report_id)');

        resolve();
      });
    });
  }

  /**
   * Create a new content report
   */
  static async createReport(reportData: Omit<ContentReport, 'id' | 'status' | 'createdAt' | 'updatedAt'>): Promise<ContentReport> {
    const db = getDatabase();
    const id = uuidv4();
    const now = new Date().toISOString();

    const query = `
      INSERT INTO content_reports (
        id, type, resource_id, reported_by, reported_user, reason, 
        description, priority, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      reportData.type,
      reportData.resourceId,
      reportData.reportedBy,
      reportData.reportedUser || null,
      reportData.reason,
      reportData.description,
      reportData.priority || 'medium',
      now,
      now
    ];

    return new Promise((resolve, reject) => {
      db.run(query, params, function(err) {
        if (err) {
          reject(err);
          return;
        }

        // Fetch the created report
        ContentModerationService.getReportById(id)
          .then(report => {
            if (!report) {
              reject(new Error('Failed to create report'));
              return;
            }
            resolve(report);
          })
          .catch(reject);
      });
    });
  }

  /**
   * Get reports with filtering and pagination
   */
  static async getReports(options: {
    page?: number;
    limit?: number;
    status?: string;
    type?: string;
    priority?: string;
    assignedTo?: string;
  }): Promise<{ reports: ContentReport[]; total: number }> {
    const db = getDatabase();
    const page = options.page || 1;
    const limit = options.limit || 20;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (options.status) {
      whereClause += ' AND status = ?';
      params.push(options.status);
    }

    if (options.type) {
      whereClause += ' AND type = ?';
      params.push(options.type);
    }

    if (options.priority) {
      whereClause += ' AND priority = ?';
      params.push(options.priority);
    }

    if (options.assignedTo) {
      whereClause += ' AND assigned_to = ?';
      params.push(options.assignedTo);
    }

    const countQuery = `SELECT COUNT(*) as total FROM content_reports ${whereClause}`;
    const reportsQuery = `
      SELECT cr.*, u1.display_name as reported_by_name, u2.display_name as reported_user_name
      FROM content_reports cr
      LEFT JOIN users u1 ON cr.reported_by = u1.id
      LEFT JOIN users u2 ON cr.reported_user = u2.id
      ${whereClause}
      ORDER BY 
        CASE priority 
          WHEN 'critical' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'medium' THEN 3 
          WHEN 'low' THEN 4 
        END,
        created_at DESC
      LIMIT ? OFFSET ?
    `;

    return new Promise((resolve, reject) => {
      db.get(countQuery, params, (err, countRow: any) => {
        if (err) {
          reject(err);
          return;
        }

        db.all(reportsQuery, [...params, limit, offset], (err, rows: any[]) => {
          if (err) {
            reject(err);
            return;
          }

          const reports = rows.map(this.mapRowToReport);
          resolve({
            reports,
            total: countRow.total
          });
        });
      });
    });
  }

  /**
   * Get a specific report by ID
   */
  static async getReportById(id: string): Promise<ContentReport | null> {
    const db = getDatabase();
    const query = `
      SELECT cr.*, u1.display_name as reported_by_name, u2.display_name as reported_user_name
      FROM content_reports cr
      LEFT JOIN users u1 ON cr.reported_by = u1.id
      LEFT JOIN users u2 ON cr.reported_user = u2.id
      WHERE cr.id = ?
    `;

    return new Promise((resolve, reject) => {
      db.get(query, [id], (err, row) => {
        if (err) {
          reject(err);
          return;
        }

        if (!row) {
          resolve(null);
          return;
        }

        resolve(this.mapRowToReport(row));
      });
    });
  }

  /**
   * Update report status
   */
  static async updateReportStatus(
    reportId: string, 
    status: ContentReport['status'], 
    assignedTo?: string,
    resolution?: string
  ): Promise<void> {
    const db = getDatabase();
    const now = new Date().toISOString();

    let query = 'UPDATE content_reports SET status = ?, updated_at = ?';
    const params: any[] = [status, now];

    if (assignedTo !== undefined) {
      query += ', assigned_to = ?';
      params.push(assignedTo);
    }

    if (resolution !== undefined) {
      query += ', resolution = ?';
      params.push(resolution);
    }

    query += ' WHERE id = ?';
    params.push(reportId);

    return new Promise((resolve, reject) => {
      db.run(query, params, function(err) {
        if (err) {
          reject(err);
          return;
        }

        if (this.changes === 0) {
          reject(new Error('Report not found'));
          return;
        }

        resolve();
      });
    });
  }

  /**
   * Create a moderation action
   */
  static async createModerationAction(actionData: Omit<ModerationAction, 'id' | 'createdAt'>): Promise<ModerationAction> {
    const db = getDatabase();
    const id = uuidv4();
    const now = new Date().toISOString();

    const query = `
      INSERT INTO moderation_actions (
        id, report_id, action_type, duration, reason, performed_by, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      actionData.reportId,
      actionData.actionType,
      actionData.duration || null,
      actionData.reason,
      actionData.performedBy,
      now
    ];

    return new Promise((resolve, reject) => {
      db.run(query, params, function(err) {
        if (err) {
          reject(err);
          return;
        }

        // Log the moderation action
        AuditLogRepository.create({
          user_id: actionData.performedBy,
          session_id: 'system',
          action: 'moderation_action',
          resource_type: 'content_report',
          resource_id: actionData.reportId,
          ip_address: 'system',
          user_agent: 'system',
          request_method: 'POST',
          request_path: '/api/admin/moderation/action',
          request_body: JSON.stringify(actionData),
          response_status: 200,
          response_time_ms: 0,
          metadata: {
            actionType: actionData.actionType,
            duration: actionData.duration
          }
        });

        resolve({
          id,
          ...actionData,
          createdAt: new Date(now)
        });
      });
    });
  }

  /**
   * Get moderation actions for a report
   */
  static async getReportActions(reportId: string): Promise<ModerationAction[]> {
    const db = getDatabase();
    const query = `
      SELECT ma.*, u.display_name as performed_by_name
      FROM moderation_actions ma
      LEFT JOIN users u ON ma.performed_by = u.id
      WHERE ma.report_id = ?
      ORDER BY ma.created_at DESC
    `;

    return new Promise((resolve, reject) => {
      db.all(query, [reportId], (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        const actions = rows.map(row => ({
          id: row.id,
          reportId: row.report_id,
          actionType: row.action_type,
          duration: row.duration,
          reason: row.reason,
          performedBy: row.performed_by,
          createdAt: new Date(row.created_at)
        }));

        resolve(actions);
      });
    });
  }

  private static mapRowToReport(row: any): ContentReport {
    return {
      id: row.id,
      type: row.type,
      resourceId: row.resource_id,
      reportedBy: row.reported_by,
      reportedUser: row.reported_user,
      reason: row.reason,
      description: row.description,
      status: row.status,
      priority: row.priority,
      assignedTo: row.assigned_to,
      resolution: row.resolution,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}
# Design Document

## Overview

The note-taking application will be built as a modern web application using Vue.js 3 with TypeScript for the frontend and Node.js with Express for the backend. The architecture follows a modular, component-based approach with clear separation of concerns to support the multi-format note editing, real-time collaboration, and responsive design requirements.

The system will support three primary note formats: Rich Text, Markdown with live preview, and Kanban boards. The application emphasizes performance with <2 second load times, <500ms search responses, and <200ms real-time sync latency while supporting 10,000+ notes per user and 1000+ concurrent users.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend (Vue.js + TypeScript)"
        UI[User Interface]
        Store[Vuex Store]
        Router[Vue Router]
        Components[Modular Components]
    end
    
    subgraph "Backend (Node.js + Express)"
        API[REST API]
        Auth[Authentication Service]
        WS[WebSocket Service]
        Business[Business Logic]
    end
    
    subgraph "Data Layer"
        SQLite[(SQLite - Dev)]
        MariaDB[(MariaDB - Prod)]
        Redis[(Redis Cache)]
    end
    
    subgraph "External Services"
        OAuth[Google OAuth]
        Email[Email Service]
        CDN[Content Delivery Network]
    end
    
    UI --> Store
    Store --> API
    API --> Business
    Business --> SQLite
    Business --> MariaDB
    API --> Redis
    Auth --> OAuth
    Business --> Email
    UI --> CDN
    WS --> Store
```

### Component Architecture

The frontend will be organized into modular components for maintainability and scalability:

- **Layout Components**: Responsive panels (Sidebar, NoteList, Editor)
- **Editor Components**: Format-specific editors (RichText, Markdown, Kanban)
- **Shared Components**: Search, Navigation, Settings, Authentication
- **Utility Components**: Modals, Tooltips, Loading states

### Database Schema Design

```mermaid
erDiagram
    Users ||--o{ Notes : creates
    Users ||--o{ Groups : belongs_to
    Users ||--o{ UserSessions : has
    Notes ||--o{ NoteVersions : has_versions
    Notes ||--o{ NoteTags : has_tags
    Notes ||--o{ NoteShares : shared_via
    Groups ||--o{ GroupMembers : contains
    Groups ||--o{ Notes : contains
    
    Users {
        uuid id PK
        string email UK
        string password_hash
        string display_name
        string avatar_url
        json preferences
        boolean email_verified
        string two_fa_secret
        timestamp created_at
        timestamp updated_at
    }
    
    Notes {
        uuid id PK
        uuid user_id FK
        uuid group_id FK
        string title
        text content
        string note_type
        json metadata
        boolean is_archived
        timestamp created_at
        timestamp updated_at
    }
    
    Groups {
        uuid id PK
        uuid owner_id FK
        string name
        text description
        json settings
        timestamp created_at
    }
```

## Components and Interfaces

### Frontend Components

#### 1. Layout System
- **AppLayout**: Main responsive container managing panel visibility
- **Sidebar**: Navigation, groups, tags with collapsible sections
- **NoteList**: Paginated note cards with filtering and sorting
- **EditorPanel**: Container for format-specific editors

#### 2. Editor Components
- **RichTextEditor**: WYSIWYG editor with toolbar and formatting
- **MarkdownEditor**: Split-pane editor with live preview
- **KanbanBoardInline**: Drag-and-drop board with customizable columns

#### 3. Core Services
- **AuthService**: JWT token management and OAuth integration
- **NoteService**: CRUD operations and real-time synchronization
- **SearchService**: Full-text search with debouncing and caching
- **CollaborationService**: WebSocket management for real-time features

### Backend API Design

#### Authentication Endpoints
```typescript
POST /api/auth/register
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
POST /api/auth/forgot-password
POST /api/auth/reset-password
POST /api/auth/verify-email
POST /api/auth/google
```

#### Note Management Endpoints
```typescript
GET    /api/notes              // List notes with pagination
POST   /api/notes              // Create new note
GET    /api/notes/:id          // Get specific note
PUT    /api/notes/:id          // Update note
DELETE /api/notes/:id          // Delete note
GET    /api/notes/:id/versions // Get note version history
POST   /api/notes/:id/share    // Share note
```

#### Search and Organization
```typescript
GET /api/search?q=query&type=note_type&tags=tag1,tag2
GET /api/tags
POST /api/tags
GET /api/groups
POST /api/groups
```

### WebSocket Events

Real-time collaboration will use WebSocket connections with the following event structure:

```typescript
// Client to Server
interface ClientEvents {
  'note:join': { noteId: string }
  'note:leave': { noteId: string }
  'note:edit': { noteId: string, operation: Operation }
  'cursor:update': { noteId: string, position: CursorPosition }
}

// Server to Client
interface ServerEvents {
  'note:operation': { noteId: string, operation: Operation, userId: string }
  'user:joined': { noteId: string, user: User }
  'user:left': { noteId: string, userId: string }
  'cursor:update': { noteId: string, userId: string, position: CursorPosition }
}
```

## Data Models

### Core Data Models

#### User Model
```typescript
interface User {
  id: string
  email: string
  displayName: string
  avatarUrl?: string
  preferences: UserPreferences
  emailVerified: boolean
  twoFactorEnabled: boolean
  createdAt: Date
  updatedAt: Date
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  autoSaveInterval: number
  notifications: NotificationSettings
}
```

#### Note Model
```typescript
interface Note {
  id: string
  userId: string
  groupId?: string
  title: string
  content: string | KanbanBoard | RichTextContent
  noteType: 'richtext' | 'markdown' | 'kanban'
  metadata: NoteMetadata
  tags: string[]
  isArchived: boolean
  shareSettings: ShareSettings
  createdAt: Date
  updatedAt: Date
}

interface NoteMetadata {
  wordCount?: number
  readingTime?: number
  lastEditedBy?: string
  collaborators?: string[]
}
```

#### Collaboration Models
```typescript
interface Group {
  id: string
  ownerId: string
  name: string
  description: string
  members: GroupMember[]
  settings: GroupSettings
  createdAt: Date
}

interface GroupMember {
  userId: string
  role: 'admin' | 'editor' | 'viewer'
  joinedAt: Date
}

interface ShareSettings {
  accessLevel: 'private' | 'shared' | 'unlisted' | 'public'
  permissions: Permission[]
  expiresAt?: Date
  passwordProtected: boolean
}
```

## Error Handling

### Frontend Error Handling
- **Global Error Boundary**: Catch and display user-friendly error messages
- **Network Error Handling**: Retry logic with exponential backoff
- **Validation Errors**: Real-time form validation with clear feedback
- **Offline Handling**: Queue operations and sync when reconnected

### Backend Error Handling
- **Structured Error Responses**: Consistent error format across all endpoints
- **Input Validation**: Comprehensive validation using Joi or similar
- **Rate Limiting**: Prevent abuse with configurable limits
- **Logging**: Structured logging with correlation IDs for debugging

```typescript
interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: string
  requestId: string
}
```

## Testing Strategy

### Frontend Testing
- **Unit Tests**: Vue Test Utils for component testing
- **Integration Tests**: API integration and user flow testing
- **E2E Tests**: Cypress for critical user journeys
- **Performance Tests**: Lighthouse CI for performance regression detection

### Backend Testing
- **Unit Tests**: Jest for business logic and utilities
- **Integration Tests**: Supertest for API endpoint testing
- **Database Tests**: In-memory SQLite for fast test execution
- **Load Tests**: Artillery or k6 for performance validation

### Test Coverage Goals
- Unit Tests: >90% code coverage
- Integration Tests: All API endpoints and critical user flows
- E2E Tests: Core user journeys (auth, note creation, collaboration)
- Performance Tests: Load time, search response, sync latency validation

### Continuous Integration
- **Pre-commit Hooks**: Linting, type checking, unit tests
- **Pull Request Checks**: Full test suite, security scanning
- **Deployment Pipeline**: Automated testing in staging environment
- **Monitoring**: Real-time performance and error tracking in production

## Security Considerations

### Authentication & Authorization
- JWT tokens with short expiration and refresh token rotation
- OAuth 2.0 integration with Google for secure third-party authentication
- Role-based access control (RBAC) for group and note permissions
- Two-factor authentication support for enhanced security

### Data Protection
- Encryption at rest using AES-256 for sensitive note content
- TLS 1.3 for all client-server communication
- Input sanitization and validation to prevent XSS and injection attacks
- CSRF protection using SameSite cookies and CSRF tokens

### Privacy & Compliance
- GDPR compliance with data export and deletion capabilities
- Audit logging for all user actions and data access
- Configurable data retention policies
- Privacy-first design with minimal data collection

## Performance Optimization

### Frontend Optimization
- Code splitting and lazy loading for reduced initial bundle size
- Virtual scrolling for large note lists
- Debounced search and auto-save to reduce API calls
- Service worker for offline functionality and caching

### Backend Optimization
- Database indexing for fast search and retrieval
- Redis caching for frequently accessed data
- Connection pooling for database efficiency
- CDN integration for static asset delivery

### Real-time Performance
- WebSocket connection pooling and management
- Operational transformation for conflict-free collaborative editing
- Efficient diff algorithms for minimal data transfer
- Client-side prediction for responsive user experience
// Performance optimization utilities

/**
 * Debounce function to limit the rate of function execution
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

/**
 * Throttle function to limit the rate of function execution
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(this: unknown, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * Lazy loading utility for components
 */
export function lazyLoad<T>(
  importFunc: () => Promise<T>,
  delay = 0
): () => Promise<T> {
  let componentPromise: Promise<T> | null = null
  
  return () => {
    if (!componentPromise) {
      componentPromise = new Promise(resolve => {
        setTimeout(() => {
          importFunc().then(resolve)
        }, delay)
      })
    }
    return componentPromise
  }
}

/**
 * Intersection Observer utility for lazy loading
 */
export function createIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  }
  
  return new IntersectionObserver(callback, defaultOptions)
}

/**
 * Performance measurement utility
 */
export class PerformanceMonitor {
  private marks: Map<string, number> = new Map()
  private measures: Map<string, number> = new Map()
  
  mark(name: string): void {
    this.marks.set(name, performance.now())
  }
  
  measure(name: string, startMark: string): number {
    const startTime = this.marks.get(startMark)
    if (!startTime) {
      console.warn(`Start mark "${startMark}" not found`)
      return 0
    }
    
    const duration = performance.now() - startTime
    this.measures.set(name, duration)
    
    if (import.meta.env.MODE === 'development') {
      console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`)
    }
    
    return duration
  }
  
  getMeasure(name: string): number | undefined {
    return this.measures.get(name)
  }
  
  clear(): void {
    this.marks.clear()
    this.measures.clear()
  }
  
  getReport(): Record<string, number> {
    return Object.fromEntries(this.measures)
  }
}

/**
 * Memory usage monitoring
 */
export function getMemoryUsage(): any | null {
  if ('memory' in performance) {
    return (performance as any).memory
  }
  return null
}

/**
 * Bundle size analyzer helper
 */
export function analyzeBundleSize(): void {
  if (import.meta.env.MODE === 'development') {
    // Dynamic import to avoid including in production
    import('rollup-plugin-visualizer').then(() => {
      console.log('Bundle analysis available at /dist/stats.html after build')
    }).catch(() => {
      console.log('Bundle analyzer not available')
    })
  }
}

/**
 * Image lazy loading utility
 */
export function lazyLoadImage(
  img: HTMLImageElement,
  src: string,
  placeholder?: string
): void {
  if (placeholder) {
    img.src = placeholder
  }
  
  const observer = createIntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const image = entry.target as HTMLImageElement
          image.src = src
          image.onload = () => {
            image.classList.add('loaded')
          }
          observer.unobserve(image)
        }
      })
    },
    { rootMargin: '100px' }
  )
  
  observer.observe(img)
}

/**
 * Code splitting utility for dynamic imports
 */
export function createAsyncComponent<T>(
  importFunc: () => Promise<T>,
  loadingComponent?: any,
  errorComponent?: any,
  delay = 200,
  timeout = 10000
) {
  return {
    component: importFunc,
    loading: loadingComponent,
    error: errorComponent,
    delay,
    timeout
  }
}

/**
 * Resource preloading utility
 */
export function preloadResource(
  href: string,
  as: 'script' | 'style' | 'font' | 'image' = 'script'
): void {
  const link = document.createElement('link')
  link.rel = 'preload'
  link.href = href
  link.as = as
  
  if (as === 'font') {
    link.crossOrigin = 'anonymous'
  }
  
  document.head.appendChild(link)
}

/**
 * Critical resource hints
 */
export function addResourceHints(): void {
  // Preconnect to external domains
  const preconnectDomains = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com'
  ]
  
  preconnectDomains.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = domain
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  })
}

/**
 * Web Vitals monitoring
 */
export async function measureWebVitals(): Promise<void> {
  try {
    const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('web-vitals')
    
    getCLS(console.log)
    getFID(console.log)
    getFCP(console.log)
    getLCP(console.log)
    getTTFB(console.log)
  } catch (error) {
    console.warn('Web Vitals not available:', error)
  }
}
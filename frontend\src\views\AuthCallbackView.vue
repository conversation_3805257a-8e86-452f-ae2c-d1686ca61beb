<template>
  <div class="auth-callback-container">
    <div class="container">
      <div class="columns is-centered">
        <div class="column is-narrow">
          <div class="card">
            <div class="card-content has-text-centered">
              <div v-if="isLoading" class="loading-state">
                <div class="spinner"></div>
                <p class="mt-4">Completing authentication...</p>
              </div>
              
              <div v-else-if="error" class="error-state">
                <span class="icon is-large has-text-danger">
                  <i class="fas fa-exclamation-triangle fa-2x"></i>
                </span>
                <h2 class="title is-4 mt-4">Authentication Failed</h2>
                <p class="has-text-grey">{{ error }}</p>
                <div class="buttons is-centered mt-5">
                  <router-link to="/login" class="button is-primary">
                    <span class="icon">
                      <i class="fas fa-sign-in-alt"></i>
                    </span>
                    <span>Try Again</span>
                  </router-link>
                </div>
              </div>
              
              <div v-else class="success-state">
                <span class="icon is-large has-text-success">
                  <i class="fas fa-check-circle fa-2x"></i>
                </span>
                <h2 class="title is-4 mt-4">Authentication Successful</h2>
                <p class="has-text-grey">Redirecting to dashboard...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const isLoading = ref(true)
const error = ref<string | null>(null)

onMounted(async () => {
  try {
    const { token, refresh } = route.query
    
    if (!token || !refresh) {
      throw new Error('Missing authentication tokens')
    }

    // Set tokens in the auth store
    authStore.setTokens(token as string, refresh as string)
    
    // Initialize user data
    await authStore.initializeAuth()
    
    if (authStore.isAuthenticated) {
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/dashboard')
      }, 1500)
    } else {
      throw new Error('Failed to authenticate user')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Authentication failed'
  } finally {
    isLoading.value = false
  }
})
</script>

<style scoped>
.auth-callback-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
  min-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3273dc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state,
.error-state,
.success-state {
  padding: 2rem 1rem;
}
</style>
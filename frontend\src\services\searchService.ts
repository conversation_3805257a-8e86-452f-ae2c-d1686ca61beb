import { httpClient } from '../utils/http'
import type { Note, Tag } from './noteService'

export interface SearchFilters {
  query?: string
  noteType?: 'richtext' | 'markdown' | 'kanban'
  tags?: string[]
  dateFrom?: string
  dateTo?: string
  archived?: boolean
}

export interface SearchOptions {
  page: number
  limit: number
  sortBy?: 'relevance' | 'created_at' | 'updated_at' | 'title'
  sortOrder?: 'asc' | 'desc'
}

export interface SearchHighlight {
  title?: string
  content?: string
}

export interface SearchResult {
  note: Note
  tags: Tag[]
  highlights: SearchHighlight
  relevanceScore: number
}

export interface SearchResponse {
  results: SearchResult[]
  total: number
  searchTime: number
  suggestions?: string[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  filters: {
    query: string | null
    noteType: string | null
    tags: string[] | null
    dateFrom: string | null
    dateTo: string | null
    isArchived: boolean
  }
}

export interface SearchSuggestions {
  titles: string[]
  tags: string[]
}

export interface SearchStats {
  totalNotes: number
  notesByType: {
    richtext: number
    markdown: number
    kanban: number
  }
  totalTags: number
}

class SearchService {
  // Debounce timeout for search queries
  private searchTimeout: number | null = null
  private readonly DEBOUNCE_DELAY = 300

  // Search notes with full-text search
  async searchNotes(
    filters: SearchFilters = {},
    options: SearchOptions = { page: 1, limit: 20 }
  ): Promise<SearchResponse> {
    const params = new URLSearchParams()

    // Add search query
    if (filters.query) {
      params.append('q', filters.query)
    }

    // Add filters
    if (filters.noteType) {
      params.append('type', filters.noteType)
    }

    if (filters.tags && filters.tags.length > 0) {
      params.append('tags', filters.tags.join(','))
    }

    if (filters.dateFrom) {
      params.append('dateFrom', filters.dateFrom)
    }

    if (filters.dateTo) {
      params.append('dateTo', filters.dateTo)
    }

    if (filters.archived !== undefined) {
      params.append('archived', filters.archived.toString())
    }

    // Add pagination and sorting
    params.append('page', options.page.toString())
    params.append('limit', options.limit.toString())

    if (options.sortBy) {
      params.append('sortBy', options.sortBy)
    }

    if (options.sortOrder) {
      params.append('sortOrder', options.sortOrder)
    }

    const response = await httpClient.get<SearchResponse>(`/search?${params.toString()}`)

    if (response.error) {
      throw new Error(response.error)
    }

    return response.data!
  }

  // Debounced search for real-time search as user types
  async debouncedSearch(
    filters: SearchFilters,
    options: SearchOptions = { page: 1, limit: 20 }
  ): Promise<SearchResponse> {
    return new Promise((resolve, reject) => {
      // Clear existing timeout
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout)
      }

      // Set new timeout
      this.searchTimeout = window.setTimeout(async () => {
        try {
          const result = await this.searchNotes(filters, options)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }, this.DEBOUNCE_DELAY)
    })
  }

  // Get search suggestions
  async getSearchSuggestions(query: string): Promise<SearchSuggestions> {
    if (!query || query.trim().length < 2) {
      return { titles: [], tags: [] }
    }

    const params = new URLSearchParams()
    params.append('q', query.trim())

    const response = await httpClient.get<{ suggestions: SearchSuggestions }>(`/search/suggestions?${params.toString()}`)

    if (response.error) {
      throw new Error(response.error)
    }

    return response.data!.suggestions
  }

  // Get search statistics
  async getSearchStats(): Promise<SearchStats> {
    const response = await httpClient.get<SearchStats>('/search/stats')

    if (response.error) {
      throw new Error(response.error)
    }

    return response.data!
  }

  // Reindex search data (for maintenance)
  async reindexSearch(): Promise<{ message: string; notesReindexed: number }> {
    const response = await httpClient.post<{ message: string; notesReindexed: number }>('/search/reindex')

    if (response.error) {
      throw new Error(response.error)
    }

    return response.data!
  }

  // Cancel any pending debounced search
  cancelDebouncedSearch(): void {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout)
      this.searchTimeout = null
    }
  }

  // Build search query with advanced operators
  buildAdvancedQuery(terms: {
    include?: string[]
    exclude?: string[]
    exact?: string[]
    title?: string
    content?: string
  }): string {
    const queryParts: string[] = []

    // Include terms (OR)
    if (terms.include && terms.include.length > 0) {
      queryParts.push(terms.include.join(' OR '))
    }

    // Exclude terms
    if (terms.exclude && terms.exclude.length > 0) {
      queryParts.push(...terms.exclude.map(term => `-${term}`))
    }

    // Exact phrases
    if (terms.exact && terms.exact.length > 0) {
      queryParts.push(...terms.exact.map(phrase => `"${phrase}"`))
    }

    // Title-specific search
    if (terms.title) {
      queryParts.push(`title:${terms.title}`)
    }

    // Content-specific search
    if (terms.content) {
      queryParts.push(`content:${terms.content}`)
    }

    return queryParts.join(' ')
  }

  // Parse search query to extract filters
  parseSearchQuery(query: string): {
    cleanQuery: string | undefined
    extractedFilters: Partial<SearchFilters>
  } {
    let cleanQuery = query
    const extractedFilters: Partial<SearchFilters> = {}

    // Extract note type filter (type:markdown)
    const typeMatch = query.match(/type:(\w+)/i)
    if (typeMatch) {
      const noteType = typeMatch[1].toLowerCase()
      if (['richtext', 'markdown', 'kanban'].includes(noteType)) {
        extractedFilters.noteType = noteType as any
        cleanQuery = cleanQuery.replace(typeMatch[0], '').trim()
      }
    }

    // Extract tag filters (tag:work tag:personal)
    const tagMatches = query.match(/tag:(\w+)/gi)
    if (tagMatches) {
      extractedFilters.tags = tagMatches.map(match => match.replace('tag:', ''))
      tagMatches.forEach(match => {
        cleanQuery = cleanQuery.replace(match, '').trim()
      })
    }

    // Extract date filters (after:2024-01-01 before:2024-12-31)
    const afterMatch = query.match(/after:(\d{4}-\d{2}-\d{2})/i)
    if (afterMatch) {
      extractedFilters.dateFrom = afterMatch[1]
      cleanQuery = cleanQuery.replace(afterMatch[0], '').trim()
    }

    const beforeMatch = query.match(/before:(\d{4}-\d{2}-\d{2})/i)
    if (beforeMatch) {
      extractedFilters.dateTo = beforeMatch[1]
      cleanQuery = cleanQuery.replace(beforeMatch[0], '').trim()
    }

    // Extract archived filter (archived:true)
    const archivedMatch = query.match(/archived:(true|false)/i)
    if (archivedMatch) {
      extractedFilters.archived = archivedMatch[1].toLowerCase() === 'true'
      cleanQuery = cleanQuery.replace(archivedMatch[0], '').trim()
    }

    // Clean up extra spaces
    cleanQuery = cleanQuery.replace(/\s+/g, ' ').trim()

    return {
      cleanQuery: cleanQuery || undefined,
      extractedFilters
    }
  }

  // Format search results for display
  formatSearchResults(results: SearchResult[]): SearchResult[] {
    return results.map(result => ({
      ...result,
      highlights: {
        title: this.cleanHighlight(result.highlights.title),
        content: this.cleanHighlight(result.highlights.content)
      }
    }))
  }

  // Clean HTML highlights for safe display
  private cleanHighlight(highlight?: string): string | undefined {
    if (!highlight) return undefined

    // Only allow <mark> tags for highlighting
    return highlight.replace(/<(?!\/?(mark)\b)[^>]*>/gi, '')
  }

  // Get search history from localStorage
  getSearchHistory(): string[] {
    try {
      const history = localStorage.getItem('searchHistory')
      return history ? JSON.parse(history) : []
    } catch (error) {
      console.error('Failed to parse search history:', error)
      return []
    }
  }

  // Add search query to history
  addToSearchHistory(query: string): void {
    if (!query || query.trim().length < 2) return

    try {
      const history = this.getSearchHistory()
      const trimmedQuery = query.trim()

      // Remove if already exists
      const filtered = history.filter(item => item !== trimmedQuery)

      // Add to beginning
      filtered.unshift(trimmedQuery)

      // Keep only last 20 searches
      const limited = filtered.slice(0, 20)

      localStorage.setItem('searchHistory', JSON.stringify(limited))
    } catch (error) {
      console.error('Failed to save search history:', error)
    }
  }

  // Clear search history
  clearSearchHistory(): void {
    try {
      localStorage.removeItem('searchHistory')
    } catch (error) {
      console.error('Failed to clear search history:', error)
    }
  }

  // Get popular search terms
  getPopularSearchTerms(): string[] {
    const history = this.getSearchHistory()
    const termCounts = new Map<string, number>()

    // Count word frequency in search history
    history.forEach(query => {
      const words = query.toLowerCase().split(/\s+/)
      words.forEach(word => {
        if (word.length > 2) { // Only count words longer than 2 characters
          termCounts.set(word, (termCounts.get(word) || 0) + 1)
        }
      })
    })

    // Sort by frequency and return top terms
    return Array.from(termCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([term]) => term)
  }
}

export const searchService = new SearchService()
export default searchService
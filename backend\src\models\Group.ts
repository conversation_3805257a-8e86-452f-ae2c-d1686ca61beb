import { v4 as uuidv4 } from 'uuid';

export interface GroupSettings {
  allowMemberInvites: boolean;
  defaultNotePermissions: 'view' | 'edit';
  requireApprovalForJoin: boolean;
  maxMembers: number;
}

export interface Group {
  id: string;
  ownerId: string;
  name: string;
  description?: string;
  settings: GroupSettings;
  createdAt: Date;
}

export interface GroupMember {
  groupId: string;
  userId: string;
  role: 'admin' | 'editor' | 'viewer';
  joinedAt: Date;
}

export interface GroupWithMembers extends Group {
  members: (GroupMember & {
    displayName: string;
    email: string;
    avatarUrl?: string;
  })[];
  memberCount: number;
}

export interface CreateGroupData {
  ownerId: string;
  name: string;
  description?: string;
  settings?: Partial<GroupSettings>;
}

export interface UpdateGroupData {
  name?: string;
  description?: string;
  settings?: Partial<GroupSettings>;
}

export interface GroupInvitation {
  id: string;
  groupId: string;
  invitedBy: string;
  invitedEmail: string;
  role: 'admin' | 'editor' | 'viewer';
  token: string;
  expiresAt: Date;
  createdAt: Date;
}

export interface CreateInvitationData {
  groupId: string;
  invitedBy: string;
  invitedEmail: string;
  role: 'admin' | 'editor' | 'viewer';
}

export class GroupModel {
  static generateId(): string {
    return uuidv4();
  }

  static getDefaultSettings(): GroupSettings {
    return {
      allowMemberInvites: true,
      defaultNotePermissions: 'view',
      requireApprovalForJoin: false,
      maxMembers: 50
    };
  }

  static validateGroupName(name: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!name || name.trim().length === 0) {
      errors.push('Group name is required');
    }
    
    if (name.length > 100) {
      errors.push('Group name must be less than 100 characters');
    }

    if (name.length < 3) {
      errors.push('Group name must be at least 3 characters');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static validateDescription(description?: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (description && description.length > 500) {
      errors.push('Group description must be less than 500 characters');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static validateRole(role: string): boolean {
    return ['admin', 'editor', 'viewer'].includes(role);
  }

  static validateSettings(settings: Partial<GroupSettings>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (settings.maxMembers !== undefined) {
      if (settings.maxMembers < 1) {
        errors.push('Maximum members must be at least 1');
      }
      if (settings.maxMembers > 1000) {
        errors.push('Maximum members cannot exceed 1000');
      }
    }

    if (settings.defaultNotePermissions !== undefined) {
      if (!['view', 'edit'].includes(settings.defaultNotePermissions)) {
        errors.push('Default note permissions must be either "view" or "edit"');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static canUserPerformAction(
    userRole: 'admin' | 'editor' | 'viewer',
    action: 'invite' | 'remove_member' | 'edit_settings' | 'delete_group' | 'edit_notes' | 'view_notes'
  ): boolean {
    const permissions = {
      admin: ['invite', 'remove_member', 'edit_settings', 'delete_group', 'edit_notes', 'view_notes'],
      editor: ['edit_notes', 'view_notes'],
      viewer: ['view_notes']
    };

    return permissions[userRole].includes(action);
  }

  static generateInvitationToken(): string {
    return uuidv4();
  }

  static getInvitationExpiryDate(): Date {
    // Invitations expire in 7 days
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 7);
    return expiryDate;
  }
}
<template>
  <div 
    v-if="isVisible" 
    class="theme-loading-indicator"
    :class="{ 'is-compact': compact }"
  >
    <div class="loading-content">
      <div class="loading-spinner">
        <div class="spinner"></div>
      </div>
      
      <div class="loading-text">
        <div class="loading-title">{{ title }}</div>
        <div v-if="showProgress" class="loading-subtitle">
          {{ currentTheme || 'Preparing themes...' }}
        </div>
      </div>
      
      <div v-if="showProgress" class="loading-progress">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
        <div class="progress-text">
          {{ loaded }} / {{ total }}
        </div>
      </div>
    </div>
    
    <div v-if="errors.length > 0 && showErrors" class="loading-errors">
      <div class="error-title">
        <i class="fas fa-exclamation-triangle"></i>
        Some themes failed to load
      </div>
      <div class="error-list">
        <div v-for="error in errors.slice(0, 3)" :key="error" class="error-item">
          {{ error }}
        </div>
        <div v-if="errors.length > 3" class="error-more">
          +{{ errors.length - 3 }} more errors
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { PreloadProgress } from '@/utils/ThemePreloader'

interface Props {
  progress?: PreloadProgress | null
  title?: string
  compact?: boolean
  showProgress?: boolean
  showErrors?: boolean
  autoHide?: boolean
  autoHideDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  progress: null,
  title: 'Loading themes...',
  compact: false,
  showProgress: true,
  showErrors: true,
  autoHide: true,
  autoHideDelay: 2000
})

const isVisible = ref(false)
const hideTimeout = ref<number | null>(null)

// Computed properties from progress
const loaded = computed(() => props.progress?.loaded || 0)
const total = computed(() => props.progress?.total || 0)
const currentTheme = computed(() => props.progress?.current)
const errors = computed(() => props.progress?.errors || [])

const progressPercentage = computed(() => {
  if (total.value === 0) return 0
  return Math.round((loaded.value / total.value) * 100)
})

// Watch for progress changes
watch(
  () => props.progress,
  (newProgress) => {
    if (newProgress && newProgress.total > 0) {
      isVisible.value = true
      
      // Clear existing timeout
      if (hideTimeout.value) {
        clearTimeout(hideTimeout.value)
        hideTimeout.value = null
      }
      
      // Auto-hide when complete
      if (props.autoHide && newProgress.loaded >= newProgress.total && !newProgress.current) {
        hideTimeout.value = window.setTimeout(() => {
          isVisible.value = false
        }, props.autoHideDelay)
      }
    } else if (!newProgress) {
      isVisible.value = false
    }
  },
  { immediate: true }
)

// Manual show/hide methods
const show = () => {
  isVisible.value = true
}

const hide = () => {
  isVisible.value = false
  if (hideTimeout.value) {
    clearTimeout(hideTimeout.value)
    hideTimeout.value = null
  }
}

defineExpose({
  show,
  hide
})
</script>

<style scoped>
.theme-loading-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-6);
  min-width: 320px;
  max-width: 480px;
  z-index: var(--z-modal);
  backdrop-filter: blur(8px);
  animation: fadeInScale 0.3s ease-out;
}

.theme-loading-indicator.is-compact {
  padding: var(--spacing-4);
  min-width: 240px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.theme-loading-indicator.is-compact .loading-content {
  gap: var(--spacing-3);
}

/* Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-surface);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.theme-loading-indicator.is-compact .spinner {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

/* Text */
.loading-text {
  text-align: center;
}

.loading-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-strong);
  margin-bottom: var(--spacing-1);
}

.theme-loading-indicator.is-compact .loading-title {
  font-size: var(--font-size-base);
}

.loading-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  font-family: var(--font-mono);
}

/* Progress */
.loading-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--color-surface);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
  animation: shimmer 2s infinite;
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  font-weight: var(--font-weight-medium);
  min-width: 48px;
  text-align: right;
}

/* Errors */
.loading-errors {
  margin-top: var(--spacing-2);
  padding: var(--spacing-3);
  background: var(--notification-danger-background);
  border: 1px solid var(--notification-danger-border);
  border-radius: var(--radius);
}

.error-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--notification-danger-text);
  margin-bottom: var(--spacing-2);
}

.error-list {
  font-size: var(--font-size-xs);
  color: var(--notification-danger-text);
}

.error-item {
  margin-bottom: var(--spacing-1);
  font-family: var(--font-mono);
}

.error-more {
  font-style: italic;
  opacity: 0.8;
}

/* Animations */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.progress-fill {
  background: linear-gradient(
    90deg,
    var(--color-primary) 0%,
    var(--color-primary-light) 50%,
    var(--color-primary) 100%
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite, width 0.3s ease;
}

/* Responsive */
@media (max-width: 480px) {
  .theme-loading-indicator {
    left: var(--spacing-4);
    right: var(--spacing-4);
    transform: translateY(-50%);
    min-width: auto;
    max-width: none;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .theme-loading-indicator {
    animation: none;
  }
  
  .spinner {
    animation: none;
    border-top-color: var(--color-primary);
  }
  
  .progress-fill {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .theme-loading-indicator {
    border-width: 2px;
  }
  
  .progress-bar {
    border: 1px solid var(--color-border);
  }
}
</style>
# Rich Text Editor Component

## Overview

The RichTextEditor component is a comprehensive WYSIWYG editor built with TipTap and Vue 3. It provides a full-featured rich text editing experience with a customizable toolbar and extensive formatting options.

## Features

### Basic Formatting
- **Bold** (Ctrl+B)
- **Italic** (Ctrl+I) 
- **Underline** (Ctrl+U)
- **Strikethrough**

### Headers
- H1, H2, H3 heading levels

### Lists
- Bullet lists
- Numbered lists

### Links and Images
- Link insertion with URL prompt (Ctrl+K)
- Image insertion via URL
- Image upload from local files

### Tables
- Insert tables with headers
- Add/remove columns and rows
- Delete entire tables
- Resizable columns

### Text Alignment
- Left align
- Center align
- Right align

### Text Styling
- Text color picker
- Text highlighting

### Advanced Features
- Undo/Redo functionality (Ctrl+Z, Ctrl+Y)
- Content validation and sanitization
- Keyboard shortcuts
- Responsive toolbar design

## Usage

```vue
<template>
  <RichTextEditor
    v-model="content"
    @change="handleContentChange"
    :disabled="isReadOnly"
    placeholder="Start writing..."
  />
</template>

<script setup>
import RichTextEditor from '@/components/editors/RichTextEditor.vue'
import { ref } from 'vue'

const content = ref('<p>Initial content</p>')
const isReadOnly = ref(false)

const handleContentChange = (newContent) => {
  console.log('Content changed:', newContent)
}
</script>
```

## Props

- `modelValue` (string): The HTML content of the editor
- `placeholder` (string): Placeholder text when editor is empty
- `disabled` (boolean): Whether the editor is read-only

## Events

- `update:modelValue`: Emitted when content changes
- `change`: Emitted when content changes (same as update:modelValue)

## Keyboard Shortcuts

- `Ctrl+B` / `Cmd+B`: Toggle bold
- `Ctrl+I` / `Cmd+I`: Toggle italic
- `Ctrl+U` / `Cmd+U`: Toggle underline
- `Ctrl+K` / `Cmd+K`: Insert/edit link
- `Ctrl+Z` / `Cmd+Z`: Undo
- `Ctrl+Y` / `Cmd+Y` / `Ctrl+Shift+Z`: Redo

## Security

The component includes built-in content sanitization that:
- Removes `<script>` tags
- Strips dangerous event handlers (`onclick`, etc.)
- Removes `javascript:` URLs

## Styling

The component uses BulmaCSS classes and includes comprehensive styling for:
- Toolbar buttons with hover and active states
- Editor content with proper typography
- Table styling with borders and selection
- Color picker integration
- Responsive design
# Kanban Board Components

## Overview

The kanban functionality is handled by two main components:
- **KanbanBoardInline**: Integration wrapper for notes
- **KanbanBoard**: Main interactive kanban board component

## Usage

```vue
<template>
  <KanbanBoardInline
    v-model="boardData"
    @change="handleBoardChange"
    :disabled="isReadOnly"
    placeholder="Start building your kanban board..."
  />
</template>

<script setup>
import KanbanBoardInline from '@/components/editors/KanbanBoardInline.vue'
import { ref } from 'vue'

const boardData = ref('')
const isReadOnly = ref(false)

const handleBoardChange = (newBoardData) => {
  console.log('Board changed:', JSON.parse(newBoardData))
}
</script>
```
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { UserRepository } from '../../repositories/UserRepository';
import { getDatabase } from '../../config/database';

// Mock database
vi.mock('../../config/database');

describe('UserRepository', () => {
  let mockDb: any;

  beforeEach(() => {
    mockDb = {
      get: vi.fn(),
      run: vi.fn(),
      all: vi.fn(),
      prepare: vi.fn(() => ({
        get: vi.fn(),
        run: vi.fn(),
        all: vi.fn()
      }))
    };
    vi.mocked(getDatabase).mockReturnValue(mockDb);
  });

  describe('findByEmail', () => {
    it('should return user when found', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        display_name: 'Test User',
        created_at: new Date().toISOString()
      };

      mockDb.get.mockResolvedValue(mockUser);

      const result = await UserRepository.findByEmail('<EMAIL>');

      expect(result).toEqual(mockUser);
      expect(mockDb.get).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM users WHERE email = ?'),
        ['<EMAIL>']
      );
    });

    it('should return null when user not found', async () => {
      mockDb.get.mockResolvedValue(undefined);

      const result = await UserRepository.findByEmail('<EMAIL>');

      expect(result).toBeNull();
    });
  });

  describe('findById', () => {
    it('should return user when found', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        display_name: 'Test User'
      };

      mockDb.get.mockResolvedValue(mockUser);

      const result = await UserRepository.findById('user-123');

      expect(result).toEqual(mockUser);
      expect(mockDb.get).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM users WHERE id = ?'),
        ['user-123']
      );
    });

    it('should return null when user not found', async () => {
      mockDb.get.mockResolvedValue(undefined);

      const result = await UserRepository.findById('nonexistent');

      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should create user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        display_name: 'Test User'
      };

      const mockCreatedUser = {
        id: 'user-123',
        email: '<EMAIL>',
        display_name: 'Test User',
        created_at: new Date().toISOString()
      };

      mockDb.run.mockResolvedValue({ lastID: 'user-123' });
      mockDb.get.mockResolvedValue(mockCreatedUser);

      const result = await UserRepository.create(userData);

      expect(result).toEqual(mockCreatedUser);
      expect(mockDb.run).toHaveBeenCalled();
    });

    it('should handle creation errors', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        display_name: 'Test User'
      };

      mockDb.run.mockRejectedValue(new Error('Database error'));

      await expect(UserRepository.create(userData)).rejects.toThrow('Database error');
    });
  });

  describe('update', () => {
    it('should update user successfully', async () => {
      const updateData = {
        display_name: 'Updated Name',
        avatar_url: 'https://example.com/avatar.jpg'
      };

      const mockUpdatedUser = {
        id: 'user-123',
        email: '<EMAIL>',
        display_name: 'Updated Name',
        avatar_url: 'https://example.com/avatar.jpg'
      };

      mockDb.run.mockResolvedValue({ changes: 1 });
      mockDb.get.mockResolvedValue(mockUpdatedUser);

      const result = await UserRepository.update('user-123', updateData);

      expect(result).toEqual(mockUpdatedUser);
      expect(mockDb.run).toHaveBeenCalled();
    });

    it('should handle update errors', async () => {
      mockDb.run.mockRejectedValue(new Error('Update failed'));

      await expect(
        UserRepository.update('user-123', { display_name: 'New Name' })
      ).rejects.toThrow('Update failed');
    });
  });

  describe('delete', () => {
    it('should delete user successfully', async () => {
      mockDb.run.mockResolvedValue({ changes: 1 });

      const result = await UserRepository.delete('user-123');

      expect(result).toBe(true);
      expect(mockDb.run).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM users WHERE id = ?'),
        ['user-123']
      );
    });

    it('should return false when user not found', async () => {
      mockDb.run.mockResolvedValue({ changes: 0 });

      const result = await UserRepository.delete('nonexistent');

      expect(result).toBe(false);
    });
  });
});
<template>
  <div class="markdown-editor">
    <!-- Toolbar -->
    <div class="toolbar">
      <div class="toolbar-group">
        <!-- Text formatting -->
        <button @click="insertMarkdown('**', '**')" class="button is-small" title="Bold (Ctrl+B)">
          <i class="fas fa-bold"></i>
        </button>

        <button @click="insertMarkdown('*', '*')" class="button is-small" title="Italic (Ctrl+I)">
          <i class="fas fa-italic"></i>
        </button>

        <button @click="insertMarkdown('~~', '~~')" class="button is-small" title="Strikethrough">
          <i class="fas fa-strikethrough"></i>
        </button>

        <button @click="insertMarkdown('`', '`')" class="button is-small" title="Inline Code">
          <i class="fas fa-code"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Headers -->
        <button @click="insertLineMarkdown('# ')" class="button is-small" title="Heading 1">
          H1
        </button>

        <button @click="insertLineMarkdown('## ')" class="button is-small" title="Heading 2">
          H2
        </button>

        <button @click="insertLineMarkdown('### ')" class="button is-small" title="Heading 3">
          H3
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Lists -->
        <button @click="insertLineMarkdown('- ')" class="button is-small" title="Bullet List">
          <i class="fas fa-list-ul"></i>
        </button>

        <button @click="insertLineMarkdown('1. ')" class="button is-small" title="Numbered List">
          <i class="fas fa-list-ol"></i>
        </button>

        <button @click="insertLineMarkdown('- [ ] ')" class="button is-small" title="Task List">
          <i class="fas fa-tasks"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Links and Images -->
        <button @click="insertLink" class="button is-small" title="Add Link (Ctrl+K)">
          <i class="fas fa-link"></i>
        </button>

        <button @click="insertImage" class="button is-small" title="Add Image">
          <i class="fas fa-image"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Code and Quote -->
        <button @click="insertCodeBlock" class="button is-small" title="Code Block">
          <i class="fas fa-code"></i>
        </button>

        <button @click="insertLineMarkdown('> ')" class="button is-small" title="Quote">
          <i class="fas fa-quote-right"></i>
        </button>

        <button @click="insertTable" class="button is-small" title="Insert Table">
          <i class="fas fa-table"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Search and Replace -->
        <button @click="toggleSearchReplace" :class="{ 'is-active': showSearchReplace }" class="button is-small"
          title="Search & Replace (Ctrl+F)">
          <i class="fas fa-search"></i>
        </button>

        <!-- Export -->
        <button @click="exportMarkdown" class="button is-small" title="Export Markdown">
          <i class="fas fa-download"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- View options -->
        <button @click="togglePreview" :class="{ 'is-active': showPreview }" class="button is-small"
          title="Toggle Preview">
          <i class="fas fa-eye"></i>
        </button>

        <button @click="toggleSyncScroll" :class="{ 'is-active': syncScroll }" class="button is-small"
          title="Sync Scroll">
          <i class="fas fa-sync"></i>
        </button>
      </div>
    </div>

    <!-- Search and Replace Panel -->
    <div v-if="showSearchReplace" class="search-replace-panel">
      <div class="field is-grouped">
        <div class="control is-expanded">
          <input ref="searchInput" v-model="searchQuery" @input="performSearch" @keydown.enter="findNext"
            @keydown.escape="toggleSearchReplace" class="input is-small" type="text" placeholder="Search..." />
        </div>
        <div class="control">
          <button @click="findPrevious" class="button is-small" title="Previous (Shift+Enter)"
            :disabled="!searchResults.length">
            <i class="fas fa-chevron-up"></i>
          </button>
        </div>
        <div class="control">
          <button @click="findNext" class="button is-small" title="Next (Enter)" :disabled="!searchResults.length">
            <i class="fas fa-chevron-down"></i>
          </button>
        </div>
        <div class="control">
          <button @click="toggleSearchReplace" class="button is-small" title="Close (Esc)">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <div class="field is-grouped" v-if="showReplaceRow">
        <div class="control is-expanded">
          <input v-model="replaceQuery" @keydown.enter="replaceNext" class="input is-small" type="text"
            placeholder="Replace with..." />
        </div>
        <div class="control">
          <button @click="replaceNext" class="button is-small" title="Replace Next" :disabled="!searchResults.length">
            Replace
          </button>
        </div>
        <div class="control">
          <button @click="replaceAll" class="button is-small" title="Replace All" :disabled="!searchResults.length">
            All
          </button>
        </div>
      </div>

      <div class="search-info" v-if="searchQuery">
        <span class="tag is-light">
          {{ currentSearchIndex + 1 }} of {{ searchResults.length }} matches
        </span>
        <button @click="showReplaceRow = !showReplaceRow" class="button is-small is-text">
          {{ showReplaceRow ? 'Hide' : 'Show' }} Replace
        </button>
      </div>
    </div>

    <!-- Auto-completion dropdown -->
    <div v-if="showAutoComplete && autoCompleteItems.length" class="autocomplete-dropdown" :style="autoCompleteStyle">
      <div v-for="(item, index) in autoCompleteItems" :key="index" @click="selectAutoComplete(item)"
        class="autocomplete-item" :class="{ 'is-active': index === selectedAutoCompleteIndex }">
        <span class="autocomplete-text">{{ item.text }}</span>
        <span class="autocomplete-description">{{ item.description }}</span>
      </div>
    </div>

    <!-- Editor Content -->
    <div class="editor-content" :class="{ 'split-view': showPreview }">
      <!-- Editor Pane -->
      <div class="editor-pane" :class="{ 'half-width': showPreview }">
        <textarea ref="editorTextarea" v-model="localContent" @input="onInput" @scroll="onEditorScroll"
          @keydown="onKeyDown" @select="onSelect" @click="onCursorMove" class="markdown-textarea"
          :placeholder="placeholder" :disabled="disabled" spellcheck="false"></textarea>
      </div>

      <!-- Preview Pane -->
      <div v-if="showPreview" class="preview-pane half-width" ref="previewPane" @scroll="onPreviewScroll">
        <div class="preview-content" v-html="renderedMarkdown"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { marked } from 'marked'
import { highlightCode } from '@/utils/highlightConfig'
import 'highlight.js/styles/github.css'

interface Props {
  modelValue: string
  placeholder?: string
  disabled?: boolean
  showPreview?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Start writing your markdown...',
  disabled: false,
  showPreview: true
})

const emit = defineEmits<Emits>()

// Refs
const editorTextarea = ref<HTMLTextAreaElement>()
const previewPane = ref<HTMLElement>()
const searchInput = ref<HTMLInputElement>()

// Local state
const localContent = ref('')
const showPreview = ref(props.showPreview)
const syncScroll = ref(true)
const isScrollingSynced = ref(false)

// Search and Replace state
const showSearchReplace = ref(false)
const showReplaceRow = ref(false)
const searchQuery = ref('')
const replaceQuery = ref('')
const searchResults = ref<Array<{ start: number; end: number }>>([])
const currentSearchIndex = ref(-1)

// Auto-completion state
const showAutoComplete = ref(false)
const autoCompleteItems = ref<Array<{ text: string; description: string; insert: string }>>([])
const selectedAutoCompleteIndex = ref(0)
const autoCompleteStyle = ref({})
const cursorPosition = ref(0)

// Auto-completion suggestions
const autoCompleteSuggestions = [
  { text: '**bold**', description: 'Bold text', insert: '****', cursorOffset: -2 },
  { text: '*italic*', description: 'Italic text', insert: '**', cursorOffset: -1 },
  { text: '~~strikethrough~~', description: 'Strikethrough text', insert: '~~~~', cursorOffset: -2 },
  { text: '`code`', description: 'Inline code', insert: '``', cursorOffset: -1 },
  { text: '```code block```', description: 'Code block', insert: '```\n\n```', cursorOffset: -4 },
  { text: '[link](url)', description: 'Link', insert: '[]()', cursorOffset: -3 },
  { text: '![image](url)', description: 'Image', insert: '![]()', cursorOffset: -3 },
  { text: '# Heading 1', description: 'Level 1 heading', insert: '# ' },
  { text: '## Heading 2', description: 'Level 2 heading', insert: '## ' },
  { text: '### Heading 3', description: 'Level 3 heading', insert: '### ' },
  { text: '- List item', description: 'Bullet list', insert: '- ' },
  { text: '1. Numbered item', description: 'Numbered list', insert: '1. ' },
  { text: '- [ ] Task', description: 'Task list', insert: '- [ ] ' },
  { text: '> Quote', description: 'Blockquote', insert: '> ' },
  { text: '---', description: 'Horizontal rule', insert: '---' },
  {
    text: 'Table',
    description: 'Insert table',
    insert: '| Header 1 | Header 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |'
  }
]

// Configure marked with custom renderer overrides (v16 API expects token objects)
const renderer = new marked.Renderer()

renderer.code = ({ text, lang }: any) => {
  try {
    const highlighted = highlightCode(text, lang)
    const langClass = lang ? `language-${lang}` : ''
    return `<pre><code class="hljs ${langClass}">${highlighted}</code></pre>`
  } catch (err) {
    console.warn('Syntax highlighting failed:', err)
    const langClass = lang ? `language-${lang}` : ''
    return `<pre><code class="${langClass}">${text}</code></pre>`
  }
}

renderer.table = (token: any) => {
  const header = token.header || ''
  const body = (token.rows && token.rows.join('')) || token.body || ''
  return `<table class="table is-striped is-hoverable">
    <thead>${header}</thead>
    <tbody>${body}</tbody>
  </table>`
}

renderer.link = ({ href, title, tokens }: any) => {
  const titleAttr = title ? ` title="${title}"` : ''
  const text = (tokens && tokens.map((t: any) => t.raw).join('')) || href
  return `<a href="${href}"${titleAttr} target="_blank" rel="noopener noreferrer">${text}</a>`
}

marked.setOptions({
  renderer,
  gfm: true,
  breaks: true,
  pedantic: false,
  // smartLists and similar options are enabled by default in v16
})

// Computed
const renderedMarkdown = computed(() => {
  if (!localContent.value.trim()) {
    return '<p class="has-text-grey-light">Preview will appear here...</p>'
  }

  try {
    return marked(localContent.value)
  } catch (error) {
    console.error('Markdown parsing error:', error)
    return '<p class="has-text-danger">Error parsing markdown</p>'
  }
})

// Methods
const onInput = () => {
  emit('update:modelValue', localContent.value)
  emit('change', localContent.value)
}

const insertMarkdown = (before: string, after: string = before) => {
  if (!editorTextarea.value) return

  const textarea = editorTextarea.value
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const selectedText = localContent.value.substring(start, end)

  const replacement = before + selectedText + after
  const newContent =
    localContent.value.substring(0, start) +
    replacement +
    localContent.value.substring(end)

  localContent.value = newContent

  // Set cursor position
  nextTick(() => {
    const newCursorPos = selectedText ? start + replacement.length : start + before.length
    textarea.setSelectionRange(newCursorPos, newCursorPos)
    textarea.focus()
  })

  onInput()
}

const insertLineMarkdown = (prefix: string) => {
  if (!editorTextarea.value) return

  const textarea = editorTextarea.value
  const start = textarea.selectionStart
  const lines = localContent.value.split('\n')

  // Find which line the cursor is on
  let currentPos = 0
  let lineIndex = 0

  for (let i = 0; i < lines.length; i++) {
    if (currentPos + lines[i].length >= start) {
      lineIndex = i
      break
    }
    currentPos += lines[i].length + 1 // +1 for newline
  }

  // Insert prefix at beginning of line
  lines[lineIndex] = prefix + lines[lineIndex]
  localContent.value = lines.join('\n')

  // Update cursor position
  nextTick(() => {
    const newCursorPos = start + prefix.length
    textarea.setSelectionRange(newCursorPos, newCursorPos)
    textarea.focus()
  })

  onInput()
}

const insertLink = () => {
  const url = window.prompt('Enter URL:')
  if (url) {
    const text = window.prompt('Enter link text:', url)
    insertMarkdown(`[${text || url}](${url})`, '')
  }
}

const insertImage = () => {
  const url = window.prompt('Enter image URL:')
  if (url) {
    const alt = window.prompt('Enter alt text:', 'Image')
    insertMarkdown(`![${alt || 'Image'}](${url})`, '')
  }
}

const insertCodeBlock = () => {
  const language = window.prompt('Enter language (optional):') || ''
  insertMarkdown(`\`\`\`${language}\n`, '\n```')
}

const insertTable = () => {
  const tableMarkdown = `| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |`

  insertMarkdown(tableMarkdown, '')
}

const togglePreview = () => {
  showPreview.value = !showPreview.value
}

const toggleSyncScroll = () => {
  syncScroll.value = !syncScroll.value
}

// Search and Replace methods
const toggleSearchReplace = () => {
  showSearchReplace.value = !showSearchReplace.value
  showReplaceRow.value = false

  if (showSearchReplace.value) {
    nextTick(() => {
      searchInput.value?.focus()
    })
  } else {
    searchQuery.value = ''
    replaceQuery.value = ''
    searchResults.value = []
    currentSearchIndex.value = -1
    clearSearchHighlights()
  }
}

const performSearch = () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    currentSearchIndex.value = -1
    clearSearchHighlights()
    return
  }

  const content = localContent.value
  const query = searchQuery.value
  const results: Array<{ start: number; end: number }> = []

  let index = 0
  while (index < content.length) {
    const found = content.toLowerCase().indexOf(query.toLowerCase(), index)
    if (found === -1) break

    results.push({
      start: found,
      end: found + query.length
    })

    index = found + 1
  }

  searchResults.value = results
  currentSearchIndex.value = results.length > 0 ? 0 : -1

  if (results.length > 0) {
    highlightSearchResult(0)
  }
}

const findNext = () => {
  if (searchResults.value.length === 0) return

  currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length
  highlightSearchResult(currentSearchIndex.value)
}

const findPrevious = () => {
  if (searchResults.value.length === 0) return

  currentSearchIndex.value = currentSearchIndex.value <= 0
    ? searchResults.value.length - 1
    : currentSearchIndex.value - 1
  highlightSearchResult(currentSearchIndex.value)
}

const highlightSearchResult = (index: number) => {
  if (!editorTextarea.value || index < 0 || index >= searchResults.value.length) return

  const result = searchResults.value[index]
  editorTextarea.value.setSelectionRange(result.start, result.end)
  editorTextarea.value.focus()
}

const clearSearchHighlights = () => {
  // Clear any search highlights in the editor
  if (editorTextarea.value) {
    const start = editorTextarea.value.selectionStart
    editorTextarea.value.setSelectionRange(start, start)
  }
}

const replaceNext = () => {
  if (searchResults.value.length === 0 || currentSearchIndex.value < 0) return

  const result = searchResults.value[currentSearchIndex.value]
  const newContent =
    localContent.value.substring(0, result.start) +
    replaceQuery.value +
    localContent.value.substring(result.end)

  localContent.value = newContent
  onInput()

  // Update search results after replacement
  setTimeout(() => {
    performSearch()
  }, 10)
}

const replaceAll = () => {
  if (!searchQuery.value.trim() || searchResults.value.length === 0) return

  const regex = new RegExp(searchQuery.value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi')
  localContent.value = localContent.value.replace(regex, replaceQuery.value)
  onInput()

  // Clear search after replace all
  searchResults.value = []
  currentSearchIndex.value = -1
}

// Auto-completion methods
const showAutoCompleteDropdown = (position: number) => {
  if (!editorTextarea.value) return

  const textarea = editorTextarea.value
  const rect = textarea.getBoundingClientRect()

  // Calculate approximate position based on cursor
  const lineHeight = 20
  const charWidth = 8

  const lines = localContent.value.substring(0, position).split('\n')
  const currentLine = lines.length - 1
  const currentColumn = lines[lines.length - 1].length

  autoCompleteStyle.value = {
    position: 'absolute',
    top: `${rect.top + (currentLine * lineHeight) + lineHeight + 5}px`,
    left: `${rect.left + (currentColumn * charWidth)}px`,
    zIndex: 1000
  }

  showAutoComplete.value = true
  selectedAutoCompleteIndex.value = 0
}

const hideAutoComplete = () => {
  showAutoComplete.value = false
  autoCompleteItems.value = []
  selectedAutoCompleteIndex.value = 0
}

const filterAutoComplete = (trigger: string) => {
  const filtered = autoCompleteSuggestions.filter(item =>
    item.text.toLowerCase().includes(trigger.toLowerCase()) ||
    item.description.toLowerCase().includes(trigger.toLowerCase())
  )

  autoCompleteItems.value = filtered.slice(0, 10) // Limit to 10 items

  if (filtered.length > 0) {
    showAutoCompleteDropdown(cursorPosition.value)
  } else {
    hideAutoComplete()
  }
}

const selectAutoComplete = (item: any) => {
  if (!editorTextarea.value) return

  const textarea = editorTextarea.value
  const start = textarea.selectionStart
  const end = textarea.selectionEnd

  // Find the trigger text to replace
  const beforeCursor = localContent.value.substring(0, start)
  const triggerMatch = beforeCursor.match(/(\w+)$/)
  const triggerStart = triggerMatch ? start - triggerMatch[1].length : start

  const newContent =
    localContent.value.substring(0, triggerStart) +
    item.insert +
    localContent.value.substring(end)

  localContent.value = newContent

  // Set cursor position
  nextTick(() => {
    const newCursorPos = triggerStart + item.insert.length + (item.cursorOffset || 0)
    textarea.setSelectionRange(newCursorPos, newCursorPos)
    textarea.focus()
  })

  hideAutoComplete()
  onInput()
}

// Export functionality
const exportMarkdown = () => {
  const content = localContent.value
  const blob = new Blob([content], { type: 'text/markdown' })
  const url = URL.createObjectURL(blob)

  const link = document.createElement('a')
  link.href = url
  link.download = 'document.md'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  URL.revokeObjectURL(url)
}

// Enhanced table editing assistance
const insertTableRow = () => {
  if (!editorTextarea.value) return

  const textarea = editorTextarea.value
  const start = textarea.selectionStart
  const lines = localContent.value.split('\n')

  // Find current line
  let currentPos = 0
  let lineIndex = 0

  for (let i = 0; i < lines.length; i++) {
    if (currentPos + lines[i].length >= start) {
      lineIndex = i
      break
    }
    currentPos += lines[i].length + 1
  }

  const currentLine = lines[lineIndex]

  // Check if we're in a table
  if (currentLine.includes('|')) {
    const columns = currentLine.split('|').length - 1
    const newRow = '|' + ' Cell |'.repeat(columns)

    lines.splice(lineIndex + 1, 0, newRow)
    localContent.value = lines.join('\n')

    // Position cursor in first cell of new row
    nextTick(() => {
      const newPos = currentPos + currentLine.length + 1 + 2 // +1 for newline, +2 for '| '
      textarea.setSelectionRange(newPos, newPos + 4) // Select 'Cell'
      textarea.focus()
    })

    onInput()
  }
}

const insertTableColumn = () => {
  if (!editorTextarea.value) return

  const textarea = editorTextarea.value
  const start = textarea.selectionStart
  const lines = localContent.value.split('\n')

  // Find current line and table
  let currentPos = 0
  let lineIndex = 0

  for (let i = 0; i < lines.length; i++) {
    if (currentPos + lines[i].length >= start) {
      lineIndex = i
      break
    }
    currentPos += lines[i].length + 1
  }

  // Find table boundaries
  let tableStart = lineIndex
  let tableEnd = lineIndex

  // Find start of table
  for (let i = lineIndex; i >= 0; i--) {
    if (lines[i].includes('|')) {
      tableStart = i
    } else {
      break
    }
  }

  // Find end of table
  for (let i = lineIndex; i < lines.length; i++) {
    if (lines[i].includes('|')) {
      tableEnd = i
    } else {
      break
    }
  }

  // Add column to each table row
  for (let i = tableStart; i <= tableEnd; i++) {
    if (lines[i].includes('|')) {
      if (lines[i].includes('---')) {
        // Header separator row
        lines[i] = lines[i].replace(/\|$/, '| --- |')
      } else {
        // Regular row
        lines[i] = lines[i].replace(/\|$/, '| Cell |')
      }
    }
  }

  localContent.value = lines.join('\n')
  onInput()
}

// Synchronized scrolling
const onEditorScroll = () => {
  if (!syncScroll.value || !showPreview.value || isScrollingSynced.value) return

  const editor = editorTextarea.value
  const preview = previewPane.value

  if (!editor || !preview) return

  isScrollingSynced.value = true

  const scrollPercentage = editor.scrollTop / (editor.scrollHeight - editor.clientHeight)
  const targetScrollTop = scrollPercentage * (preview.scrollHeight - preview.clientHeight)

  preview.scrollTop = targetScrollTop

  setTimeout(() => {
    isScrollingSynced.value = false
  }, 100)
}

const onPreviewScroll = () => {
  if (!syncScroll.value || !showPreview.value || isScrollingSynced.value) return

  const editor = editorTextarea.value
  const preview = previewPane.value

  if (!editor || !preview) return

  isScrollingSynced.value = true

  const scrollPercentage = preview.scrollTop / (preview.scrollHeight - preview.clientHeight)
  const targetScrollTop = scrollPercentage * (editor.scrollHeight - editor.clientHeight)

  editor.scrollTop = targetScrollTop

  setTimeout(() => {
    isScrollingSynced.value = false
  }, 100)
}

// Keyboard shortcuts
const onKeyDown = (event: KeyboardEvent) => {
  const { ctrlKey, metaKey, key, shiftKey } = event
  const isCmd = ctrlKey || metaKey

  // Handle auto-completion navigation
  if (showAutoComplete.value) {
    switch (key) {
      case 'ArrowDown':
        event.preventDefault()
        selectedAutoCompleteIndex.value = Math.min(
          selectedAutoCompleteIndex.value + 1,
          autoCompleteItems.value.length - 1
        )
        break
      case 'ArrowUp':
        event.preventDefault()
        selectedAutoCompleteIndex.value = Math.max(selectedAutoCompleteIndex.value - 1, 0)
        break
      case 'Enter':
      case 'Tab':
        event.preventDefault()
        if (autoCompleteItems.value[selectedAutoCompleteIndex.value]) {
          selectAutoComplete(autoCompleteItems.value[selectedAutoCompleteIndex.value])
        }
        break
      case 'Escape':
        event.preventDefault()
        hideAutoComplete()
        break
    }
    return
  }

  // Handle search navigation
  if (showSearchReplace.value && event.target === searchInput.value) {
    switch (key) {
      case 'Enter':
        event.preventDefault()
        if (shiftKey) {
          findPrevious()
        } else {
          findNext()
        }
        break
      case 'Escape':
        event.preventDefault()
        toggleSearchReplace()
        break
    }
    return
  }

  if (isCmd) {
    switch (key.toLowerCase()) {
      case 'f':
        event.preventDefault()
        toggleSearchReplace()
        break
      case 'b':
        event.preventDefault()
        insertMarkdown('**', '**')
        break
      case 'i':
        event.preventDefault()
        insertMarkdown('*', '*')
        break
      case 'k':
        event.preventDefault()
        insertLink()
        break
      case 'e':
        event.preventDefault()
        insertMarkdown('`', '`')
        break
      case 'l':
        if (shiftKey) {
          event.preventDefault()
          insertLineMarkdown('- ')
        }
        break
      case 'enter':
        if (shiftKey) {
          event.preventDefault()
          insertCodeBlock()
        }
        break
      case 's':
        event.preventDefault()
        exportMarkdown()
        break
    }
  }

  // Tab handling for indentation
  if (key === 'Tab') {
    event.preventDefault()
    insertMarkdown(shiftKey ? '' : '  ', '')
  }

  // Enter key handling for auto-completion and smart lists
  if (key === 'Enter' && !isCmd && !showAutoComplete.value) {
    handleSmartEnter(event)
  }

  // Trigger auto-completion on certain characters
  if (!isCmd && !showSearchReplace.value) {
    setTimeout(() => {
      handleAutoCompletion()
    }, 10)
  }
}

const handleSmartEnter = (event: KeyboardEvent) => {
  if (!editorTextarea.value) return

  const textarea = editorTextarea.value
  const start = textarea.selectionStart
  const lines = localContent.value.split('\n')

  // Find current line
  let currentPos = 0
  let lineIndex = 0

  for (let i = 0; i < lines.length; i++) {
    if (currentPos + lines[i].length >= start) {
      lineIndex = i
      break
    }
    currentPos += lines[i].length + 1
  }

  const currentLine = lines[lineIndex]

  // Auto-continue lists
  const listMatch = currentLine.match(/^(\s*)([-*+]|\d+\.)\s/)
  const taskMatch = currentLine.match(/^(\s*)-\s\[([ x])\]\s/)

  if (listMatch || taskMatch) {
    event.preventDefault()

    let prefix = ''
    if (taskMatch) {
      prefix = `${taskMatch[1]}- [ ] `
    } else if (listMatch) {
      if (listMatch[2].match(/\d+\./)) {
        const num = parseInt(listMatch[2]) + 1
        prefix = `${listMatch[1]}${num}. `
      } else {
        prefix = `${listMatch[1]}${listMatch[2]} `
      }
    }

    const newContent =
      localContent.value.substring(0, start) +
      '\n' + prefix +
      localContent.value.substring(start)

    localContent.value = newContent

    nextTick(() => {
      const newCursorPos = start + 1 + prefix.length
      textarea.setSelectionRange(newCursorPos, newCursorPos)
    })

    onInput()
  }
}

const handleAutoCompletion = () => {
  if (!editorTextarea.value) return

  const textarea = editorTextarea.value
  const start = textarea.selectionStart
  cursorPosition.value = start

  // Get text before cursor
  const beforeCursor = localContent.value.substring(0, start)
  const triggerMatch = beforeCursor.match(/(\w{2,})$/)

  if (triggerMatch && triggerMatch[1].length >= 2) {
    filterAutoComplete(triggerMatch[1])
  } else {
    hideAutoComplete()
  }
}

const onCursorMove = () => {
  if (!editorTextarea.value) return
  cursorPosition.value = editorTextarea.value.selectionStart

  // Hide auto-complete when cursor moves
  if (showAutoComplete.value) {
    hideAutoComplete()
  }
}

const onSelect = () => {
  // Store selection for toolbar actions
}

// Watch for external content changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (localContent.value !== newValue) {
      localContent.value = newValue
    }
  },
  { immediate: true }
)

// Focus method for parent components
const focus = () => {
  editorTextarea.value?.focus()
}

// Expose methods
defineExpose({
  focus,
  insertMarkdown,
  insertLineMarkdown,
  insertLink,
  insertImage,
  insertCodeBlock,
  insertTable,
  insertTableRow,
  insertTableColumn,
  exportMarkdown,
  toggleSearchReplace,
  performSearch,
  findNext,
  findPrevious,
  replaceNext,
  replaceAll,
  filterAutoComplete,
  selectAutoComplete,
  autoCompleteItems
})
</script>

<style scoped>
.markdown-editor {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--card-background);
  display: flex;
  flex-direction: column;
  height: 500px;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-bottom: 1px solid var(--color-border);
  background: var(--card-header-background);
  flex-wrap: wrap;
  flex-shrink: 0;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.toolbar-separator {
  width: 1px;
  height: 1.5rem;
  background: #dbdbdb;
  margin: 0 0.25rem;
}

.toolbar .button {
  border: 1px solid transparent;
  background: transparent;
  color: #4a4a4a;
  min-width: 2rem;
  height: 2rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar .button:hover {
  background: #e8e8e8;
  border-color: #b5b5b5;
}

.toolbar .button.is-active {
  background: #3273dc;
  color: white;
  border-color: #3273dc;
}

.editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.editor-content.split-view {
  /* Split view styles handled by half-width class */
}

.editor-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-pane.half-width {
  flex: 0 0 50%;
  border-right: 1px solid #dbdbdb;
}

.preview-pane {
  flex: 1;
  overflow-y: auto;
  background: #fafafa;
}

.preview-pane.half-width {
  flex: 0 0 50%;
}

.markdown-textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: none;
  padding: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  background: var(--input-background);
}

.markdown-textarea:disabled {
  background: #f5f5f5;
  color: #999;
}

.preview-content {
  padding: 1rem;
  line-height: 1.6;
  color: #333;
}

/* Markdown preview styles */
:deep(.preview-content h1) {
  font-size: 2rem;
  font-weight: bold;
  margin: 1.5rem 0 1rem 0;
  line-height: 1.2;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.5rem;
}

:deep(.preview-content h2) {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1.25rem 0 0.75rem 0;
  line-height: 1.3;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.25rem;
}

:deep(.preview-content h3) {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
}

:deep(.preview-content h4) {
  font-size: 1.1rem;
  font-weight: bold;
  margin: 0.75rem 0 0.5rem 0;
}

:deep(.preview-content h5) {
  font-size: 1rem;
  font-weight: bold;
  margin: 0.5rem 0 0.25rem 0;
}

:deep(.preview-content h6) {
  font-size: 0.9rem;
  font-weight: bold;
  margin: 0.5rem 0 0.25rem 0;
  color: #666;
}

:deep(.preview-content p) {
  margin: 0 0 1rem 0;
}

:deep(.preview-content ul) {
  list-style-type: disc;
  margin: 1rem 0;
  padding-left: 2rem;
}

:deep(.preview-content ol) {
  list-style-type: decimal;
  margin: 1rem 0;
  padding-left: 2rem;
}

:deep(.preview-content li) {
  margin: 0.25rem 0;
}

:deep(.preview-content li input[type="checkbox"]) {
  margin-right: 0.5rem;
}

:deep(.preview-content blockquote) {
  border-left: 4px solid #ddd;
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  background: #f9f9f9;
  color: #666;
}

:deep(.preview-content code) {
  background: #f1f1f1;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

:deep(.preview-content pre) {
  background: #f8f8f8;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
}

:deep(.preview-content pre code) {
  background: none;
  padding: 0;
  border-radius: 0;
  font-size: 0.875rem;
}

:deep(.preview-content table) {
  width: 100%;
  margin: 1rem 0;
  border-collapse: collapse;
}

:deep(.preview-content table th),
:deep(.preview-content table td) {
  border: 1px solid #ddd;
  padding: 0.5rem;
  text-align: left;
}

:deep(.preview-content table th) {
  background: #f5f5f5;
  font-weight: bold;
}

:deep(.preview-content table tr:nth-child(even)) {
  background: #f9f9f9;
}

:deep(.preview-content a) {
  color: #3273dc;
  text-decoration: none;
}

:deep(.preview-content a:hover) {
  text-decoration: underline;
}

:deep(.preview-content img) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 1rem 0;
}

:deep(.preview-content hr) {
  border: none;
  border-top: 2px solid #eee;
  margin: 2rem 0;
}

/* Syntax highlighting adjustments */
:deep(.preview-content .hljs) {
  background: #f8f8f8 !important;
  color: #333 !important;
}

/* Search and Replace Panel */
.search-replace-panel {
  padding: 0.75rem;
  background: #f8f9fa;
  border-bottom: 1px solid #dbdbdb;
}

.search-replace-panel .field {
  margin-bottom: 0.5rem;
}

.search-replace-panel .field:last-child {
  margin-bottom: 0;
}

.search-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.5rem;
}

.search-info .tag {
  font-size: 0.75rem;
}

/* Auto-completion Dropdown */
.autocomplete-dropdown {
  background: var(--card-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  min-width: 200px;
}

.autocomplete-item {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  flex-direction: column;
}

.autocomplete-item:last-child {
  border-bottom: none;
}

.autocomplete-item:hover,
.autocomplete-item.is-active {
  background: #f0f8ff;
}

.autocomplete-text {
  font-weight: 500;
  color: #333;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.autocomplete-description {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.125rem;
}

/* Enhanced table styles in preview */
:deep(.preview-content table) {
  width: 100%;
  margin: 1rem 0;
  border-collapse: collapse;
  font-size: 0.9rem;
}

:deep(.preview-content table th),
:deep(.preview-content table td) {
  border: 1px solid #ddd;
  padding: 0.75rem 0.5rem;
  text-align: left;
  vertical-align: top;
}

:deep(.preview-content table th) {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

:deep(.preview-content table tr:nth-child(even)) {
  background: #f8f9fa;
}

:deep(.preview-content table tr:hover) {
  background: #e9ecef;
}

/* Enhanced code block styles */
:deep(.preview-content pre) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
  position: relative;
}

:deep(.preview-content pre code) {
  background: none;
  padding: 0;
  border-radius: 0;
  font-size: 0.875rem;
  line-height: 1.45;
}

/* Language label for code blocks */
:deep(.preview-content pre[class*="language-"]:before) {
  content: attr(class);
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.75rem;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 500;
}

/* Task list styles */
:deep(.preview-content li input[type="checkbox"]) {
  margin-right: 0.5rem;
  transform: scale(1.1);
}

:deep(.preview-content li:has(input[type="checkbox"]:checked)) {
  color: #6c757d;
  text-decoration: line-through;
}

/* Enhanced blockquote styles */
:deep(.preview-content blockquote) {
  border-left: 4px solid #007bff;
  margin: 1.5rem 0;
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  color: #495057;
  font-style: italic;
  position: relative;
}

:deep(.preview-content blockquote:before) {
  content: '"';
  font-size: 3rem;
  color: #007bff;
  position: absolute;
  top: -0.5rem;
  left: 0.5rem;
  opacity: 0.3;
}

/* Enhanced link styles */
:deep(.preview-content a) {
  color: #007bff;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

:deep(.preview-content a:hover) {
  border-bottom-color: #007bff;
}

:deep(.preview-content a[href^="http"]:after) {
  content: " ↗";
  font-size: 0.8em;
  opacity: 0.7;
}

/* Focus styles */
.markdown-editor:focus-within {
  border-color: #3273dc;
  box-shadow: 0 0 0 0.125em rgba(50, 115, 220, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
  .editor-content.split-view {
    flex-direction: column;
  }

  .editor-pane.half-width,
  .preview-pane.half-width {
    flex: 1;
    border-right: none;
    border-bottom: 1px solid #dbdbdb;
  }

  .toolbar {
    padding: 0.5rem;
  }

  .toolbar-group {
    gap: 0.125rem;
  }
}
</style>
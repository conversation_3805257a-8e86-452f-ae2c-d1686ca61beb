<template>
  <div class="group-invitations">
    <div class="invitations-header">
      <div class="header-content">
        <h3 class="section-title">Pending Invitations</h3>
        <button 
          class="button is-primary"
          @click="$emit('invitation-sent')"
        >
          <span class="icon">
            <i class="fas fa-user-plus"></i>
          </span>
          <span>Send Invitation</span>
        </button>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="loading-state">
      <div class="loader"></div>
      <p>Loading invitations...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="error-state">
      <div class="error-content">
        <span class="icon">
          <i class="fas fa-exclamation-circle"></i>
        </span>
        <p>{{ error }}</p>
        <button class="button is-small" @click="clearError">Dismiss</button>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else-if="invitations.length === 0" class="empty-state">
      <div class="empty-content">
        <span class="icon">
          <i class="fas fa-envelope"></i>
        </span>
        <h4>No pending invitations</h4>
        <p>All sent invitations have been accepted or have expired.</p>
      </div>
    </div>

    <!-- Invitations list -->
    <div v-else class="invitations-list">
      <div v-for="invitation in invitations" :key="invitation.id" class="invitation-card">
        <div class="invitation-info">
          <div class="invitation-details">
            <h4 class="invitation-email">{{ invitation.email }}</h4>
            <div class="invitation-meta">
              <span class="role-badge" :class="getRoleColor(invitation.role)">
                {{ getRoleDisplayName(invitation.role) }}
              </span>
              <span class="expiry-info" :class="{ 'is-warning': isExpiringSoon(invitation.expiresAt) }">
                Expires {{ formatDate(invitation.expiresAt) }}
                <span v-if="isExpiringSoon(invitation.expiresAt)" class="warning-badge">
                  Soon
                </span>
              </span>
              <span class="sent-date">
                Sent {{ formatDate(invitation.createdAt) }}
              </span>
            </div>
          </div>
        </div>
        <div class="invitation-actions">
          <button 
            class="button is-small is-light"
            @click="copyInvitationLink(invitation)"
            title="Copy invitation link"
          >
            <span class="icon">
              <i class="fas fa-copy"></i>
            </span>
          </button>
          <button 
            class="button is-small is-light"
            @click="resendInvitation(invitation)"
            title="Resend invitation"
          >
            <span class="icon">
              <i class="fas fa-paper-plane"></i>
            </span>
          </button>
          <button 
            class="button is-small is-danger is-light"
            @click="cancelInvitation(invitation)"
            title="Cancel invitation"
          >
            <span class="icon">
              <i class="fas fa-times"></i>
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Success notification -->
    <div v-if="successMessage" class="success-notification">
      <div class="notification-content">
        <span class="icon">
          <i class="fas fa-check-circle"></i>
        </span>
        <p>{{ successMessage }}</p>
        <button class="button is-small" @click="successMessage = ''">Dismiss</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useGroupsStore } from '../../stores/groups';
import { getRoleDisplayName, getRoleColor } from '../../types/group';
import type { GroupWithMembers, GroupInvitation, UserRole } from '../../types/group';

// Props
const props = defineProps<{
  group: GroupWithMembers;
}>();

// Emits
const emit = defineEmits<{
  'invitation-sent': [];
}>();

const groupsStore = useGroupsStore();

const successMessage = ref('');

// Computed properties
const invitations = computed(() => groupsStore.groupInvitations);
const loading = computed(() => groupsStore.loading);
const error = computed(() => groupsStore.error);

// Methods
const clearError = () => {
  groupsStore.clearError();
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

const isExpiringSoon = (expiresAt: string): boolean => {
  const expiryDate = new Date(expiresAt);
  const now = new Date();
  const daysDiff = (expiryDate.getTime() - now.getTime()) / (1000 * 3600 * 24);
  return daysDiff <= 2; // Expires within 2 days
};

const copyInvitationLink = async (invitation: GroupInvitation) => {
  try {
    // In a real implementation, this would be the actual invitation URL
    const invitationUrl = `${window.location.origin}/groups/invite/${invitation.id}`;
    await navigator.clipboard.writeText(invitationUrl);
    successMessage.value = 'Invitation link copied to clipboard!';
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);
  } catch (error) {
    console.error('Failed to copy invitation link:', error);
  }
};

const resendInvitation = async (invitation: GroupInvitation) => {
  try {
    // In a real implementation, this would call an API to resend the invitation email
    console.log('Resending invitation to:', invitation.email);
    successMessage.value = `Invitation resent to ${invitation.email}`;
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);
  } catch (error) {
    console.error('Failed to resend invitation:', error);
  }
};

const cancelInvitation = async (invitation: GroupInvitation) => {
  if (!confirm(`Are you sure you want to cancel the invitation for ${invitation.email}?`)) {
    return;
  }

  try {
    // In a real implementation, this would call an API to cancel the invitation
    console.log('Canceling invitation for:', invitation.email);
    
    // Remove from local list (in real implementation, this would be handled by the API)
    const index = invitations.value.findIndex(inv => inv.id === invitation.id);
    if (index !== -1) {
      groupsStore.groupInvitations.splice(index, 1);
    }
    
    successMessage.value = `Invitation for ${invitation.email} has been canceled`;
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);
  } catch (error) {
    console.error('Failed to cancel invitation:', error);
  }
};

// Lifecycle
onMounted(async () => {
  try {
    await groupsStore.loadGroupInvitations(props.group.id);
  } catch (error) {
    console.error('Failed to load group invitations:', error);
  }
});
</script>

<style scoped>
.group-invitations {
  padding: 1.5rem;
}

/* Header */
.invitations-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #363636;
  margin: 0;
}

/* Button styling */
.button.is-primary {
  background: #007bff;
  color: white;
  border: 1px solid #007bff;
  border-radius: 6px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
}

.button.is-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Loading state */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loader {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: #6c757d;
  margin: 0;
}

/* Error state */
.error-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.error-content .icon {
  color: #dc3545;
  font-size: 2rem;
}

.error-content p {
  color: #6c757d;
  margin: 0;
}

/* Empty state */
.empty-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 3rem;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.empty-content .icon {
  color: #6c757d;
  font-size: 3rem;
}

.empty-content h4 {
  color: #363636;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.empty-content p {
  color: #6c757d;
  margin: 0;
}

/* Invitations list */
.invitations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Invitation card */
.invitation-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
}

.invitation-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* Invitation info */
.invitation-info {
  flex: 1;
}

.invitation-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.invitation-email {
  font-size: 1.125rem;
  font-weight: 600;
  color: #363636;
  margin: 0;
}

.invitation-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Role badge */
.role-badge {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  font-weight: 500;
}

.role-badge.is-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.role-badge.is-info {
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.role-badge.is-success {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

/* Expiry and date info */
.expiry-info {
  color: #6c757d;
  font-size: 0.9rem;
}

.expiry-info.is-warning {
  color: #dc3545;
  font-weight: 500;
}

.warning-badge {
  background: #ffc107;
  color: #212529;
  border-radius: 4px;
  padding: 0.125rem 0.5rem;
  font-size: 0.7rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.sent-date {
  color: #6c757d;
  font-size: 0.8rem;
}

/* Invitation actions */
.invitation-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.button.is-small {
  border-radius: 6px;
  padding: 0.5rem;
  transition: all 0.2s ease;
  border: 1px solid #e9ecef;
}

.button.is-small.is-light {
  background: #f8f9fa;
  color: #6c757d;
}

.button.is-small.is-light:hover {
  background: #e9ecef;
  color: #495057;
  transform: translateY(-1px);
}

.button.is-small.is-danger.is-light {
  background: #f8f9fa;
  color: #dc3545;
  border-color: #f5c6cb;
}

.button.is-small.is-danger.is-light:hover {
  background: #f8d7da;
  color: #721c24;
  transform: translateY(-1px);
}

/* Success notification */
.success-notification {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-top: 1rem;
  border-left: 4px solid #28a745;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-content .icon {
  color: #28a745;
  font-size: 1.25rem;
}

.notification-content p {
  color: #363636;
  margin: 0;
  flex: 1;
}

/* Responsive design */
@media (max-width: 768px) {
  .group-invitations {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .button {
    width: 100%;
    justify-content: center;
  }
  
  .invitation-card {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .invitation-actions {
    align-self: flex-end;
  }
  
  .section-title {
    font-size: 1.125rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .group-invitations {
    padding: 0.75rem;
  }
  
  .invitation-card {
    padding: 0.75rem;
  }
  
  .invitation-email {
    font-size: 1rem;
  }
  
  .invitation-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .section-title {
    font-size: 1rem;
  }
}
</style>
<template>
  <div class="card">
    <div class="card-content has-text-centered">
      <!-- Loading State -->
      <div v-if="isVerifying" class="py-6">
        <div class="icon is-large has-text-info mb-4">
          <i class="fas fa-spinner fa-pulse fa-3x"></i>
        </div>
        <h3 class="title is-4">Verifying Your Email</h3>
        <p class="has-text-grey">
          Please wait while we verify your email address...
        </p>
      </div>

      <!-- Success State -->
      <div v-else-if="verificationSuccess" class="py-6">
        <div class="icon is-large has-text-success mb-4">
          <i class="fas fa-check-circle fa-3x"></i>
        </div>
        <h3 class="title is-4">Email Verified Successfully</h3>
        <p class="has-text-grey mb-4">
          Your email address has been verified. You can now access all features of your account.
        </p>
        <router-link to="/login" class="button is-primary">
          <span class="icon">
            <i class="fas fa-sign-in-alt"></i>
          </span>
          <span>Sign In</span>
        </router-link>
      </div>

      <!-- Error State -->
      <div v-else class="py-6">
        <div class="icon is-large has-text-danger mb-4">
          <i class="fas fa-times-circle fa-3x"></i>
        </div>
        <h3 class="title is-4">Verification Failed</h3>
        <p class="has-text-grey mb-4">
          {{ errorMessage || 'The verification link is invalid or has expired.' }}
        </p>
        <div class="buttons is-centered">
          <router-link to="/login" class="button is-light">
            <span class="icon">
              <i class="fas fa-sign-in-alt"></i>
            </span>
            <span>Sign In</span>
          </router-link>
          <router-link to="/register" class="button is-primary">
            <span class="icon">
              <i class="fas fa-user-plus"></i>
            </span>
            <span>Register Again</span>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const route = useRoute()
const authStore = useAuthStore()

const isVerifying = ref(true)
const verificationSuccess = ref(false)
const errorMessage = ref('')

onMounted(async () => {
  const token = route.query.token as string
  
  if (!token) {
    isVerifying.value = false
    errorMessage.value = 'No verification token provided'
    return
  }

  try {
    const result = await authStore.verifyEmail(token)
    
    if (result.success) {
      verificationSuccess.value = true
    } else {
      errorMessage.value = result.error || 'Verification failed'
    }
  } catch (error) {
    errorMessage.value = 'An unexpected error occurred'
  } finally {
    isVerifying.value = false
  }
})
</script>

<style scoped>
</style>
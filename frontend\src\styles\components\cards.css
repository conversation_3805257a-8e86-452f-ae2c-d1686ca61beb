/* Dashboard Widgets */
.dashboard-widget {
  background: var(--card-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition-fast);
}

.dashboard-widget:hover {
  box-shadow: var(--shadow-md);
}

.widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.widget-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.widget-title h3 {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.widget-title .icon {
  color: var(--color-text-muted);
}

.widget-content {
  padding: var(--spacing-5);
}

/* Size variants */
.widget-small .widget-content {
  padding: var(--spacing-4);
}

.widget-large .widget-content {
  padding: var(--spacing-6);
}

/* Color variants */
.widget-primary .widget-header {
  background: var(--color-primary);
  color: white;
}

.widget-primary .widget-title h3,
.widget-primary .widget-title .icon {
  color: white;
}

.widget-info .widget-header {
  background: var(--color-info);
  color: white;
}

.widget-info .widget-title h3,
.widget-info .widget-title .icon {
  color: white;
}

.widget-warning .widget-header {
  background: var(--color-warning);
  color: rgba(0,0,0,0.8);
}

.widget-danger .widget-header {
  background: var(--color-danger);
  color: white;
}

/* Generic loader */
.loader {
  border: 4px solid var(--color-surface);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
/* Card Component Styles */

.card {
  background: var(--card-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--card-border);
  overflow: hidden;
  transition: var(--transition-fast);
  display: flex;
  flex-direction: column;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Card Header */
.card-header {
  background: var(--card-header-background);
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--color-border);
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.card-header-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-strong);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.card-header-icon {
  color: var(--color-text-muted);
  cursor: pointer;
  transition: var(--transition-fast);
}

.card-header-icon:hover {
  color: var(--color-primary);
}

/* Card Image */
.card-image {
  display: block;
  position: relative;
  overflow: hidden;
}

.card-image img {
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: var(--transition-normal);
}

.card:hover .card-image img {
  transform: scale(1.05);
}

.card-image.is-square {
  aspect-ratio: 1;
}

.card-image.is-16by9 {
  aspect-ratio: 16/9;
}

.card-image.is-4by3 {
  aspect-ratio: 4/3;
}

.card-image.is-3by2 {
  aspect-ratio: 3/2;
}

/* Card Content */
.card-content {
  padding: var(--spacing-6);
  flex: 1;
  color: var(--color-text);
}

.card-content:last-child {
  padding-bottom: var(--spacing-6);
}

.card-content .title {
  margin-bottom: var(--spacing-2);
  color: var(--color-text-strong);
}

.card-content .subtitle {
  margin-bottom: var(--spacing-4);
  color: var(--color-text-muted);
}

.card-content p {
  line-height: var(--line-height-relaxed);
}

.card-content p:last-child {
  margin-bottom: 0;
}

/* Card Footer */
.card-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  background: var(--color-surface);
}

.card-footer-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
}

.card-footer-item .icon {
  color: var(--color-text-muted);
}

.card-footer-item:hover {
  color: var(--color-text);
}

.card-footer-item:hover .icon {
  color: var(--color-primary);
}

/* Card Variants */
.card.is-primary {
  border-color: var(--color-primary);
}

.card.is-primary .card-header {
  background: var(--color-primary);
  color: white;
  border-bottom-color: var(--color-primary-dark);
}

.card.is-success {
  border-color: var(--color-success);
}

.card.is-success .card-header {
  background: var(--color-success);
  color: white;
  border-bottom-color: var(--color-success-dark);
}

.card.is-danger {
  border-color: var(--color-danger);
}

.card.is-danger .card-header {
  background: var(--color-danger);
  color: white;
  border-bottom-color: var(--color-danger-dark);
}

.card.is-warning {
  border-color: var(--color-warning);
}

.card.is-warning .card-header {
  background: var(--color-warning);
  color: rgba(0, 0, 0, 0.7);
  border-bottom-color: var(--color-warning-dark);
}

.card.is-info {
  border-color: var(--color-info);
}

.card.is-info .card-header {
  background: var(--color-info);
  color: white;
  border-bottom-color: var(--color-info-dark);
}

/* Card Sizes */
.card.is-small {
  font-size: var(--font-size-sm);
}

.card.is-small .card-header,
.card.is-small .card-content,
.card.is-small .card-footer {
  padding: var(--spacing-3) var(--spacing-4);
}

.card.is-medium {
  font-size: var(--font-size-lg);
}

.card.is-large {
  font-size: var(--font-size-xl);
}

.card.is-large .card-header,
.card.is-large .card-content,
.card.is-large .card-footer {
  padding: var(--spacing-8) var(--spacing-10);
}

/* Card States */
.card.is-loading {
  position: relative;
  overflow: hidden;
}

.card.is-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading-shimmer 1.5s infinite;
}

.card.is-disabled {
  opacity: 0.6;
  pointer-events: none;
}

.card.is-selected {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--input-focus-shadow);
}

/* Card Layouts */
.card.is-horizontal {
  flex-direction: row;
}

.card.is-horizontal .card-image {
  flex-shrink: 0;
  width: 200px;
}

.card.is-horizontal .card-content {
  flex: 1;
}

.card.is-horizontal .card-footer {
  border-top: none;
  border-left: 1px solid var(--color-border);
  flex-direction: column;
  width: auto;
  min-width: 120px;
}

/* Compact Card */
.card.is-compact .card-header,
.card.is-compact .card-content,
.card.is-compact .card-footer {
  padding: var(--spacing-2) var(--spacing-3);
}

.card.is-compact .card-header-title {
  font-size: var(--font-size-base);
}

/* Borderless Card */
.card.is-borderless {
  border: none;
  box-shadow: none;
}

.card.is-borderless:hover {
  box-shadow: var(--shadow);
}

/* Flat Card */
.card.is-flat {
  box-shadow: none;
  border: 1px solid var(--color-border);
}

.card.is-flat:hover {
  box-shadow: var(--shadow-sm);
}

/* Card Groups */
.cards {
  display: grid;
  gap: var(--spacing-4);
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.cards.is-2-columns {
  grid-template-columns: repeat(2, 1fr);
}

.cards.is-3-columns {
  grid-template-columns: repeat(3, 1fr);
}

.cards.is-4-columns {
  grid-template-columns: repeat(4, 1fr);
}

/* Card Animations */
@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes card-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card.is-animating {
  animation: card-fade-in 0.3s ease-out;
}

/* Media Card (special layout) */
.media-card {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
}

.media-card .media-left,
.media-card .media-right {
  flex-shrink: 0;
}

.media-card .media-content {
  flex: 1;
  overflow: hidden;
}

.media-card .media-object {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  object-fit: cover;
}

/* Responsive Cards */
@media screen and (max-width: 768px) {
  .cards {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .cards.is-2-columns,
  .cards.is-3-columns,
  .cards.is-4-columns {
    grid-template-columns: 1fr;
  }

  .card.is-horizontal {
    flex-direction: column;
  }

  .card.is-horizontal .card-image {
    width: 100%;
  }

  .card.is-horizontal .card-footer {
    border-left: none;
    border-top: 1px solid var(--color-border);
    flex-direction: row;
    width: 100%;
    min-width: auto;
  }

  .card-header,
  .card-content,
  .card-footer {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .card.is-large .card-header,
  .card.is-large .card-content,
  .card.is-large .card-footer {
    padding: var(--spacing-4) var(--spacing-5);
  }

  .media-card {
    padding: var(--spacing-3);
    gap: var(--spacing-3);
  }

  .media-card .media-object {
    width: 40px;
    height: 40px;
  }
}

import { Request, Response } from 'express';
import { AdminRequest } from '../middleware/adminAuth';
import { UserRepository } from '../repositories/UserRepository';
import { NoteRepository } from '../repositories/NoteRepository';
import { GroupRepository } from '../repositories/GroupRepository';
import { AuditLogRepository } from '../repositories/AuditLogRepository';
import { ContentModerationService } from '../services/ContentModerationService';
import { SystemConfigService } from '../services/SystemConfigService';
import { getDatabase } from '../config/database';
import { v4 as uuidv4 } from 'uuid';

export class AdminController {
  /**
   * GET /api/admin/dashboard - Get admin dashboard overview
   */
  static async getDashboard(req: AdminRequest, res: Response): Promise<void> {
    try {
      const db = getDatabase();
      
      // Get system metrics
      const [
        userStats,
        noteStats,
        groupStats,
        recentActivity,
        systemHealth
      ] = await Promise.all([
        AdminController.getUserStats(),
        AdminController.getNoteStats(),
        AdminController.getGroupStats(),
        AdminController.getRecentActivity(),
        AdminController.getSystemHealth()
      ]);

      res.json({
        userStats,
        noteStats,
        groupStats,
        recentActivity,
        systemHealth,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Admin dashboard error:', error);
      res.status(500).json({ error: 'Failed to load dashboard data' });
    }
  }

  /**
   * GET /api/admin/users - Get user management data
   */
  static async getUsers(req: AdminRequest, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const search = req.query.search as string;
      const status = req.query.status as string; // active, suspended, banned
      const sortBy = req.query.sortBy as string || 'created_at';
      const sortOrder = req.query.sortOrder as string || 'desc';

      const offset = (page - 1) * limit;
      const db = getDatabase();

      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (search) {
        whereClause += ' AND (email LIKE ? OR display_name LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
      }

      if (status) {
        // Status is stored in preferences JSON
        whereClause += ' AND json_extract(preferences, "$.status") = ?';
        params.push(status);
      }

      const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
      const usersQuery = `
        SELECT 
          id, email, display_name, avatar_url, preferences, 
          email_verified, oauth_provider, created_at, updated_at,
          (SELECT COUNT(*) FROM notes WHERE user_id = users.id) as note_count,
          (SELECT COUNT(*) FROM group_members WHERE user_id = users.id) as group_count
        FROM users 
        ${whereClause}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT ? OFFSET ?
      `;

      const [countResult, users] = await Promise.all([
        new Promise<any>((resolve, reject) => {
          db.get(countQuery, params, (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        }),
        new Promise<any[]>((resolve, reject) => {
          db.all(usersQuery, [...params, limit, offset], (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          });
        })
      ]);

      // Parse preferences and add computed fields
      const processedUsers = users.map(user => {
        const preferences = typeof user.preferences === 'string' 
          ? JSON.parse(user.preferences) 
          : user.preferences;
        
        return {
          ...user,
          preferences,
          status: preferences?.status || 'active',
          isAdmin: preferences?.isAdmin === true,
          lastLogin: preferences?.lastLogin || null
        };
      });

      res.json({
        users: processedUsers,
        pagination: {
          page,
          limit,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / limit)
        }
      });
    } catch (error) {
      console.error('Admin get users error:', error);
      res.status(500).json({ error: 'Failed to fetch users' });
    }
  }

  /**
   * PUT /api/admin/users/:id/status - Update user status
   */
  static async updateUserStatus(req: AdminRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status, reason } = req.body;

      if (!['active', 'suspended', 'banned'].includes(status)) {
        res.status(400).json({ error: 'Invalid status' });
        return;
      }

      const user = await UserRepository.findById(id);
      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      // Update user preferences with new status
      const preferences = typeof user.preferences === 'string' 
        ? JSON.parse(user.preferences) 
        : user.preferences;
      
      preferences.status = status;
      preferences.statusReason = reason;
      preferences.statusUpdatedBy = req.user.id;
      preferences.statusUpdatedAt = new Date().toISOString();

      await UserRepository.updatePreferences(id, preferences);

      // Log the action
      await AuditLogRepository.create({
        user_id: req.user.id,
        session_id: (req as any).sessionID || 'unknown',
        action: 'user_status_update',
        resource_type: 'user',
        resource_id: id,
        ip_address: req.ip || 'unknown',
        user_agent: req.get('User-Agent') || 'unknown',
        request_method: 'PUT',
        request_path: req.path,
        request_body: JSON.stringify({ status, reason }),
        response_status: 200,
        response_time_ms: 0,
        metadata: { 
          targetUser: user.email,
          oldStatus: (user.preferences as any)?.status || 'active',
          newStatus: status 
        }
      });

      res.json({ message: 'User status updated successfully' });
    } catch (error) {
      console.error('Admin update user status error:', error);
      res.status(500).json({ error: 'Failed to update user status' });
    }
  }

  /**
   * PUT /api/admin/users/:id/admin - Toggle admin status
   */
  static async toggleAdminStatus(req: AdminRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { isAdmin } = req.body;

      const user = await UserRepository.findById(id);
      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      // Update user preferences with admin status
      const preferences = typeof user.preferences === 'string' 
        ? JSON.parse(user.preferences) 
        : user.preferences;
      
      preferences.isAdmin = isAdmin;
      preferences.adminUpdatedBy = req.user.id;
      preferences.adminUpdatedAt = new Date().toISOString();

      await UserRepository.updatePreferences(id, preferences);

      // Log the action
      await AuditLogRepository.create({
        user_id: req.user.id,
        session_id: (req as any).sessionID || 'unknown',
        action: 'admin_status_update',
        resource_type: 'user',
        resource_id: id,
        ip_address: req.ip || 'unknown',
        user_agent: req.get('User-Agent') || 'unknown',
        request_method: 'PUT',
        request_path: req.path,
        request_body: JSON.stringify({ isAdmin }),
        response_status: 200,
        response_time_ms: 0,
        metadata: { 
          targetUser: user.email,
          newAdminStatus: isAdmin 
        }
      });

      res.json({ message: 'Admin status updated successfully' });
    } catch (error) {
      console.error('Admin toggle admin status error:', error);
      res.status(500).json({ error: 'Failed to update admin status' });
    }
  }

  /**
   * GET /api/admin/content/reports - Get content moderation reports
   */
  static async getContentReports(req: AdminRequest, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const status = req.query.status as string;
      const type = req.query.type as string;
      const priority = req.query.priority as string;

      const result = await ContentModerationService.getReports({
        page,
        limit,
        status,
        type,
        priority
      });

      res.json({
        reports: result.reports,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: Math.ceil(result.total / limit)
        }
      });
    } catch (error) {
      console.error('Admin get content reports error:', error);
      res.status(500).json({ error: 'Failed to fetch content reports' });
    }
  }

  /**
   * GET /api/admin/system/metrics - Get system performance metrics
   */
  static async getSystemMetrics(req: AdminRequest, res: Response): Promise<void> {
    try {
      const hours = parseInt(req.query.hours as string) || 24;
      
      const metrics = await AdminController.getDetailedSystemMetrics(hours);
      
      res.json(metrics);
    } catch (error) {
      console.error('Admin get system metrics error:', error);
      res.status(500).json({ error: 'Failed to fetch system metrics' });
    }
  }

  /**
   * GET /api/admin/system/config - Get system configuration
   */
  static async getSystemConfig(req: AdminRequest, res: Response): Promise<void> {
    try {
      const config = await SystemConfigService.getConfig();
      res.json(config);
    } catch (error) {
      console.error('Admin get system config error:', error);
      res.status(500).json({ error: 'Failed to fetch system configuration' });
    }
  }

  /**
   * PUT /api/admin/system/config - Update system configuration
   */
  static async updateSystemConfig(req: AdminRequest, res: Response): Promise<void> {
    try {
      const updates = req.body;
      await SystemConfigService.updateConfig(updates);

      // Log the configuration change
      await AuditLogRepository.create({
        user_id: req.user.id,
        session_id: (req as any).sessionID || 'unknown',
        action: 'system_config_update',
        resource_type: 'system',
        resource_id: 'config',
        ip_address: req.ip || 'unknown',
        user_agent: req.get('User-Agent') || 'unknown',
        request_method: 'PUT',
        request_path: req.path,
        request_body: JSON.stringify(updates),
        response_status: 200,
        response_time_ms: 0,
        metadata: { configKeys: Object.keys(updates) }
      });

      res.json({ message: 'System configuration updated successfully' });
    } catch (error) {
      console.error('Admin update system config error:', error);
      res.status(500).json({ error: 'Failed to update system configuration' });
    }
  }

  /**
   * POST /api/admin/system/maintenance - Toggle maintenance mode
   */
  static async toggleMaintenanceMode(req: AdminRequest, res: Response): Promise<void> {
    try {
      const { enabled, message } = req.body;
      await SystemConfigService.toggleMaintenanceMode(enabled, message);

      // Log the maintenance mode change
      await AuditLogRepository.create({
        user_id: req.user.id,
        session_id: (req as any).sessionID || 'unknown',
        action: 'maintenance_mode_toggle',
        resource_type: 'system',
        resource_id: 'maintenance',
        ip_address: req.ip || 'unknown',
        user_agent: req.get('User-Agent') || 'unknown',
        request_method: 'POST',
        request_path: req.path,
        request_body: JSON.stringify({ enabled, message }),
        response_status: 200,
        response_time_ms: 0,
        metadata: { maintenanceEnabled: enabled }
      });

      res.json({ 
        message: `Maintenance mode ${enabled ? 'enabled' : 'disabled'} successfully`,
        maintenanceMode: enabled
      });
    } catch (error) {
      console.error('Admin toggle maintenance mode error:', error);
      res.status(500).json({ error: 'Failed to toggle maintenance mode' });
    }
  }

  /**
   * PUT /api/admin/content/reports/:id/status - Update content report status
   */
  static async updateReportStatus(req: AdminRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status, resolution } = req.body;

      await ContentModerationService.updateReportStatus(id, status, req.user.id, resolution);

      // Log the action
      await AuditLogRepository.create({
        user_id: req.user.id,
        session_id: (req as any).sessionID || 'unknown',
        action: 'content_report_status_update',
        resource_type: 'content_report',
        resource_id: id,
        ip_address: req.ip || 'unknown',
        user_agent: req.get('User-Agent') || 'unknown',
        request_method: 'PUT',
        request_path: req.path,
        request_body: JSON.stringify({ status, resolution }),
        response_status: 200,
        response_time_ms: 0,
        metadata: { newStatus: status, hasResolution: !!resolution }
      });

      res.json({ message: 'Report status updated successfully' });
    } catch (error) {
      console.error('Admin update report status error:', error);
      res.status(500).json({ error: 'Failed to update report status' });
    }
  }

  /**
   * POST /api/admin/content/reports/:id/action - Create moderation action
   */
  static async createModerationAction(req: AdminRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { actionType, duration, reason } = req.body;

      const action = await ContentModerationService.createModerationAction({
        reportId: id,
        actionType,
        duration,
        reason,
        performedBy: req.user.id
      });

      // Log the action
      await AuditLogRepository.create({
        user_id: req.user.id,
        session_id: (req as any).sessionID || 'unknown',
        action: 'moderation_action_created',
        resource_type: 'content_report',
        resource_id: id,
        ip_address: req.ip || 'unknown',
        user_agent: req.get('User-Agent') || 'unknown',
        request_method: 'POST',
        request_path: req.path,
        request_body: JSON.stringify({ actionType, duration, reason }),
        response_status: 200,
        response_time_ms: 0,
        metadata: { actionType, duration, actionId: action.id }
      });

      res.json({ 
        message: 'Moderation action created successfully',
        action
      });
    } catch (error) {
      console.error('Admin create moderation action error:', error);
      res.status(500).json({ error: 'Failed to create moderation action' });
    }
  }

  // Helper methods for dashboard data
  private static async getUserStats() {
    const db = getDatabase();
    
    return new Promise<any>((resolve, reject) => {
      const query = `
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN email_verified = 1 THEN 1 END) as verified,
          COUNT(CASE WHEN oauth_provider IS NOT NULL THEN 1 END) as oauth_users,
          COUNT(CASE WHEN created_at >= datetime('now', '-7 days') THEN 1 END) as new_this_week,
          COUNT(CASE WHEN created_at >= datetime('now', '-30 days') THEN 1 END) as new_this_month,
          (SELECT COUNT(DISTINCT user_id) 
           FROM audit_logs 
           WHERE user_id IS NOT NULL 
           AND created_at >= datetime('now', '-10 minutes')) as online
        FROM users
      `;
      
      db.get(query, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  private static async getNoteStats() {
    const db = getDatabase();
    
    return new Promise<any>((resolve, reject) => {
      const query = `
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN note_type = 'richtext' THEN 1 END) as richtext,
          COUNT(CASE WHEN note_type = 'markdown' THEN 1 END) as markdown,
          COUNT(CASE WHEN note_type = 'kanban' THEN 1 END) as kanban,
          COUNT(CASE WHEN is_archived = 1 THEN 1 END) as archived,
          COUNT(CASE WHEN created_at >= datetime('now', '-7 days') THEN 1 END) as created_this_week
        FROM notes
      `;
      
      db.get(query, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  private static async getGroupStats() {
    const db = getDatabase();
    
    return new Promise<any>((resolve, reject) => {
      const query = `
        SELECT 
          COUNT(*) as total,
          AVG(member_count) as avg_members,
          COUNT(CASE WHEN created_at >= datetime('now', '-7 days') THEN 1 END) as created_this_week
        FROM (
          SELECT g.id, COUNT(gm.user_id) as member_count, g.created_at
          FROM groups g
          LEFT JOIN group_members gm ON g.id = gm.group_id
          GROUP BY g.id, g.created_at
        )
      `;
      
      db.get(query, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  private static async getRecentActivity() {
    const db = getDatabase();
    
    return new Promise<any[]>((resolve, reject) => {
      const query = `
        SELECT 
          action, resource_type, user_id, created_at,
          COUNT(*) as count
        FROM audit_logs 
        WHERE created_at >= datetime('now', '-24 hours')
        GROUP BY action, resource_type, user_id
        ORDER BY created_at DESC
        LIMIT 20
      `;
      
      db.all(query, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  private static async getSystemHealth() {
    const db = getDatabase();
    
    return new Promise<any>((resolve, reject) => {
      const query = `
        SELECT 
          AVG(response_time_ms) as avg_response_time,
          COUNT(CASE WHEN response_status >= 400 THEN 1 END) as error_count,
          COUNT(*) as total_requests
        FROM audit_logs 
        WHERE created_at >= datetime('now', '-1 hour')
      `;
      
      db.get(query, (err, row) => {
        if (err) reject(err);
        else {
          const result = row as any;
          resolve({
            ...result,
            error_rate: result.total_requests > 0 ? (result.error_count / result.total_requests) * 100 : 0,
            status: result.avg_response_time < 1000 && (result.error_count / result.total_requests) < 0.05 ? 'healthy' : 'warning'
          });
        }
      });
    });
  }

  private static async getDetailedSystemMetrics(hours: number) {
    const db = getDatabase();
    
    return new Promise<any>((resolve, reject) => {
      const query = `
        SELECT 
          strftime('%Y-%m-%d %H:00:00', created_at) as hour,
          AVG(response_time_ms) as avg_response_time,
          COUNT(*) as request_count,
          COUNT(CASE WHEN response_status >= 400 THEN 1 END) as error_count,
          COUNT(CASE WHEN response_status >= 500 THEN 1 END) as server_error_count
        FROM audit_logs 
        WHERE created_at >= datetime('now', '-${hours} hours')
        GROUP BY strftime('%Y-%m-%d %H:00:00', created_at)
        ORDER BY hour DESC
      `;
      
      db.all(query, (err, rows) => {
        if (err) reject(err);
        else {
          const metrics = rows as any[];
          resolve({
            hourly_metrics: metrics,
            summary: {
              total_requests: metrics.reduce((sum, row) => sum + row.request_count, 0),
              avg_response_time: metrics.reduce((sum, row) => sum + row.avg_response_time, 0) / metrics.length,
              error_rate: metrics.reduce((sum, row) => sum + row.error_count, 0) / metrics.reduce((sum, row) => sum + row.request_count, 0) * 100
            }
          });
        }
      });
    });
  }

  /**
   * GET /api/admin/notifications - Get admin notifications
   */
  static async getNotifications(req: AdminRequest, res: Response): Promise<void> {
    try {
      const { AdminNotificationService } = await import('../services/AdminNotificationService');
      const { AdminNotificationRepository } = await import('../repositories/AdminNotificationRepository');
      
      const db = getDatabase();
      const notificationRepository = new AdminNotificationRepository(db);
      const notificationService = new AdminNotificationService(notificationRepository);

      const filters = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        type: req.query.type as string,
        category: req.query.category as string,
        read: req.query.read === 'true' ? true : req.query.read === 'false' ? false : undefined
      };

      const result = await notificationService.getNotifications(filters);
      res.json(result);
    } catch (error) {
      console.error('Get notifications error:', error);
      res.status(500).json({ error: 'Failed to load notifications' });
    }
  }

  /**
   * GET /api/admin/notifications/unread-count - Get unread notification count
   */
  static async getUnreadNotificationCount(req: AdminRequest, res: Response): Promise<void> {
    try {
      const { AdminNotificationService } = await import('../services/AdminNotificationService');
      const { AdminNotificationRepository } = await import('../repositories/AdminNotificationRepository');
      
      const db = getDatabase();
      const notificationRepository = new AdminNotificationRepository(db);
      const notificationService = new AdminNotificationService(notificationRepository);

      const count = await notificationService.getUnreadCount();
      res.json({ count });
    } catch (error) {
      console.error('Get unread count error:', error);
      res.status(500).json({ error: 'Failed to get unread count' });
    }
  }

  /**
   * PUT /api/admin/notifications/:id/read - Mark notification as read
   */
  static async markNotificationAsRead(req: AdminRequest, res: Response): Promise<void> {
    try {
      const { AdminNotificationService } = await import('../services/AdminNotificationService');
      const { AdminNotificationRepository } = await import('../repositories/AdminNotificationRepository');
      
      const db = getDatabase();
      const notificationRepository = new AdminNotificationRepository(db);
      const notificationService = new AdminNotificationService(notificationRepository);

      const { id } = req.params;
      const success = await notificationService.markAsRead(id);

      if (!success) {
        res.status(404).json({ error: 'Notification not found' });
        return;
      }

      res.json({ success: true });
    } catch (error) {
      console.error('Mark notification as read error:', error);
      res.status(500).json({ error: 'Failed to mark notification as read' });
    }
  }

  /**
   * PUT /api/admin/notifications/mark-all-read - Mark all notifications as read
   */
  static async markAllNotificationsAsRead(req: AdminRequest, res: Response): Promise<void> {
    try {
      const { AdminNotificationService } = await import('../services/AdminNotificationService');
      const { AdminNotificationRepository } = await import('../repositories/AdminNotificationRepository');
      
      const db = getDatabase();
      const notificationRepository = new AdminNotificationRepository(db);
      const notificationService = new AdminNotificationService(notificationRepository);

      const count = await notificationService.markAllAsRead();
      res.json({ success: true, markedCount: count });
    } catch (error) {
      console.error('Mark all notifications as read error:', error);
      res.status(500).json({ error: 'Failed to mark all notifications as read' });
    }
  }

  /**
   * DELETE /api/admin/notifications/:id - Delete notification
   */
  static async deleteNotification(req: AdminRequest, res: Response): Promise<void> {
    try {
      const { AdminNotificationService } = await import('../services/AdminNotificationService');
      const { AdminNotificationRepository } = await import('../repositories/AdminNotificationRepository');
      
      const db = getDatabase();
      const notificationRepository = new AdminNotificationRepository(db);
      const notificationService = new AdminNotificationService(notificationRepository);

      const { id } = req.params;
      const success = await notificationService.deleteNotification(id);

      if (!success) {
        res.status(404).json({ error: 'Notification not found' });
        return;
      }

      res.json({ success: true });
    } catch (error) {
      console.error('Delete notification error:', error);
      res.status(500).json({ error: 'Failed to delete notification' });
    }
  }
}
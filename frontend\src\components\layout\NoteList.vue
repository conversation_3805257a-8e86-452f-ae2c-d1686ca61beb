<template>
  <div class="note-list">
    <!-- Header -->
    <div class="note-list-header">
      <div class="header-content">
        <h2 class="section-title">{{ sectionTitle }}</h2>
        <div class="header-actions">
          <div class="dropdown is-right" :class="{ 'is-active': showSortDropdown }">
            <div class="dropdown-trigger">
              <button class="button is-ghost is-small" @click.stop="showSortDropdown = !showSortDropdown"
                title="Sort options">
                <span class="icon">
                  <i class="fas fa-ellipsis-v"></i>
                </span>
              </button>
            </div>
            <div class="dropdown-menu" v-show="showSortDropdown">
              <div class="dropdown-content">
                <a class="dropdown-item" @click.stop="setSortBy('updated')">
                  <span class="icon">
                    <i class="fas fa-clock"></i>
                  </span>
                  <span>Sort by Date</span>
                </a>
                <a class="dropdown-item" @click.stop="setSortBy('title')">
                  <span class="icon">
                    <i class="fas fa-sort-alpha-down"></i>
                  </span>
                  <span>Sort by Title</span>
                </a>
                <a class="dropdown-item" @click.stop="setSortBy('type')">
                  <span class="icon">
                    <i class="fas fa-layer-group"></i>
                  </span>
                  <span>Sort by Type</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Search Bar -->
      <div class="search-container">
        <div class="field has-addons">
          <div class="control has-icons-left is-expanded">
            <input class="input is-small" type="text" placeholder="Search notes..." v-model="searchQuery"
              @input="handleSearch">
            <span class="icon is-small is-left">
              <i class="fas fa-search"></i>
            </span>
          </div>
          <div class="control">
            <button class="button is-small" @click="clearSearch" v-if="searchQuery">
              <span class="icon">
                <i class="fas fa-times"></i>
              </span>
            </button>
          </div>
        </div>
      </div>


    </div>

    <!-- Note Cards Container -->
    <div class="note-cards-container">
      <!-- Loading State -->
      <div v-if="notesStore.isLoading" class="loading-state">
        <div class="has-text-centered">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse"></i>
          </span>
          <p>Loading notes...</p>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="filteredNotes.length === 0" class="empty-state">
        <div class="has-text-centered">
          <span class="icon is-large has-text-grey-light">
            <i class="fas fa-sticky-note"></i>
          </span>
          <h3 class="title is-5 has-text-grey">No notes found</h3>
          <p class="has-text-grey">
            {{ searchQuery ? 'Try adjusting your search terms' : 'Create your first note to get started' }}
          </p>
          <button v-if="!searchQuery" class="button is-primary" @click="createNote">
            <span class="icon">
              <i class="fas fa-plus"></i>
            </span>
            <span>Create Note</span>
          </button>
        </div>
      </div>

      <!-- Note Cards -->
      <div v-else class="note-cards">
        <div v-for="note in paginatedNotes" :key="note.id" class="note-card"
          :class="{ 'is-selected': selectedNoteId === note.id }" :data-note-id="note.id" @click="selectNote(note)">
          <!-- Note Type Icon -->
          <div class="note-type-icon">
            <span class="icon" :class="getNoteTypeClass(note.noteType)">
              <i :class="getNoteTypeIcon(note.noteType)"></i>
            </span>
          </div>

          <!-- Note Content -->
          <div class="note-content">
            <h4 class="note-title">{{ note.title || 'Untitled' }}</h4>
            <p class="note-preview">{{ getPreviewText(note) }}</p>

            <!-- Note Meta -->
            <div class="note-meta">
              <div class="meta-left">
                <span class="note-date">{{ formatDate(note.updatedAt) }}</span>
                <span v-if="note.tags && note.tags.length > 0" class="note-tags">
                  <span v-for="tag in note.tags.slice(0, 2)" :key="tag.id || tag.name" class="tag is-small">
                    {{ tag.name || tag }}
                  </span>
                  <span v-if="note.tags.length > 2" class="tag is-small has-text-grey">
                    +{{ note.tags.length - 2 }}
                  </span>
                </span>
              </div>
              <div class="meta-right">
                <span v-if="hasActiveShares(note.id)" class="icon is-small has-text-success" title="Shared">
                  <i class="fas fa-share-alt"></i>
                </span>
                <span v-if="note.groupId" class="icon is-small has-text-info" title="In Group">
                  <i class="fas fa-users"></i>
                </span>
                <span v-if="note.metadata?.isFavorite" class="icon is-small has-text-warning" title="Favorite">
                  <i class="fas fa-star"></i>
                </span>
              </div>
            </div>
          </div>

          <!-- Note Actions -->
          <div class="note-actions">
            <div class="dropdown is-right" :class="{ 'is-active': activeDropdown === note.id }">
              <div class="dropdown-trigger">
                <button class="button is-ghost is-small" @click.stop="toggleDropdown(note.id)"
                  :title="'More actions for ' + (note.title || 'Untitled')">
                  <span class="icon">
                    <i class="fas fa-ellipsis-v"></i>
                  </span>
                </button>
              </div>
              <div class="dropdown-menu" v-show="activeDropdown === note.id">
                <div class="dropdown-content">
                  <a class="dropdown-item" @click.stop="toggleFavorite(note)">
                    <span class="icon">
                      <i :class="note.metadata?.isFavorite ? 'fas fa-star' : 'far fa-star'"></i>
                    </span>
                    <span>{{ note.metadata?.isFavorite ? 'Remove from' : 'Add to' }} Favorites</span>
                  </a>
                  <a class="dropdown-item" @click.stop="shareNote(note)">
                    <span class="icon">
                      <i class="fas fa-share-alt"></i>
                    </span>
                    <span>Share</span>
                  </a>
                  <a class="dropdown-item" @click.stop="duplicateNote(note)">
                    <span class="icon">
                      <i class="fas fa-copy"></i>
                    </span>
                    <span>Duplicate</span>
                  </a>
                  <hr class="dropdown-divider">
                  <a class="dropdown-item" @click.stop="archiveNote(note)">
                    <span class="icon">
                      <i class="fas fa-archive"></i>
                    </span>
                    <span>Archive</span>
                  </a>
                  <a class="dropdown-item has-text-danger" @click.stop="deleteNote(note)">
                    <span class="icon">
                      <i class="fas fa-trash"></i>
                    </span>
                    <span>Delete</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="pagination-container">
        <nav class="pagination is-small">
          <button class="pagination-previous" :disabled="currentPage === 1" @click="goToPage(currentPage - 1)">
            Previous
          </button>
          <button class="pagination-next" :disabled="currentPage === totalPages" @click="goToPage(currentPage + 1)">
            Next
          </button>
          <ul class="pagination-list">
            <li v-for="page in visiblePages" :key="page">
              <button v-if="page !== '...'" class="pagination-link" :class="{ 'is-current': page === currentPage }"
                @click="goToPage(page as number)">
                {{ page }}
              </button>
              <span v-else class="pagination-ellipsis">&hellip;</span>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useNotesStore } from '../../stores/notes'
import { useNoteSharesStore } from '../../stores/noteShares'

// Props
interface Props {
  section?: string
  tagFilter?: { selectedTags: string[], filterMode: 'any' | 'all' } | null
}

const props = withDefaults(defineProps<Props>(), {
  section: 'all-notes',
  tagFilter: null
})

// Emits
const emit = defineEmits<{
  'note-selected': [note: any]
  'create-note': []
  'share-note': [note: any]
}>()

// Stores
const notesStore = useNotesStore()
const shareStore = useNoteSharesStore()

// Reactive state
const searchQuery = ref('')
const selectedNoteId = ref<string | null>(null)
const currentPage = ref(1)
const pageSize = ref(20)
const sortField = ref<'updated' | 'title' | 'type'>('updated')
const sortOrder = ref<'asc' | 'desc'>('desc')
const activeDropdown = ref<string | null>(null)
const showSortDropdown = ref(false)

// Computed properties
const sectionTitle = computed(() => {
  switch (props.section) {
    case 'all-notes': return 'All Notes'
    case 'recent': return 'Recent Notes'
    case 'favorites': return 'Favorites'
    case 'shared': return 'Shared Notes'
    case 'archived': return 'Archived Notes'
    default: return 'Notes'
  }
})

const filteredNotes = computed(() => {
  let filtered = [...notesStore.notes]

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(note =>
      note.title.toLowerCase().includes(query) ||
      note.content.toLowerCase().includes(query) ||
      note.tags.some(tag => tag.name.toLowerCase().includes(query))
    )
  }

  // Filter by active tags
  if (props.tagFilter && props.tagFilter.selectedTags.length > 0) {
    const { selectedTags, filterMode } = props.tagFilter

    if (filterMode === 'all') {
      // ALL mode: note must have ALL selected tags
      filtered = filtered.filter(note =>
        selectedTags.every(tag =>
          note.tags.some(noteTag => (noteTag.name || noteTag) === tag)
        )
      )
    } else {
      // ANY mode: note must have at least one of the selected tags
      filtered = filtered.filter(note =>
        selectedTags.some(tag =>
          note.tags.some(noteTag => (noteTag.name || noteTag) === tag)
        )
      )
    }
  }

  // Filter by section
  switch (props.section) {
    case 'favorites':
      filtered = filtered.filter(note => note.metadata?.isFavorite)
      break
    case 'shared':
      filtered = filtered.filter(note => note.groupId || hasActiveShares(note.id))
      break
    case 'recent':
      filtered = filtered.filter(note => {
        const daysDiff = (Date.now() - new Date(note.updatedAt).getTime()) / (1000 * 60 * 60 * 24)
        return daysDiff <= 7
      })
      break
    case 'archived':
      filtered = filtered.filter(note => note.isArchived)
      break
    case 'all-notes':
      filtered = filtered.filter(note => !note.isArchived)
      break
    case 'dashboard':
      // For dashboard, show recent non-archived notes
      filtered = filtered.filter(note => !note.isArchived)
      filtered = filtered.slice(0, 10) // Limit to 10 most recent
      break
  }

  // Sort notes
  filtered.sort((a, b) => {
    let comparison = 0
    switch (sortField.value) {
      case 'title':
        comparison = a.title.localeCompare(b.title)
        break
      case 'type':
        comparison = a.noteType.localeCompare(b.noteType)
        break
      case 'updated':
      default:
        comparison = new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        break
    }
    return sortOrder.value === 'desc' ? comparison : -comparison
  })

  return filtered
})

const totalPages = computed(() => Math.ceil(filteredNotes.value.length / pageSize.value))

const paginatedNotes = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredNotes.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value

  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    pages.push(1)
    if (current > 4) pages.push('...')

    const start = Math.max(2, current - 2)
    const end = Math.min(total - 1, current + 2)

    for (let i = start; i <= end; i++) {
      pages.push(i)
    }

    if (current < total - 3) pages.push('...')
    pages.push(total)
  }

  return pages
})

// Extract all unique tags from current filtered notes
const availableTagsInCurrentView = computed(() => {
  const tagSet = new Set<string>()
  filteredNotes.value.forEach(note => {
    note.tags.forEach((tag: any) => {
      const tagName = tag.name || tag
      if (tagName) tagSet.add(tagName)
    })
  })
  return Array.from(tagSet)
})

// Methods
const selectNote = (note: any) => {
  selectedNoteId.value = note.id
  emit('note-selected', note)
}

const setSortBy = (field: 'updated' | 'title' | 'type') => {
  sortField.value = field
  currentPage.value = 1
  showSortDropdown.value = false
}

const handleSearch = () => {
  currentPage.value = 1
}

const clearSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
}



const goToPage = (page: number) => {
  currentPage.value = page
}

const createNote = () => {
  emit('create-note')
}

const toggleDropdown = (noteId: string) => {
  activeDropdown.value = activeDropdown.value === noteId ? null : noteId
}

const toggleFavorite = async (note: any) => {
  try {
    // Update the note locally first for immediate feedback
    const noteIndex = notesStore.notes.findIndex(n => n.id === note.id)
    if (noteIndex !== -1) {
      notesStore.notes[noteIndex].metadata = {
        ...note.metadata,
        isFavorite: !note.metadata?.isFavorite
      }
    }

    // Then try to update on server
    if (notesStore.updateNote) {
      await notesStore.updateNote(note.id, {
        metadata: { ...note.metadata, isFavorite: !note.metadata?.isFavorite }
      })
    }

    activeDropdown.value = null
  } catch (error) {
    console.error('Failed to toggle favorite:', error)
    // Revert the local change if server update failed
    const noteIndex = notesStore.notes.findIndex(n => n.id === note.id)
    if (noteIndex !== -1) {
      notesStore.notes[noteIndex].metadata = {
        ...note.metadata,
        isFavorite: note.metadata?.isFavorite
      }
    }
  }
}

const shareNote = (note: any) => {
  activeDropdown.value = null
  emit('share-note', note)
}

const duplicateNote = async (note: any) => {
  try {
    activeDropdown.value = null

    // Create a duplicate note locally first
    const duplicatedNote = {
      id: `duplicate-${Date.now()}`,
      userId: 'current-user',
      title: `${note.title} (Copy)`,
      content: note.content,
      noteType: note.noteType,
      tags: note.tags || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isArchived: false,
      metadata: { isFavorite: false }
    }

    notesStore.notes.unshift(duplicatedNote as any)

    // Try to create on server if method exists
    if (notesStore.createNote) {
      await notesStore.createNote({
        title: `${note.title} (Copy)`,
        content: note.content,
        noteType: note.noteType,
        tags: note.tags.map((t: any) => t.name || t)
      })
    }
  } catch (error) {
    console.error('Failed to duplicate note:', error)
  }
}

const archiveNote = async (note: any) => {
  try {
    activeDropdown.value = null

    // Update locally first
    const noteIndex = notesStore.notes.findIndex(n => n.id === note.id)
    if (noteIndex !== -1) {
      notesStore.notes[noteIndex].isArchived = true
    }

    // Try to update on server
    if (notesStore.updateNote) {
      await notesStore.updateNote(note.id, {
        isArchived: true
      })
    }
  } catch (error) {
    console.error('Failed to archive note:', error)
    // Revert local change
    const noteIndex = notesStore.notes.findIndex(n => n.id === note.id)
    if (noteIndex !== -1) {
      notesStore.notes[noteIndex].isArchived = false
    }
  }
}

const deleteNote = async (note: any) => {
  if (confirm('Are you sure you want to delete this note?')) {
    try {
      activeDropdown.value = null

      // Remove locally first
      const noteIndex = notesStore.notes.findIndex(n => n.id === note.id)
      if (noteIndex !== -1) {
        notesStore.notes.splice(noteIndex, 1)
      }

      // Try to delete on server
      if (notesStore.deleteNote) {
        await notesStore.deleteNote(note.id)
      }
    } catch (error) {
      console.error('Failed to delete note:', error)
      // Could add the note back if server delete failed
    }
  }
}

const getNoteTypeIcon = (type: string) => {
  switch (type) {
    case 'richtext': return 'fas fa-align-left'
    case 'markdown': return 'fab fa-markdown'
    case 'kanban': return 'fas fa-columns'
    default: return 'fas fa-file-alt'
  }
}

const getNoteTypeClass = (type: string) => {
  switch (type) {
    case 'richtext': return 'has-text-info'
    case 'markdown': return 'has-text-success'
    case 'kanban': return 'has-text-warning'
    default: return 'has-text-grey'
  }
}

const getPreviewText = (note: any) => {
  let preview = note.content || ''
  if (note.noteType === 'markdown') {
    // Remove markdown syntax for preview
    preview = preview.replace(/[#*`]/g, '')
  } else if (note.noteType === 'kanban') {
    preview = 'Kanban board with tasks and columns'
  } else if (note.noteType === 'richtext') {
    // Remove HTML tags for preview
    preview = preview.replace(/<[^>]*>/g, '')
  }
  return preview.substring(0, 120) + (preview.length > 120 ? '...' : '')
}

const formatDate = (date: string | Date) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffMs = now.getTime() - dateObj.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return 'Today'
  } else if (diffDays === 1) {
    return 'Yesterday'
  } else if (diffDays < 7) {
    return `${diffDays} days ago`
  } else {
    return dateObj.toLocaleDateString()
  }
}

const hasActiveShares = (noteId: string) => {
  return shareStore.hasActiveShares(noteId)
}



// Initialize notes when component mounts
onMounted(async () => {
  try {
    if (notesStore.notes.length === 0) {
      await notesStore.loadNotes()
    }
  } catch (error) {
    console.error('Failed to load notes:', error)
    // Create some sample notes if loading fails
    createSampleNotes()
  }
})

// Create sample notes for testing
const createSampleNotes = () => {
  const sampleNotes = [
    {
      id: 'sample-1',
      userId: 'current-user',
      title: 'Welcome to Notes',
      content: 'This is your first note. You can edit, share, and organize your notes here.',
      noteType: 'richtext' as const,
      tags: [{ name: 'welcome', id: 'tag-1' }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isArchived: false,
      metadata: { isFavorite: false }
    },
    {
      id: 'sample-2',
      userId: 'current-user',
      title: 'Getting Started',
      content: '# Getting Started\n\nThis is a markdown note. You can use **bold** and *italic* text.',
      noteType: 'markdown' as const,
      tags: [{ name: 'guide', id: 'tag-2' }],
      createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      updatedAt: new Date(Date.now() - 86400000).toISOString(),
      isArchived: false,
      metadata: { isFavorite: true }
    },
    {
      id: 'sample-3',
      userId: 'current-user',
      title: 'Project Tasks',
      content: 'Kanban board for project management',
      noteType: 'kanban' as const,
      tags: [{ name: 'project', id: 'tag-3' }, { name: 'tasks', id: 'tag-4' }],
      createdAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
      updatedAt: new Date(Date.now() - 172800000).toISOString(),
      isArchived: false,
      metadata: { isFavorite: false }
    }
  ]

  // Add sample notes to store
  notesStore.notes = sampleNotes as any
}

// Watch for section changes
watch(() => props.section, () => {
  currentPage.value = 1
})

// Note: Removed automatic tags-updated emission to prevent reactive loops
// The tags-updated event should only be emitted when user manually removes tags

// Watch for search query changes
watch(searchQuery, () => {
  notesStore.setSearchQuery(searchQuery.value)
})

// Close dropdowns when clicking outside
let handleClickOutside: ((event: Event) => void) | null = null

onMounted(() => {
  handleClickOutside = (event: Event) => {
    const target = event.target as Element

    // Don't close if clicking on dropdown trigger or content
    if (target.closest('.dropdown')) {
      return
    }

    // Close all dropdowns
    activeDropdown.value = null
    showSortDropdown.value = false
  }

  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  if (handleClickOutside) {
    document.removeEventListener('click', handleClickOutside)
  }
})
</script>

<style scoped>
.note-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-background);
}

.note-list-header {
  padding: 1rem;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-strong);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.search-container {
  margin-bottom: 0.75rem;
}



.note-cards-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  /* Ensure dropdowns can escape the container */
  position: relative;
}

.loading-state,
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
}

.note-cards {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.note-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  background: var(--card-background);
  cursor: pointer;
  transition: all 0.2s ease;
}

.note-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.note-card.is-selected {
  border-color: var(--color-primary);
  background: var(--color-surface);
  box-shadow: var(--shadow-md);
}

.note-type-icon {
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.note-content {
  flex: 1;
  min-width: 0;
}

.note-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-strong);
  margin: 0 0 0.5rem 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.note-preview {
  font-size: 0.875rem;
  color: #6c757d;
  line-height: 1.4;
  margin: 0 0 0.75rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.note-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.meta-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.note-date {
  font-size: 0.75rem;
  color: #6c757d;
  white-space: nowrap;
}

.note-tags {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.meta-right {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.note-actions {
  flex-shrink: 0;
}

.pagination-container {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

/* Dropdown styles */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  left: auto;
  min-width: 200px;
  z-index: 10000;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
  background: var(--card-background);
  border-radius: 4px;
}

.dropdown.is-active .dropdown-menu {
  display: block;
}

.dropdown-content {
  padding: 0.5rem 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  color: #363636;
  text-decoration: none;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #363636;
}

.dropdown-divider {
  margin: 0.5rem 0;
  border: none;
  border-top: 1px solid #e9ecef;
}

/* Ensure dropdowns appear above other content */
.note-actions {
  position: relative;
  z-index: 100;
}

.note-actions .dropdown {
  z-index: 100;
}

.note-actions .dropdown.is-active {
  z-index: 10000;
}

.note-actions .dropdown-menu {
  z-index: 10000;
}

/* Responsive */
@media screen and (max-width: 768px) {
  .note-list-header {
    padding: 0.75rem;
  }

  .note-cards-container {
    padding: 0.75rem;
  }

  .note-card {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .section-title {
    font-size: 1.125rem;
  }
}
</style>
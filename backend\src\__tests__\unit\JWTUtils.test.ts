import { describe, it, expect, beforeEach, vi } from 'vitest';
import { JWTUtils } from '../../utils/jwt';
import jwt from 'jsonwebtoken';

// Mock jsonwebtoken
vi.mock('jsonwebtoken');

describe('JWTUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Set up environment variables
    process.env.JWT_SECRET = 'test-jwt-secret-that-is-at-least-32-characters-long';
    process.env.JWT_REFRESH_SECRET = 'test-jwt-refresh-secret-that-is-at-least-32-characters-long';
  });

  describe('generateTokenPair', () => {
    it('should generate access and refresh tokens', () => {
      vi.mocked(jwt.sign).mockReturnValueOnce('access-token').mockReturnValueOnce('refresh-token');

      const result = JWTUtils.generateTokenPair('user-123', '<EMAIL>');

      expect(result).toEqual({
        accessToken: 'access-token',
        refreshToken: 'refresh-token'
      });

      expect(jwt.sign).toHaveBeenCalledTimes(2);
      
      // Check access token call
      expect(jwt.sign).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          userId: 'user-123',
          email: '<EMAIL>',
          type: 'access'
        }),
        'test-jwt-secret-that-is-at-least-32-characters-long',
        expect.objectContaining({
          expiresIn: '15m'
        })
      );

      // Check refresh token call
      expect(jwt.sign).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining({
          userId: 'user-123',
          email: '<EMAIL>',
          type: 'refresh'
        }),
        'test-jwt-refresh-secret-that-is-at-least-32-characters-long',
        expect.objectContaining({
          expiresIn: '7d'
        })
      );
    });

    it('should include unique JTI in tokens', () => {
      vi.mocked(jwt.sign).mockReturnValue('token');

      JWTUtils.generateTokenPair('user-123', '<EMAIL>');

      const accessTokenCall = vi.mocked(jwt.sign).mock.calls[0];
      const refreshTokenCall = vi.mocked(jwt.sign).mock.calls[1];

      expect(accessTokenCall[0]).toHaveProperty('jti');
      expect(refreshTokenCall[0]).toHaveProperty('jti');
      expect(typeof accessTokenCall[0].jti).toBe('string');
      expect(typeof refreshTokenCall[0].jti).toBe('string');
    });
  });

  describe('generateEmailVerificationToken', () => {
    it('should generate email verification token', () => {
      vi.mocked(jwt.sign).mockReturnValue('verification-token');

      const result = JWTUtils.generateEmailVerificationToken('user-123', '<EMAIL>');

      expect(result).toBe('verification-token');
      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user-123',
          email: '<EMAIL>',
          type: 'email-verification'
        }),
        'test-jwt-secret-that-is-at-least-32-characters-long',
        expect.objectContaining({
          expiresIn: '24h'
        })
      );
    });
  });

  describe('generatePasswordResetToken', () => {
    it('should generate password reset token', () => {
      vi.mocked(jwt.sign).mockReturnValue('reset-token');

      const result = JWTUtils.generatePasswordResetToken('user-123', '<EMAIL>');

      expect(result).toBe('reset-token');
      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user-123',
          email: '<EMAIL>',
          type: 'password-reset'
        }),
        'test-jwt-secret-that-is-at-least-32-characters-long',
        expect.objectContaining({
          expiresIn: '1h'
        })
      );
    });
  });

  describe('verifyToken', () => {
    it('should verify valid token', () => {
      const mockPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        type: 'access',
        jti: 'unique-id'
      };

      vi.mocked(jwt.verify).mockReturnValue(mockPayload);

      const result = JWTUtils.verifyToken('valid-token');

      expect(result).toEqual(mockPayload);
      expect(jwt.verify).toHaveBeenCalledWith(
        'valid-token',
        'test-jwt-secret-that-is-at-least-32-characters-long'
      );
    });

    it('should throw error for invalid token', () => {
      vi.mocked(jwt.verify).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      expect(() => {
        JWTUtils.verifyToken('invalid-token');
      }).toThrow('Invalid token');
    });

    it('should throw error for expired token', () => {
      vi.mocked(jwt.verify).mockImplementation(() => {
        const error = new Error('Token expired');
        (error as any).name = 'TokenExpiredError';
        throw error;
      });

      expect(() => {
        JWTUtils.verifyToken('expired-token');
      }).toThrow('Token expired');
    });
  });

  describe('verifyRefreshToken', () => {
    it('should verify valid refresh token', () => {
      const mockPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        type: 'refresh',
        jti: 'unique-id'
      };

      vi.mocked(jwt.verify).mockReturnValue(mockPayload);

      const result = JWTUtils.verifyRefreshToken('valid-refresh-token');

      expect(result).toEqual(mockPayload);
      expect(jwt.verify).toHaveBeenCalledWith(
        'valid-refresh-token',
        'test-jwt-refresh-secret-that-is-at-least-32-characters-long'
      );
    });

    it('should throw error for invalid refresh token', () => {
      vi.mocked(jwt.verify).mockImplementation(() => {
        throw new Error('Invalid refresh token');
      });

      expect(() => {
        JWTUtils.verifyRefreshToken('invalid-refresh-token');
      }).toThrow('Invalid refresh token');
    });
  });

  describe('error handling', () => {
    it('should handle missing JWT_SECRET', () => {
      delete process.env.JWT_SECRET;

      expect(() => {
        JWTUtils.generateTokenPair('user-123', '<EMAIL>');
      }).toThrow();
    });

    it('should handle missing JWT_REFRESH_SECRET', () => {
      delete process.env.JWT_REFRESH_SECRET;

      expect(() => {
        JWTUtils.generateTokenPair('user-123', '<EMAIL>');
      }).toThrow();
    });

    it('should handle malformed tokens', () => {
      vi.mocked(jwt.verify).mockImplementation(() => {
        const error = new Error('Malformed token');
        (error as any).name = 'JsonWebTokenError';
        throw error;
      });

      expect(() => {
        JWTUtils.verifyToken('malformed.token');
      }).toThrow('Malformed token');
    });
  });

  describe('token validation', () => {
    it('should validate token type in payload', () => {
      const mockPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        type: 'access',
        jti: 'unique-id'
      };

      vi.mocked(jwt.verify).mockReturnValue(mockPayload);

      const result = JWTUtils.verifyToken('token');

      expect(result.type).toBe('access');
    });

    it('should validate refresh token type', () => {
      const mockPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        type: 'refresh',
        jti: 'unique-id'
      };

      vi.mocked(jwt.verify).mockReturnValue(mockPayload);

      const result = JWTUtils.verifyRefreshToken('refresh-token');

      expect(result.type).toBe('refresh');
    });
  });
});
<template>
  <div class="auth-layout">
    <div class="container">
      <div class="columns is-centered">
        <div class="column is-narrow">
          <EmailVerification />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import EmailVerification from '../components/auth/EmailVerification.vue'
</script>

<style scoped>
.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
}
</style>
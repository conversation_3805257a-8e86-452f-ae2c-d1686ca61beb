// Service Worker for Theme Caching and Offline Support
const CACHE_NAME = 'theme-cache-v1';
const THEME_CACHE_NAME = 'themes-v1';

// Theme-related URLs to cache
const THEME_URLS = [
  '/src/styles/themes/bulmaswatch/index.json',
  '/src/styles/themes/bulmaswatch/default.json',
  '/src/styles/themes/bulmaswatch/darkly.json',
  '/src/styles/themes/bulmaswatch/flatly.json',
  '/src/styles/themes/bulmaswatch/cerulean.json',
];

// CSS files to cache
const THEME_CSS_PATTERN = /\/src\/styles\/themes\/bulmaswatch\/.*\.css$/;

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(THEME_CACHE_NAME).then(cache => {
      console.log('Service Worker: Caching theme files');
      return cache.addAll(THEME_URLS.filter(url => !url.endsWith('.css')));
    })
  );
});

self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          // Clean up old caches
          if (cacheName !== CACHE_NAME && cacheName !== THEME_CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

self.addEventListener('fetch', event => {
  const url = new URL(event.request.url);

  // Handle theme-related requests
  if (isThemeRequest(url.pathname)) {
    event.respondWith(handleThemeRequest(event.request));
  }
});

function isThemeRequest(pathname) {
  return (
    pathname.includes('/styles/themes/') ||
    pathname.includes('bulmaswatch') ||
    THEME_CSS_PATTERN.test(pathname)
  );
}

async function handleThemeRequest(request) {
  const cache = await caches.open(THEME_CACHE_NAME);

  try {
    // Try to fetch from network first for fresh content
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache the fresh response
      cache.put(request, networkResponse.clone());
      return networkResponse;
    }

    // If network fails, try cache
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      console.log('Service Worker: Serving theme from cache', request.url);
      return cachedResponse;
    }

    // If both fail, return network response (will be error)
    return networkResponse;
  } catch (error) {
    // Network failed, try cache
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      console.log('Service Worker: Serving theme from cache (offline)', request.url);
      return cachedResponse;
    }

    // Return a basic error response
    return new Response('Theme not available offline', {
      status: 503,
      statusText: 'Service Unavailable',
    });
  }
}

// Handle theme preloading requests from main thread
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'PRELOAD_THEMES') {
    preloadThemes(event.data.themes);
  }
});

async function preloadThemes(themeUrls) {
  const cache = await caches.open(THEME_CACHE_NAME);

  const preloadPromises = themeUrls.map(async url => {
    try {
      const response = await fetch(url);
      if (response.ok) {
        await cache.put(url, response);
        console.log('Service Worker: Preloaded theme', url);
      }
    } catch (error) {
      console.warn('Service Worker: Failed to preload theme', url, error);
    }
  });

  await Promise.allSettled(preloadPromises);
}

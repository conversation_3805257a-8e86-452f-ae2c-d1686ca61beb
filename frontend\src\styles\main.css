/* Main CSS Entry Point */
/* This file imports all CSS files in the correct order for proper cascade */

/* 1. Theme System - Must be loaded first for CSS custom properties */
@import './themes/themes.css';

/* 2. Base Styles - Foundation styles that set up the document */
@import './base/reset.css';
@import './base/typography.css';

/* 2. Base Styles - Foundation styles that set up the document */
@import './base/variables.css';

/* 3. Component Styles - Reusable UI components */
@import './components/layout.css';
@import './components/buttons.css';
@import './components/forms.css';
@import './components/navigation.css';
@import './components/modals.css';
@import './components/cards.css';
@import './components/auth.css';
@import './components/editors.css';
@import './components/dropdowns.css';

/* 4. Utility Classes - Helper classes for quick styling */
@import './utilities/spacing.css';
@import './utilities/colors.css';
@import './utilities/typography.css';
@import './utilities/responsive.css';

/* 5. Vendor Styles - Third-party library styles */
@import '@fortawesome/fontawesome-free/css/all.css';

/* 6. Theme System Overrides - Must be loaded after vendor styles */
@import './overrides/bulmaswatch-overrides.css';

/* 7. Additional Component Styles */

/* Tables */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--table-background);
  color: var(--color-text);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: var(--spacing-3) var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--table-border);
  vertical-align: top;
}

.table th {
  font-weight: var(--font-weight-semibold);
  background: var(--table-header-background);
  color: var(--color-text-strong);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.table tbody tr:hover {
  background: var(--table-row-hover);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

.table.is-striped tbody tr:nth-child(even) {
  background: var(--color-surface);
}

.table.is-narrow th,
.table.is-narrow td {
  padding: var(--spacing-2) var(--spacing-3);
}

.table.is-hoverable tbody tr:hover {
  background: var(--table-row-hover);
}

.table.is-fullwidth {
  width: 100%;
}

/* Dropdowns - Now handled by components/dropdowns.css */

/* Notifications */
.notification {
  padding: var(--spacing-4) var(--spacing-6);
  border-radius: var(--radius);
  margin-bottom: var(--spacing-4);
  border: 1px solid transparent;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
}

.notification:last-child {
  margin-bottom: 0;
}

.notification .delete {
  margin-left: auto;
  flex-shrink: 0;
}

.notification.is-success {
  background: var(--notification-success-background);
  color: var(--notification-success-text);
  border-color: var(--notification-success-border);
}

.notification.is-danger {
  background: var(--notification-danger-background);
  color: var(--notification-danger-text);
  border-color: var(--notification-danger-border);
}

.notification.is-warning {
  background: var(--notification-warning-background);
  color: var(--notification-warning-text);
  border-color: var(--notification-warning-border);
}

.notification.is-info {
  background: var(--notification-info-background);
  color: var(--notification-info-text);
  border-color: var(--notification-info-border);
}

/* Tags */
.tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  align-items: center;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  background: var(--color-surface);
  color: var(--color-text);
  white-space: nowrap;
  transition: var(--transition-fast);
}

.tag.is-primary {
  background: var(--color-primary);
  color: white;
}

.tag.is-success {
  background: var(--color-success);
  color: white;
}

.tag.is-danger {
  background: var(--color-danger);
  color: white;
}

.tag.is-warning {
  background: var(--color-warning);
  color: rgba(0, 0, 0, 0.7);
}

.tag.is-info {
  background: var(--color-info);
  color: white;
}

.tag.is-link {
  background: var(--color-link);
  color: white;
}

.tag.is-light {
  background: var(--color-surface);
  color: var(--color-text);
}

.tag.is-dark {
  background: var(--color-text-strong);
  color: white;
}

.tag.is-medium {
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-3);
}

.tag.is-large {
  font-size: var(--font-size-base);
  padding: var(--spacing-2) var(--spacing-4);
}

.tag .delete {
  margin-left: var(--spacing-1);
  margin-right: calc(-1 * var(--spacing-1));
}

.tags.has-addons .tag {
  margin-right: 0;
  border-radius: 0;
}

.tags.has-addons .tag:first-child {
  border-top-left-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}

.tags.has-addons .tag:last-child {
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
}

/* Images */
.image {
  display: block;
  position: relative;
  overflow: hidden;
}

.image img {
  display: block;
  height: auto;
  width: 100%;
  object-fit: cover;
}

/* Avatar placeholder styling */
.avatar-placeholder {
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: 100% !important;
  height: 100% !important;
  background: var(--color-surface);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-full);
  color: var(--color-text-muted);
  transition: var(--transition-fast);
}

.avatar-placeholder:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-border-hover);
}

.image.is-16x16 {
  width: 16px;
  height: 16px;
}

.image.is-24x24 {
  width: 24px;
  height: 24px;
}

.image.is-32x32 {
  width: 32px;
  height: 32px;
}

.image.is-48x48 {
  width: 48px;
  height: 48px;
}

.image.is-64x64 {
  width: 64px;
  height: 64px;
}

.image.is-96x96 {
  width: 96px;
  height: 96px;
}

.image.is-128x128 {
  width: 128px;
  height: 128px;
}

.image.is-rounded img {
  border-radius: var(--radius-full);
}

.image.is-square {
  aspect-ratio: 1;
}

/* Icons */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  flex-shrink: 0;
  color: currentColor;
}

.icon.is-small {
  width: 1rem;
  height: 1rem;
}

.icon.is-medium {
  width: 2rem;
  height: 2rem;
}

.icon.is-large {
  width: 3rem;
  height: 3rem;
}

/* Progress bars */
.progress {
  appearance: none;
  border: none;
  border-radius: var(--radius-full);
  display: block;
  height: 1rem;
  overflow: hidden;
  padding: 0;
  width: 100%;
  background-color: var(--color-surface);
}

.progress::-webkit-progress-bar {
  background-color: var(--color-surface);
  border-radius: var(--radius-full);
}

.progress::-webkit-progress-value {
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
}

.progress::-moz-progress-bar {
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
}

.progress.is-primary::-webkit-progress-value {
  background-color: var(--color-primary);
}

.progress.is-success::-webkit-progress-value {
  background-color: var(--color-success);
}

.progress.is-danger::-webkit-progress-value {
  background-color: var(--color-danger);
}

.progress.is-warning::-webkit-progress-value {
  background-color: var(--color-warning);
}

.progress.is-info::-webkit-progress-value {
  background-color: var(--color-info);
}

.progress.is-small {
  height: 0.75rem;
}

.progress.is-medium {
  height: 1.25rem;
}

.progress.is-large {
  height: 1.5rem;
}

/* Loading animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.fa-spin,
.is-spinning {
  animation: spin 2s infinite linear;
}

.fa-pulse,
.is-pulsing {
  animation: pulse 2s infinite;
}

.is-bouncing {
  animation: bounce 1s infinite;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}

/* Selection styling */
::selection {
  background: var(--color-primary);
  color: white;
}

::-moz-selection {
  background: var(--color-primary);
  color: white;
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .is-hidden-print {
    display: none !important;
  }

  .modal,
  .modal-background {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .button {
    border-width: 2px;
  }

  .input,
  .textarea,
  .select select {
    border-width: 2px;
  }

  .card {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .fa-spin,
  .is-spinning {
    animation: none;
  }

  .fa-pulse,
  .is-pulsing {
    animation: none;
  }

  .is-bouncing {
    animation: none;
  }
}

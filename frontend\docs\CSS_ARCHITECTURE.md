# CSS Architecture Documentation

## Overview

This document describes the new organized CSS architecture implemented for the Notes application. The system is designed for maintainability, performance, and theme flexibility.

## Architecture Structure

```
frontend/src/styles/
├── base/                    # Foundation styles
│   ├── reset.css           # CSS reset and normalize
│   ├── typography.css      # Font definitions and text styles
│   └── variables.css       # CSS custom properties for themes
├── components/             # Component-specific styles
│   ├── buttons.css         # Button component styles
│   ├── cards.css          # Card component styles
│   ├── forms.css          # Form component styles
│   ├── layout.css         # Layout and grid styles
│   ├── modals.css         # Modal and overlay styles
│   └── navigation.css     # Navigation and sidebar styles
├── themes/                # Theme system
│   ├── bulmaswatch/       # Bulmaswatch theme files
│   │   ├── default.css    # Default light theme
│   │   ├── darkly.css     # Dark theme
│   │   ├── flatly.css     # Flat design theme
│   │   └── cerulean.css   # Blue accent theme
│   └── themes.css         # Theme switching logic
├── utilities/             # Utility classes
│   ├── colors.css         # Color utility classes
│   ├── responsive.css     # Responsive utility classes
│   ├── spacing.css        # Margin and padding utilities
│   └── typography.css     # Text utility classes
├── vendor/                # Third-party styles
│   └── fontawesome.css    # Optimized FontAwesome styles
└── main.css              # Main entry point
```

## Design Principles

### 1. Separation of Concerns
- **Base**: Foundation styles that apply globally
- **Components**: Styles specific to UI components
- **Themes**: Color schemes and visual variations
- **Utilities**: Helper classes for quick styling
- **Vendor**: Third-party library styles

### 2. CSS Custom Properties
All colors and theme-related values use CSS custom properties for easy theming:

```css
:root {
  --color-primary: #3273dc;
  --color-background: #ffffff;
  --color-text: #363636;
  --transition-theme: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 3. Progressive Enhancement
- Critical styles load first
- Non-critical styles load asynchronously
- Themes are lazy-loaded on demand

### 4. Performance Optimization
- CSS code splitting by category
- Tree shaking for unused styles
- Optimized font loading
- Compressed and minified output

## File Organization

### Base Styles (`base/`)

#### `reset.css`
Modern CSS reset that normalizes browser defaults while preserving useful defaults.

#### `typography.css`
Font definitions, text styles, and typography scales.

#### `variables.css`
CSS custom properties for:
- Colors (primary, secondary, semantic)
- Spacing scales
- Typography scales
- Border radius values
- Shadow definitions
- Transition timings

### Component Styles (`components/`)

Each component file contains styles for a specific UI component:

#### `buttons.css`
- Button base styles
- Button variants (primary, secondary, danger, etc.)
- Button sizes (small, medium, large)
- Button states (hover, active, disabled)

#### `forms.css`
- Input field styles
- Form layout and spacing
- Validation states
- Form controls (checkboxes, radios, selects)

#### `navigation.css`
- Navbar styles
- Sidebar navigation
- Breadcrumb navigation
- Menu components

#### `modals.css`
- Modal overlay and container
- Modal animations
- Modal responsive behavior

#### `cards.css`
- Card container styles
- Card header, body, footer
- Card variants and states

#### `layout.css`
- Grid system
- Flexbox utilities
- Container styles
- Layout helpers

### Theme System (`themes/`)

#### Theme Structure
Each theme provides:
- Color palette definitions
- Component color overrides
- Dark/light mode variations
- Accessibility considerations

#### `themes.css`
Contains the theme switching logic and CSS custom property definitions for different themes.

#### Bulmaswatch Themes (`bulmaswatch/`)
Professional themes based on the Bulmaswatch collection:
- **default**: Clean, minimal light theme
- **darkly**: Professional dark theme
- **flatly**: Modern flat design
- **cerulean**: Blue accent theme

### Utility Classes (`utilities/`)

#### `spacing.css`
Margin and padding utilities following a consistent scale:
```css
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
/* ... */
```

#### `colors.css`
Color utility classes for text and backgrounds:
```css
.has-text-primary { color: var(--color-primary); }
.has-background-primary { background-color: var(--color-primary); }
```

#### `typography.css`
Text utility classes for sizing, weight, and alignment:
```css
.is-size-1 { font-size: 3rem; }
.has-text-weight-bold { font-weight: 700; }
.has-text-centered { text-align: center; }
```

#### `responsive.css`
Responsive utility classes for different screen sizes:
```css
.is-hidden-mobile { display: none; }
@media screen and (min-width: 769px) {
  .is-hidden-mobile { display: block; }
}
```

## Import Order

The `main.css` file imports all styles in the correct cascade order:

1. **Base styles** - Foundation and variables
2. **Component styles** - UI component definitions
3. **Utility classes** - Helper classes
4. **Vendor styles** - Third-party libraries

```css
/* 1. Base Styles */
@import './base/variables.css';
@import './base/reset.css';
@import './base/typography.css';

/* 2. Component Styles */
@import './components/layout.css';
@import './components/buttons.css';
@import './components/forms.css';
/* ... */

/* 3. Utility Classes */
@import './utilities/spacing.css';
@import './utilities/colors.css';
/* ... */

/* 4. Vendor Styles */
@import './vendor/fontawesome.css';
```

## Performance Considerations

### CSS Code Splitting
- Base styles: ~20KB
- Component styles: ~80KB
- Utility styles: ~30KB
- Theme styles: ~25KB each
- Vendor styles: ~40KB

### Lazy Loading
- Default theme loads immediately
- Additional themes load on demand
- Non-critical utilities load after initial render

### Optimization Features
- CSS custom property optimization
- Unused style removal
- Automatic vendor prefixing
- Minification and compression

## Browser Support

### Modern Browsers
Full support for:
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

### CSS Features Used
- CSS Custom Properties (CSS Variables)
- CSS Grid and Flexbox
- CSS Transitions and Animations
- Modern CSS selectors

### Fallbacks
- Graceful degradation for older browsers
- Progressive enhancement approach
- Fallback colors for unsupported custom properties

## Accessibility

### Color Contrast
- All themes meet WCAG AA standards
- High contrast mode support
- Reduced motion preferences respected

### Focus Management
- Visible focus indicators
- Keyboard navigation support
- Screen reader compatibility

### Theme Accessibility
- Automatic contrast checking
- Color-blind friendly palettes
- System preference detection

## Migration Notes

### From Previous System
The old `minimal.css` file has been split into the new organized structure:

- Global styles → `base/`
- Component styles → `components/`
- Utility classes → `utilities/`
- Theme styles → `themes/`

### Breaking Changes
- No breaking changes for existing components
- All Bulma classes remain compatible
- CSS custom properties replace hardcoded colors

### Upgrade Path
1. Update imports to use `main.css`
2. Replace hardcoded colors with CSS custom properties
3. Use new utility classes for consistent spacing
4. Adopt new theme system for color management
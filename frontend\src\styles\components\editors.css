/* Editors: Markdown Editor and Renderer */

.markdown-editor {
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  background: var(--card-background);
  display: flex;
  flex-direction: column;
  height: 500px;
}

.markdown-editor .toolbar {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: 0.75rem;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
  flex-wrap: wrap;
  flex-shrink: 0;
}

.markdown-editor .toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.markdown-editor .toolbar-separator {
  width: 1px;
  height: 1.5rem;
  background: var(--color-border);
  margin: 0 0.25rem;
}

.markdown-editor .toolbar .button {
  border: 1px solid transparent;
  background: transparent;
  color: var(--color-text);
  min-width: 2rem;
  height: 2rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.markdown-editor .toolbar .button:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-border-hover);
}

.markdown-editor .toolbar .button.is-active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.markdown-editor .editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.markdown-editor .editor-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.markdown-editor .editor-pane.half-width {
  flex: 0 0 50%;
  border-right: 1px solid var(--color-border);
}

.markdown-editor .preview-pane {
  flex: 1;
  overflow-y: auto;
  background: var(--color-surface);
}

.markdown-editor .preview-pane.half-width {
  flex: 0 0 50%;
}

.markdown-editor .markdown-textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: none;
  padding: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  background: var(--color-background);
  color: var(--color-text);
}

.markdown-editor .markdown-textarea:disabled {
  background: var(--color-surface);
  color: var(--color-text-muted);
}

.markdown-editor .preview-content {
  padding: 1rem;
  line-height: 1.6;
  color: var(--color-text);
}

/* Markdown preview styles */
.markdown-editor .preview-content h1 {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  margin: 1.5rem 0 1rem 0;
  line-height: 1.2;
  border-bottom: 2px solid var(--color-border);
  padding-bottom: 0.5rem;
}

.markdown-editor .preview-content h2 {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  margin: 1.25rem 0 0.75rem 0;
  line-height: 1.3;
  border-bottom: 1px solid var(--color-border);
  padding-bottom: 0.25rem;
}

.markdown-editor .preview-content h3 {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
}

.markdown-editor .preview-content h4 {
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  margin: 0.75rem 0 0.5rem 0;
}

.markdown-editor .preview-content h5 {
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
  margin: 0.5rem 0 0.25rem 0;
}

.markdown-editor .preview-content h6 {
  font-size: 0.9rem;
  font-weight: var(--font-weight-bold);
  margin: 0.5rem 0 0.25rem 0;
  color: var(--color-text-muted);
}

.markdown-editor .preview-content p { margin: 0 0 1rem 0; }

.markdown-editor .preview-content ul { list-style-type: disc; margin: 1rem 0; padding-left: 2rem; }
.markdown-editor .preview-content ol { list-style-type: decimal; margin: 1rem 0; padding-left: 2rem; }
.markdown-editor .preview-content li { margin: 0.25rem 0; }
.markdown-editor .preview-content li input[type="checkbox"] { margin-right: 0.5rem; }

.markdown-editor .preview-content blockquote {
  border-left: 4px solid var(--color-border);
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  background: var(--color-surface);
  color: var(--color-text-muted);
}

.markdown-editor .preview-content code {
  background: var(--color-surface);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.markdown-editor .preview-content pre {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
}

.markdown-editor .preview-content pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-size: 0.875rem;
}

.markdown-editor .preview-content table {
  width: 100%;
  margin: 1rem 0;
  border-collapse: collapse;
}
.markdown-editor .preview-content table th,
.markdown-editor .preview-content table td {
  border: 1px solid var(--color-border);
  padding: 0.5rem;
  text-align: left;
}
.markdown-editor .preview-content table th {
  background: var(--color-surface);
  font-weight: var(--font-weight-semibold);
}
.markdown-editor .preview-content table tr:nth-child(even) { background: var(--color-surface); }

.markdown-editor .preview-content a { color: var(--color-link); text-decoration: none; }
.markdown-editor .preview-content a:hover { text-decoration: underline; }

.markdown-editor .search-replace-panel {
  padding: 0.75rem;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}
.markdown-editor .search-replace-panel .field { margin-bottom: 0.5rem; }
.markdown-editor .search-replace-panel .field:last-child { margin-bottom: 0; }
.markdown-editor .search-info { display: flex; align-items: center; justify-content: space-between; margin-top: 0.5rem; }
.markdown-editor .search-info .tag { font-size: 0.75rem; }

.markdown-editor .autocomplete-dropdown {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  max-height: 200px;
  overflow-y: auto;
  min-width: 200px;
}
.markdown-editor .autocomplete-item {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid var(--color-surface);
  display: flex;
  flex-direction: column;
}
.markdown-editor .autocomplete-item:last-child { border-bottom: none; }
.markdown-editor .autocomplete-item:hover, .markdown-editor .autocomplete-item.is-active { background: var(--color-surface); }
.markdown-editor .autocomplete-text { font-weight: 500; color: var(--color-text); font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.875rem; }
.markdown-editor .autocomplete-description { font-size: 0.75rem; color: var(--color-text-muted); margin-top: 0.125rem; }

.markdown-editor:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 0.125em rgba(50, 115, 220, 0.25);
}

@media (max-width: 768px) {
  .markdown-editor .editor-content.split-view { flex-direction: column; }
  .markdown-editor .editor-pane.half-width,
  .markdown-editor .preview-pane.half-width {
    flex: 1;
    border-right: none;
    border-bottom: 1px solid var(--color-border);
  }
  .markdown-editor .toolbar { padding: 0.5rem; }
  .markdown-editor .toolbar-group { gap: 0.125rem; }
}


import { describe, it, expect, beforeEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { body, validationResult } from 'express-validator';

// Mock middleware and dependencies
vi.mock('../../middleware/auth');
vi.mock('../../models/User');
vi.mock('../../models/Note');

const app = express();

// Apply security middleware
app.use(helmet());
app.use(express.json({ limit: '10mb' }));

// Rate limiting middleware
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

app.use('/api/', limiter);

// Validation middleware
const validateRegistration = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
  body('displayName').isLength({ min: 1, max: 100 }).trim().escape(),
  (req: any, res: any, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  }
];

const validateNoteCreation = [
  body('title').isLength({ min: 1, max: 200 }).trim().escape(),
  body('content').isLength({ max: 1000000 }), // 1MB limit
  body('noteType').isIn(['richtext', 'markdown', 'kanban']),
  body('tags').optional().isArray({ max: 20 }),
  body('tags.*').isLength({ min: 1, max: 50 }).trim().escape(),
  (req: any, res: any, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  }
];

// Test routes
app.post('/api/auth/register', validateRegistration, (req, res) => {
  res.status(201).json({ message: 'User registered successfully' });
});

app.post('/api/notes', validateNoteCreation, (req, res) => {
  res.status(201).json({ message: 'Note created successfully' });
});

// XSS test route
app.post('/api/test/xss', body('content').escape(), (req, res) => {
  res.json({ content: req.body.content });
});

// SQL injection test route (simulated)
app.get('/api/test/sql/:id', (req, res) => {
  const id = req.params.id;
  // Simulate SQL injection vulnerability check
  if (id.includes("'") || id.includes('"') || id.includes(';')) {
    return res.status(400).json({ error: 'Invalid ID format' });
  }
  res.json({ id });
});

describe('Security and Validation Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Input Validation', () => {
    describe('User Registration Validation', () => {
      it('should accept valid registration data', async () => {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email: '<EMAIL>',
            password: 'SecurePass123',
            displayName: 'Test User'
          });

        expect(response.status).toBe(201);
      });

      it('should reject invalid email formats', async () => {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email: 'invalid-email',
            password: 'SecurePass123',
            displayName: 'Test User'
          });

        expect(response.status).toBe(400);
        expect(response.body.errors).toBeDefined();
        expect(response.body.errors.some((e: any) => e.path === 'email')).toBe(true);
      });

      it('should reject weak passwords', async () => {
        const weakPasswords = [
          'short',           // Too short
          'nouppercase123',  // No uppercase
          'NOLOWERCASE123',  // No lowercase
          'NoNumbers',       // No numbers
          '12345678'         // Only numbers
        ];

        for (const password of weakPasswords) {
          const response = await request(app)
            .post('/api/auth/register')
            .send({
              email: '<EMAIL>',
              password,
              displayName: 'Test User'
            });

          expect(response.status).toBe(400);
          expect(response.body.errors.some((e: any) => e.path === 'password')).toBe(true);
        }
      });

      it('should sanitize display name', async () => {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email: '<EMAIL>',
            password: 'SecurePass123',
            displayName: '<script>alert("xss")</script>Test User'
          });

        expect(response.status).toBe(201);
        // The display name should be escaped/sanitized
      });

      it('should reject display names that are too long', async () => {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email: '<EMAIL>',
            password: 'SecurePass123',
            displayName: 'A'.repeat(101) // Too long
          });

        expect(response.status).toBe(400);
        expect(response.body.errors.some((e: any) => e.path === 'displayName')).toBe(true);
      });
    });

    describe('Note Creation Validation', () => {
      it('should accept valid note data', async () => {
        const response = await request(app)
          .post('/api/notes')
          .send({
            title: 'Valid Note Title',
            content: 'This is valid note content',
            noteType: 'richtext',
            tags: ['work', 'important']
          });

        expect(response.status).toBe(201);
      });

      it('should reject invalid note types', async () => {
        const response = await request(app)
          .post('/api/notes')
          .send({
            title: 'Test Note',
            content: 'Content',
            noteType: 'invalid-type',
            tags: []
          });

        expect(response.status).toBe(400);
        expect(response.body.errors.some((e: any) => e.path === 'noteType')).toBe(true);
      });

      it('should reject titles that are too long', async () => {
        const response = await request(app)
          .post('/api/notes')
          .send({
            title: 'A'.repeat(201), // Too long
            content: 'Content',
            noteType: 'richtext'
          });

        expect(response.status).toBe(400);
        expect(response.body.errors.some((e: any) => e.path === 'title')).toBe(true);
      });

      it('should reject content that exceeds size limit', async () => {
        const response = await request(app)
          .post('/api/notes')
          .send({
            title: 'Large Content Note',
            content: 'A'.repeat(1000001), // Exceeds 1MB limit
            noteType: 'richtext'
          });

        expect(response.status).toBe(400);
        expect(response.body.errors.some((e: any) => e.path === 'content')).toBe(true);
      });

      it('should limit number of tags', async () => {
        const tooManyTags = Array.from({ length: 21 }, (_, i) => `tag${i}`);
        
        const response = await request(app)
          .post('/api/notes')
          .send({
            title: 'Too Many Tags',
            content: 'Content',
            noteType: 'richtext',
            tags: tooManyTags
          });

        expect(response.status).toBe(400);
        expect(response.body.errors.some((e: any) => e.path === 'tags')).toBe(true);
      });

      it('should sanitize tag content', async () => {
        const response = await request(app)
          .post('/api/notes')
          .send({
            title: 'Tag Sanitization Test',
            content: 'Content',
            noteType: 'richtext',
            tags: ['<script>alert("xss")</script>work', 'normal-tag']
          });

        expect(response.status).toBe(201);
        // Tags should be sanitized
      });
    });
  });

  describe('XSS Protection', () => {
    it('should escape HTML content to prevent XSS', async () => {
      const maliciousContent = '<script>alert("XSS Attack!")</script>';
      
      const response = await request(app)
        .post('/api/test/xss')
        .send({ content: maliciousContent });

      expect(response.status).toBe(200);
      expect(response.body.content).not.toContain('<script>');
      expect(response.body.content).toContain('&lt;script&gt;');
    });

    it('should handle various XSS attack vectors', async () => {
      const xssVectors = [
        '<img src="x" onerror="alert(1)">',
        '<svg onload="alert(1)">',
        'javascript:alert(1)',
        '<iframe src="javascript:alert(1)"></iframe>',
        '<object data="javascript:alert(1)"></object>'
      ];

      for (const vector of xssVectors) {
        const response = await request(app)
          .post('/api/test/xss')
          .send({ content: vector });

        expect(response.status).toBe(200);
        expect(response.body.content).not.toContain('javascript:');
        expect(response.body.content).not.toContain('onerror=');
        expect(response.body.content).not.toContain('onload=');
      }
    });
  });

  describe('SQL Injection Protection', () => {
    it('should reject SQL injection attempts in URL parameters', async () => {
      const sqlInjectionAttempts = [
        "1'; DROP TABLE users; --",
        "1' OR '1'='1",
        "1' UNION SELECT * FROM users --",
        '1"; DELETE FROM notes; --'
      ];

      for (const attempt of sqlInjectionAttempts) {
        const response = await request(app)
          .get(`/api/test/sql/${encodeURIComponent(attempt)}`);

        expect(response.status).toBe(400);
        expect(response.body.error).toBe('Invalid ID format');
      }
    });

    it('should accept valid ID formats', async () => {
      const validIds = [
        'user-123',
        'note_456',
        'abc123def',
        '12345'
      ];

      for (const id of validIds) {
        const response = await request(app)
          .get(`/api/test/sql/${id}`);

        expect(response.status).toBe(200);
        expect(response.body.id).toBe(id);
      }
    });
  });

  describe('Rate Limiting', () => {
    it('should allow requests within rate limit', async () => {
      // Make several requests within limit
      for (let i = 0; i < 5; i++) {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email: `test${i}@example.com`,
            password: 'SecurePass123',
            displayName: 'Test User'
          });

        expect(response.status).toBe(201);
      }
    });

    // Note: This test would need to be run with a lower rate limit for practical testing
    it.skip('should block requests exceeding rate limit', async () => {
      // This test is skipped because it would require making 100+ requests
      // In a real scenario, you'd configure a lower limit for testing
      
      const promises = [];
      for (let i = 0; i < 102; i++) {
        promises.push(
          request(app)
            .post('/api/auth/register')
            .send({
              email: `test${i}@example.com`,
              password: 'SecurePass123',
              displayName: 'Test User'
            })
        );
      }

      const responses = await Promise.all(promises);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/api/test/sql/123');

      // Check for Helmet security headers
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('0');
      expect(response.headers['strict-transport-security']).toBeDefined();
    });
  });

  describe('Content Security Policy', () => {
    it('should set appropriate CSP headers', async () => {
      const response = await request(app)
        .get('/api/test/sql/123');

      expect(response.headers['content-security-policy']).toBeDefined();
    });
  });

  describe('Request Size Limits', () => {
    it('should reject requests exceeding size limit', async () => {
      // Create a payload larger than 10MB
      const largePayload = {
        content: 'A'.repeat(11 * 1024 * 1024) // 11MB
      };

      const response = await request(app)
        .post('/api/test/xss')
        .send(largePayload);

      expect(response.status).toBe(413); // Payload Too Large
    });

    it('should accept requests within size limit', async () => {
      // Create a payload smaller than 10MB
      const normalPayload = {
        content: 'A'.repeat(1024) // 1KB
      };

      const response = await request(app)
        .post('/api/test/xss')
        .send(normalPayload);

      expect(response.status).toBe(200);
    });
  });

  describe('Input Sanitization Edge Cases', () => {
    it('should handle null and undefined values', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: null,
          password: undefined,
          displayName: ''
        });

      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
    });

    it('should handle special characters in input', async () => {
      const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123',
          displayName: specialChars
        });

      expect(response.status).toBe(201);
    });

    it('should handle Unicode characters', async () => {
      const unicodeText = '测试用户 🚀 émojis';
      
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123',
          displayName: unicodeText
        });

      expect(response.status).toBe(201);
    });

    it('should trim whitespace from inputs', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '  <EMAIL>  ',
          password: 'SecurePass123',
          displayName: '  Test User  '
        });

      expect(response.status).toBe(201);
    });
  });
});
<template>
  <div class="kanban-card" :class="{
    'is-dragging': isDragging,
    'has-custom-color': card.color,
    'has-column-color': !card.color && column.color,
    [`card-color-${getColorName(card.color)}`]: card.color,
    'card-column-color': !card.color && column.color
  }" draggable="true" @dragstart="handleDragStart" @dragend="handleDragEnd"
    @click="$emit('click', card)">
    <!-- Card Cover -->
    <div v-if="coverImage" class="card-cover">
      <img :src="coverImage" :alt="card.title" />
    </div>

    <!-- Card Labels -->
    <div v-if="card.labels.length" class="card-labels">
      <span v-for="label in card.labels" :key="label.id" class="card-label" :class="getLabelColorClass(label.color)">
        {{ label.name }}
      </span>
    </div>

    <!-- Card Title -->
    <div class="card-title">
      {{ card.title }}
    </div>

    <!-- Card Description -->
    <div v-if="card.description" class="card-description">
      {{ truncatedDescription }}
    </div>

    <!-- Card Metadata -->
    <div class="card-metadata">
      <!-- Due Date -->
      <div v-if="card.dueDate" class="card-due-date" :class="dueDateClass">
        <i class="fas fa-clock"></i>
        {{ formatDueDate(card.dueDate) }}
      </div>

      <!-- Priority -->
      <div v-if="card.priority !== 'medium'" class="card-priority" :class="`priority-${card.priority}`">
        <i class="fas fa-flag"></i>
        {{ card.priority }}
      </div>
    </div>

    <!-- Card Footer -->
    <div v-if="hasFooterItems" class="card-footer">
      <!-- Checklist Progress -->
      <div v-if="card.checklist.length" class="checklist-progress">
        <i class="fas fa-check-square"></i>
        {{ completedChecklistItems }}/{{ card.checklist.length }}
      </div>

      <!-- Attachments -->
      <div v-if="card.attachments.length" class="attachments-count">
        <i class="fas fa-paperclip"></i>
        {{ card.attachments.length }}
      </div>

      <!-- Comments -->
      <div v-if="card.comments.length" class="comments-count">
        <i class="fas fa-comment"></i>
        {{ card.comments.length }}
      </div>

      <!-- Assignees -->
      <div v-if="card.assignees.length" class="card-assignees">
        <div v-for="assignee in displayedAssignees" :key="assignee.id" class="assignee-avatar" :title="assignee.name">
          <img v-if="assignee.avatar" :src="assignee.avatar" :alt="assignee.name" />
          <span v-else>{{ assignee.name.charAt(0).toUpperCase() }}</span>
        </div>
        <div v-if="extraAssigneesCount > 0" class="extra-assignees">
          +{{ extraAssigneesCount }}
        </div>
      </div>
    </div>

    <!-- Card Actions (shown on hover) -->
    <div class="card-actions">
      <button class="card-action-btn" @click.stop="showColorPicker = true" title="Change color">
        <i class="fas fa-palette"></i>
      </button>
      <button class="card-action-btn" @click.stop="$emit('delete:card', card.id)" title="Delete card">
        <i class="fas fa-trash"></i>
      </button>
    </div>

    <!-- Color Picker -->
    <div v-if="showColorPicker" class="color-picker-overlay" @click="showColorPicker = false">
      <div class="color-picker" @click.stop>
        <h6 class="title is-6">Choose Card Color</h6>
        <div class="color-options">
          <button v-for="color in colorOptions" :key="color.value" class="color-option"
            :class="{ 'is-selected': card.color === color.value, [`label-color-${getColorName(color.value)}`]: true }"
            @click="updateCardColor(color.value)" :title="color.name" />
          <button class="color-option is-clear" :class="{ 'is-selected': !card.color }" @click="updateCardColor(null)"
            title="Default">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { KanbanCard, KanbanColumn, KanbanBoard } from '@/types/kanban'
import { useDragAndDrop } from '@/composables/useDragAndDrop'

interface Props {
  card: KanbanCard
  column: KanbanColumn
  board: KanbanBoard
}

interface Emits {
  (e: 'update:card', cardId: string, updates: Partial<KanbanCard>): void
  (e: 'delete:card', cardId: string): void
  (e: 'drag:start', card: KanbanCard): void
  (e: 'drag:end'): void
  (e: 'click', card: KanbanCard): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Drag and drop composable
const { startCardDrag, endCardDrag } = useDragAndDrop()

// Reactive state
const isDragging = ref(false)
const showColorPicker = ref(false)

// Color options for cards
const colorOptions = [
  { name: 'Red', value: '#ff6b6b' },
  { name: 'Orange', value: '#ffa726' },
  { name: 'Yellow', value: '#ffeb3b' },
  { name: 'Green', value: '#66bb6a' },
  { name: 'Blue', value: '#42a5f5' },
  { name: 'Purple', value: '#ab47bc' },
  { name: 'Pink', value: '#ec407a' },
  { name: 'Teal', value: '#26a69a' },
  { name: 'Indigo', value: '#5c6bc0' },
  { name: 'Brown', value: '#8d6e63' },
  { name: 'Grey', value: '#78909c' }
]

// Computed properties

const coverImage = computed(() => {
  const imageAttachment = props.card.attachments.find(att =>
    att.type.startsWith('image/') && props.board.settings.cardCoverImages
  )
  return imageAttachment?.url
})

const truncatedDescription = computed(() => {
  if (!props.card.description) return ''
  return props.card.description.length > 100
    ? props.card.description.substring(0, 100) + '...'
    : props.card.description
})

const completedChecklistItems = computed(() => {
  return props.card.checklist.filter(item => item.completed).length
})

const displayedAssignees = computed(() => {
  return props.card.assignees.slice(0, 3)
})

// Helper methods for color classes
const getColorName = (color: string | null): string => {
  if (!color) return ''
  
  const colorMap: { [key: string]: string } = {
    '#ff6b6b': 'red',
    '#ffa726': 'orange',
    '#ffeb3b': 'yellow',
    '#66bb6a': 'green',
    '#42a5f5': 'blue',
    '#ab47bc': 'purple',
    '#ec407a': 'pink',
    '#26a69a': 'teal',
    '#5c6bc0': 'indigo',
    '#8d6e63': 'brown',
    '#78909c': 'grey'
  }
  
  return colorMap[color] || 'blue'
}

const getLabelColorClass = (color: string): string => {
  return `label-color-${getColorName(color)}`
}

const extraAssigneesCount = computed(() => {
  return Math.max(0, props.card.assignees.length - 3)
})

const hasFooterItems = computed(() => {
  return props.card.checklist.length > 0 ||
    props.card.attachments.length > 0 ||
    props.card.comments.length > 0 ||
    props.card.assignees.length > 0
})

const dueDateClass = computed(() => {
  if (!props.card.dueDate) return ''

  const now = new Date()
  const dueDate = new Date(props.card.dueDate)
  const diffTime = dueDate.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return 'is-overdue'
  if (diffDays === 0) return 'is-due-today'
  if (diffDays <= 3) return 'is-due-soon'
  return ''
})

// Methods
const formatDueDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = date.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Tomorrow'
  if (diffDays === -1) return 'Yesterday'
  if (diffDays < 0) return `${Math.abs(diffDays)} days ago`
  if (diffDays <= 7) return `${diffDays} days`

  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  })
}

const updateCardColor = (color: string | null) => {
  emit('update:card', props.card.id, { color: color || undefined })
  showColorPicker.value = false
}

const handleDragStart = (event: DragEvent) => {
  isDragging.value = true
  emit('drag:start', props.card)

  // Set global drag state
  startCardDrag(props.card)

  const dragData = {
    type: 'card',
    cardId: props.card.id,
    sourceColumnId: props.card.columnId,
    currentPosition: props.card.position
  }

  console.log('KanbanCard: Starting drag with data:', dragData)

  event.dataTransfer!.effectAllowed = 'move'
  event.dataTransfer!.setData('text/plain', JSON.stringify(dragData))
}

const handleDragEnd = () => {
  isDragging.value = false
  emit('drag:end')

  // Clear global drag state
  endCardDrag()
}
</script>

<style scoped>
.kanban-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0.75rem;
  cursor: grab;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.kanban-card:active {
  cursor: grabbing;
}

.kanban-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.kanban-card:hover .card-actions {
  opacity: 1;
}

.kanban-card.is-dragging {
  opacity: 0.7;
  transform: rotate(3deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.card-cover {
  margin: -0.75rem -0.75rem 0.75rem -0.75rem;
  border-radius: 6px 6px 0 0;
  overflow: hidden;
}

.card-cover img {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.card-labels {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.card-label {
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  color: white;
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.card-title {
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
  margin-bottom: 0.5rem;
  word-wrap: break-word;
}

.card-description {
  font-size: 0.875rem;
  color: #6c757d;
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

.card-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.card-due-date,
.card-priority {
  font-size: 0.75rem;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.card-due-date {
  background: #e9ecef;
  color: #495057;
}

.card-due-date.is-overdue {
  background: #dc3545;
  color: white;
}

.card-due-date.is-due-today {
  background: #ffc107;
  color: #212529;
}

.card-due-date.is-due-soon {
  background: #fd7e14;
  color: white;
}

.card-priority.priority-low {
  background: #28a745;
  color: white;
}

.card-priority.priority-high {
  background: #fd7e14;
  color: white;
}

.card-priority.priority-urgent {
  background: #dc3545;
  color: white;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #f1f3f4;
}

.checklist-progress,
.attachments-count,
.comments-count {
  font-size: 0.75rem;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.card-assignees {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-left: auto;
}

.assignee-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  overflow: hidden;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.assignee-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.extra-assignees {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #6c757d;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.6rem;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.card-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s;
}

.card-action-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  transition: background-color 0.2s;
}

.card-action-btn:hover {
  background: rgba(0, 0, 0, 0.8);
}

.color-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.color-picker {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  min-width: 300px;
}

.color-options {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.5rem;
  margin-top: 1rem;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.is-selected {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.color-option.is-clear {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  color: #6c757d;
}

/* Card color utility classes */
.card-color-red {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 107, 107, 0.1));
  border-left: 4px solid #ff6b6b;
}

.card-color-orange {
  background: linear-gradient(135deg, rgba(255, 167, 38, 0.2), rgba(255, 167, 38, 0.1));
  border-left: 4px solid #ffa726;
}

.card-color-yellow {
  background: linear-gradient(135deg, rgba(255, 235, 59, 0.2), rgba(255, 235, 59, 0.1));
  border-left: 4px solid #ffeb3b;
}

.card-color-green {
  background: linear-gradient(135deg, rgba(102, 187, 106, 0.2), rgba(102, 187, 106, 0.1));
  border-left: 4px solid #66bb6a;
}

.card-color-blue {
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.2), rgba(66, 165, 245, 0.1));
  border-left: 4px solid #42a5f5;
}

.card-color-purple {
  background: linear-gradient(135deg, rgba(171, 71, 188, 0.2), rgba(171, 71, 188, 0.1));
  border-left: 4px solid #ab47bc;
}

.card-color-pink {
  background: linear-gradient(135deg, rgba(236, 64, 122, 0.2), rgba(236, 64, 122, 0.1));
  border-left: 4px solid #ec407a;
}

.card-color-teal {
  background: linear-gradient(135deg, rgba(38, 166, 154, 0.2), rgba(38, 166, 154, 0.1));
  border-left: 4px solid #26a69a;
}

.card-color-indigo {
  background: linear-gradient(135deg, rgba(92, 107, 192, 0.2), rgba(92, 107, 192, 0.1));
  border-left: 4px solid #5c6bc0;
}

.card-color-brown {
  background: linear-gradient(135deg, rgba(141, 110, 99, 0.2), rgba(141, 110, 99, 0.1));
  border-left: 4px solid #8d6e63;
}

.card-color-grey {
  background: linear-gradient(135deg, rgba(120, 144, 156, 0.2), rgba(120, 144, 156, 0.1));
  border-left: 4px solid #78909c;
}

/* Column color inheritance */
.card-column-color {
  background: linear-gradient(135deg, rgba(var(--color-primary-rgb, 66, 165, 245), 0.15), rgba(var(--color-primary-rgb, 66, 165, 245), 0.05));
  border-left: 4px solid rgba(var(--color-primary-rgb, 66, 165, 245), 0.4);
}

/* Label color utility classes */
.label-color-red { background-color: #ff6b6b; }
.label-color-orange { background-color: #ffa726; }
.label-color-yellow { background-color: #ffeb3b; }
.label-color-green { background-color: #66bb6a; }
.label-color-blue { background-color: #42a5f5; }
.label-color-purple { background-color: #ab47bc; }
.label-color-pink { background-color: #ec407a; }
.label-color-teal { background-color: #26a69a; }
.label-color-indigo { background-color: #5c6bc0; }
.label-color-brown { background-color: #8d6e63; }
.label-color-grey { background-color: #78909c; }
</style>
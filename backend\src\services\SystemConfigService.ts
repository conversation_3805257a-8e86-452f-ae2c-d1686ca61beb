import { getDatabase } from '../config/database';
import { v4 as uuidv4 } from 'uuid';

export interface SystemConfig {
  features: {
    registration: boolean;
    googleAuth: boolean;
    twoFactor: boolean;
    emailVerification: boolean;
    maintenance: boolean;
  };
  limits: {
    maxNotesPerUser: number;
    maxFileSize: number;
    rateLimit: {
      general: number;
      auth: number;
    };
  };
  maintenance: {
    mode: boolean;
    message: string;
    scheduledStart?: string;
    scheduledEnd?: string;
  };
  notifications: {
    emailEnabled: boolean;
    adminAlerts: boolean;
    securityAlerts: boolean;
  };
}

export class SystemConfigService {
  private static config: SystemConfig | null = null;
  private static initialized = false;

  /**
   * Initialize system configuration from database and environment
   */
  static async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Create config table if it doesn't exist
      await this.createConfigTable();
      
      // Load configuration from database or create default
      this.config = await this.loadConfig();
      this.initialized = true;
      
      console.log('System configuration initialized');
    } catch (error) {
      console.error('Failed to initialize system configuration:', error);
      // Use default config as fallback
      this.config = this.getDefaultConfig();
      this.initialized = true;
    }
  }

  /**
   * Get current system configuration
   */
  static async getConfig(): Promise<SystemConfig> {
    if (!this.initialized) {
      await this.initialize();
    }
    return this.config!;
  }

  /**
   * Update system configuration
   */
  static async updateConfig(updates: Partial<SystemConfig>): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    const newConfig = { ...this.config!, ...updates };
    await this.saveConfig(newConfig);
    this.config = newConfig;
  }

  /**
   * Check if a feature is enabled
   */
  static async isFeatureEnabled(feature: keyof SystemConfig['features']): Promise<boolean> {
    const config = await this.getConfig();
    return config.features[feature];
  }

  /**
   * Toggle maintenance mode
   */
  static async toggleMaintenanceMode(enabled: boolean, message?: string): Promise<void> {
    const updates: Partial<SystemConfig> = {
      features: {
        ...(await this.getConfig()).features,
        maintenance: enabled
      },
      maintenance: {
        ...(await this.getConfig()).maintenance,
        mode: enabled,
        message: message || 'System maintenance in progress'
      }
    };

    await this.updateConfig(updates);
  }

  /**
   * Get system limits
   */
  static async getLimits(): Promise<SystemConfig['limits']> {
    const config = await this.getConfig();
    return config.limits;
  }

  private static async createConfigTable(): Promise<void> {
    const db = getDatabase();
    const query = `
      CREATE TABLE IF NOT EXISTS system_config (
        id TEXT PRIMARY KEY,
        config_key TEXT UNIQUE NOT NULL,
        config_value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    return new Promise((resolve, reject) => {
      db.run(query, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  private static async loadConfig(): Promise<SystemConfig> {
    const db = getDatabase();
    const query = 'SELECT config_key, config_value FROM system_config';

    return new Promise((resolve, reject) => {
      db.all(query, (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        if (rows.length === 0) {
          // No config in database, create default
          const defaultConfig = this.getDefaultConfig();
          this.saveConfig(defaultConfig).then(() => resolve(defaultConfig)).catch(reject);
          return;
        }

        try {
          const config = this.getDefaultConfig();
          
          // Override with database values
          rows.forEach(row => {
            const keys = row.config_key.split('.');
            let current: any = config;
            
            for (let i = 0; i < keys.length - 1; i++) {
              if (!current[keys[i]]) current[keys[i]] = {};
              current = current[keys[i]];
            }
            
            const value = JSON.parse(row.config_value);
            current[keys[keys.length - 1]] = value;
          });

          resolve(config);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  private static async saveConfig(config: SystemConfig): Promise<void> {
    const db = getDatabase();
    const flatConfig = this.flattenConfig(config);

    // Use transaction for atomic updates
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        let completed = 0;
        let hasError = false;
        const entries = Object.entries(flatConfig);

        if (entries.length === 0) {
          db.run('COMMIT');
          resolve();
          return;
        }

        entries.forEach(([key, value]) => {
          const query = `
            INSERT OR REPLACE INTO system_config (id, config_key, config_value, updated_at)
            VALUES (?, ?, ?, ?)
          `;
          
          db.run(query, [uuidv4(), key, JSON.stringify(value), new Date().toISOString()], (err) => {
            if (err && !hasError) {
              hasError = true;
              db.run('ROLLBACK');
              reject(err);
              return;
            }

            completed++;
            if (completed === entries.length && !hasError) {
              db.run('COMMIT');
              resolve();
            }
          });
        });
      });
    });
  }

  private static getDefaultConfig(): SystemConfig {
    return {
      features: {
        registration: process.env.ENABLE_REGISTRATION !== 'false',
        googleAuth: !!process.env.GOOGLE_CLIENT_ID,
        twoFactor: process.env.ENABLE_2FA === 'true',
        emailVerification: process.env.ENABLE_EMAIL_VERIFICATION !== 'false',
        maintenance: process.env.MAINTENANCE_MODE === 'true'
      },
      limits: {
        maxNotesPerUser: parseInt(process.env.MAX_NOTES_PER_USER || '10000'),
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB
        rateLimit: {
          general: parseInt(process.env.RATE_LIMIT_GENERAL || '100'),
          auth: parseInt(process.env.RATE_LIMIT_AUTH || '10')
        }
      },
      maintenance: {
        mode: process.env.MAINTENANCE_MODE === 'true',
        message: process.env.MAINTENANCE_MESSAGE || 'System maintenance in progress'
      },
      notifications: {
        emailEnabled: process.env.EMAIL_ENABLED === 'true',
        adminAlerts: process.env.ADMIN_ALERTS === 'true',
        securityAlerts: process.env.SECURITY_ALERTS === 'true'
      }
    };
  }

  private static flattenConfig(config: SystemConfig, prefix = ''): Record<string, any> {
    const result: Record<string, any> = {};

    Object.entries(config).forEach(([key, value]) => {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        Object.assign(result, this.flattenConfig(value as any, newKey));
      } else {
        result[newKey] = value;
      }
    });

    return result;
  }
}
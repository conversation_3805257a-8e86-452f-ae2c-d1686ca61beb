# Folder Structure System Design

## Overview

The folder structure system will extend the existing note organization capabilities by adding hierarchical folder management alongside the current tag-based system. This design integrates seamlessly with the existing Vue.js + Pinia architecture, leveraging the current note service patterns and store management approach.

The system will provide users with familiar file-system-like organization while maintaining the flexibility of tags for cross-cutting categorization. Folders will be implemented as a complementary organizational layer that works in conjunction with existing features like search, filtering, and collaboration.

## Architecture

### Database Schema Extensions

The folder system requires minimal database schema changes to the existing structure:

```sql
-- New folders table
CREATE TABLE folders (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  name VARCHAR(255) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  parent_id VARCHAR(36) NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  <PERSON>OR<PERSON><PERSON><PERSON> KEY (parent_id) REFERENCES folders(id) ON DELETE CASCADE,
  INDEX idx_folders_user_id (user_id),
  INDEX idx_folders_parent_id (parent_id)
);

-- Add folder reference to existing notes table
ALTER TABLE notes ADD COLUMN folder_id VARCHAR(36) NULL;
ALTER TABLE notes ADD FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE SET NULL;
ALTER TABLE notes ADD INDEX idx_notes_folder_id (folder_id);

-- Create default "Uncategorized" folder for existing notes
INSERT INTO folders (id, name, user_id, parent_id) 
SELECT UUID(), 'Uncategorized', id, NULL FROM users;
```

### Frontend Architecture Integration

The folder system integrates with the existing frontend architecture through:

1. **New Folder Store** (`frontend/src/stores/folders.ts`)
2. **Extended Note Service** (additions to `frontend/src/services/noteService.ts`)
3. **New Folder Types** (`frontend/src/types/folder.ts`)
4. **Enhanced Sidebar Component** (modifications to existing `Sidebar.vue`)
5. **New Folder Components** (tree view, context menus, drag & drop)

## Components and Interfaces

### Core Data Types

```typescript
// frontend/src/types/folder.ts
export interface Folder {
  id: string
  name: string
  userId: string
  parentId?: string
  createdAt: string
  updatedAt: string
  noteCount: number
  subfolders: Folder[]
  isExpanded?: boolean // UI state
}

export interface FolderTree {
  id: string
  name: string
  noteCount: number
  subfolders: FolderTree[]
  isExpanded: boolean
  level: number
}

export interface CreateFolderData {
  name: string
  parentId?: string
}

export interface UpdateFolderData {
  name?: string
  parentId?: string
}

export interface FolderFilters {
  search?: string
  includeEmpty?: boolean
}

export interface MoveNoteToFolderData {
  noteId: string
  folderId?: string // undefined means move to "Uncategorized"
}
```

### Folder Service

```typescript
// Extensions to frontend/src/services/noteService.ts
export interface FoldersResponse {
  folders: Folder[]
}

class FolderService {
  // Get user's folder tree
  async getFolders(): Promise<Folder[]>
  
  // Create new folder
  async createFolder(folderData: CreateFolderData): Promise<Folder>
  
  // Update folder
  async updateFolder(id: string, updateData: UpdateFolderData): Promise<Folder>
  
  // Delete folder (moves notes to parent or Uncategorized)
  async deleteFolder(id: string): Promise<void>
  
  // Move note to folder
  async moveNoteToFolder(noteId: string, folderId?: string): Promise<Note>
  
  // Get notes in specific folder
  async getNotesInFolder(folderId: string, includeSubfolders: boolean = false): Promise<Note[]>
}
```

### Folder Store (Pinia)

```typescript
// frontend/src/stores/folders.ts
export const useFoldersStore = defineStore('folders', () => {
  // State
  const folders = ref<Folder[]>([])
  const selectedFolderId = ref<string | null>(null)
  const expandedFolders = ref<Set<string>>(new Set())
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const folderTree = computed(() => buildFolderTree(folders.value))
  const selectedFolder = computed(() => 
    folders.value.find(f => f.id === selectedFolderId.value)
  )
  const uncategorizedFolder = computed(() =>
    folders.value.find(f => f.name === 'Uncategorized' && !f.parentId)
  )

  // Actions
  const loadFolders = async () => { /* ... */ }
  const createFolder = async (folderData: CreateFolderData) => { /* ... */ }
  const updateFolder = async (id: string, updateData: UpdateFolderData) => { /* ... */ }
  const deleteFolder = async (id: string) => { /* ... */ }
  const selectFolder = (folderId: string | null) => { /* ... */ }
  const toggleFolderExpansion = (folderId: string) => { /* ... */ }
  const moveNoteToFolder = async (noteId: string, folderId?: string) => { /* ... */ }

  return {
    // State & getters
    folders, selectedFolderId, folderTree, selectedFolder,
    // Actions
    loadFolders, createFolder, updateFolder, deleteFolder,
    selectFolder, toggleFolderExpansion, moveNoteToFolder
  }
})
```

### UI Components

#### FolderTree Component
```vue
<!-- frontend/src/components/folders/FolderTree.vue -->
<template>
  <div class="folder-tree">
    <div class="folder-tree-header">
      <h3 class="section-title">Folders</h3>
      <button @click="createRootFolder" class="button is-ghost is-small">
        <i class="fas fa-plus"></i>
      </button>
    </div>
    
    <div class="folder-tree-content">
      <FolderTreeNode 
        v-for="folder in rootFolders" 
        :key="folder.id"
        :folder="folder"
        :level="0"
        @select="selectFolder"
        @create-subfolder="createSubfolder"
        @rename="renameFolder"
        @delete="deleteFolder"
        @move-note="moveNoteToFolder"
      />
    </div>
  </div>
</template>
```

#### FolderTreeNode Component
```vue
<!-- frontend/src/components/folders/FolderTreeNode.vue -->
<template>
  <div class="folder-node" :style="{ paddingLeft: `${level * 16}px` }">
    <div class="folder-item" 
         :class="{ 'is-active': isSelected, 'is-expanded': isExpanded }"
         @click="handleSelect"
         @contextmenu="showContextMenu">
      
      <button v-if="hasSubfolders" 
              @click.stop="toggleExpansion"
              class="folder-toggle">
        <i :class="isExpanded ? 'fas fa-chevron-down' : 'fas fa-chevron-right'"></i>
      </button>
      
      <i class="fas fa-folder folder-icon"></i>
      
      <span v-if="!isEditing" class="folder-name">{{ folder.name }}</span>
      <input v-else 
             v-model="editName"
             @blur="saveEdit"
             @keyup.enter="saveEdit"
             @keyup.escape="cancelEdit"
             class="folder-name-input">
      
      <span class="folder-count">{{ folder.noteCount }}</span>
    </div>

    <!-- Subfolders -->
    <div v-if="isExpanded && hasSubfolders" class="folder-children">
      <FolderTreeNode 
        v-for="subfolder in folder.subfolders"
        :key="subfolder.id"
        :folder="subfolder"
        :level="level + 1"
        @select="$emit('select', $event)"
        @create-subfolder="$emit('create-subfolder', $event)"
        @rename="$emit('rename', $event)"
        @delete="$emit('delete', $event)"
        @move-note="$emit('move-note', $event)"
      />
    </div>

    <!-- Context Menu -->
    <FolderContextMenu 
      v-if="showMenu"
      :folder="folder"
      :position="menuPosition"
      @create-subfolder="handleCreateSubfolder"
      @rename="startEdit"
      @delete="handleDelete"
      @close="showMenu = false"
    />
  </div>
</template>
```

#### Enhanced Sidebar Integration
```vue
<!-- Modifications to frontend/src/components/layout/Sidebar.vue -->
<template>
  <div class="sidebar">
    <!-- Existing sections... -->
    
    <!-- Tags Section (existing) -->
    <div class="sidebar-section tags-section">
      <!-- Existing tags implementation -->
    </div>

    <!-- NEW: Folders Section -->
    <div class="sidebar-section folders-section">
      <FolderTree 
        :is-collapsed="isCollapsed"
        @folder-selected="handleFolderSelection"
        @note-moved="handleNoteMoved"
      />
    </div>

    <!-- Quick Actions (existing) -->
    <div class="sidebar-section actions-section">
      <!-- Existing quick actions -->
    </div>
  </div>
</template>
```

## Data Models

### Folder Hierarchy Management

The folder system uses a self-referencing tree structure with the following characteristics:

- **Root folders**: `parentId` is `null`
- **Subfolders**: `parentId` references parent folder
- **Maximum depth**: 5 levels to prevent performance issues
- **Circular reference prevention**: Backend validation ensures no cycles
- **Orphan handling**: When parent is deleted, children move to grandparent or root

### Note-Folder Relationships

- **One-to-many**: Each note belongs to at most one folder
- **Optional relationship**: Notes can exist without folders (in "Uncategorized")
- **Cascade behavior**: When folder is deleted, notes move to parent folder or "Uncategorized"
- **Migration support**: Existing notes without folders are automatically assigned to "Uncategorized"

### Folder Tree Building Algorithm

```typescript
function buildFolderTree(folders: Folder[]): FolderTree[] {
  const folderMap = new Map<string, Folder>()
  const rootFolders: FolderTree[] = []

  // Create lookup map
  folders.forEach(folder => folderMap.set(folder.id, folder))

  // Build tree structure
  folders.forEach(folder => {
    if (!folder.parentId) {
      // Root folder
      rootFolders.push(createTreeNode(folder, 0))
    } else {
      // Child folder - add to parent's subfolders
      const parent = folderMap.get(folder.parentId)
      if (parent) {
        parent.subfolders.push(createTreeNode(folder, getLevel(folder, folderMap) + 1))
      }
    }
  })

  return rootFolders.sort((a, b) => a.name.localeCompare(b.name))
}
```

## Error Handling

### Validation Rules

1. **Folder names**: 1-255 characters, no special characters except spaces, hyphens, underscores
2. **Nesting depth**: Maximum 5 levels deep
3. **Circular references**: Prevented at API level
4. **Duplicate names**: Allowed (folders identified by ID)
5. **Reserved names**: "Uncategorized" reserved for system use

### Error Recovery

1. **Network failures**: Offline queue for folder operations
2. **Concurrent modifications**: Optimistic updates with rollback
3. **Invalid operations**: Clear error messages with suggested actions
4. **Data corruption**: Automatic repair of broken folder hierarchies

### User Feedback

```typescript
// Error handling patterns
try {
  await foldersStore.createFolder({ name: folderName, parentId })
} catch (error) {
  if (error.code === 'FOLDER_NAME_INVALID') {
    showError('Folder name contains invalid characters')
  } else if (error.code === 'MAX_DEPTH_EXCEEDED') {
    showError('Cannot create folder: maximum nesting depth reached')
  } else {
    showError('Failed to create folder. Please try again.')
  }
}
```

## Testing Strategy

### Unit Tests

1. **Folder Store**: State management, computed properties, actions
2. **Folder Service**: API interactions, offline handling, error scenarios
3. **Tree Building**: Hierarchy construction, sorting, depth calculation
4. **Validation**: Name validation, depth limits, circular reference prevention

### Integration Tests

1. **Folder-Note Integration**: Moving notes between folders, filtering by folder
2. **Search Integration**: Searching within folders, folder name search
3. **Tag Integration**: Combined folder + tag filtering
4. **Collaboration**: Folder behavior in shared groups

### E2E Tests

1. **Folder Management**: Create, rename, delete, move folders
2. **Note Organization**: Drag & drop notes, bulk operations
3. **Navigation**: Folder selection, breadcrumb navigation
4. **Mobile Experience**: Touch interactions, responsive behavior

### Performance Tests

1. **Large Folder Trees**: 1000+ folders, deep nesting
2. **Bulk Operations**: Moving multiple notes simultaneously
3. **Search Performance**: Searching within large folder structures
4. **Memory Usage**: Tree rendering with many expanded folders

## Performance Considerations

### Frontend Optimizations

1. **Virtual Scrolling**: For large folder lists (>100 folders)
2. **Lazy Loading**: Load subfolders on expansion
3. **Memoization**: Cache computed folder trees
4. **Debounced Updates**: Batch folder expansion state changes

### Backend Optimizations

1. **Indexed Queries**: Efficient folder hierarchy queries
2. **Batch Operations**: Bulk note moving operations
3. **Caching**: Cache folder trees per user
4. **Pagination**: Limit folder tree depth in single query

### Database Optimizations

```sql
-- Optimized folder tree query with note counts
WITH RECURSIVE folder_tree AS (
  -- Base case: root folders
  SELECT id, name, parent_id, 0 as level
  FROM folders 
  WHERE parent_id IS NULL AND user_id = ?
  
  UNION ALL
  
  -- Recursive case: child folders
  SELECT f.id, f.name, f.parent_id, ft.level + 1
  FROM folders f
  INNER JOIN folder_tree ft ON f.parent_id = ft.id
  WHERE ft.level < 5  -- Limit depth
)
SELECT 
  ft.*,
  COALESCE(note_counts.count, 0) as note_count
FROM folder_tree ft
LEFT JOIN (
  SELECT folder_id, COUNT(*) as count
  FROM notes 
  WHERE user_id = ? AND deleted_at IS NULL
  GROUP BY folder_id
) note_counts ON ft.id = note_counts.folder_id
ORDER BY ft.level, ft.name;
```

## Security Considerations

### Access Control

1. **User Isolation**: Users can only access their own folders
2. **Group Integration**: Shared notes maintain folder context per user
3. **Permission Inheritance**: Folder permissions don't override note permissions
4. **API Security**: All folder operations require authentication

### Data Validation

1. **Input Sanitization**: Folder names sanitized against XSS
2. **SQL Injection Prevention**: Parameterized queries for all operations
3. **Rate Limiting**: Prevent folder creation spam
4. **Audit Logging**: Track folder operations for security monitoring

## Migration Strategy

### Existing Data Migration

1. **Create Default Folders**: "Uncategorized" folder for each user
2. **Assign Existing Notes**: All notes without folders → "Uncategorized"
3. **Preserve Metadata**: Maintain all existing note properties
4. **Gradual Rollout**: Feature flag for controlled deployment

### Backward Compatibility

1. **API Versioning**: New folder endpoints don't break existing clients
2. **Optional Fields**: `folder_id` is nullable in notes table
3. **Graceful Degradation**: App works without folder features enabled
4. **Data Export**: Include folder structure in existing export formats
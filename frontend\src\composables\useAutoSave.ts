import { ref, watch, onUnmounted } from 'vue'
import { useNotesStore } from '../stores/notes'
import type { UpdateNoteData } from '../services/noteService'

export interface AutoSaveOptions {
  interval?: number // Auto-save interval in milliseconds
  enabled?: boolean // Whether auto-save is enabled
  onSave?: (data: UpdateNoteData) => void // Callback when auto-save triggers
  onError?: (error: Error) => void // Callback when auto-save fails
}

export function useAutoSave(noteId: string, options: AutoSaveOptions = {}) {
  const notesStore = useNotesStore()
  
  const {
    interval = notesStore.autoSaveInterval,
    enabled = notesStore.autoSaveEnabled,
    onSave,
    onError
  } = options

  const isDirty = ref(false)
  const lastSavedContent = ref<string>('')
  const pendingChanges = ref<UpdateNoteData>({})
  
  let autoSaveTimeout: number | null = null

  // Schedule auto-save
  const scheduleAutoSave = () => {
    if (!enabled || !isDirty.value) return

    // Clear existing timeout
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout)
    }

    // Set new timeout
    autoSaveTimeout = window.setTimeout(async () => {
      if (isDirty.value && Object.keys(pendingChanges.value).length > 0) {
        try {
          await notesStore.autoSaveNote(noteId, pendingChanges.value)
          
          // Mark as clean
          isDirty.value = false
          lastSavedContent.value = pendingChanges.value.content || lastSavedContent.value
          
          // Call success callback
          if (onSave) {
            onSave(pendingChanges.value)
          }
          
          // Clear pending changes
          pendingChanges.value = {}
        } catch (error) {
          if (onError && error instanceof Error) {
            onError(error)
          }
        }
      }
    }, interval)
  }

  // Update content and trigger auto-save
  const updateContent = (content: string) => {
    if (content !== lastSavedContent.value) {
      isDirty.value = true
      pendingChanges.value = { ...pendingChanges.value, content }
      scheduleAutoSave()
    }
  }

  // Update title and trigger auto-save
  const updateTitle = (title: string) => {
    isDirty.value = true
    pendingChanges.value = { ...pendingChanges.value, title }
    scheduleAutoSave()
  }

  // Update tags and trigger auto-save
  const updateTags = (tags: string[]) => {
    isDirty.value = true
    pendingChanges.value = { ...pendingChanges.value, tags }
    scheduleAutoSave()
  }

  // Force save immediately
  const forceSave = async () => {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout)
      autoSaveTimeout = null
    }

    if (isDirty.value && Object.keys(pendingChanges.value).length > 0) {
      try {
        await notesStore.updateNote(noteId, pendingChanges.value)
        
        // Mark as clean
        isDirty.value = false
        lastSavedContent.value = pendingChanges.value.content || lastSavedContent.value
        
        // Call success callback
        if (onSave) {
          onSave(pendingChanges.value)
        }
        
        // Clear pending changes
        pendingChanges.value = {}
      } catch (error) {
        if (onError && error instanceof Error) {
          onError(error)
        }
        throw error
      }
    }
  }

  // Cancel pending auto-save
  const cancelAutoSave = () => {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout)
      autoSaveTimeout = null
    }
    
    notesStore.cancelAutoSave(noteId)
    isDirty.value = false
    pendingChanges.value = {}
  }

  // Initialize with current note content
  const initialize = (initialContent: string) => {
    lastSavedContent.value = initialContent
    isDirty.value = false
    pendingChanges.value = {}
  }

  // Watch for changes in auto-save settings
  watch(
    () => notesStore.autoSaveEnabled,
    (newEnabled) => {
      if (!newEnabled && autoSaveTimeout) {
        clearTimeout(autoSaveTimeout)
        autoSaveTimeout = null
      }
    }
  )

  // Cleanup on unmount
  onUnmounted(() => {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout)
    }
  })

  return {
    isDirty,
    lastSavedContent,
    pendingChanges,
    updateContent,
    updateTitle,
    updateTags,
    forceSave,
    cancelAutoSave,
    initialize,
    scheduleAutoSave
  }
}

// Composable for handling offline functionality
export function useOfflineSync() {
  const isOnline = ref(navigator.onLine)
  const queuedOperations = ref<number>(0)

  // Update online status
  const updateOnlineStatus = () => {
    isOnline.value = navigator.onLine
  }

  // Listen for online/offline events
  window.addEventListener('online', updateOnlineStatus)
  window.addEventListener('offline', updateOnlineStatus)

  // Get queued operations count from localStorage
  const updateQueuedOperations = () => {
    try {
      const queue = localStorage.getItem('noteOfflineQueue')
      if (queue) {
        const parsed = JSON.parse(queue)
        queuedOperations.value = Array.isArray(parsed) ? parsed.length : 0
      } else {
        queuedOperations.value = 0
      }
    } catch {
      queuedOperations.value = 0
    }
  }

  // Initialize
  updateQueuedOperations()

  // Watch for changes in localStorage (from other tabs or service worker)
  window.addEventListener('storage', (e) => {
    if (e.key === 'noteOfflineQueue') {
      updateQueuedOperations()
    }
  })

  onUnmounted(() => {
    window.removeEventListener('online', updateOnlineStatus)
    window.removeEventListener('offline', updateOnlineStatus)
  })

  return {
    isOnline,
    queuedOperations,
    updateQueuedOperations
  }
}
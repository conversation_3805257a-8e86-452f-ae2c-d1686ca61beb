The Obsidian Canvas is a core plugin that provides an infinite, zoomable space to visually organize and connect your notes, media, and ideas. Its primary function is to serve as a spatial, non-linear thinking tool, much like a whiteboard or a mind map.

Core Functionalities
Infinite Spatial Canvas
The Canvas is a boundless, two-dimensional space. You can pan and zoom infinitely, allowing you to create sprawling mind maps, detailed flowcharts, or simple dashboards without ever running out of room. This freeform environment encourages visual thinking and helps you see the bigger picture of your interconnected ideas.

Versatile Cards
The fundamental building blocks of the Canvas are cards. You can create several types of cards:

Text Cards: Simple, standalone cards where you can jot down quick thoughts, labels, or paragraphs. These exist only on the canvas.

Note Cards: You can drag and drop existing notes directly from your Obsidian vault onto the canvas. This creates a card that embeds and displays the content of that note. Any changes made in the card are reflected in the original note file, and vice-versa.

Media Cards: Add images, audio files, videos, and PDFs directly onto the canvas. This is great for creating mood boards or annotating visual information.

Web Page Cards: You can embed live, interactive web pages by pasting a URL.

Connections and Relationships
You can visually link any two cards by drawing connection lines between them. This is the key to mapping relationships.

Directionality: Lines can be unidirectional (with an arrow on one end), bidirectional (arrows on both ends), or non-directional (no arrows).

Customization: You can change the color and add labels to connection lines to define the nature of the relationship (e.g., "leads to," "contradicts," "is an example of").

Grouping and Organization
To create visual clusters and organize related ideas, you can use groups.

Grouping: Select multiple cards and create a group around them, which acts like a container.

Labeling and Coloring: You can name groups and assign them different colors to create distinct thematic sections on your canvas. This helps to structure complex layouts and make them easier to navigate.

The .canvas File: A JSON Data Structure
At its core, a canvas is a plain text file with a .canvas extension, which stores its data in JSON (JavaScript Object Notation) format. This file contains two main arrays:

"nodes": Represents all the cards on the canvas.

"edges": Represents all the connection lines between the cards.

Each node object within the array has key-value pairs that define its properties, such as:

id: A unique string to identify the card.

x, y: Integer coordinates defining the card's position on the canvas grid.

width, height: The dimensions of the card.

type: Specifies the card's nature. It can be "text" for a simple text card, "file" for a card embedding a note from your vault, or "link" for a web page.

text or file: Contains the actual content or the path to the note file.

color: Stores the hex code or index for the card's color.

Here's a simplified example of a node entry in a .canvas file:

JSON

{
  "nodes": [
    {
      "id": "a8f3b1e9c2d0a4b6",
      "x": -250,
      "y": -150,
      "width": 300,
      "height": 100,
      "type": "file",
      "file": "Notes/My Project Idea.md"
    }
  ],
  "edges": []
}
What Happens When You Select a Card
When you click on a card, a sequence of events is triggered within the Obsidian application, which is built on web technologies inside an Electron shell.

Event Listening & DOM Interaction: The browser engine's event listener detects a mousedown or click event on the specific HTML or SVG element that represents the card in the Document Object Model (DOM). This element is visually rendered on the screen using CSS for styling.

State Management Update: The application's internal state manager is updated. The id of the selected card is registered as the "active" or "selected" element. This state change is crucial because it informs the entire UI what object is currently in focus.

UI Rendering: The rendering engine reacts to the state change.

Visual Feedback: The CSS classes associated with the card's element are updated to apply a visual highlight (like a blue border) and display control handles for resizing.

Contextual Menus: The application's command palette and right-click context menu now populate with actions relevant to the selected card type (e.g., "Convert to file," "Go to note," "Change color").

Data Binding: If the card is of type: 'file', a data-binding link is actively maintained between the card's content area and the actual Markdown file in your vault. When you start typing in the card, the changes are written directly to the .md file on your disk through Obsidian's file system API. This ensures perfect synchronization between the canvas view and the source note.

In short, selecting a card is not just a visual action; it's an event-driven process that updates the application's state, modifies the DOM for visual feedback, and prepares the system for context-specific commands based on the underlying JSON data of that specific node.
/* Non-theme-specific CSS Custom Properties */
/* Theme-specific colors are now defined in themes/themes.css */

:root {
  /* Spacing Scale - These don't change with themes */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;

  /* Font Sizes - These don't change with themes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Font Weights - These don't change with themes */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights - These don't change with themes */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Border Radius - These don't change with themes */
  --radius: 0.375rem;
  --radius-sm: 0.25rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-full: 9999px;

  /* Z-Index Scale - These don't change with themes */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;

  /* Transitions - These don't change with themes */
  --transition-theme: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease;
  --transition-medium: all 0.25s ease;
  --transition-normal: all 0.25s ease;
  --transition-slow: all 0.35s ease;

  /* Typography - These don't change with themes */
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* Breakpoints - These don't change with themes */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Note: All color-related variables are now defined in themes/themes.css */
/* This includes: */
/* - --color-* variables */
/* - --navbar-* variables */
/* - --sidebar-* variables */
/* - --card-* variables */
/* - --modal-* variables */
/* - --input-* variables */
/* - --button-* variables */
/* - --table-* variables */
/* - --dropdown-* variables */
/* - --notification-* variables */
/* - --shadow-* variables */

<template>
  <div class="modal is-active">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">Invite Member to {{ group.name }}</p>
        <button class="delete" @click="$emit('close')"></button>
      </header>

      <section class="modal-card-body">
        <form @submit.prevent="handleSubmit">
          <!-- Email -->
          <div class="field">
            <label class="label">Email Address *</label>
            <div class="control has-icons-left">
              <input v-model="form.email" class="input" :class="{ 'is-danger': errors.email }" type="email"
                placeholder="Enter email address" required />
              <span class="icon is-small is-left">
                <i class="fas fa-envelope"></i>
              </span>
            </div>
            <p v-if="errors.email" class="help is-danger">{{ errors.email }}</p>
          </div>

          <!-- Role -->
          <div class="field">
            <label class="label">Role *</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="form.role" required>
                  <option value="">Select a role</option>
                  <option value="viewer">Viewer</option>
                  <option value="editor">Editor</option>
                  <option value="admin">Administrator</option>
                </select>
              </div>
            </div>
            <p v-if="errors.role" class="help is-danger">{{ errors.role }}</p>
          </div>

          <!-- Role descriptions -->
          <div class="field">
            <div class="content is-small">
              <h6 class="title is-6">Role Permissions:</h6>
              <ul>
                <li><strong>Viewer:</strong> Can view group notes and members</li>
                <li><strong>Editor:</strong> Can view and edit group notes</li>
                <li><strong>Administrator:</strong> Full access including member management and settings</li>
              </ul>
            </div>
          </div>

          <!-- Group settings info -->
          <div v-if="!group.settings.allowMemberInvites && userRole !== 'admin'"
            class="notification is-warning is-light">
            <p class="is-size-7">
              <strong>Note:</strong> This group doesn't allow member invitations. Only administrators can invite new
              members.
            </p>
          </div>

          <!-- Error Display -->
          <div v-if="submitError" class="notification is-danger">
            {{ submitError }}
          </div>
        </form>
      </section>

      <footer class="modal-card-foot">
        <button class="button is-primary" :class="{ 'is-loading': loading }" :disabled="loading || !isFormValid"
          @click="handleSubmit">
          Send Invitation
        </button>
        <button class="button" @click="$emit('close')">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import { useGroupsStore } from '../../stores/groups';
import { useAuthStore } from '../../stores/auth';
import type { GroupWithMembers, InviteUserData, UserRole } from '../../types/group';

// Props
const props = defineProps<{
  group: GroupWithMembers;
}>();

// Emits
const emit = defineEmits<{
  close: [];
  invited: [];
}>();

const groupsStore = useGroupsStore();
const authStore = useAuthStore();

// Form data
const form = reactive<InviteUserData>({
  email: '',
  role: 'viewer'
});

// Form state
const loading = ref(false);
const submitError = ref<string | null>(null);
const errors = reactive({
  email: '',
  role: ''
});

// Computed
const userRole = computed((): UserRole | null => {
  if (!authStore.user) return null;
  return groupsStore.getUserRole(props.group.id, authStore.user.id) as UserRole;
});

const isFormValid = computed(() => {
  return form.email.trim().length > 0 &&
    form.role.length > 0 &&
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email);
});

// Methods
const validateForm = (): boolean => {
  // Reset errors
  errors.email = '';
  errors.role = '';

  let isValid = true;

  // Validate email
  if (!form.email.trim()) {
    errors.email = 'Email is required';
    isValid = false;
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = 'Invalid email format';
    isValid = false;
  }

  // Check if user is already a member
  const existingMember = props.group.members.find(m => m.email.toLowerCase() === form.email.toLowerCase());
  if (existingMember) {
    errors.email = 'This user is already a member of the group';
    isValid = false;
  }

  // Validate role
  if (!form.role) {
    errors.role = 'Role is required';
    isValid = false;
  } else if (!['admin', 'editor', 'viewer'].includes(form.role)) {
    errors.role = 'Invalid role selected';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    loading.value = true;
    submitError.value = null;

    await groupsStore.inviteUser(props.group.id, {
      email: form.email.trim().toLowerCase(),
      role: form.role as UserRole
    });

    emit('invited');
  } catch (error: any) {
    console.error('Error inviting user:', error);
    submitError.value = error.message || 'Failed to send invitation';
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.modal-card {
  width: 90%;
  max-width: 500px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-card-head {
  background: #007bff;
  color: white;
  border: none;
  padding: 1.5rem 2rem;
}

.modal-card-title {
  color: white;
  font-weight: 600;
  font-size: 1.25rem;
}

.delete {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.delete:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modal-card-body {
  padding: 2rem;
  background: var(--card-background);
}

.modal-card-foot {
  background: var(--card-header-background);
  border: none;
  padding: 1.5rem 2rem;
  justify-content: flex-end;
  gap: 1rem;
}

/* Form styling */
.field {
  margin-bottom: 1.5rem;
}

.label {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.control {
  position: relative;
}

.input,
.select select {
  border: 2px solid var(--color-border);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: var(--input-background);
}

.input:focus,
.select select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-alpha);
  outline: none;
}

.input.is-danger {
  border-color: #dc3545;
}

.input.is-danger:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.icon.is-left {
  color: #6c757d;
  left: 0.75rem;
}

.has-icons-left .input {
  padding-left: 2.5rem;
}

.select {
  width: 100%;
}

.select select {
  width: 100%;
  cursor: pointer;
}

.select:not(.is-multiple):not(.is-loading)::after {
  border-color: #007bff;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

/* Help text styling */
.help {
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.help.is-danger {
  color: #dc3545;
  font-weight: 500;
}

/* Content styling */
.content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.25rem;
  border: 1px solid #e9ecef;
}

.content h6.title {
  color: #2c3e50;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.content ul {
  margin-left: 1rem;
  margin-bottom: 0;
}

.content ul li {
  margin-bottom: 0.5rem;
  color: #495057;
  font-size: 0.9rem;
  line-height: 1.4;
}

.content ul li strong {
  color: #2c3e50;
  font-weight: 600;
}

/* Notification styling */
.notification {
  border-radius: 8px;
  border: 1px solid transparent;
  margin-bottom: 1rem;
}

.notification.is-warning.is-light {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.notification.is-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.notification p {
  margin-bottom: 0;
}

/* Button styling */
.button {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.button.is-primary {
  background: #007bff;
  border: 1px solid #007bff;
  color: white;
}

.button.is-primary:hover:not(:disabled) {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.button.is-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button.is-primary.is-loading {
  color: transparent;
}

.button:not(.is-primary) {
  background: var(--button-background);
  border-color: var(--button-border);
  color: var(--button-text);
}

.button:not(.is-primary):hover {
  background: #f8f9fa;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-card {
    width: 95%;
    margin: 1rem;
  }

  .modal-card-head,
  .modal-card-body,
  .modal-card-foot {
    padding: 1.25rem;
  }

  .modal-card-title {
    font-size: 1.1rem;
  }

  .input,
  .select select {
    font-size: 0.9rem;
  }

  .content {
    padding: 1rem;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .modal-card-body {
    background: #2d3748;
    color: #e2e8f0;
  }

  .modal-card-foot {
    background: #4a5568;
  }

  .label {
    color: #f7fafc;
  }

  .input,
  .select select {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }

  .input:focus,
  .select select:focus {
    border-color: #667eea;
    background: #4a5568;
  }

  .content {
    background: #4a5568;
    border-color: #718096;
  }

  .content h6.title {
    color: #f7fafc;
  }

  .content ul li {
    color: #cbd5e0;
  }

  .content ul li strong {
    color: #f7fafc;
  }

  .icon.is-left {
    color: #a0aec0;
  }

  .button:not(.is-primary) {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }

  .button:not(.is-primary):hover {
    background: #718096;
    border-color: #a0aec0;
  }
}
</style>
import { Database } from 'sqlite3';
import { 
  NoteShare, 
  ShareAccess, 
  CreateShareData, 
  UpdateShareData, 
  ShareFilters,
  NoteShareModel 
} from '../models/NoteShare';
import { DatabaseManager } from '../utils/database';

export class NoteShareRepository {
  private static get db(): Database {
    return DatabaseManager.getInstance();
  }

  // Create database tables for note sharing
  static async createTables(): Promise<void> {
    return new Promise((resolve, reject) => {
      const createSharesTable = `
        CREATE TABLE IF NOT EXISTS note_shares (
          id TEXT PRIMARY KEY,
          note_id TEXT NOT NULL,
          share_token TEXT UNIQUE NOT NULL,
          access_level TEXT NOT NULL CHECK (access_level IN ('private', 'shared', 'unlisted', 'public')),
          permissions TEXT NOT NULL, -- JSON array of permissions
          expires_at DATETIME,
          password_hash TEXT,
          allowed_ips TEXT, -- JSON array of allowed IPs
          watermark BOOLEAN DEFAULT 0,
          created_by TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          access_count INTEGER DEFAULT 0,
          last_accessed_at DATETIME,
          FOREIGN KEY (note_id) REFERENCES notes (id) ON DELETE CASCADE,
          FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE CASCADE
        )
      `;

      const createShareAccessTable = `
        CREATE TABLE IF NOT EXISTS share_access_logs (
          id TEXT PRIMARY KEY,
          share_id TEXT NOT NULL,
          ip_address TEXT NOT NULL,
          user_agent TEXT,
          accessed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          user_id TEXT,
          FOREIGN KEY (share_id) REFERENCES note_shares (id) ON DELETE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
        )
      `;

      const createIndexes = `
        CREATE INDEX IF NOT EXISTS idx_note_shares_note_id ON note_shares (note_id);
        CREATE INDEX IF NOT EXISTS idx_note_shares_token ON note_shares (share_token);
        CREATE INDEX IF NOT EXISTS idx_note_shares_created_by ON note_shares (created_by);
        CREATE INDEX IF NOT EXISTS idx_share_access_share_id ON share_access_logs (share_id);
        CREATE INDEX IF NOT EXISTS idx_share_access_ip ON share_access_logs (ip_address);
      `;

      this.db.exec(createSharesTable, (err) => {
        if (err) {
          reject(err);
          return;
        }

        this.db.exec(createShareAccessTable, (err) => {
          if (err) {
            reject(err);
            return;
          }

          this.db.exec(createIndexes, (err) => {
            if (err) {
              reject(err);
              return;
            }
            resolve();
          });
        });
      });
    });
  }

  // Create a new share
  static async create(shareData: CreateShareData): Promise<NoteShare> {
    return new Promise((resolve, reject) => {
      const id = NoteShareModel.generateId();
      const shareToken = NoteShareModel.generateShareToken();
      const passwordHash = shareData.password ? NoteShareModel.hashPassword(shareData.password) : null;
      const permissions = JSON.stringify(shareData.permissions);
      const allowedIps = shareData.allowedIps ? JSON.stringify(shareData.allowedIps) : null;
      const watermark = shareData.watermark ? 1 : 0;

      const sql = `
        INSERT INTO note_shares (
          id, note_id, share_token, access_level, permissions, expires_at,
          password_hash, allowed_ips, watermark, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        id,
        shareData.noteId,
        shareToken,
        shareData.accessLevel,
        permissions,
        shareData.expiresAt ? shareData.expiresAt.toISOString() : null,
        passwordHash,
        allowedIps,
        watermark,
        shareData.createdBy
      ];

      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
          return;
        }

        // Fetch the created share
        NoteShareRepository.findById(id)
          .then(share => {
            if (share) {
              resolve(share);
            } else {
              reject(new Error('Failed to create share'));
            }
          })
          .catch(reject);
      });
    });
  }

  // Find share by ID
  static async findById(id: string): Promise<NoteShare | null> {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM note_shares WHERE id = ?
      `;

      this.db.get(sql, [id], (err, row: any) => {
        if (err) {
          reject(err);
          return;
        }

        if (!row) {
          resolve(null);
          return;
        }

        resolve(this.mapRowToShare(row));
      });
    });
  }

  // Find share by token
  static async findByToken(shareToken: string): Promise<NoteShare | null> {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM note_shares WHERE share_token = ?
      `;

      this.db.get(sql, [shareToken], (err, row: any) => {
        if (err) {
          reject(err);
          return;
        }

        if (!row) {
          resolve(null);
          return;
        }

        resolve(this.mapRowToShare(row));
      });
    });
  }

  // Find shares by note ID
  static async findByNoteId(noteId: string): Promise<NoteShare[]> {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM note_shares 
        WHERE note_id = ? 
        ORDER BY created_at DESC
      `;

      this.db.all(sql, [noteId], (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        const shares = rows.map(row => this.mapRowToShare(row));
        resolve(shares);
      });
    });
  }

  // Find shares with filters
  static async findWithFilters(filters: ShareFilters): Promise<NoteShare[]> {
    return new Promise((resolve, reject) => {
      let sql = `SELECT * FROM note_shares WHERE 1=1`;
      const params: any[] = [];

      if (filters.noteId) {
        sql += ` AND note_id = ?`;
        params.push(filters.noteId);
      }

      if (filters.createdBy) {
        sql += ` AND created_by = ?`;
        params.push(filters.createdBy);
      }

      if (filters.accessLevel) {
        sql += ` AND access_level = ?`;
        params.push(filters.accessLevel);
      }

      if (filters.isExpired !== undefined) {
        if (filters.isExpired) {
          sql += ` AND expires_at IS NOT NULL AND expires_at < datetime('now')`;
        } else {
          sql += ` AND (expires_at IS NULL OR expires_at > datetime('now'))`;
        }
      }

      sql += ` ORDER BY created_at DESC`;

      this.db.all(sql, params, (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        const shares = rows.map(row => this.mapRowToShare(row));
        resolve(shares);
      });
    });
  }

  // Update share
  static async update(id: string, updateData: UpdateShareData): Promise<NoteShare> {
    return new Promise((resolve, reject) => {
      const updates: string[] = [];
      const params: any[] = [];

      if (updateData.accessLevel !== undefined) {
        updates.push('access_level = ?');
        params.push(updateData.accessLevel);
      }

      if (updateData.permissions !== undefined) {
        updates.push('permissions = ?');
        params.push(JSON.stringify(updateData.permissions));
      }

      if (updateData.expiresAt !== undefined) {
        updates.push('expires_at = ?');
        params.push(updateData.expiresAt ? updateData.expiresAt.toISOString() : null);
      }

      if (updateData.password !== undefined) {
        updates.push('password_hash = ?');
        params.push(updateData.password ? NoteShareModel.hashPassword(updateData.password) : null);
      }

      if (updateData.allowedIps !== undefined) {
        updates.push('allowed_ips = ?');
        params.push(updateData.allowedIps ? JSON.stringify(updateData.allowedIps) : null);
      }

      if (updateData.watermark !== undefined) {
        updates.push('watermark = ?');
        params.push(updateData.watermark ? 1 : 0);
      }

      if (updates.length === 0) {
        reject(new Error('No updates provided'));
        return;
      }

      updates.push('updated_at = CURRENT_TIMESTAMP');
      params.push(id);

      const sql = `
        UPDATE note_shares 
        SET ${updates.join(', ')} 
        WHERE id = ?
      `;

      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
          return;
        }

        if (this.changes === 0) {
          reject(new Error('Share not found'));
          return;
        }

        // Fetch the updated share
        NoteShareRepository.findById(id)
          .then(share => {
            if (share) {
              resolve(share);
            } else {
              reject(new Error('Failed to fetch updated share'));
            }
          })
          .catch(reject);
      });
    });
  }

  // Delete share
  static async delete(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const sql = `DELETE FROM note_shares WHERE id = ?`;

      this.db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
          return;
        }

        if (this.changes === 0) {
          reject(new Error('Share not found'));
          return;
        }

        resolve();
      });
    });
  }

  // Log share access
  static async logAccess(shareId: string, ipAddress: string, userAgent?: string, userId?: string): Promise<ShareAccess> {
    return new Promise((resolve, reject) => {
      const id = NoteShareModel.generateId();

      const sql = `
        INSERT INTO share_access_logs (id, share_id, ip_address, user_agent, user_id)
        VALUES (?, ?, ?, ?, ?)
      `;

      this.db.run(sql, [id, shareId, ipAddress, userAgent || null, userId || null], function(err) {
        if (err) {
          reject(err);
          return;
        }

        // Update access count and last accessed time
        const updateSql = `
          UPDATE note_shares 
          SET access_count = access_count + 1, last_accessed_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;

        NoteShareRepository.db.run(updateSql, [shareId], (updateErr) => {
          if (updateErr) {
            console.error('Failed to update share access count:', updateErr);
          }

          resolve({
            id,
            shareId,
            ipAddress,
            userAgent: userAgent || '',
            accessedAt: new Date(),
            userId
          });
        });
      });
    });
  }

  // Get access logs for a share
  static async getAccessLogs(shareId: string, limit: number = 100): Promise<ShareAccess[]> {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM share_access_logs 
        WHERE share_id = ? 
        ORDER BY accessed_at DESC 
        LIMIT ?
      `;

      this.db.all(sql, [shareId, limit], (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        const accessLogs = rows.map(row => ({
          id: row.id,
          shareId: row.share_id,
          ipAddress: row.ip_address,
          userAgent: row.user_agent || '',
          accessedAt: new Date(row.accessed_at),
          userId: row.user_id
        }));

        resolve(accessLogs);
      });
    });
  }

  // Clean up expired shares
  static async cleanupExpiredShares(): Promise<number> {
    return new Promise((resolve, reject) => {
      const sql = `
        DELETE FROM note_shares 
        WHERE expires_at IS NOT NULL AND expires_at < datetime('now')
      `;

      this.db.run(sql, [], function(err) {
        if (err) {
          reject(err);
          return;
        }

        resolve(this.changes);
      });
    });
  }

  // Helper method to map database row to NoteShare object
  private static mapRowToShare(row: any): NoteShare {
    return {
      id: row.id,
      noteId: row.note_id,
      shareToken: row.share_token,
      accessLevel: row.access_level,
      permissions: JSON.parse(row.permissions),
      expiresAt: row.expires_at ? new Date(row.expires_at) : undefined,
      passwordHash: row.password_hash,
      allowedIps: row.allowed_ips ? JSON.parse(row.allowed_ips) : undefined,
      watermark: Boolean(row.watermark),
      createdBy: row.created_by,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      accessCount: row.access_count,
      lastAccessedAt: row.last_accessed_at ? new Date(row.last_accessed_at) : undefined
    };
  }
}
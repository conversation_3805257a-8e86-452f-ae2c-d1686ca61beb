import type { 
  B<PERSON>maswatchTheme, 
  ThemeConfig, 
  ThemeIndex, 
  ThemePreview 
} from '@/types/theme'
import { ThemeLoadError } from '@/types/theme'

export class ThemeManager {
  private currentTheme: string = 'default'
  private availableThemes: Map<string, BulmaswatchTheme> = new Map()
  private systemPreference: 'light' | 'dark' = 'light'
  private loadedThemes: Set<string> = new Set()
  private themeChangeCallbacks: Array<(theme: string) => void> = []
  private themeCache: Map<string, string> = new Map() // CSS content cache
  private preloadQueue: Set<string> = new Set()
  private loadingPromises: Map<string, Promise<void>> = new Map()
  private initializationPromise: Promise<void> | null = null
  private isInitialized: boolean = false

  constructor() {
    this.detectSystemPreference()
    this.watchSystemPreference()
    this.initializeCache()
  }

  /**
   * Initialize theme cache from localStorage
   */
  private initializeCache(): void {
    try {
      const cachedThemes = localStorage.getItem('theme-cache')
      if (cachedThemes) {
        const parsed = JSON.parse(cachedThemes)
        this.themeCache = new Map(Object.entries(parsed))
      }
    } catch (error) {
      console.warn('Failed to load theme cache:', error)
    }
  }

  /**
   * Save theme cache to localStorage
   */
  private saveCache(): void {
    try {
      const cacheObject = Object.fromEntries(this.themeCache)
      localStorage.setItem('theme-cache', JSON.stringify(cacheObject))
    } catch (error) {
      console.warn('Failed to save theme cache:', error)
    }
  }

  /**
   * Clear old cache entries to prevent storage bloat
   */
  private cleanCache(): void {
    const maxCacheSize = 10 // Maximum number of cached themes
    if (this.themeCache.size > maxCacheSize) {
      const entries = Array.from(this.themeCache.entries())
      // Keep only the most recently used themes (simple FIFO)
      const toKeep = entries.slice(-maxCacheSize)
      this.themeCache.clear()
      toKeep.forEach(([key, value]) => this.themeCache.set(key, value))
      this.saveCache()
    }
  }

  /**
   * Initialize the theme manager by loading available themes
   */
  async initialize(): Promise<void> {
    // Return existing promise if already initializing
    if (this.initializationPromise) {
      return this.initializationPromise
    }

    // Return immediately if already initialized
    if (this.isInitialized) {
      return Promise.resolve()
    }

    this.initializationPromise = this.performInitialization()
    return this.initializationPromise
  }

  /**
   * Perform the actual initialization
   */
  private async performInitialization(): Promise<void> {
    try {
      await this.loadAvailableThemes()
      
      // Inject mobile header overrides on initialization
      this.injectMobileHeaderOverrides()
      
      this.isInitialized = true
      console.log('Theme manager initialized successfully')
    } catch (error) {
      console.error('Failed to initialize theme manager:', error)
      this.initializationPromise = null // Reset so it can be retried
      throw error
    }
  }

  /**
   * Load the list of available themes from the index file
   */
  private async loadAvailableThemes(): Promise<void> {
    try {
      // Use fetch for both development and production for consistency
      let themeIndex: ThemeIndex
      
      const indexPath = import.meta.env.DEV 
        ? '/src/styles/themes/bulmaswatch/index.json'
        : '/src/styles/themes/bulmaswatch/index.json'
      
      const response = await fetch(indexPath)
      
      if (!response.ok) {
        throw new Error(`Failed to load theme index: ${response.statusText}`)
      }
      themeIndex = await response.json()
      
      // Load each theme manifest
      for (const themeRef of themeIndex.themes) {
        try {
          const themeManifest = await this.loadThemeManifest(themeRef.name)
          this.availableThemes.set(themeRef.name, themeManifest)
        } catch (error) {
          console.warn(`Failed to load theme manifest for ${themeRef.name}:`, error)
        }
      }
    } catch (error) {
      throw new ThemeLoadError('index', 'network', `Failed to load theme index: ${error}`)
    }
  }

  /**
   * Load a specific theme manifest file
   */
  private async loadThemeManifest(themeName: string): Promise<BulmaswatchTheme> {
    try {
      // Use fetch for both development and production for consistency
      const manifestPath = `/src/styles/themes/bulmaswatch/${themeName}.json`
      
      const response = await fetch(manifestPath)
      
      if (!response.ok) {
        throw new Error(`Failed to load theme manifest: ${response.statusText}`)
      }
      const manifest: BulmaswatchTheme = await response.json()
      
      return manifest
    } catch (error) {
      throw new ThemeLoadError(themeName, 'network', `Failed to load theme manifest: ${error}`)
    }
  }

  /**
   * Set the current theme
   */
  async setTheme(theme: 'light' | 'dark' | 'auto' | string): Promise<void> {
    // Ensure initialization is complete before setting theme
    await this.initialize()

    let targetTheme: string

    if (theme === 'auto') {
      // Use system preference to determine theme
      this.detectSystemPreference()
      targetTheme = this.systemPreference === 'dark' ? 'darkly' : 'default'
    } else if (theme === 'light') {
      targetTheme = 'default'
    } else if (theme === 'dark') {
      targetTheme = 'darkly'
    } else {
      targetTheme = theme
    }

    if (!this.availableThemes.has(targetTheme)) {
      throw new ThemeLoadError(targetTheme, 'missing', `Theme '${targetTheme}' not found`)
    }

    try {
      await this.loadTheme(targetTheme)
      this.applyTheme(targetTheme)
      this.currentTheme = targetTheme
      this.notifyThemeChange(targetTheme)
    } catch (error) {
      throw new ThemeLoadError(targetTheme, 'network', `Failed to apply theme: ${error}`)
    }
  }

  /**
   * Load a theme's CSS file if not already loaded
   */
  async loadTheme(themeName: string, isPreloading: boolean = false): Promise<void> {
    // Ensure initialization is complete before loading theme
    await this.initialize()

    // Check if already loading to prevent duplicate requests
    if (this.loadingPromises.has(themeName)) {
      return this.loadingPromises.get(themeName)!
    }

    if (this.loadedThemes.has(themeName)) {
      return // Already loaded
    }

    const theme = this.availableThemes.get(themeName)
    if (!theme) {
      throw new ThemeLoadError(themeName, 'missing', `Theme '${themeName}' not found`)
    }

    const loadPromise = this.loadThemeInternal(themeName, theme, isPreloading)
    this.loadingPromises.set(themeName, loadPromise)

    try {
      await loadPromise
    } finally {
      this.loadingPromises.delete(themeName)
    }
  }

  /**
   * Internal theme loading with caching and optimization
   */
  private async loadThemeInternal(themeName: string, theme: BulmaswatchTheme, isPreloading: boolean = false): Promise<void> {
    try {
      // Check if theme CSS is already loaded in DOM
      const existingLink = document.querySelector(`link[data-theme="${themeName}"]`)
      if (existingLink) {
        this.loadedThemes.add(themeName)
        return
      }

      // Try to load from cache first
      const cachedCSS = this.themeCache.get(themeName)
      if (cachedCSS) {
        this.injectCachedCSS(themeName, cachedCSS)
        this.loadedThemes.add(themeName)
        return
      }

      // Load theme CSS via dynamic import for better code splitting
      if (import.meta.env.DEV) {
        // Development: Use dynamic import for hot reloading
        await this.loadThemeViaImport(themeName, theme.cssFile, isPreloading)
      } else {
        // Production: Use fetch for better caching
        const cssUrl = `/src/styles/themes/bulmaswatch/${theme.cssFile}`
        await this.loadThemeViaFetch(themeName, cssUrl)
      }

      this.loadedThemes.add(themeName)
    } catch (error) {
      throw new ThemeLoadError(themeName, 'network', `Failed to load theme CSS: ${error}`)
    }
  }

  /**
   * Load theme via fetch for production (better caching)
   */
  private async loadThemeViaFetch(themeName: string, cssUrl: string): Promise<void> {
    try {
      const response = await fetch(cssUrl)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const cssContent = await response.text()
      
      // Cache the CSS content
      this.themeCache.set(themeName, cssContent)
      this.cleanCache()
      this.saveCache()

      // Inject CSS into document
      this.injectCachedCSS(themeName, cssContent)
    } catch (error) {
      throw new Error(`Failed to fetch theme CSS: ${error}`)
    }
  }

  /**
   * Load theme via dynamic import for development
   */
  private async loadThemeViaImport(themeName: string, cssFile: string, isPreloading: boolean = false): Promise<void> {
    try {
      // In development, we need to create a link element since Vite doesn't support dynamic CSS imports
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = `/src/styles/themes/bulmaswatch/${cssFile}`
      link.setAttribute('data-theme', themeName)
      
      // Use shorter timeout for preloading to prevent blocking
      const timeoutDuration = isPreloading ? 8000 : 5000 // 8s for preloading, 5s for active switching
      
      // Wait for the CSS to load with timeout to prevent hanging
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error(`Timeout loading CSS for theme '${themeName}' (${timeoutDuration}ms)`))
        }, timeoutDuration)
        
        link.onload = () => {
          clearTimeout(timeout)
          resolve()
        }
        link.onerror = (event) => {
          clearTimeout(timeout)
          reject(new Error(`Failed to load CSS for theme '${themeName}'`))
        }
        
        document.head.appendChild(link)
      })
      
      // Mark as loaded
      this.loadedThemes.add(themeName)

      // Inject mobile header CSS overrides after theme CSS is loaded
      this.injectMobileHeaderOverrides()
    } catch (error) {
      throw new Error(`Failed to import theme CSS: ${error}`)
    }
  }

  /**
   * Load theme via link element for development
   */
  private async loadThemeViaLink(themeName: string, cssUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = cssUrl
      link.setAttribute('data-theme', themeName)
      link.disabled = true // Start disabled

      link.onload = () => {
        // Inject mobile header CSS overrides after theme CSS is loaded
        this.injectMobileHeaderOverrides()
        resolve()
      }
      link.onerror = () => reject(new Error(`Failed to load CSS link for theme '${themeName}'`))
      
      document.head.appendChild(link)
    })
  }

  /**
   * Inject cached CSS content into document
   */
  private injectCachedCSS(themeName: string, cssContent: string): void {
    // Remove existing style element for this theme
    const existingStyle = document.querySelector(`style[data-theme="${themeName}"]`)
    if (existingStyle) {
      existingStyle.remove()
    }

    // Create new style element
    const style = document.createElement('style')
    style.setAttribute('data-theme', themeName)
    style.textContent = cssContent
    style.disabled = true // Start disabled
    
    document.head.appendChild(style)

    // Inject mobile header CSS overrides after theme CSS to ensure they have higher priority
    this.injectMobileHeaderOverrides()
  }

  /**
   * Inject mobile header CSS overrides to ensure visibility on mobile devices
   * This must be injected after theme CSS to override any Bulmaswatch conflicts
   */
  private injectMobileHeaderOverrides(): void {
    // Remove existing mobile header overrides
    const existingOverrides = document.querySelector('style[data-mobile-header-overrides]')
    if (existingOverrides) {
      existingOverrides.remove()
    }

    // Create mobile header CSS overrides with highest specificity
    const mobileHeaderCSS = `
      /* Mobile Header Overrides - Ensure visibility on mobile devices */
      @media (max-width: 768px) {
        .mobile-header.mobile-only {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          z-index: 40 !important;
          background: var(--color-primary) !important;
          min-height: 3.25rem !important;
        }

        .mobile-header.mobile-only .navbar-burger {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          color: white !important;
          cursor: pointer !important;
          height: 3.25rem !important;
          position: relative !important;
          width: 3.25rem !important;
          margin-left: auto !important;
          /* Override any Bulmaswatch rules */
          display: block !important;
        }

        .mobile-header.mobile-only .navbar-burger span {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          background: white !important;
          height: 1px !important;
          left: calc(50% - 8px) !important;
          position: absolute !important;
          transform-origin: center !important;
          transition-duration: 86ms !important;
          transition-property: background-color, opacity, transform !important;
          transition-timing-function: ease-out !important;
          width: 16px !important;
        }

        .mobile-header.mobile-only .navbar-burger span:nth-child(1) {
          top: calc(50% - 6px) !important;
        }

        .mobile-header.mobile-only .navbar-burger span:nth-child(2) {
          top: calc(50% - 1px) !important;
        }

        .mobile-header.mobile-only .navbar-burger span:nth-child(3) {
          top: calc(50% + 4px) !important;
        }
      }

      /* Ensure mobile header is hidden on larger screens */
      @media (min-width: 769px) {
        .mobile-header.mobile-only {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
        }
      }

      /* Mobile-only utility class */
      .mobile-only {
        display: block !important;
      }

      @media (min-width: 769px) {
        .mobile-only {
          display: none !important;
        }
      }
    `

    // Create and inject the mobile header overrides
    const style = document.createElement('style')
    style.setAttribute('data-mobile-header-overrides', 'true')
    style.textContent = mobileHeaderCSS
    
    // Insert after the last theme style to ensure it has highest priority
    const lastThemeStyle = document.querySelector('style[data-theme]:last-of-type')
    if (lastThemeStyle) {
      lastThemeStyle.after(style)
    } else {
      document.head.appendChild(style)
    }
  }

  /**
   * Apply a loaded theme by enabling its CSS and disabling others
   */
  applyTheme(themeName: string): void {
    // Disable all theme stylesheets and style elements
    const themeElements = document.querySelectorAll('link[data-theme], style[data-theme]')
    themeElements.forEach(element => {
      if (element.tagName === 'LINK') {
        ;(element as HTMLLinkElement).disabled = true
      } else if (element.tagName === 'STYLE') {
        ;(element as HTMLStyleElement).disabled = true
      }
    })

    // Enable the target theme (both link and style elements)
    const targetElements = document.querySelectorAll(`[data-theme="${themeName}"]`)
    targetElements.forEach(element => {
      if (element.tagName === 'LINK') {
        ;(element as HTMLLinkElement).disabled = false
      } else if (element.tagName === 'STYLE') {
        ;(element as HTMLStyleElement).disabled = false
      }
    })

    // Update body class for theme-specific styling
    document.body.className = document.body.className.replace(/theme-\w+/g, '')
    document.body.classList.add(`theme-${themeName}`)

    // Set data attribute for CSS targeting
    document.documentElement.setAttribute('data-theme', themeName)

    // Trigger CSS custom property updates
    this.updateThemeCustomProperties(themeName)

    // Ensure mobile header overrides are injected after theme application
    this.injectMobileHeaderOverrides()
  }

  /**
   * Update CSS custom properties for the current theme
   */
  private updateThemeCustomProperties(themeName: string): void {
    const theme = this.availableThemes.get(themeName)
    if (!theme || !theme.colors) {
      return
    }

    const root = document.documentElement
    
    // Apply theme colors as CSS custom properties
    Object.entries(theme.colors).forEach(([property, value]) => {
      root.style.setProperty(`--theme-${property}`, value)
    })

    // Set theme mode for CSS targeting
    root.style.setProperty('--theme-mode', theme.isDark ? 'dark' : 'light')
  }

  /**
   * Get the current theme name
   */
  getCurrentTheme(): string {
    return this.currentTheme
  }

  /**
   * Get all available themes
   */
  getAvailableThemes(): BulmaswatchTheme[] {
    return Array.from(this.availableThemes.values())
  }

  /**
   * Get a specific theme by name
   */
  getTheme(themeName: string): BulmaswatchTheme | undefined {
    return this.availableThemes.get(themeName)
  }

  /**
   * Get theme preview colors
   */
  getThemePreview(themeName: string): ThemePreview | null {
    const theme = this.availableThemes.get(themeName)
    return theme ? theme.preview : null
  }

  /**
   * Detect system color scheme preference
   */
  private detectSystemPreference(): void {
    if (typeof window !== 'undefined' && window.matchMedia) {
      this.systemPreference = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
  }

  /**
   * Watch for system preference changes
   */
  private watchSystemPreference(): void {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', (e) => {
        const oldPreference = this.systemPreference
        this.systemPreference = e.matches ? 'dark' : 'light'
        
        // Notify about system preference change
        console.log(`System preference changed from ${oldPreference} to ${this.systemPreference}`)
      })
    }
  }

  /**
   * Get system preference
   */
  getSystemPreference(): 'light' | 'dark' {
    return this.systemPreference
  }

  /**
   * Add a callback for theme changes
   */
  onThemeChange(callback: (theme: string) => void): void {
    this.themeChangeCallbacks.push(callback)
  }

  /**
   * Remove a theme change callback
   */
  offThemeChange(callback: (theme: string) => void): void {
    const index = this.themeChangeCallbacks.indexOf(callback)
    if (index > -1) {
      this.themeChangeCallbacks.splice(index, 1)
    }
  }

  /**
   * Notify all callbacks of theme change
   */
  private notifyThemeChange(theme: string): void {
    this.themeChangeCallbacks.forEach(callback => {
      try {
        callback(theme)
      } catch (error) {
        console.error('Error in theme change callback:', error)
      }
    })
  }

  /**
   * Preload a theme for faster switching
   */
  async preloadTheme(themeName: string): Promise<void> {
    // Ensure initialization is complete before preloading
    await this.initialize()

    if (this.loadedThemes.has(themeName)) {
      return // Already loaded
    }

    if (this.preloadQueue.has(themeName)) {
      return // Already in preload queue
    }

    this.preloadQueue.add(themeName)
    
    try {
      await this.loadTheme(themeName, true) // Pass true for isPreloading
    } catch (error) {
      console.warn(`Failed to preload theme '${themeName}':`, error)
      // Don't throw error for preloading failures - they're non-critical
    } finally {
      this.preloadQueue.delete(themeName)
    }
  }

  /**
   * Preload multiple themes with priority
   */
  async preloadThemes(themeNames: string[], priority: 'high' | 'low' = 'low'): Promise<void> {
    const preloadPromises = themeNames.map(async (themeName, index) => {
      // Add delay for low priority preloading to avoid blocking
      if (priority === 'low' && index > 0) {
        await new Promise(resolve => setTimeout(resolve, 100 * index))
      }
      
      return this.preloadTheme(themeName).catch(error => {
        console.warn(`Failed to preload theme ${themeName}:`, error)
      })
    })

    await Promise.allSettled(preloadPromises)
  }

  /**
   * Preload user's preferred themes based on usage patterns
   */
  async preloadUserPreferences(): Promise<void> {
    const commonThemes = ['default', 'darkly'] // Most commonly used themes
    const userTheme = localStorage.getItem('theme-name')
    const systemTheme = this.systemPreference === 'dark' ? 'darkly' : 'default'

    const themesToPreload = new Set([
      ...commonThemes,
      userTheme,
      systemTheme
    ].filter(Boolean) as string[])

    await this.preloadThemes(Array.from(themesToPreload), 'high')
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): { size: number; themes: string[]; hitRate?: number } {
    return {
      size: this.themeCache.size,
      themes: Array.from(this.themeCache.keys()),
      // Could add hit rate tracking in the future
    }
  }

  /**
   * Clear theme cache
   */
  clearCache(): void {
    this.themeCache.clear()
    localStorage.removeItem('theme-cache')
  }

  /**
   * Optimize theme loading performance
   */
  async optimizePerformance(): Promise<void> {
    // Preload user preferences
    await this.preloadUserPreferences()

    // Clean up unused theme elements
    this.cleanupUnusedThemes()

    // Optimize CSS custom properties
    this.optimizeCustomProperties()
  }

  /**
   * Clean up unused theme elements from DOM
   */
  private cleanupUnusedThemes(): void {
    const allThemeElements = document.querySelectorAll('link[data-theme], style[data-theme]')
    const loadedThemeNames = Array.from(this.loadedThemes)

    allThemeElements.forEach(element => {
      const themeName = element.getAttribute('data-theme')
      if (themeName && !loadedThemeNames.includes(themeName)) {
        element.remove()
      }
    })
  }

  /**
   * Optimize CSS custom properties usage
   */
  private optimizeCustomProperties(): void {
    // Remove unused CSS custom properties from root
    const root = document.documentElement
    const computedStyle = getComputedStyle(root)
    
    // Get all custom properties
    const customProps = Array.from(computedStyle).filter(prop => prop.startsWith('--theme-'))
    
    // Remove properties that are not from current theme
    const currentTheme = this.availableThemes.get(this.currentTheme)
    if (currentTheme) {
      customProps.forEach(prop => {
        const colorKey = prop.replace('--theme-', '')
        if (!currentTheme.colors || !(colorKey in currentTheme.colors)) {
          root.style.removeProperty(prop)
        }
      })
    }
  }

  /**
   * Validate theme configuration
   */
  validateTheme(theme: BulmaswatchTheme): boolean {
    const requiredFields = ['name', 'displayName', 'cssFile', 'isDark', 'category', 'preview', 'colors']
    return requiredFields.every(field => field in theme)
  }

  /**
   * Fallback to default theme in case of errors
   */
  async fallbackToDefaultTheme(): Promise<void> {
    try {
      await this.setTheme('default')
    } catch (error) {
      console.error('Failed to fallback to default theme:', error)
      // Last resort: remove all theme classes
      document.body.className = document.body.className.replace(/theme-\w+/g, '')
      document.documentElement.removeAttribute('data-theme')
    }
  }

  /**
   * Retry theme loading with exponential backoff
   */
  async retryThemeLoad(themeName: string, maxRetries: number = 3): Promise<void> {
    let retries = 0
    while (retries < maxRetries) {
      try {
        await this.loadTheme(themeName)
        return
      } catch (error) {
        retries++
        if (retries >= maxRetries) {
          throw error
        }
        // Exponential backoff: 1s, 2s, 4s
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000))
      }
    }
  }
}
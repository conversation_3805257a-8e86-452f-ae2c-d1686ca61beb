<template>
  <div class="admin-notifications">
    <!-- Notification Bell -->
    <div class="dropdown" :class="{ 'is-active': isOpen }">
      <div class="dropdown-trigger">
        <button 
          class="button is-white" 
          @click="toggleDropdown"
          aria-haspopup="true" 
          aria-controls="notifications-menu"
        >
          <span class="icon">
            <i class="fas fa-bell" :class="{ 'has-text-danger': hasUnread }"></i>
          </span>
          <span v-if="unreadCount > 0" class="tag is-danger is-small notification-badge">
            {{ unreadCount > 99 ? '99+' : unreadCount }}
          </span>
        </button>
      </div>
      <div class="dropdown-menu" id="notifications-menu" role="menu">
        <div class="dropdown-content">
          <div class="dropdown-item">
            <div class="level">
              <div class="level-left">
                <div class="level-item">
                  <h6 class="title is-6">Notifications</h6>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <button 
                    v-if="hasUnread"
                    class="button is-small is-text"
                    @click="markAllAsRead"
                    :class="{ 'is-loading': isMarkingAllRead }"
                    :disabled="isMarkingAllRead"
                  >
                    Mark all read
                  </button>
                </div>
              </div>
            </div>
          </div>
          <hr class="dropdown-divider">
          
          <!-- Notifications List -->
          <div v-if="notifications.length === 0" class="dropdown-item">
            <div class="has-text-centered py-4">
              <span class="icon is-large has-text-grey-light">
                <i class="fas fa-bell-slash fa-2x"></i>
              </span>
              <p class="has-text-grey">No notifications</p>
            </div>
          </div>
          
          <div v-else class="notifications-list">
            <a 
              v-for="notification in notifications.slice(0, 10)" 
              :key="notification.id"
              class="dropdown-item notification-item"
              :class="{ 'is-unread': !notification.read }"
              @click="handleNotificationClick(notification)"
            >
              <div class="media">
                <div class="media-left">
                  <span class="icon" :class="{
                    'has-text-danger': notification.type === 'critical',
                    'has-text-warning': notification.type === 'warning',
                    'has-text-info': notification.type === 'info',
                    'has-text-success': notification.type === 'success'
                  }">
                    <i class="fas" :class="{
                      'fa-exclamation-triangle': notification.type === 'critical',
                      'fa-exclamation-circle': notification.type === 'warning',
                      'fa-info-circle': notification.type === 'info',
                      'fa-check-circle': notification.type === 'success',
                      'fa-flag': notification.category === 'content_report',
                      'fa-user': notification.category === 'user_action',
                      'fa-cogs': notification.category === 'system'
                    }"></i>
                  </span>
                </div>
                <div class="media-content">
                  <div class="content">
                    <p class="is-size-7">
                      <strong>{{ notification.title }}</strong>
                      <br>
                      {{ notification.message }}
                      <br>
                      <small class="has-text-grey">{{ formatRelativeTime(notification.createdAt) }}</small>
                    </p>
                  </div>
                </div>
                <div v-if="!notification.read" class="media-right">
                  <span class="tag is-primary is-small">New</span>
                </div>
              </div>
            </a>
          </div>
          
          <hr v-if="notifications.length > 0" class="dropdown-divider">
          <div class="dropdown-item">
            <div class="has-text-centered">
              <router-link to="/admin/notifications" class="button is-small is-fullwidth">
                View All Notifications
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '../../stores/notifications'
import { storeToRefs } from 'pinia'

const router = useRouter()
const notificationStore = useNotificationStore()

// Get reactive state from store
const { notifications, unreadCount, hasUnread, isLoading } = storeToRefs(notificationStore)

// Local state
const isOpen = ref(false)
const isMarkingAllRead = ref(false)

// Methods
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
  if (isOpen.value) {
    loadNotifications()
  }
}

const loadNotifications = async () => {
  await notificationStore.loadNotifications({ limit: 10 })
}

const markAllAsRead = async () => {
  if (isMarkingAllRead.value) return
  
  isMarkingAllRead.value = true
  try {
    await notificationStore.markAllAsRead()
  } finally {
    isMarkingAllRead.value = false
  }
}

const handleNotificationClick = async (notification: any) => {
  // Mark as read if not already
  if (!notification.read) {
    await notificationStore.markAsRead(notification.id)
  }
  
  // Navigate to action URL if provided
  if (notification.actionUrl) {
    router.push(notification.actionUrl)
  }
  
  isOpen.value = false
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMins / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`
  
  return date.toLocaleDateString()
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.admin-notifications')) {
    isOpen.value = false
  }
}

// Auto-refresh interval
let refreshInterval: ReturnType<typeof setInterval> | null = null

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  loadNotifications()
  
  // Set up periodic refresh for notifications
  refreshInterval = setInterval(loadNotifications, 30000) // Refresh every 30 seconds
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  if (refreshInterval) {
    clearInterval(refreshInterval as any)
  }
})
</script>

<style scoped>
.admin-notifications {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  font-size: 0.6rem;
  line-height: 18px;
  padding: 0 4px;
}

.dropdown-menu {
  min-width: 320px;
  max-width: 400px;
  right: 0;
  left: auto;
}

.notifications-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  padding: 0.75rem 1rem;
  border-left: 3px solid transparent;
  transition: all 0.2s;
}

.notification-item:hover {
  background-color: #f5f5f5;
}

.notification-item.is-unread {
  background-color: #f0f8ff;
  border-left-color: #3273dc;
}

.notification-item .media {
  align-items: flex-start;
}

.notification-item .media-content {
  overflow: visible;
}

.notification-item .content p {
  margin-bottom: 0;
}

.dropdown-content {
  padding: 0;
}

.dropdown-item {
  padding: 0.75rem 1rem;
}

.dropdown-divider {
  margin: 0;
}
</style>
# Performance Monitoring System

This document describes the comprehensive performance monitoring and regression detection system implemented for the application.

## Overview

The performance monitoring system provides:

- **Real-time Core Web Vitals tracking** with console logging
- **Performance budget enforcement** with configurable thresholds
- **Automated regression detection** comparing against baselines
- **CI/CD integration** with GitHub Actions
- **Comprehensive reporting** and alerting

## Core Components

### 1. Core Web Vitals Tracking (`usePerformance.ts`)

The `usePerformance` composable provides real-time monitoring of:

- **First Contentful Paint (FCP)**: Target < 1000ms
- **Largest Contentful Paint (LCP)**: Target < 1500ms
- **Time to Interactive (TTI)**: Target < 2000ms
- **Cumulative Layout Shift (CLS)**: Target < 0.05
- **First Input Delay (FID)**: Target < 100ms
- **App Initialization Time**: Target < 600ms

#### Usage

```typescript
import { usePerformance } from '@/composables/usePerformance'

const performance = usePerformance({
  initTime: 600,  // Custom initialization time budget
  fcp: 1000,      // Custom FCP budget
  // ... other budgets
})

// Enable real-time monitoring with console logging
performance.enableRealTimeMonitoring()

// Mark initialization phases
performance.markInitStart()
performance.markStoreInitComplete()
performance.markDOMReady()
performance.markInitEnd()

// Generate comprehensive report
const report = performance.collectAndReportMetrics()
```

### 2. Performance Budget Enforcement

#### Bundle Size Budgets (`check-bundle-size.js`)

- **Initial Bundle**: 300KB (reduced from 500KB)
- **Total Application**: 1.5MB (reduced from 2MB)
- **Route Bundles**: 100KB each
- **Individual Chunks**: Specific limits per chunk type

#### Performance Time Budgets (`performance-budget-check.js`)

- **App Initialization**: 600ms target, 800ms warning, 1000ms critical
- **Store Initialization**: 300ms
- **DOM Ready**: 200ms
- **Core Web Vitals**: As specified above

### 3. Regression Detection (`performance-regression-check.js`)

Automatically detects performance regressions by comparing current metrics against stored baselines:

#### Regression Thresholds

- **Initialization Time**: 20% increase triggers alert
- **Store Init Time**: 25% increase triggers alert
- **Core Web Vitals**: 15-50% increase (varies by metric)
- **Bundle Sizes**: 10% total, 15% individual file increase

#### Baseline Management

```bash
# Update baselines with current metrics
npm run perf-baseline

# Check for regressions without failing
npm run perf-regression -- --no-fail

# Full regression check (fails on regressions)
npm run perf-regression
```

## Available Scripts

### Local Development

```bash
# Run comprehensive performance test suite
npm run perf-test

# Run performance tests with unit tests
npm run perf-test-full

# Run only bundle size check
npm run perf-budget

# Run only performance budget check
npm run perf-check

# Run only regression detection
npm run perf-regression

# Update performance baselines
npm run perf-baseline

# Run all performance checks
npm run perf-full
```

### Script Options

```bash
# Performance test suite options
npm run perf-test -- --help

# Common options:
npm run perf-test -- --force-build        # Force rebuild
npm run perf-test -- --update-baseline    # Update baselines
npm run perf-test -- --run-tests          # Include unit tests
npm run perf-test -- --no-fail-regression # Don't fail on regressions
```

## CI/CD Integration

### GitHub Actions Workflow

The `.github/workflows/performance-check.yml` workflow:

1. **Builds** the application
2. **Checks bundle sizes** against budgets
3. **Detects regressions** against cached baselines
4. **Runs Lighthouse audits** for Core Web Vitals
5. **Comments on PRs** with performance reports
6. **Updates baselines** on main branch merges
7. **Fails builds** on critical regressions

### Lighthouse Integration

Lighthouse audits run automatically on PRs with:

- **Performance score**: Minimum 80%
- **Core Web Vitals**: Enforced thresholds
- **Bundle size limits**: Resource summary checks
- **Best practices**: Accessibility, SEO checks

## Performance Budgets

### Current Targets (Optimized)

| Metric | Target | Warning | Critical |
|--------|--------|---------|----------|
| App Initialization | 600ms | 800ms | 1000ms |
| Store Initialization | 300ms | - | - |
| DOM Ready | 200ms | - | - |
| First Contentful Paint | 1000ms | - | - |
| Largest Contentful Paint | 1500ms | - | - |
| Time to Interactive | 2000ms | - | - |
| Cumulative Layout Shift | 0.05 | - | - |
| First Input Delay | 100ms | - | - |

### Bundle Size Targets

| Bundle Type | Target | Notes |
|-------------|--------|-------|
| Initial Bundle | 300KB | Main application entry |
| Total Application | 1.5MB | All chunks combined |
| Route Bundles | 100KB | Per route chunk |
| Vue Core | 100KB | Framework essentials |
| UI Components | 400KB | Bulma + icons |
| Editor | 200KB | TipTap editor |
| Utilities | 200KB | Lodash, date-fns, etc. |

## Monitoring and Alerting

### Real-time Console Logging

When `enableRealTimeMonitoring()` is called, the system logs:

```
🚀 Performance: First Contentful Paint (FCP) = 850.23ms (85.0% of 1000ms budget) ✅ PASS
🚀 Performance: Largest Contentful Paint (LCP) = 1200.45ms (80.0% of 1500ms budget) ✅ PASS
⚠️  Performance Budget Violation: Time to Interactive exceeded budget by 150.30ms
```

### Performance Reports

Comprehensive reports include:

- **Core Web Vitals** with pass/fail status
- **Initialization metrics** breakdown
- **Budget violations** with recommendations
- **Regression analysis** with percentage changes
- **Bundle size analysis** with file-by-file breakdown

### Regression Alerts

When regressions are detected:

```
🚨 PERFORMANCE REGRESSION ALERT

2 regression(s) detected:

1. App Initialization
   Current: 750ms
   Baseline: 580ms
   Change: +29.3% (threshold: 20%)

2. Bundle: assets/index-abc123.js
   Current: 350KB
   Baseline: 280KB
   Change: +25.0% (threshold: 15%)
```

## Best Practices

### 1. Baseline Management

- **Update baselines** after intentional performance changes
- **Review regressions** before updating baselines
- **Keep baselines** in version control for team visibility

### 2. Performance Testing

- **Run performance tests** before committing changes
- **Monitor trends** over time, not just absolute values
- **Test on different devices** and network conditions

### 3. Optimization Workflow

1. **Measure** current performance with `npm run perf-test`
2. **Identify** bottlenecks using browser dev tools
3. **Optimize** code, bundles, or loading strategies
4. **Verify** improvements with performance tests
5. **Update baselines** if targets are met consistently

### 4. CI/CD Integration

- **Performance checks** run on every PR
- **Baselines update** automatically on main branch
- **Critical regressions** fail the build
- **Performance reports** posted to PRs automatically

## Troubleshooting

### Common Issues

#### No Performance Metrics Found

```bash
# Ensure the app runs to generate metrics
npm run dev
# Navigate through the app, then check localStorage
# Or run the app in production mode
npm run build && npm run preview
```

#### Baseline Not Found

```bash
# Create initial baseline
npm run perf-baseline
```

#### Regression False Positives

```bash
# Check if changes are intentional
npm run perf-regression -- --no-fail
# If changes are expected, update baseline
npm run perf-baseline
```

#### Bundle Size Violations

```bash
# Analyze bundle composition
npm run bundle-analyze
# Check for unused dependencies
npm run build -- --analyze
```

### Performance Debugging

1. **Use browser dev tools** Performance tab
2. **Enable performance monitoring** in development
3. **Check network tab** for resource loading
4. **Use Lighthouse** for comprehensive audits
5. **Profile with React/Vue dev tools** for component performance

## Future Enhancements

- **Real User Monitoring (RUM)** integration
- **Performance dashboards** with historical data
- **Automated performance optimization** suggestions
- **A/B testing** for performance improvements
- **Custom performance metrics** for business logic
- **Performance correlation** with user engagement metrics

## Resources

- [Web Vitals](https://web.dev/vitals/)
- [Lighthouse Performance Auditing](https://developers.google.com/web/tools/lighthouse)
- [Performance Budget](https://web.dev/performance-budgets-101/)
- [Bundle Analysis](https://webpack.js.org/guides/code-splitting/)
import { getDatabase } from '../config/database';
import { 
  Group, 
  GroupMember, 
  GroupWithMembers, 
  CreateGroupData, 
  UpdateGroupData,
  GroupInvitation,
  CreateInvitationData,
  GroupModel 
} from '../models/Group';

export class GroupRepository {

  static async create(groupData: CreateGroupData): Promise<Group> {
    const db = getDatabase();
    const id = GroupModel.generateId();
    const settings = { ...GroupModel.getDefaultSettings(), ...groupData.settings };
    const now = new Date();

    const query = `
      INSERT INTO groups (id, owner_id, name, description, settings, created_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    await new Promise<void>((resolve, reject) => {
      db.run(
        query,
        [id, groupData.ownerId, groupData.name, groupData.description || null, JSON.stringify(settings), now.toISOString()],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    // Add the owner as an admin member
    await this.addMember(id, groupData.ownerId, 'admin');

    return {
      id,
      ownerId: groupData.ownerId,
      name: groupData.name,
      description: groupData.description,
      settings,
      createdAt: now
    };
  }

  static async findById(id: string): Promise<Group | null> {
    const db = getDatabase();
    const query = `
      SELECT id, owner_id, name, description, settings, created_at
      FROM groups
      WHERE id = ?
    `;

    return new Promise((resolve, reject) => {
      db.get(query, [id], (err, row: any) => {
        if (err) {
          reject(err);
          return;
        }

        if (!row) {
          resolve(null);
          return;
        }

        resolve({
          id: row.id,
          ownerId: row.owner_id,
          name: row.name,
          description: row.description,
          settings: JSON.parse(row.settings || '{}'),
          createdAt: new Date(row.created_at)
        });
      });
    });
  }

  static async findByIdWithMembers(id: string): Promise<GroupWithMembers | null> {
    const group = await this.findById(id);
    if (!group) return null;

    const members = await this.getGroupMembers(id);
    
    return {
      ...group,
      members,
      memberCount: members.length
    };
  }

  static async findByUserId(userId: string): Promise<GroupWithMembers[]> {
    const db = getDatabase();
    const query = `
      SELECT g.id, g.owner_id, g.name, g.description, g.settings, g.created_at,
             gm.role, gm.joined_at
      FROM groups g
      INNER JOIN group_members gm ON g.id = gm.group_id
      WHERE gm.user_id = ?
      ORDER BY g.name ASC
    `;

    return new Promise((resolve, reject) => {
      db.all(query, [userId], async (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        const groupsWithMembers: GroupWithMembers[] = [];

        for (const row of rows) {
          const members = await this.getGroupMembers(row.id);
          
          groupsWithMembers.push({
            id: row.id,
            ownerId: row.owner_id,
            name: row.name,
            description: row.description,
            settings: JSON.parse(row.settings || '{}'),
            createdAt: new Date(row.created_at),
            members,
            memberCount: members.length
          });
        }

        resolve(groupsWithMembers);
      });
    });
  }

  static async update(id: string, updateData: UpdateGroupData): Promise<Group | null> {
    const db = getDatabase();
    const existingGroup = await this.findById(id);
    if (!existingGroup) return null;

    const updates: string[] = [];
    const values: any[] = [];

    if (updateData.name !== undefined) {
      updates.push('name = ?');
      values.push(updateData.name);
    }

    if (updateData.description !== undefined) {
      updates.push('description = ?');
      values.push(updateData.description);
    }

    if (updateData.settings !== undefined) {
      const newSettings = { ...existingGroup.settings, ...updateData.settings };
      updates.push('settings = ?');
      values.push(JSON.stringify(newSettings));
    }

    if (updates.length === 0) {
      return existingGroup;
    }

    values.push(id);

    const query = `
      UPDATE groups 
      SET ${updates.join(', ')}
      WHERE id = ?
    `;

    await new Promise<void>((resolve, reject) => {
      db.run(query, values, function(err) {
        if (err) reject(err);
        else resolve();
      });
    });

    return this.findById(id);
  }

  static async delete(id: string): Promise<boolean> {
    const db = getDatabase();
    const query = 'DELETE FROM groups WHERE id = ?';

    return new Promise((resolve, reject) => {
      db.run(query, [id], function(err) {
        if (err) {
          reject(err);
          return;
        }
        resolve(this.changes > 0);
      });
    });
  }

  static async addMember(groupId: string, userId: string, role: 'admin' | 'editor' | 'viewer'): Promise<void> {
    const db = getDatabase();
    const query = `
      INSERT OR REPLACE INTO group_members (group_id, user_id, role, joined_at)
      VALUES (?, ?, ?, ?)
    `;

    await new Promise<void>((resolve, reject) => {
      db.run(query, [groupId, userId, role, new Date().toISOString()], function(err) {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  static async removeMember(groupId: string, userId: string): Promise<boolean> {
    const db = getDatabase();
    const query = 'DELETE FROM group_members WHERE group_id = ? AND user_id = ?';

    return new Promise((resolve, reject) => {
      db.run(query, [groupId, userId], function(err) {
        if (err) {
          reject(err);
          return;
        }
        resolve(this.changes > 0);
      });
    });
  }

  static async updateMemberRole(groupId: string, userId: string, role: 'admin' | 'editor' | 'viewer'): Promise<boolean> {
    const db = getDatabase();
    const query = `
      UPDATE group_members 
      SET role = ?
      WHERE group_id = ? AND user_id = ?
    `;

    return new Promise((resolve, reject) => {
      db.run(query, [role, groupId, userId], function(err) {
        if (err) {
          reject(err);
          return;
        }
        resolve(this.changes > 0);
      });
    });
  }

  static async getGroupMembers(groupId: string): Promise<(GroupMember & { displayName: string; email: string; avatarUrl?: string })[]> {
    const db = getDatabase();
    const query = `
      SELECT gm.group_id, gm.user_id, gm.role, gm.joined_at,
             u.display_name, u.email, u.avatar_url
      FROM group_members gm
      INNER JOIN users u ON gm.user_id = u.id
      WHERE gm.group_id = ?
      ORDER BY gm.role ASC, u.display_name ASC
    `;

    return new Promise((resolve, reject) => {
      db.all(query, [groupId], (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        const members = rows.map(row => ({
          groupId: row.group_id,
          userId: row.user_id,
          role: row.role,
          joinedAt: new Date(row.joined_at),
          displayName: row.display_name,
          email: row.email,
          avatarUrl: row.avatar_url
        }));

        resolve(members);
      });
    });
  }

  static async getUserRole(groupId: string, userId: string): Promise<'admin' | 'editor' | 'viewer' | null> {
    const db = getDatabase();
    const query = `
      SELECT role
      FROM group_members
      WHERE group_id = ? AND user_id = ?
    `;

    return new Promise((resolve, reject) => {
      db.get(query, [groupId, userId], (err, row: any) => {
        if (err) {
          reject(err);
          return;
        }

        resolve(row ? row.role : null);
      });
    });
  }

  static async isMember(groupId: string, userId: string): Promise<boolean> {
    const role = await this.getUserRole(groupId, userId);
    return role !== null;
  }

  // Group invitation methods
  static async createInvitation(invitationData: CreateInvitationData): Promise<GroupInvitation> {
    const db = getDatabase();
    const id = GroupModel.generateId();
    const token = GroupModel.generateInvitationToken();
    const expiresAt = GroupModel.getInvitationExpiryDate();
    const now = new Date();

    const query = `
      INSERT INTO group_invitations (id, group_id, invited_by, invited_email, role, token, expires_at, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await new Promise<void>((resolve, reject) => {
      db.run(
        query,
        [id, invitationData.groupId, invitationData.invitedBy, invitationData.invitedEmail, 
         invitationData.role, token, expiresAt.toISOString(), now.toISOString()],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    return {
      id,
      groupId: invitationData.groupId,
      invitedBy: invitationData.invitedBy,
      invitedEmail: invitationData.invitedEmail,
      role: invitationData.role,
      token,
      expiresAt,
      createdAt: now
    };
  }

  static async findInvitationByToken(token: string): Promise<GroupInvitation | null> {
    const db = getDatabase();
    const query = `
      SELECT id, group_id, invited_by, invited_email, role, token, expires_at, created_at
      FROM group_invitations
      WHERE token = ? AND expires_at > datetime('now')
    `;

    return new Promise((resolve, reject) => {
      db.get(query, [token], (err, row: any) => {
        if (err) {
          reject(err);
          return;
        }

        if (!row) {
          resolve(null);
          return;
        }

        resolve({
          id: row.id,
          groupId: row.group_id,
          invitedBy: row.invited_by,
          invitedEmail: row.invited_email,
          role: row.role,
          token: row.token,
          expiresAt: new Date(row.expires_at),
          createdAt: new Date(row.created_at)
        });
      });
    });
  }

  static async deleteInvitation(id: string): Promise<boolean> {
    const db = getDatabase();
    const query = 'DELETE FROM group_invitations WHERE id = ?';

    return new Promise((resolve, reject) => {
      db.run(query, [id], function(err) {
        if (err) {
          reject(err);
          return;
        }
        resolve(this.changes > 0);
      });
    });
  }

  static async getGroupInvitations(groupId: string): Promise<GroupInvitation[]> {
    const db = getDatabase();
    const query = `
      SELECT id, group_id, invited_by, invited_email, role, token, expires_at, created_at
      FROM group_invitations
      WHERE group_id = ? AND expires_at > datetime('now')
      ORDER BY created_at DESC
    `;

    return new Promise((resolve, reject) => {
      db.all(query, [groupId], (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        const invitations = rows.map(row => ({
          id: row.id,
          groupId: row.group_id,
          invitedBy: row.invited_by,
          invitedEmail: row.invited_email,
          role: row.role,
          token: row.token,
          expiresAt: new Date(row.expires_at),
          createdAt: new Date(row.created_at)
        }));

        resolve(invitations);
      });
    });
  }
}
// Database query optimization utilities

import { getDatabase } from '../config/database';

export interface QueryPerformanceMetrics {
  query: string;
  executionTime: number;
  rowsAffected: number;
  timestamp: Date;
}

export class QueryOptimizer {
  private static metrics: QueryPerformanceMetrics[] = [];
  private static readonly MAX_METRICS = 1000;

  /**
   * Execute a query with performance monitoring
   */
  static async executeWithMetrics<T>(
    query: string,
    params: any[] = [],
    operation: 'get' | 'all' | 'run' = 'all'
  ): Promise<T> {
    const db = getDatabase();
    const startTime = performance.now();
    
    try {
      let result: any;
      
      switch (operation) {
        case 'get':
          result = await new Promise((resolve, reject) => {
            db.get(query, params, (err, row) => {
              if (err) reject(err);
              else resolve(row);
            });
          });
          break;
        case 'run':
          result = await new Promise((resolve, reject) => {
            db.run(query, params, function(err) {
              if (err) reject(err);
              else resolve({ changes: this.changes, lastID: this.lastID });
            });
          });
          break;
        case 'all':
        default:
          result = await new Promise((resolve, reject) => {
            db.all(query, params, (err, rows) => {
              if (err) reject(err);
              else resolve(rows);
            });
          });
          break;
      }
      
      const executionTime = performance.now() - startTime;
      
      // Record metrics
      this.recordMetrics({
        query: this.sanitizeQuery(query),
        executionTime,
        rowsAffected: Array.isArray(result) ? result.length : (result?.changes || 1),
        timestamp: new Date()
      });
      
      // Log slow queries in development
      if (process.env.NODE_ENV === 'development' && executionTime > 100) {
        console.warn(`Slow query detected (${executionTime.toFixed(2)}ms):`, query);
      }
      
      return result;
    } catch (error) {
      const executionTime = performance.now() - startTime;
      console.error(`Query failed after ${executionTime.toFixed(2)}ms:`, query, error);
      throw error;
    }
  }

  /**
   * Get query performance metrics
   */
  static getMetrics(): QueryPerformanceMetrics[] {
    return [...this.metrics];
  }

  /**
   * Get slow queries (> 100ms)
   */
  static getSlowQueries(): QueryPerformanceMetrics[] {
    return this.metrics.filter(m => m.executionTime > 100);
  }

  /**
   * Get average execution time for similar queries
   */
  static getAverageExecutionTime(queryPattern: string): number {
    const matchingQueries = this.metrics.filter(m => 
      m.query.includes(queryPattern)
    );
    
    if (matchingQueries.length === 0) return 0;
    
    const totalTime = matchingQueries.reduce((sum, m) => sum + m.executionTime, 0);
    return totalTime / matchingQueries.length;
  }

  /**
   * Clear metrics
   */
  static clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Analyze database performance
   */
  static async analyzePerformance(): Promise<{
    totalQueries: number;
    averageExecutionTime: number;
    slowQueries: number;
    mostFrequentQueries: Array<{ query: string; count: number; avgTime: number }>;
  }> {
    const totalQueries = this.metrics.length;
    const averageExecutionTime = totalQueries > 0 
      ? this.metrics.reduce((sum, m) => sum + m.executionTime, 0) / totalQueries 
      : 0;
    const slowQueries = this.getSlowQueries().length;
    
    // Group by query pattern
    const queryGroups = new Map<string, { count: number; totalTime: number }>();
    
    this.metrics.forEach(metric => {
      const pattern = this.getQueryPattern(metric.query);
      const existing = queryGroups.get(pattern) || { count: 0, totalTime: 0 };
      queryGroups.set(pattern, {
        count: existing.count + 1,
        totalTime: existing.totalTime + metric.executionTime
      });
    });
    
    const mostFrequentQueries = Array.from(queryGroups.entries())
      .map(([query, stats]) => ({
        query,
        count: stats.count,
        avgTime: stats.totalTime / stats.count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
    
    return {
      totalQueries,
      averageExecutionTime,
      slowQueries,
      mostFrequentQueries
    };
  }

  /**
   * Optimize database by running ANALYZE and VACUUM
   */
  static async optimizeDatabase(): Promise<void> {
    const db = getDatabase();
    
    try {
      console.log('Running database optimization...');
      
      // Update statistics for query planner
      await new Promise<void>((resolve, reject) => {
        db.run('ANALYZE', (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
      
      // Only run VACUUM in production or when explicitly requested
      if (process.env.NODE_ENV === 'production' || process.env.FORCE_VACUUM === 'true') {
        await new Promise<void>((resolve, reject) => {
          db.run('VACUUM', (err) => {
            if (err) reject(err);
            else resolve();
          });
        });
        console.log('Database VACUUM completed');
      }
      
      console.log('Database optimization completed');
    } catch (error) {
      console.error('Database optimization failed:', error);
      throw error;
    }
  }

  /**
   * Get database size and statistics
   */
  static async getDatabaseStats(): Promise<{
    pageCount: number;
    pageSize: number;
    databaseSize: number;
    freePages: number;
    tableStats: Array<{ name: string; rowCount: number }>;
  }> {
    const db = getDatabase();
    
    const pageCount = await new Promise<number>((resolve, reject) => {
      db.get('PRAGMA page_count', (err, row: any) => {
        if (err) reject(err);
        else resolve(row.page_count);
      });
    });
    
    const pageSize = await new Promise<number>((resolve, reject) => {
      db.get('PRAGMA page_size', (err, row: any) => {
        if (err) reject(err);
        else resolve(row.page_size);
      });
    });
    
    const freePages = await new Promise<number>((resolve, reject) => {
      db.get('PRAGMA freelist_count', (err, row: any) => {
        if (err) reject(err);
        else resolve(row.freelist_count);
      });
    });
    
    // Get table statistics
    const tables = await new Promise<any[]>((resolve, reject) => {
      db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    const tableStats = await Promise.all(
      tables.map(async (table) => {
        const count = await new Promise<number>((resolve, reject) => {
          db.get(`SELECT COUNT(*) as count FROM ${table.name}`, (err, row: any) => {
            if (err) resolve(0); // Ignore errors for system tables
            else resolve(row.count);
          });
        });
        return { name: table.name, rowCount: count };
      })
    );
    
    return {
      pageCount,
      pageSize,
      databaseSize: pageCount * pageSize,
      freePages,
      tableStats: tableStats.filter(t => t.rowCount > 0)
    };
  }

  private static recordMetrics(metrics: QueryPerformanceMetrics): void {
    this.metrics.push(metrics);
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }
  }

  private static sanitizeQuery(query: string): string {
    // Remove parameter values for grouping similar queries
    return query
      .replace(/\$\d+/g, '?')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private static getQueryPattern(query: string): string {
    // Extract the main operation and table
    const match = query.match(/^(SELECT|INSERT|UPDATE|DELETE)\s+.*?\s+(?:FROM|INTO|SET)\s+(\w+)/i);
    if (match) {
      return `${match[1].toUpperCase()} ${match[2]}`;
    }
    return query.substring(0, 50);
  }
}

/**
 * Query builder with optimization hints
 */
export class OptimizedQueryBuilder {
  private query: string = '';
  private params: any[] = [];

  static select(columns: string[] = ['*']): OptimizedQueryBuilder {
    const builder = new OptimizedQueryBuilder();
    builder.query = `SELECT ${columns.join(', ')}`;
    return builder;
  }

  from(table: string): OptimizedQueryBuilder {
    this.query += ` FROM ${table}`;
    return this;
  }

  where(condition: string, ...params: any[]): OptimizedQueryBuilder {
    const whereClause = this.query.includes('WHERE') ? ' AND' : ' WHERE';
    this.query += `${whereClause} ${condition}`;
    this.params.push(...params);
    return this;
  }

  orderBy(column: string, direction: 'ASC' | 'DESC' = 'ASC'): OptimizedQueryBuilder {
    this.query += ` ORDER BY ${column} ${direction}`;
    return this;
  }

  limit(count: number, offset?: number): OptimizedQueryBuilder {
    this.query += ` LIMIT ${count}`;
    if (offset !== undefined) {
      this.query += ` OFFSET ${offset}`;
    }
    return this;
  }

  async execute<T>(): Promise<T> {
    return QueryOptimizer.executeWithMetrics<T>(this.query, this.params, 'all');
  }

  async executeOne<T>(): Promise<T> {
    return QueryOptimizer.executeWithMetrics<T>(this.query, this.params, 'get');
  }

  getQuery(): { query: string; params: any[] } {
    return { query: this.query, params: this.params };
  }
}
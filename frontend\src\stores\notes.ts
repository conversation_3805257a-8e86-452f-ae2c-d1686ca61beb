import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  noteService,
  type Note,
  type Tag,
  type CreateNoteData,
  type UpdateNoteData,
  type NoteFilters,
  type PaginationOptions,
  type NoteVersion
} from '../services/noteService'
import { useTagsStore } from './tags'

export const useNotesStore = defineStore('notes', () => {
  // Get tags store instance
  const tagsStore = useTagsStore()

  // State
  const notes = ref<Note[]>([])
  const currentNote = ref<Note | null>(null)
  const noteVersions = ref<NoteVersion[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const autoSaveStatus = ref<'idle' | 'saving' | 'saved' | 'error'>('idle')

  // Pagination state
  const currentPage = ref(1)
  const totalPages = ref(1)
  const totalNotes = ref(0)
  const notesPerPage = ref(20)

  // Filter state
  const currentFilters = ref<NoteFilters>({})
  const searchQuery = ref('')
  const selectedTags = ref<string[]>([])
  const selectedNoteType = ref<'richtext' | 'markdown' | 'kanban' | undefined>()
  const showArchived = ref(false)

  // Auto-save configuration
  const autoSaveInterval = ref(2000) // 2 seconds default
  const autoSaveEnabled = ref(true)

  // Getters
  const filteredNotes = computed(() => {
    let filtered = notes.value

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(note =>
        note.title.toLowerCase().includes(query) ||
        note.content.toLowerCase().includes(query)
      )
    }

    if (selectedTags.value.length > 0) {
      filtered = filtered.filter(note =>
        note.tags.some(tag => selectedTags.value.includes(tag.name))
      )
    }

    if (selectedNoteType.value) {
      filtered = filtered.filter(note => note.noteType === selectedNoteType.value)
    }

    if (!showArchived.value) {
      filtered = filtered.filter(note => !note.isArchived)
    }

    return filtered
  })

  const notesByType = computed(() => {
    return {
      richtext: notes.value.filter(note => note.noteType === 'richtext'),
      markdown: notes.value.filter(note => note.noteType === 'markdown'),
      kanban: notes.value.filter(note => note.noteType === 'kanban')
    }
  })

  const recentNotes = computed(() => {
    return [...notes.value]
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 10)
  })

  const tagUsageCount = computed(() => {
    const usage = new Map<string, number>()
    notes.value.forEach(note => {
      note.tags.forEach(tag => {
        usage.set(tag.name, (usage.get(tag.name) || 0) + 1)
      })
    })
    return usage
  })

  // Helper function to update tag counts in tags store
  const updateTagCounts = () => {
    const tagCounts: Record<string, number> = {}
    notes.value.forEach(note => {
      note.tags.forEach(tag => {
        const tagName = typeof tag === 'string' ? tag : tag.name
        tagCounts[tagName] = (tagCounts[tagName] || 0) + 1
      })
    })
    tagsStore.updateTagCounts(tagCounts)
  }

  // Actions
  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  // Load notes with pagination and filtering
  const loadNotes = async (
    filters: NoteFilters = {},
    pagination: PaginationOptions = { page: 1, limit: 20 }
  ) => {
    isLoading.value = true
    clearError()

    try {
      const response = await noteService.getNotes(filters, pagination)

      if (pagination.page === 1) {
        // Replace notes for first page
        notes.value = response.notes
      } else {
        // Append notes for subsequent pages
        notes.value.push(...response.notes)
      }

      currentPage.value = response.pagination.page
      totalPages.value = response.pagination.totalPages
      totalNotes.value = response.pagination.total
      notesPerPage.value = response.pagination.limit
      currentFilters.value = filters

      // Update tag counts in tags store
      updateTagCounts()

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load notes')
    } finally {
      isLoading.value = false
    }
  }

  // Load more notes (pagination)
  const loadMoreNotes = async () => {
    if (currentPage.value < totalPages.value && !isLoading.value) {
      await loadNotes(currentFilters.value, {
        page: currentPage.value + 1,
        limit: notesPerPage.value
      })
    }
  }

  // Get a specific note
  const loadNote = async (id: string) => {
    isLoading.value = true
    clearError()

    try {
      const note = await noteService.getNoteById(id)
      currentNote.value = note

      // Update note in the list if it exists
      const index = notes.value.findIndex(n => n.id === id)
      if (index !== -1) {
        notes.value[index] = note
      }

      return note
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load note')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Create a new note
  const createNote = async (noteData: CreateNoteData) => {
    isLoading.value = true
    clearError()

    try {
      const newNote = await noteService.createNote(noteData)

      // Add to the beginning of the notes array
      notes.value.unshift(newNote)
      totalNotes.value += 1

      // Update tag counts in tags store
      updateTagCounts()

      return newNote
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create note')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Update a note
  const updateNote = async (id: string, updateData: UpdateNoteData) => {
    clearError()

    try {
      const updatedNote = await noteService.updateNote(id, updateData)

      // Update in notes array
      const index = notes.value.findIndex(note => note.id === id)
      if (index !== -1) {
        notes.value[index] = updatedNote
      }

      // Update current note if it's the same
      if (currentNote.value?.id === id) {
        currentNote.value = updatedNote
      }

      // Update tag counts in tags store
      updateTagCounts()

      return updatedNote
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update note')
      throw err
    }
  }

  // Auto-save a note
  const autoSaveNote = async (id: string, updateData: UpdateNoteData) => {
    if (!autoSaveEnabled.value) return

    autoSaveStatus.value = 'saving'
    clearError()

    try {
      const updatedNote = await noteService.scheduleAutoSave(id, updateData, autoSaveInterval.value)

      // Update in notes array
      const index = notes.value.findIndex(note => note.id === id)
      if (index !== -1) {
        notes.value[index] = updatedNote
      }

      // Update current note if it's the same
      if (currentNote.value?.id === id) {
        currentNote.value = updatedNote
      }

      autoSaveStatus.value = 'saved'

      // Reset status after a delay
      setTimeout(() => {
        if (autoSaveStatus.value === 'saved') {
          autoSaveStatus.value = 'idle'
        }
      }, 2000)

      return updatedNote
    } catch (err) {
      autoSaveStatus.value = 'error'
      setError(err instanceof Error ? err.message : 'Auto-save failed')
      throw err
    }
  }

  // Cancel auto-save for a note
  const cancelAutoSave = (id: string) => {
    noteService.cancelAutoSave(id)
    if (autoSaveStatus.value === 'saving') {
      autoSaveStatus.value = 'idle'
    }
  }

  // Delete a note
  const deleteNote = async (id: string) => {
    isLoading.value = true
    clearError()

    try {
      await noteService.deleteNote(id)

      // Remove from notes array
      notes.value = notes.value.filter(note => note.id !== id)
      totalNotes.value -= 1

      // Clear current note if it's the deleted one
      if (currentNote.value?.id === id) {
        currentNote.value = null
      }

      // Update tag counts in tags store
      updateTagCounts()

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete note')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Load note versions
  const loadNoteVersions = async (id: string) => {
    isLoading.value = true
    clearError()

    try {
      const versions = await noteService.getNoteVersions(id)
      noteVersions.value = versions
      return versions
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load note versions')
      throw err
    } finally {
      isLoading.value = false
    }
  }



  // Filter and search actions
  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  const setSelectedTags = (tagNames: string[]) => {
    selectedTags.value = tagNames
  }

  const setSelectedNoteType = (noteType: 'richtext' | 'markdown' | 'kanban' | undefined) => {
    selectedNoteType.value = noteType
  }

  const setShowArchived = (show: boolean) => {
    showArchived.value = show
  }

  const clearFilters = () => {
    searchQuery.value = ''
    selectedTags.value = []
    selectedNoteType.value = undefined
    setShowArchived(false)
  }

  // Apply current filters and reload notes
  const applyFilters = async () => {
    const filters: NoteFilters = {
      search: searchQuery.value || undefined,
      tags: selectedTags.value.length > 0 ? selectedTags.value : undefined,
      noteType: selectedNoteType.value,
      isArchived: showArchived.value || undefined
    }

    await loadNotes(filters, { page: 1, limit: notesPerPage.value })
  }

  // Auto-save configuration
  const setAutoSaveInterval = (interval: number) => {
    autoSaveInterval.value = interval
  }

  const setAutoSaveEnabled = (enabled: boolean) => {
    autoSaveEnabled.value = enabled
  }

  // Optimistic updates for better UX
  const optimisticUpdateNote = (id: string, updateData: Partial<Note>) => {
    const index = notes.value.findIndex(note => note.id === id)
    if (index !== -1) {
      notes.value[index] = { ...notes.value[index], ...updateData }
    }

    if (currentNote.value?.id === id) {
      currentNote.value = { ...currentNote.value, ...updateData }
    }
  }

  // Initialize store
  const initialize = async () => {
    // Initialize offline queue
    noteService.initializeOfflineQueue()

    // Initialize tags store first
    await tagsStore.initialize()

    // Load notes and update tag counts
    await loadNotes()
  }

  // Reset store state
  const reset = () => {
    notes.value = []
    currentNote.value = null
    noteVersions.value = []
    error.value = null
    autoSaveStatus.value = 'idle'
    currentPage.value = 1
    totalPages.value = 1
    totalNotes.value = 0
    clearFilters()
  }

  return {
    // State
    notes,
    currentNote,
    noteVersions,
    isLoading,
    error,
    autoSaveStatus,
    currentPage,
    totalPages,
    totalNotes,
    notesPerPage,
    searchQuery,
    selectedTags,
    selectedNoteType,
    showArchived,
    autoSaveInterval,
    autoSaveEnabled,

    // Getters
    filteredNotes,
    notesByType,
    recentNotes,
    tagUsageCount,

    // Actions
    loadNotes,
    loadMoreNotes,
    loadNote,
    createNote,
    updateNote,
    autoSaveNote,
    cancelAutoSave,
    deleteNote,
    loadNoteVersions,
    setSearchQuery,
    setSelectedTags,
    setSelectedNoteType,
    setShowArchived,
    clearFilters,
    applyFilters,
    setAutoSaveInterval,
    setAutoSaveEnabled,
    optimisticUpdateNote,
    initialize,
    reset,
    setError,
    clearError,
    updateTagCounts
  }
})
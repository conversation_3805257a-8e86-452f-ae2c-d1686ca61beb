import express from 'express';
import { authenticateToken } from '../middleware/auth';
import { TwoFactorAuthService } from '../services/TwoFactorAuthService';

const router = express.Router();

// Setup 2FA - Generate secret and QR code
router.post('/setup', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    const appName = process.env.APP_NAME || 'Note Taking App';
    
    const setupResult = await TwoFactorAuthService.setupTwoFactor(userId, appName);
    
    res.json({
      secret: setupResult.secret,
      qrCodeUrl: setupResult.qrCodeUrl,
      backupCodes: setupResult.backupCodes
    });
  } catch (error) {
    console.error('Error setting up 2FA:', error);
    res.status(500).json({ 
      error: 'Failed to setup two-factor authentication',
      code: 'SETUP_2FA_FAILED'
    });
  }
});

// Verify 2FA code during setup
router.post('/verify-setup', authenticateToken, async (req, res) => {
  try {
    const { code } = req.body;
    const userId = req.user!.id;

    if (!code) {
      return res.status(400).json({
        error: 'Verification code is required',
        code: 'CODE_REQUIRED'
      });
    }

    const result = await TwoFactorAuthService.verifyTwoFactor(userId, code);
    
    if (result.success) {
      return res.json({
        success: true,
        message: 'Two-factor authentication enabled successfully'
      });
    } else {
      return res.status(400).json({
        error: 'Invalid verification code',
        code: 'INVALID_CODE'
      });
    }
  } catch (error) {
    console.error('Error verifying 2FA setup:', error);
    return res.status(500).json({
      error: 'Failed to verify two-factor authentication',
      code: 'VERIFY_2FA_FAILED'
    });
  }
});

// Verify 2FA code during login
router.post('/verify', async (req, res) => {
  try {
    const { userId, code } = req.body;

    if (!userId || !code) {
      return res.status(400).json({
        error: 'User ID and verification code are required',
        code: 'MISSING_PARAMETERS'
      });
    }

    const result = await TwoFactorAuthService.verifyTwoFactor(userId, code);
    
    return res.json({
      success: result.success,
      backupCodeUsed: result.backupCodeUsed
    });
  } catch (error) {
    console.error('Error verifying 2FA:', error);
    return res.status(500).json({
      error: 'Failed to verify two-factor authentication',
      code: 'VERIFY_2FA_FAILED'
    });
  }
});

// Check 2FA status
router.get('/status', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    const enabled = await TwoFactorAuthService.isTwoFactorEnabled(userId);
    
    res.json({
      enabled
    });
  } catch (error) {
    console.error('Error checking 2FA status:', error);
    res.status(500).json({
      error: 'Failed to check two-factor authentication status',
      code: 'CHECK_2FA_STATUS_FAILED'
    });
  }
});

// Disable 2FA
router.post('/disable', authenticateToken, async (req, res) => {
  try {
    const { password } = req.body;
    const userId = req.user!.id;

    if (!password) {
      return res.status(400).json({
        error: 'Password is required to disable 2FA',
        code: 'PASSWORD_REQUIRED'
      });
    }

    // Verify password before disabling 2FA
    const { UserRepository } = await import('../repositories/UserRepository');
    const { UserModel } = await import('../models/User');
    
    const user = await UserRepository.findById(userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    const isValidPassword = await UserModel.comparePassword(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Invalid password',
        code: 'INVALID_PASSWORD'
      });
    }

    await TwoFactorAuthService.disableTwoFactor(userId);
    
    return res.json({
      success: true,
      message: 'Two-factor authentication disabled successfully'
    });
  } catch (error) {
    console.error('Error disabling 2FA:', error);
    return res.status(500).json({
      error: 'Failed to disable two-factor authentication',
      code: 'DISABLE_2FA_FAILED'
    });
  }
});

// Regenerate backup codes
router.post('/regenerate-backup-codes', authenticateToken, async (req, res) => {
  try {
    const { password } = req.body;
    const userId = req.user!.id;

    if (!password) {
      return res.status(400).json({
        error: 'Password is required to regenerate backup codes',
        code: 'PASSWORD_REQUIRED'
      });
    }

    // Verify password before regenerating backup codes
    const { UserRepository } = await import('../repositories/UserRepository');
    const { UserModel } = await import('../models/User');
    
    const user = await UserRepository.findById(userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    const isValidPassword = await UserModel.comparePassword(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Invalid password',
        code: 'INVALID_PASSWORD'
      });
    }

    const backupCodes = await TwoFactorAuthService.regenerateBackupCodes(userId);
    
    return res.json({
      backupCodes,
      message: 'Backup codes regenerated successfully'
    });
  } catch (error) {
    console.error('Error regenerating backup codes:', error);
    return res.status(500).json({
      error: 'Failed to regenerate backup codes',
      code: 'REGENERATE_BACKUP_CODES_FAILED'
    });
  }
});

export default router;
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { StoreInitializer, type StoreConfig } from '../storeInitializer'

// Mock store modules
const mockAuthStore = {
  initializeAuthWithTimeout: vi.fn(),
  initializeAuth: vi.fn()
}

const mockSettingsStore = {
  initializeSettings: vi.fn()
}

const mockCacheService = {
  preloadCriticalData: vi.fn()
}

// Mock the dynamic imports to return the mock objects directly
vi.mock('../../stores/auth', () => ({
  useAuthStore: () => mockAuthStore
}))

vi.mock('../../stores/settings', () => ({
  useSettingsStore: () => mockSettingsStore
}))

vi.mock('../../services/cacheService', () => ({
  cacheService: mockCacheService
}))

// Mock the import paths used in the store initializer
const mockImports = new Map([
  ['../stores/auth', { useAuthStore: () => mockAuthStore }],
  ['../stores/settings', { useSettingsStore: () => mockSettingsStore }],
  ['../services/cacheService', { cacheService: mockCacheService }]
])

// Override the dynamic import function in the StoreInitializer
const originalImport = StoreInitializer.prototype['importStoreModule']
StoreInitializer.prototype['importStoreModule'] = async function(config: StoreConfig) {
  const mockModule = mockImports.get(config.importPath)
  if (mockModule) {
    return mockModule
  }
  // Fall back to original behavior for unmocked imports
  throw new Error(`Cannot find module '${config.importPath}'`)
}

describe('StoreInitializer', () => {
  let initializer: StoreInitializer
  
  beforeEach(() => {
    initializer = new StoreInitializer()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('parallel store initialization', () => {
    it('should initialize independent stores in parallel', async () => {
      const configs: StoreConfig[] = [
        {
          name: 'auth',
          importPath: '../stores/auth',
          initMethod: 'initializeAuthWithTimeout',
          critical: true
        },
        {
          name: 'settings', 
          importPath: '../stores/settings',
          initMethod: 'initializeSettings',
          critical: false
        }
      ]

      mockAuthStore.initializeAuthWithTimeout.mockResolvedValue(undefined)
      mockSettingsStore.initializeSettings.mockResolvedValue(undefined)

      const startTime = performance.now()
      const results = await initializer.initializeStores(configs)
      const endTime = performance.now()

      expect(results.size).toBe(2)
      expect(results.get('auth')?.success).toBe(true)
      expect(results.get('settings')?.success).toBe(true)
      
      // Both should have been called
      expect(mockAuthStore.initializeAuthWithTimeout).toHaveBeenCalledOnce()
      expect(mockSettingsStore.initializeSettings).toHaveBeenCalledOnce()
    })

    it('should handle store dependencies correctly', async () => {
      const configs: StoreConfig[] = [
        {
          name: 'auth',
          importPath: '../stores/auth',
          initMethod: 'initializeAuthWithTimeout',
          critical: true
        },
        {
          name: 'settings',
          importPath: '../stores/settings', 
          initMethod: 'initializeSettings',
          dependencies: ['auth'],
          critical: false
        }
      ]

      mockAuthStore.initializeAuthWithTimeout.mockResolvedValue(undefined)
      mockSettingsStore.initializeSettings.mockResolvedValue(undefined)

      const results = await initializer.initializeStores(configs)

      expect(results.size).toBe(2)
      expect(results.get('auth')?.success).toBe(true)
      expect(results.get('settings')?.success).toBe(true)
    })
  })

  describe('timeout handling', () => {
    it('should timeout auth store initialization after 3 seconds', async () => {
      const configs: StoreConfig[] = [
        {
          name: 'auth',
          importPath: '../stores/auth',
          initMethod: 'initializeAuthWithTimeout',
          timeout: 100, // Short timeout for testing
          critical: true
        }
      ]

      // Mock a slow initialization
      mockAuthStore.initializeAuthWithTimeout.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 200))
      )

      await expect(initializer.initializeStores(configs)).rejects.toThrow()
      
      const results = initializer.getInitializationResults()
      const authResult = results.get('auth')
      expect(authResult?.success).toBe(false)
      expect(authResult?.error).toContain('timeout')
    })

    it('should continue with non-critical stores on timeout', async () => {
      const configs: StoreConfig[] = [
        {
          name: 'settings',
          importPath: '../stores/settings',
          initMethod: 'initializeSettings', 
          timeout: 100,
          critical: false
        }
      ]

      // Mock a slow initialization
      mockSettingsStore.initializeSettings.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 200))
      )

      const results = await initializer.initializeStores(configs)
      
      const settingsResult = results.get('settings')
      expect(settingsResult?.success).toBe(false)
      expect(settingsResult?.error).toContain('timeout')
    })
  })

  describe('error handling', () => {
    it('should handle store import failures gracefully', async () => {
      const configs: StoreConfig[] = [
        {
          name: 'nonexistent',
          importPath: '../stores/nonexistent',
          initMethod: 'initialize',
          critical: false
        }
      ]

      const results = await initializer.initializeStores(configs)
      
      const result = results.get('nonexistent')
      expect(result?.success).toBe(false)
      expect(result?.error).toContain('Failed to import')
    })

    it('should throw for critical store failures', async () => {
      const configs: StoreConfig[] = [
        {
          name: 'auth',
          importPath: '../stores/auth',
          initMethod: 'initializeAuthWithTimeout',
          critical: true
        }
      ]

      mockAuthStore.initializeAuthWithTimeout.mockRejectedValue(new Error('Auth failed'))

      await expect(initializer.initializeStores(configs)).rejects.toThrow('Critical store auth failed')
    })

    it('should continue with non-critical store failures', async () => {
      const configs: StoreConfig[] = [
        {
          name: 'auth',
          importPath: '../stores/auth', 
          initMethod: 'initializeAuthWithTimeout',
          critical: true
        },
        {
          name: 'settings',
          importPath: '../stores/settings',
          initMethod: 'initializeSettings',
          critical: false
        }
      ]

      mockAuthStore.initializeAuthWithTimeout.mockResolvedValue(undefined)
      mockSettingsStore.initializeSettings.mockRejectedValue(new Error('Settings failed'))

      const results = await initializer.initializeStores(configs)
      
      expect(results.get('auth')?.success).toBe(true)
      expect(results.get('settings')?.success).toBe(false)
    })
  })

  describe('performance tracking', () => {
    it('should track initialization timing for each store', async () => {
      const configs: StoreConfig[] = [
        {
          name: 'auth',
          importPath: '../stores/auth',
          initMethod: 'initializeAuthWithTimeout',
          critical: true
        }
      ]

      mockAuthStore.initializeAuthWithTimeout.mockResolvedValue(undefined)

      const results = await initializer.initializeStores(configs)
      const authResult = results.get('auth')
      
      expect(authResult?.duration).toBeGreaterThan(0)
      expect(typeof authResult?.duration).toBe('number')
    })

    it('should provide initialization statistics', async () => {
      const configs: StoreConfig[] = [
        {
          name: 'auth',
          importPath: '../stores/auth',
          initMethod: 'initializeAuthWithTimeout',
          critical: true
        },
        {
          name: 'settings',
          importPath: '../stores/settings',
          initMethod: 'initializeSettings', 
          critical: false
        }
      ]

      mockAuthStore.initializeAuthWithTimeout.mockResolvedValue(undefined)
      mockSettingsStore.initializeSettings.mockRejectedValue(new Error('Failed'))

      await initializer.initializeStores(configs)
      const stats = initializer.getStats()

      expect(stats.totalStores).toBe(2)
      expect(stats.successful).toBe(1)
      expect(stats.failed).toBe(1)
      expect(stats.totalTime).toBeGreaterThan(0)
      expect(stats.averageTime).toBeGreaterThan(0)
      expect(stats.failedStores).toHaveLength(1)
      expect(stats.failedStores[0].name).toBe('settings')
    })
  })

  describe('store access', () => {
    it('should provide access to initialized stores', async () => {
      const configs: StoreConfig[] = [
        {
          name: 'auth',
          importPath: '../stores/auth',
          initMethod: 'initializeAuthWithTimeout',
          critical: true
        }
      ]

      mockAuthStore.initializeAuthWithTimeout.mockResolvedValue(undefined)

      await initializer.initializeStores(configs)
      const authStore = initializer.getStore('auth')
      
      expect(authStore).toBeDefined()
      expect(authStore).toBe(mockAuthStore)
    })
  })
})
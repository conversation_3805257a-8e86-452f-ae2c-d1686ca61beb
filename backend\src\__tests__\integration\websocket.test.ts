import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import { Server } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { io as Client, Socket as ClientSocket } from 'socket.io-client';
import express from 'express';
import jwt from 'jsonwebtoken';

// Mock the collaboration service
vi.mock('../../services/CollaborationService');

describe('WebSocket Integration Tests', () => {
  let httpServer: Server;
  let ioServer: SocketIOServer;
  let serverSocket: any;
  let clientSocket: ClientSocket;
  let clientSocket2: ClientSocket;
  const port = 3001;

  beforeAll((done) => {
    const app = express();
    httpServer = app.listen(port);
    
    ioServer = new SocketIOServer(httpServer, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    // Mock authentication middleware for WebSocket
    ioServer.use((socket, next) => {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error'));
      }

      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'test-secret');
        socket.userId = (decoded as any).userId;
        socket.userEmail = (decoded as any).email;
        next();
      } catch (err) {
        next(new Error('Authentication error'));
      }
    });

    // Set up WebSocket event handlers
    ioServer.on('connection', (socket) => {
      serverSocket = socket;

      socket.on('note:join', (data) => {
        socket.join(`note:${data.noteId}`);
        socket.to(`note:${data.noteId}`).emit('user:joined', {
          userId: socket.userId,
          userEmail: socket.userEmail,
          noteId: data.noteId
        });
      });

      socket.on('note:leave', (data) => {
        socket.leave(`note:${data.noteId}`);
        socket.to(`note:${data.noteId}`).emit('user:left', {
          userId: socket.userId,
          noteId: data.noteId
        });
      });

      socket.on('note:edit', (data) => {
        // Broadcast edit operation to other users in the same note
        socket.to(`note:${data.noteId}`).emit('note:operation', {
          noteId: data.noteId,
          operation: data.operation,
          userId: socket.userId,
          timestamp: Date.now()
        });
      });

      socket.on('cursor:update', (data) => {
        socket.to(`note:${data.noteId}`).emit('cursor:update', {
          noteId: data.noteId,
          userId: socket.userId,
          position: data.position,
          selection: data.selection
        });
      });

      socket.on('typing:start', (data) => {
        socket.to(`note:${data.noteId}`).emit('user:typing', {
          noteId: data.noteId,
          userId: socket.userId,
          isTyping: true
        });
      });

      socket.on('typing:stop', (data) => {
        socket.to(`note:${data.noteId}`).emit('user:typing', {
          noteId: data.noteId,
          userId: socket.userId,
          isTyping: false
        });
      });

      socket.on('disconnect', () => {
        // Handle cleanup when user disconnects
        socket.rooms.forEach(room => {
          if (room.startsWith('note:')) {
            const noteId = room.replace('note:', '');
            socket.to(room).emit('user:left', {
              userId: socket.userId,
              noteId
            });
          }
        });
      });
    });

    done();
  });

  afterAll((done) => {
    ioServer.close();
    httpServer.close(done);
  });

  beforeEach((done) => {
    // Create test JWT tokens
    const testToken1 = jwt.sign(
      { userId: 'user-1', email: '<EMAIL>' },
      process.env.JWT_SECRET || 'test-secret'
    );
    
    const testToken2 = jwt.sign(
      { userId: 'user-2', email: '<EMAIL>' },
      process.env.JWT_SECRET || 'test-secret'
    );

    // Connect test clients
    clientSocket = Client(`http://localhost:${port}`, {
      auth: { token: testToken1 }
    });

    clientSocket2 = Client(`http://localhost:${port}`, {
      auth: { token: testToken2 }
    });

    let connectedCount = 0;
    const onConnect = () => {
      connectedCount++;
      if (connectedCount === 2) {
        done();
      }
    };

    clientSocket.on('connect', onConnect);
    clientSocket2.on('connect', onConnect);
  });

  afterEach(() => {
    if (clientSocket.connected) {
      clientSocket.disconnect();
    }
    if (clientSocket2.connected) {
      clientSocket2.disconnect();
    }
  });

  describe('Authentication', () => {
    it('should reject connection without token', (done) => {
      const unauthorizedClient = Client(`http://localhost:${port}`);
      
      unauthorizedClient.on('connect_error', (error) => {
        expect(error.message).toBe('Authentication error');
        unauthorizedClient.disconnect();
        done();
      });
    });

    it('should reject connection with invalid token', (done) => {
      const unauthorizedClient = Client(`http://localhost:${port}`, {
        auth: { token: 'invalid-token' }
      });
      
      unauthorizedClient.on('connect_error', (error) => {
        expect(error.message).toBe('Authentication error');
        unauthorizedClient.disconnect();
        done();
      });
    });

    it('should accept connection with valid token', () => {
      expect(clientSocket.connected).toBe(true);
      expect(clientSocket2.connected).toBe(true);
    });
  });

  describe('Note Collaboration', () => {
    const testNoteId = 'note-123';

    it('should handle users joining and leaving notes', (done) => {
      let eventsReceived = 0;

      // User 2 should receive notification when user 1 joins
      clientSocket2.on('user:joined', (data) => {
        expect(data.userId).toBe('user-1');
        expect(data.noteId).toBe(testNoteId);
        eventsReceived++;
        
        if (eventsReceived === 2) done();
      });

      // User 1 should receive notification when user 2 joins
      clientSocket.on('user:joined', (data) => {
        expect(data.userId).toBe('user-2');
        expect(data.noteId).toBe(testNoteId);
        eventsReceived++;
        
        if (eventsReceived === 2) done();
      });

      // Both users join the same note
      clientSocket.emit('note:join', { noteId: testNoteId });
      clientSocket2.emit('note:join', { noteId: testNoteId });
    });

    it('should broadcast edit operations to other users', (done) => {
      const operation = {
        type: 'insert',
        position: 10,
        content: 'Hello World',
        author: 'user-1'
      };

      // User 2 should receive the edit operation from user 1
      clientSocket2.on('note:operation', (data) => {
        expect(data.noteId).toBe(testNoteId);
        expect(data.operation).toEqual(operation);
        expect(data.userId).toBe('user-1');
        expect(data.timestamp).toBeDefined();
        done();
      });

      // Both users join the note first
      clientSocket.emit('note:join', { noteId: testNoteId });
      clientSocket2.emit('note:join', { noteId: testNoteId });

      // User 1 makes an edit
      setTimeout(() => {
        clientSocket.emit('note:edit', {
          noteId: testNoteId,
          operation
        });
      }, 100);
    });

    it('should handle cursor position updates', (done) => {
      const cursorData = {
        position: { line: 5, column: 10 },
        selection: { start: { line: 5, column: 10 }, end: { line: 5, column: 20 } }
      };

      // User 2 should receive cursor update from user 1
      clientSocket2.on('cursor:update', (data) => {
        expect(data.noteId).toBe(testNoteId);
        expect(data.userId).toBe('user-1');
        expect(data.position).toEqual(cursorData.position);
        expect(data.selection).toEqual(cursorData.selection);
        done();
      });

      // Both users join the note
      clientSocket.emit('note:join', { noteId: testNoteId });
      clientSocket2.emit('note:join', { noteId: testNoteId });

      // User 1 updates cursor position
      setTimeout(() => {
        clientSocket.emit('cursor:update', {
          noteId: testNoteId,
          ...cursorData
        });
      }, 100);
    });

    it('should handle typing indicators', (done) => {
      let typingEvents = 0;

      // User 2 should receive typing indicators from user 1
      clientSocket2.on('user:typing', (data) => {
        expect(data.noteId).toBe(testNoteId);
        expect(data.userId).toBe('user-1');
        
        typingEvents++;
        if (typingEvents === 1) {
          expect(data.isTyping).toBe(true);
        } else if (typingEvents === 2) {
          expect(data.isTyping).toBe(false);
          done();
        }
      });

      // Both users join the note
      clientSocket.emit('note:join', { noteId: testNoteId });
      clientSocket2.emit('note:join', { noteId: testNoteId });

      // User 1 starts and stops typing
      setTimeout(() => {
        clientSocket.emit('typing:start', { noteId: testNoteId });
        
        setTimeout(() => {
          clientSocket.emit('typing:stop', { noteId: testNoteId });
        }, 100);
      }, 100);
    });

    it('should handle user disconnection cleanup', (done) => {
      // User 2 should receive notification when user 1 disconnects
      clientSocket2.on('user:left', (data) => {
        expect(data.userId).toBe('user-1');
        expect(data.noteId).toBe(testNoteId);
        done();
      });

      // Both users join the note
      clientSocket.emit('note:join', { noteId: testNoteId });
      clientSocket2.emit('note:join', { noteId: testNoteId });

      // User 1 disconnects
      setTimeout(() => {
        clientSocket.disconnect();
      }, 100);
    });
  });

  describe('Real-time Performance', () => {
    const testNoteId = 'performance-note';

    it('should handle multiple rapid operations', (done) => {
      const operations = [];
      const numOperations = 10;
      let receivedOperations = 0;

      // Generate test operations
      for (let i = 0; i < numOperations; i++) {
        operations.push({
          type: 'insert',
          position: i * 10,
          content: `Operation ${i}`,
          author: 'user-1'
        });
      }

      // User 2 should receive all operations
      clientSocket2.on('note:operation', (data) => {
        receivedOperations++;
        expect(data.userId).toBe('user-1');
        expect(data.noteId).toBe(testNoteId);
        
        if (receivedOperations === numOperations) {
          done();
        }
      });

      // Both users join the note
      clientSocket.emit('note:join', { noteId: testNoteId });
      clientSocket2.emit('note:join', { noteId: testNoteId });

      // Send operations rapidly
      setTimeout(() => {
        operations.forEach((operation, index) => {
          setTimeout(() => {
            clientSocket.emit('note:edit', {
              noteId: testNoteId,
              operation
            });
          }, index * 10); // 10ms between operations
        });
      }, 100);
    });

    it('should maintain low latency for real-time updates', (done) => {
      const startTime = Date.now();

      clientSocket2.on('note:operation', (data) => {
        const latency = Date.now() - data.timestamp;
        expect(latency).toBeLessThan(200); // Should be under 200ms
        done();
      });

      // Both users join the note
      clientSocket.emit('note:join', { noteId: testNoteId });
      clientSocket2.emit('note:join', { noteId: testNoteId });

      // Send operation with timestamp
      setTimeout(() => {
        clientSocket.emit('note:edit', {
          noteId: testNoteId,
          operation: {
            type: 'insert',
            position: 0,
            content: 'Latency test',
            timestamp: Date.now()
          }
        });
      }, 100);
    });
  });

  describe('Room Management', () => {
    it('should isolate operations between different notes', (done) => {
      const note1Id = 'note-1';
      const note2Id = 'note-2';
      let operationsReceived = 0;

      // User 1 joins note 1, User 2 joins note 2
      clientSocket.emit('note:join', { noteId: note1Id });
      clientSocket2.emit('note:join', { noteId: note2Id });

      // User 2 should not receive operations from note 1
      clientSocket2.on('note:operation', (data) => {
        // This should not be called
        expect(true).toBe(false);
      });

      // User 1 should not receive operations from note 2
      clientSocket.on('note:operation', (data) => {
        // This should not be called
        expect(true).toBe(false);
      });

      // Send operations to different notes
      setTimeout(() => {
        clientSocket.emit('note:edit', {
          noteId: note1Id,
          operation: { type: 'insert', position: 0, content: 'Note 1 edit' }
        });

        clientSocket2.emit('note:edit', {
          noteId: note2Id,
          operation: { type: 'insert', position: 0, content: 'Note 2 edit' }
        });

        // Wait and verify no cross-contamination
        setTimeout(() => {
          done();
        }, 200);
      }, 100);
    });
  });
});
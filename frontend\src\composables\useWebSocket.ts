import { ref, onMounted, onUnmounted, computed } from 'vue'
import { websocketService, type NoteOperation, type CursorUpdate, type UserPresence } from '@/services/websocketService'
import { useAuthStore } from '@/stores/auth'

export function useWebSocket() {
  const authStore = useAuthStore()
  const isConnected = ref(false)
  const connectionError = ref<string | null>(null)
  const isConnecting = ref(false)

  // Connection status
  const connectionStatus = computed(() => {
    if (isConnecting.value) return 'connecting'
    if (isConnected.value) return 'connected'
    if (connectionError.value) return 'error'
    return 'disconnected'
  })

  // Connect to WebSocket
  const connect = async () => {
    if (!authStore.isAuthenticated) {
      connectionError.value = 'Authentication required'
      return false
    }

    try {
      isConnecting.value = true
      connectionError.value = null
      
      await websocketService.connect()
      isConnected.value = true
      return true
    } catch (error) {
      console.error('WebSocket connection failed:', error)
      connectionError.value = error instanceof Error ? error.message : 'Connection failed'
      return false
    } finally {
      isConnecting.value = false
    }
  }

  // Disconnect from WebSocket
  const disconnect = () => {
    websocketService.disconnect()
    isConnected.value = false
    connectionError.value = null
  }

  // Auto-connect when authenticated
  const autoConnect = async () => {
    if (authStore.isAuthenticated && !isConnected.value && !isConnecting.value) {
      await connect()
    }
  }

  // Setup connection event listeners
  const setupConnectionListeners = () => {
    websocketService.on('connection', (data: { connected: boolean; reason?: string }) => {
      isConnected.value = data.connected
      if (!data.connected && data.reason) {
        connectionError.value = `Disconnected: ${data.reason}`
      }
    })

    websocketService.on('connection_failed', (data: { error: Error; attempts: number }) => {
      connectionError.value = `Connection failed after ${data.attempts} attempts: ${data.error.message}`
      isConnected.value = false
    })
  }

  // Cleanup connection listeners
  const cleanupConnectionListeners = () => {
    websocketService.off('connection')
    websocketService.off('connection_failed')
  }

  onMounted(() => {
    setupConnectionListeners()
    autoConnect()
  })

  onUnmounted(() => {
    cleanupConnectionListeners()
  })

  return {
    // State
    isConnected: computed(() => isConnected.value),
    connectionError: computed(() => connectionError.value),
    isConnecting: computed(() => isConnecting.value),
    connectionStatus,

    // Methods
    connect,
    disconnect,
    autoConnect
  }
}

export function useNoteCollaboration(noteId: string) {
  const { isConnected } = useWebSocket()
  
  // Collaboration state
  const activeUsers = ref<Set<string>>(new Set())
  const typingUsers = ref<Set<string>>(new Set())
  const userCursors = ref<Map<string, { position: number; selection?: any }>>(new Map())

  // Join note collaboration
  const joinNote = () => {
    if (isConnected.value) {
      websocketService.joinNote(noteId)
    }
  }

  // Leave note collaboration
  const leaveNote = () => {
    if (isConnected.value) {
      websocketService.leaveNote(noteId)
    }
  }

  // Send note operation
  const sendOperation = (operation: NoteOperation['operation']) => {
    if (isConnected.value) {
      websocketService.sendNoteOperation(noteId, operation)
    }
  }

  // Send cursor update
  const sendCursorUpdate = (position: number, selection?: any) => {
    if (isConnected.value) {
      websocketService.sendCursorUpdate(noteId, position, selection)
    }
  }

  // Send typing indicators
  const sendTypingStart = () => {
    if (isConnected.value) {
      websocketService.sendTypingStart(noteId)
    }
  }

  const sendTypingStop = () => {
    if (isConnected.value) {
      websocketService.sendTypingStop(noteId)
    }
  }

  // Event handlers
  const onUserJoined = (callback: (data: any) => void) => {
    const handler = (data: any) => {
      if (data.noteId === noteId) {
        activeUsers.value.add(data.userId)
        callback(data)
      }
    }
    websocketService.on('user:joined', handler)
    return () => websocketService.off('user:joined', handler)
  }

  const onUserLeft = (callback: (data: any) => void) => {
    const handler = (data: any) => {
      if (data.noteId === noteId) {
        activeUsers.value.delete(data.userId)
        typingUsers.value.delete(data.userId)
        userCursors.value.delete(data.userId)
        callback(data)
      }
    }
    websocketService.on('user:left', handler)
    return () => websocketService.off('user:left', handler)
  }

  const onNoteOperation = (callback: (data: NoteOperation) => void) => {
    const handler = (data: NoteOperation) => {
      if (data.noteId === noteId) {
        callback(data)
      }
    }
    websocketService.on('note:operation', handler)
    return () => websocketService.off('note:operation', handler)
  }

  const onCursorUpdate = (callback: (data: CursorUpdate) => void) => {
    const handler = (data: CursorUpdate) => {
      if (data.noteId === noteId) {
        userCursors.value.set(data.userId, {
          position: data.position,
          selection: data.selection
        })
        callback(data)
      }
    }
    websocketService.on('cursor:update', handler)
    return () => websocketService.off('cursor:update', handler)
  }

  const onUserTyping = (callback: (data: UserPresence) => void) => {
    const handler = (data: UserPresence) => {
      if (data.noteId === noteId) {
        if (data.isTyping) {
          typingUsers.value.add(data.userId)
        } else {
          typingUsers.value.delete(data.userId)
        }
        callback(data)
      }
    }
    websocketService.on('user:typing', handler)
    return () => websocketService.off('user:typing', handler)
  }

  // Cleanup on unmount
  onUnmounted(() => {
    leaveNote()
  })

  return {
    // State
    activeUsers: computed(() => Array.from(activeUsers.value)),
    typingUsers: computed(() => Array.from(typingUsers.value)),
    userCursors: computed(() => userCursors.value),

    // Methods
    joinNote,
    leaveNote,
    sendOperation,
    sendCursorUpdate,
    sendTypingStart,
    sendTypingStop,

    // Event handlers
    onUserJoined,
    onUserLeft,
    onNoteOperation,
    onCursorUpdate,
    onUserTyping
  }
}

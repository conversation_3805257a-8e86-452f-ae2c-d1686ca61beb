import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import SettingsPanel from '../SettingsPanel.vue'

describe('SettingsPanel', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(SettingsPanel, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
        stubs: {
          PreferencesTab: true,
          ProfileTab: true,
          AccountTab: true
        }
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.settings-panel').exists()).toBe(true)
  })

  it('displays settings title', () => {
    expect(wrapper.find('.title').text()).toBe('Settings')
  })

  it('has three tabs', () => {
    const tabs = wrapper.findAll('.tabs li')
    expect(tabs).toHaveLength(3)
    expect(tabs[0].text()).toContain('Preferences')
    expect(tabs[1].text()).toContain('Profile')
    expect(tabs[2].text()).toContain('Account')
  })

  it('defaults to preferences tab', () => {
    const activeTab = wrapper.find('.tabs li.is-active')
    expect(activeTab.text()).toContain('Preferences')
  })

  it('emits close event when close button is clicked', async () => {
    await wrapper.find('.delete').trigger('click')
    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('switches tabs when clicked', async () => {
    const profileTab = wrapper.findAll('.tabs li')[1]
    await profileTab.find('a').trigger('click')
    
    expect(wrapper.vm.activeTab).toBe('profile')
  })
})
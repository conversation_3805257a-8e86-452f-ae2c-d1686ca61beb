<template>
  <div class="search-results">
    <!-- Search Summary -->
    <div v-if="hasResults || isSearching" class="search-summary mb-4">
      <div class="level">
        <div class="level-left">
          <div class="level-item">
            <p class="subtitle is-6" v-if="!isSearching">
              {{ searchSummary }}
            </p>
            <p class="subtitle is-6" v-else>
              <span class="icon">
                <i class="fas fa-spinner fa-spin"></i>
              </span>
              Searching...
            </p>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="field has-addons">
              <div class="control">
                <div class="select is-small">
                  <select v-model="sortBy" @change="handleSortChange">
                    <option value="relevance">Relevance</option>
                    <option value="updated_at">Last Modified</option>
                    <option value="created_at">Date Created</option>
                    <option value="title">Title</option>
                  </select>
                </div>
              </div>
              <div class="control">
                <button
                  class="button is-small"
                  :class="{ 'is-primary': sortOrder === 'desc' }"
                  @click="toggleSortOrder"
                  :title="sortOrder === 'desc' ? 'Descending' : 'Ascending'"
                >
                  <span class="icon">
                    <i :class="sortOrder === 'desc' ? 'fas fa-sort-down' : 'fas fa-sort-up'"></i>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Results -->
    <div v-if="!hasResults && !isSearching && hasQuery" class="no-results">
      <div class="has-text-centered py-6">
        <span class="icon is-large has-text-grey-light">
          <i class="fas fa-search fa-3x"></i>
        </span>
        <h3 class="title is-4 has-text-grey">No results found</h3>
        <p class="subtitle is-6 has-text-grey">
          Try adjusting your search terms or filters
        </p>
        
        <!-- Search suggestions -->
        <div v-if="searchSuggestions.length > 0" class="mt-4">
          <p class="has-text-weight-semibold mb-2">Did you mean:</p>
          <div class="tags is-centered">
            <button
              v-for="suggestion in searchSuggestions"
              :key="suggestion"
              class="tag is-link is-light"
              @click="applySuggestion(suggestion)"
            >
              {{ suggestion }}
            </button>
          </div>
        </div>

        <!-- Quick actions -->
        <div class="mt-4">
          <div class="buttons is-centered">
            <button class="button is-light" @click="clearSearch">
              <span class="icon">
                <i class="fas fa-times"></i>
              </span>
              <span>Clear Search</span>
            </button>
            <button class="button is-light" @click="showAllNotes">
              <span class="icon">
                <i class="fas fa-list"></i>
              </span>
              <span>Show All Notes</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Search Results -->
    <div v-if="hasResults" class="results-list">
      <div
        v-for="result in searchResults"
        :key="result.note.id"
        class="result-item card mb-4"
        @click="openNote(result.note)"
      >
        <div class="card-content">
          <div class="media">
            <div class="media-left">
              <span class="icon is-medium" :class="getNoteTypeClass(result.note.noteType)">
                <i :class="getNoteTypeIcon(result.note.noteType)"></i>
              </span>
            </div>
            <div class="media-content">
              <div class="content">
                <!-- Title with highlighting -->
                <h4 class="title is-5 mb-2">
                  <span
                    v-if="result.highlights.title"
                    v-html="result.highlights.title"
                  ></span>
                  <span v-else>{{ result.note.title }}</span>
                </h4>

                <!-- Content preview with highlighting -->
                <div class="content-preview">
                  <p
                    v-if="result.highlights.content"
                    v-html="result.highlights.content"
                    class="has-text-grey-dark"
                  ></p>
                  <p v-else class="has-text-grey-dark">
                    {{ getContentPreview(result.note.content, result.note.noteType) }}
                  </p>
                </div>

                <!-- Tags -->
                <div v-if="result.tags.length > 0" class="tags mt-2">
                  <span
                    v-for="tag in result.tags"
                    :key="tag.id"
                    class="tag is-small is-primary is-light"
                    @click.stop="searchByTag(tag.name)"
                  >
                    {{ tag.name }}
                  </span>
                </div>

                <!-- Metadata -->
                <div class="result-metadata mt-2">
                  <div class="level is-mobile">
                    <div class="level-left">
                      <div class="level-item">
                        <small class="has-text-grey">
                          <span class="icon is-small">
                            <i class="fas fa-clock"></i>
                          </span>
                          {{ formatDate(result.note.updatedAt) }}
                        </small>
                      </div>
                      <div class="level-item" v-if="result.note.metadata.wordCount">
                        <small class="has-text-grey">
                          <span class="icon is-small">
                            <i class="fas fa-file-word"></i>
                          </span>
                          {{ result.note.metadata.wordCount }} words
                        </small>
                      </div>
                      <div class="level-item" v-if="result.note.metadata.readingTime">
                        <small class="has-text-grey">
                          <span class="icon is-small">
                            <i class="fas fa-book-reader"></i>
                          </span>
                          {{ result.note.metadata.readingTime }} min read
                        </small>
                      </div>
                    </div>
                    <div class="level-right">
                      <div class="level-item" v-if="result.relevanceScore > 0">
                        <small class="has-text-grey">
                          Relevance: {{ Math.round(result.relevanceScore * 100) }}%
                        </small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="media-right">
              <div class="dropdown is-hoverable is-right">
                <div class="dropdown-trigger">
                  <button
                    class="button is-small is-ghost"
                    @click.stop
                  >
                    <span class="icon">
                      <i class="fas fa-ellipsis-v"></i>
                    </span>
                  </button>
                </div>
                <div class="dropdown-menu">
                  <div class="dropdown-content">
                    <a class="dropdown-item" @click.stop="openNote(result.note)">
                      <span class="icon">
                        <i class="fas fa-eye"></i>
                      </span>
                      <span>Open</span>
                    </a>
                    <a class="dropdown-item" @click.stop="editNote(result.note)">
                      <span class="icon">
                        <i class="fas fa-edit"></i>
                      </span>
                      <span>Edit</span>
                    </a>
                    <hr class="dropdown-divider">
                    <a class="dropdown-item" @click.stop="shareNote(result.note)">
                      <span class="icon">
                        <i class="fas fa-share"></i>
                      </span>
                      <span>Share</span>
                    </a>
                    <a class="dropdown-item" @click.stop="duplicateNote(result.note)">
                      <span class="icon">
                        <i class="fas fa-copy"></i>
                      </span>
                      <span>Duplicate</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMoreResults" class="has-text-centered mt-4">
        <button
          class="button is-primary is-outlined"
          :class="{ 'is-loading': isSearching }"
          @click="loadMoreResults"
          :disabled="isSearching"
        >
          <span class="icon">
            <i class="fas fa-plus"></i>
          </span>
          <span>Load More Results</span>
        </button>
      </div>
    </div>

    <!-- Empty State (no search query) -->
    <div v-if="!hasQuery && !isSearching" class="empty-state">
      <div class="has-text-centered py-6">
        <span class="icon is-large has-text-grey-light">
          <i class="fas fa-search fa-3x"></i>
        </span>
        <h3 class="title is-4 has-text-grey">Search your notes</h3>
        <p class="subtitle is-6 has-text-grey">
          Enter keywords, use filters, or try advanced search
        </p>

        <!-- Quick search options -->
        <div class="mt-4">
          <div class="buttons is-centered">
            <button class="button is-light" @click="searchByType('richtext')">
              <span class="icon">
                <i class="fas fa-font"></i>
              </span>
              <span>Rich Text Notes</span>
            </button>
            <button class="button is-light" @click="searchByType('markdown')">
              <span class="icon">
                <i class="fab fa-markdown"></i>
              </span>
              <span>Markdown Notes</span>
            </button>
            <button class="button is-light" @click="searchByType('kanban')">
              <span class="icon">
                <i class="fas fa-columns"></i>
              </span>
              <span>Kanban Boards</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useSearchStore } from '../../stores/search'
import type { Note } from '../../services/noteService'
import type { SearchResult } from '../../services/searchService'

interface Emits {
  (e: 'note-select', note: Note): void
  (e: 'tag-search', tag: string): void
  (e: 'clear-search'): void
  (e: 'share-note', note: Note): void
}

const emit = defineEmits<Emits>()

const router = useRouter()
const searchStore = useSearchStore()

// Local state
const sortBy = ref(searchStore.searchOptions.sortBy || 'relevance')
const sortOrder = ref(searchStore.searchOptions.sortOrder || 'desc')

// Computed
const searchResults = computed(() => searchStore.searchResults)
const hasResults = computed(() => searchStore.hasResults)
const hasQuery = computed(() => searchStore.hasQuery)
const isSearching = computed(() => searchStore.isSearching)
const hasMoreResults = computed(() => searchStore.hasMoreResults)
const searchSummary = computed(() => searchStore.searchSummary)
const searchSuggestions = computed(() => searchStore.suggestions.titles.slice(0, 3))

// Methods
const handleSortChange = async () => {
  searchStore.setSearchOptions({ sortBy: sortBy.value as any })
  if (hasQuery.value) {
    await searchStore.search()
  }
}

const toggleSortOrder = async () => {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
  searchStore.setSearchOptions({ sortOrder: sortOrder.value })
  if (hasQuery.value) {
    await searchStore.search()
  }
}

const loadMoreResults = async () => {
  await searchStore.loadMoreResults()
}

const openNote = (note: Note) => {
  emit('note-select', note)
  router.push(`/notes/${note.id}`)
}

const editNote = (note: Note) => {
  emit('note-select', note)
  router.push(`/notes/${note.id}/edit`)
}

const shareNote = (note: Note) => {
  emit('share-note', note)
}

const duplicateNote = (note: Note) => {
  // TODO: Implement duplicate functionality
  console.log('Duplicate note:', note.id)
}

const searchByTag = (tagName: string) => {
  emit('tag-search', tagName)
  searchStore.searchByTag(tagName)
}

const searchByType = (noteType: 'richtext' | 'markdown' | 'kanban') => {
  searchStore.searchByNoteType(noteType)
}

const applySuggestion = (suggestion: string) => {
  searchStore.setSearchQuery(suggestion)
  searchStore.search(suggestion)
}

const clearSearch = () => {
  emit('clear-search')
  searchStore.resetSearch()
}

const showAllNotes = () => {
  router.push('/notes')
}

const getNoteTypeIcon = (noteType: string): string => {
  switch (noteType) {
    case 'richtext':
      return 'fas fa-font'
    case 'markdown':
      return 'fab fa-markdown'
    case 'kanban':
      return 'fas fa-columns'
    default:
      return 'fas fa-file-alt'
  }
}

const getNoteTypeClass = (noteType: string): string => {
  switch (noteType) {
    case 'richtext':
      return 'has-text-info'
    case 'markdown':
      return 'has-text-success'
    case 'kanban':
      return 'has-text-warning'
    default:
      return 'has-text-grey'
  }
}

const getContentPreview = (content: string, noteType: string): string => {
  if (noteType === 'kanban') {
    try {
      const kanban = JSON.parse(content)
      const cardCount = kanban.columns?.reduce((total: number, col: any) => 
        total + (col.cards?.length || 0), 0) || 0
      return `Kanban board with ${kanban.columns?.length || 0} columns and ${cardCount} cards`
    } catch {
      return 'Kanban board'
    }
  }

  // Strip HTML and markdown, then truncate
  const plainText = content
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/[#*_`~\[\]()]/g, '') // Remove markdown formatting
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()

  return plainText.length > 150 ? plainText.substring(0, 150) + '...' : plainText
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return 'Today'
  } else if (diffDays === 1) {
    return 'Yesterday'
  } else if (diffDays < 7) {
    return `${diffDays} days ago`
  } else {
    return date.toLocaleDateString()
  }
}

// Watch for sort option changes from store
watch(() => searchStore.searchOptions.sortBy, (newSortBy) => {
  if (newSortBy) {
    sortBy.value = newSortBy
  }
})

watch(() => searchStore.searchOptions.sortOrder, (newSortOrder) => {
  if (newSortOrder) {
    sortOrder.value = newSortOrder
  }
})
</script>

<style scoped>
.search-results {
  min-height: 400px;
}

.result-item {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e0e0e0;
}

.result-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.content-preview {
  max-height: 3em;
  overflow: hidden;
  line-height: 1.5;
}

.result-metadata .level {
  margin-bottom: 0;
}

.result-metadata .level-item {
  margin-right: 1rem;
}

.result-metadata .level-item:last-child {
  margin-right: 0;
}

/* Highlight styling */
:deep(mark) {
  background-color: #ffeb3b;
  padding: 0.1em 0.2em;
  border-radius: 2px;
  font-weight: bold;
}

/* Tag hover effect */
.tags .tag {
  cursor: pointer;
  transition: background-color 0.2s;
}

.tags .tag:hover {
  background-color: #3273dc !important;
  color: white !important;
}

/* Empty state and no results styling */
.empty-state,
.no-results {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Dropdown menu styling */
.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Loading animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.result-item {
  animation: fadeIn 0.3s ease-out;
}

/* Responsive design */
@media (max-width: 768px) {
  .result-metadata .level {
    display: block;
  }
  
  .result-metadata .level-left,
  .result-metadata .level-right {
    margin-bottom: 0.5rem;
  }
  
  .content-preview {
    max-height: 4em;
  }
}
</style>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container" style="padding: 2rem;">
        <h1 class="title">Dropdown Test</h1>
        
        <!-- Test dropdown similar to NoteList -->
        <div class="note-card" style="border: 1px solid #e9ecef; padding: 1rem; margin: 1rem 0; display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h4>Test Note</h4>
                <p>This is a test note to verify dropdown functionality</p>
            </div>
            
            <!-- Note Actions Dropdown -->
            <div class="note-actions">
                <div class="dropdown is-right" id="dropdown-1">
                    <div class="dropdown-trigger">
                        <button class="button is-ghost is-small" onclick="toggleDropdown('dropdown-1')">
                            <span class="icon">
                                <i class="fas fa-ellipsis-v"></i>
                            </span>
                        </button>
                    </div>
                    <div class="dropdown-menu">
                        <div class="dropdown-content">
                            <a class="dropdown-item" href="#" onclick="alert('Favorite clicked')">
                                <span class="icon">
                                    <i class="far fa-star"></i>
                                </span>
                                <span>Add to Favorites</span>
                            </a>
                            <a class="dropdown-item" href="#" onclick="alert('Share clicked')">
                                <span class="icon">
                                    <i class="fas fa-share-alt"></i>
                                </span>
                                <span>Share</span>
                            </a>
                            <a class="dropdown-item" href="#" onclick="alert('Duplicate clicked')">
                                <span class="icon">
                                    <i class="fas fa-copy"></i>
                                </span>
                                <span>Duplicate</span>
                            </a>
                            <hr class="dropdown-divider">
                            <a class="dropdown-item has-text-danger" href="#" onclick="alert('Delete clicked')">
                                <span class="icon">
                                    <i class="fas fa-trash"></i>
                                </span>
                                <span>Delete</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Another test dropdown -->
        <div class="note-card" style="border: 1px solid #e9ecef; padding: 1rem; margin: 1rem 0; display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h4>Another Test Note</h4>
                <p>This is another test note</p>
            </div>
            
            <div class="note-actions">
                <div class="dropdown is-right" id="dropdown-2">
                    <div class="dropdown-trigger">
                        <button class="button is-ghost is-small" onclick="toggleDropdown('dropdown-2')">
                            <span class="icon">
                                <i class="fas fa-ellipsis-v"></i>
                            </span>
                        </button>
                    </div>
                    <div class="dropdown-menu">
                        <div class="dropdown-content">
                            <a class="dropdown-item" href="#" onclick="alert('Favorite clicked')">
                                <span class="icon">
                                    <i class="far fa-star"></i>
                                </span>
                                <span>Add to Favorites</span>
                            </a>
                            <a class="dropdown-item" href="#" onclick="alert('Share clicked')">
                                <span class="icon">
                                    <i class="fas fa-share-alt"></i>
                                </span>
                                <span>Share</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let activeDropdown = null;

        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            
            // Close any other open dropdowns
            if (activeDropdown && activeDropdown !== dropdownId) {
                const prevDropdown = document.getElementById(activeDropdown);
                if (prevDropdown) {
                    prevDropdown.classList.remove('is-active');
                }
            }
            
            // Toggle current dropdown
            if (dropdown.classList.contains('is-active')) {
                dropdown.classList.remove('is-active');
                activeDropdown = null;
            } else {
                dropdown.classList.add('is-active');
                activeDropdown = dropdownId;
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                // Close all dropdowns
                document.querySelectorAll('.dropdown.is-active').forEach(dropdown => {
                    dropdown.classList.remove('is-active');
                });
                activeDropdown = null;
            }
        });

        // Prevent dropdown from closing when clicking inside dropdown content
        document.addEventListener('click', function(event) {
            if (event.target.closest('.dropdown-content')) {
                event.stopPropagation();
            }
        });
    </script>

    <style>
        .dropdown-menu {
            z-index: 10000 !important;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15) !important;
        }

        .dropdown.is-active .dropdown-menu {
            display: block !important;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
    </style>
</body>
</html>
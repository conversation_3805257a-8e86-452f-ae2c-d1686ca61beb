import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

// Critical CSS is now inlined in HTML - no need to import

// Load remaining styles immediately after critical path
import './styles/main.css'
// New organized CSS architecture with theme support

// Full app initialization - loaded after minimal app
// This file is renamed to main-full.ts and loaded dynamically

// Performance monitoring with Core Web Vitals tracking
const startTime = performance.now()

// Initialize critical resource preloading
import('./utils/criticalPreload').then(({ initializeCriticalPreload }) => {
  initializeCriticalPreload()
}).catch(error => {
  console.warn('Failed to initialize critical preloading:', error)
})

// Initialize FontAwesome optimization
import('./utils/FontAwesomeOptimizer').then(({ fontAwesomeOptimizer }) => {
  fontAwesomeOptimizer.initialize().then(() => {
    console.log('✅ FontAwesome optimization initialized')
  }).catch(error => {
    console.warn('⚠️ FontAwesome optimization failed:', error)
  })
}).catch(error => {
  console.warn('Failed to load FontAwesome optimizer:', error)
})

// Initialize performance monitoring with comprehensive error handling
let performanceMonitor: any = null
let performanceMonitoringFailed = false

const initPerformanceMonitoring = async () => {
  try {
    console.log('🔄 Initializing performance monitoring...')
    
    // Use timeout to prevent performance monitoring from blocking app startup
    const performanceModule = await Promise.race([
      import('./composables/usePerformance'),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Performance monitoring import timeout after 2000ms')), 2000)
      )
    ]) as any
    
    if (!performanceModule || !performanceModule.usePerformance) {
      throw new Error('Performance monitoring module not available')
    }
    
    // Initialize with error handling for each step
    try {
      performanceMonitor = performanceModule.usePerformance({
        initTime: 600, // Target initialization time
        fcp: 1000,     // First Contentful Paint target
        lcp: 1500,     // Largest Contentful Paint target
        tti: 2000,     // Time to Interactive target
        cls: 0.05,     // Cumulative Layout Shift target
        fid: 100       // First Input Delay target
      })
      
      console.log('✅ Performance monitor instance created')
    } catch (instanceError) {
      console.warn('⚠️ Failed to create performance monitor instance:', instanceError)
      throw instanceError
    }
    
    // Mark initialization start with error handling
    try {
      performanceMonitor.markInitStart()
      console.log('✅ Performance monitoring start marker set')
    } catch (markError) {
      console.warn('⚠️ Failed to set performance start marker:', markError)
      // Continue without start marker - not critical
    }
    
    // Start monitoring manually since we're not in a component context
    try {
      performanceMonitor.startMonitoring()
      console.log('✅ Performance monitoring started manually')
    } catch (startError) {
      console.warn('⚠️ Failed to start performance monitoring:', startError)
      // Continue without monitoring - not critical
    }

    // Enable real-time monitoring with error handling
    try {
      performanceMonitor.enableRealTimeMonitoring()
      console.log('✅ Real-time performance monitoring enabled')
    } catch (monitoringError) {
      console.warn('⚠️ Failed to enable real-time monitoring:', monitoringError)
      // Continue without real-time monitoring - basic monitoring should still work
    }
    
    console.log('🚀 Performance monitoring initialized successfully with Core Web Vitals tracking')
    
  } catch (error) {
    performanceMonitoringFailed = true
    console.warn('⚠️ Performance monitoring initialization failed:', error)
    
    // Log detailed error for debugging
    if (import.meta.env.MODE === 'development') {
      console.error('Performance monitoring error details:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      })
    }
    
    // Create minimal fallback performance monitor
    performanceMonitor = createFallbackPerformanceMonitor()
    
    // Dispatch event for error tracking
    window.dispatchEvent(new CustomEvent('performance-monitoring-failed', {
      detail: { 
        error: error instanceof Error ? error.message : String(error),
        fallbackEnabled: true
      }
    }))
  }
}

// Create fallback performance monitor that doesn't block the app
function createFallbackPerformanceMonitor() {
  console.log('📝 Creating fallback performance monitor with minimal functionality')
  
  return {
    markInitStart: () => {
      try {
        performance.mark('app-init-start-fallback')
      } catch (error) {
        // Silently fail - performance marks are not critical
      }
    },
    markStoreInitComplete: () => {
      try {
        performance.mark('store-init-complete-fallback')
      } catch (error) {
        // Silently fail
      }
    },
    markDOMReady: () => {
      try {
        performance.mark('dom-ready-fallback')
      } catch (error) {
        // Silently fail
      }
    },
    markInitEnd: () => {
      try {
        performance.mark('app-init-end-fallback')
        console.log('📊 Basic performance tracking completed (fallback mode)')
      } catch (error) {
        // Silently fail
      }
    },
    collectAndReportMetrics: () => {
      try {
        const initTime = performance.now()
        console.log(`📊 App initialization completed in ${initTime.toFixed(2)}ms (fallback measurement)`)
        return { initTime }
      } catch (error) {
        console.warn('⚠️ Failed to collect fallback metrics:', error)
        return {}
      }
    },
    enableRealTimeMonitoring: () => {
      // No-op in fallback mode
      console.log('📝 Real-time monitoring not available in fallback mode')
    }
  }
}

// Safe performance monitoring wrapper functions
function safeMarkStoreInitComplete() {
  try {
    if (performanceMonitor && !performanceMonitoringFailed) {
      performanceMonitor.markStoreInitComplete()
    }
  } catch (error) {
    console.warn('⚠️ Failed to mark store init complete:', error)
    // Don't let performance monitoring errors block the app
  }
}

function safeMarkDOMReady() {
  try {
    if (performanceMonitor && !performanceMonitoringFailed) {
      performanceMonitor.markDOMReady()
    }
  } catch (error) {
    console.warn('⚠️ Failed to mark DOM ready:', error)
    // Don't let performance monitoring errors block the app
  }
}

function safeMarkInitEnd() {
  try {
    if (performanceMonitor && !performanceMonitoringFailed) {
      performanceMonitor.markInitEnd()
    }
  } catch (error) {
    console.warn('⚠️ Failed to mark init end:', error)
    // Don't let performance monitoring errors block the app
  }
}

function safeCollectAndReportMetrics() {
  try {
    if (performanceMonitor && !performanceMonitoringFailed) {
      return performanceMonitor.collectAndReportMetrics()
    } else if (performanceMonitor) {
      // Use fallback metrics collection
      return performanceMonitor.collectAndReportMetrics()
    }
  } catch (error) {
    console.warn('⚠️ Failed to collect performance metrics:', error)
    // Return empty metrics to prevent errors
    return {}
  }
}

// Initialize performance monitoring immediately
initPerformanceMonitoring()

// Initialize notification service for user feedback on loading failures
import('./services/notificationService').then(({ notificationService }) => {
  // Notification service will automatically set up event listeners
  console.log('📢 Notification service initialized for user feedback')
}).catch(error => {
  console.warn('⚠️ Failed to initialize notification service:', error)
  // Continue without notifications - not critical for app functionality
})

// Export function to mount full app
export async function mountFullApp() {
  // Get the app element and unmount any existing Vue app
  const appElement = document.getElementById('app')
  if (appElement && appElement.__vue_app__) {
    console.log('🔄 Unmounting existing app...')
    appElement.__vue_app__.unmount()
  }

  // Create app instance
  const app = createApp(App)
  const pinia = createPinia()

  app.use(pinia)

  // Add router with error handling and ensure it's ready
  try {
    app.use(router)
    
    // Wait for router to be ready before mounting
    await router.isReady()
    console.log('✅ Router is ready')
  } catch (error) {
    console.error('Router initialization error:', error)
    // Continue without router - components will handle gracefully
  }

  // Mount full app, replacing minimal app
  app.mount('#app')
  
  console.log('🚀 Full app mounted successfully')
}

// Defer non-critical CSS loading to improve initialization time
function loadNonCriticalCSS() {
  // This function is kept for compatibility but CSS is now loaded above
  console.log('Non-critical CSS loading handled by direct imports')
}

// Schedule non-critical CSS loading during idle time
if ('requestIdleCallback' in window) {
  window.requestIdleCallback(loadNonCriticalCSS, { timeout: 2000 })
} else {
  setTimeout(loadNonCriticalCSS, 100)
}

// Error logging and tracking system
interface StoreError {
  storeName: string
  errorType: 'import' | 'initialization' | 'timeout' | 'network' | 'general' | 'fallback' | 'default-init' | 'startup' | 'load' | 'runtime' | 'setup' | 'execution' | 'requestIdleCallback'
  error: Error
  timestamp: number
  context?: any
}

const storeErrors: StoreError[] = []

function logStoreError(storeName: string, errorType: StoreError['errorType'], error: any, context?: any) {
  const storeError: StoreError = {
    storeName,
    errorType,
    error: error instanceof Error ? error : new Error(String(error)),
    timestamp: Date.now(),
    context
  }
  
  storeErrors.push(storeError)
  
  // Keep only last 50 errors to prevent memory issues
  if (storeErrors.length > 50) {
    storeErrors.splice(0, storeErrors.length - 50)
  }
  
  // Log to console with detailed information
  console.error(`🚨 Store Error [${storeName}:${errorType}]:`, {
    message: storeError.error.message,
    stack: storeError.error.stack,
    timestamp: new Date(storeError.timestamp).toISOString(),
    context
  })
  
  // Dispatch event for error tracking services
  window.dispatchEvent(new CustomEvent('store-error', {
    detail: storeError
  }))
}

// Auth-specific error handlers
async function handleAuthTimeout(authStore: any) {
  try {
    console.log('🔄 Handling auth timeout - checking for cached credentials')
    
    // Try to use cached user data if available
    const cachedToken = localStorage.getItem('auth_token')
    const cachedUser = localStorage.getItem('cached_user')
    
    if (cachedToken && cachedUser) {
      console.log('📋 Found cached credentials - continuing with cached auth state')
      // The auth store should handle this internally
      return
    }
    
    console.log('🚫 No cached credentials available - initializing guest mode')
    await initializeGuestMode()
  } catch (error) {
    console.error('❌ Failed to handle auth timeout:', error)
    logStoreError('auth', 'timeout', error)
    await initializeGuestMode()
  }
}

async function handleNetworkError(authStore: any) {
  try {
    console.log('🌐 Handling network error - enabling offline mode')
    
    // Check if we have cached credentials to work offline
    const cachedToken = localStorage.getItem('auth_token')
    if (cachedToken) {
      console.log('📱 Offline mode enabled with cached credentials')
      
      // Dispatch offline mode event
      window.dispatchEvent(new CustomEvent('app-offline-mode', {
        detail: { reason: 'network-error', hasAuth: true }
      }))
      return
    }
    
    console.log('📱 Offline mode enabled without authentication')
    window.dispatchEvent(new CustomEvent('app-offline-mode', {
      detail: { reason: 'network-error', hasAuth: false }
    }))
    
    await initializeGuestMode()
  } catch (error) {
    console.error('❌ Failed to handle network error:', error)
    logStoreError('auth', 'network', error)
    await initializeGuestMode()
  }
}

// Settings-specific error handlers
async function handleSettingsFailure(error: any) {
  try {
    console.log('⚙️ Handling settings failure - applying default settings')
    
    // Try to load theme from authStore first
    let themeApplied = false
    try {
      const { useAuthStore } = await import('./stores/auth')
      const authStore = useAuthStore()
      const savedTheme = authStore.loadThemePreference()
      
      if (savedTheme) {
        const html = document.documentElement
        html.classList.remove('dark', 'light')
        
        if (savedTheme === 'dark') {
          html.classList.add('dark')
          themeApplied = true
        } else if (savedTheme === 'light') {
          html.classList.add('light')
          themeApplied = true
        } else {
          // Auto mode
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
          html.classList.add(prefersDark ? 'dark' : 'light')
          themeApplied = true
        }
      }
    } catch (error) {
      console.warn('Failed to load theme from authStore:', error)
    }
    
    // If no theme was applied from authStore, apply system theme preference
    if (!themeApplied) {
      const html = document.documentElement
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      
      if (prefersDark) {
        html.classList.add('dark')
        html.classList.remove('light')
      } else {
        html.classList.add('light')
        html.classList.remove('dark')
      }
    }
    
    console.log('🎨 Default theme applied based on system preference or saved preference')
    
    // Dispatch event for UI components to handle settings failure
    window.dispatchEvent(new CustomEvent('settings-failure', {
      detail: { error: error.message, defaultsApplied: true }
    }))
    
  } catch (fallbackError) {
    console.error('❌ Failed to apply default settings:', fallbackError)
    logStoreError('settings', 'fallback', fallbackError)
  }
}

async function initializeDefaultSettings() {
  try {
    console.log('⚙️ Initializing default settings')
    
    // Try to load theme from authStore first
    let themeApplied = false
    try {
      const { useAuthStore } = await import('./stores/auth')
      const authStore = useAuthStore()
      const savedTheme = authStore.loadThemePreference()
      
      if (savedTheme) {
        const html = document.documentElement
        html.classList.remove('dark', 'light')
        
        if (savedTheme === 'dark') {
          html.classList.add('dark')
          themeApplied = true
        } else if (savedTheme === 'light') {
          html.classList.add('light')
          themeApplied = true
        } else {
          // Auto mode
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
          html.classList.add(prefersDark ? 'dark' : 'light')
          themeApplied = true
        }
      }
    } catch (error) {
      console.warn('Failed to load theme from authStore:', error)
    }
    
    // If no theme was applied from authStore, apply system theme preference
    if (!themeApplied) {
      const html = document.documentElement
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      
      if (prefersDark) {
        html.classList.add('dark')
      } else {
        html.classList.add('light')
      }
    }
    
    // Store default preferences in localStorage as fallback
    const defaultPreferences = {
      theme: 'auto',
      language: 'en',
      timezone: 'UTC',
      autoSaveInterval: 30000,
      notifications: {
        email: true,
        push: true,
        mentions: true
      }
    }
    
    localStorage.setItem('default_preferences', JSON.stringify(defaultPreferences))
    
    console.log('✅ Default settings initialized successfully')
    
    // Dispatch event for components that depend on settings
    window.dispatchEvent(new CustomEvent('default-settings-ready', {
      detail: { preferences: defaultPreferences }
    }))
    
  } catch (error) {
    console.error('❌ Failed to initialize default settings:', error)
    logStoreError('settings', 'default-init', error)
  }
}

// Get store error summary for debugging
function getStoreErrorSummary() {
  const errorsByStore = storeErrors.reduce((acc, error) => {
    if (!acc[error.storeName]) {
      acc[error.storeName] = []
    }
    acc[error.storeName].push({
      type: error.errorType,
      message: error.error.message,
      timestamp: error.timestamp
    })
    return acc
  }, {} as Record<string, any[]>)
  
  return {
    totalErrors: storeErrors.length,
    errorsByStore,
    recentErrors: storeErrors.slice(-10)
  }
}

// Expose error summary for debugging
if (import.meta.env.MODE === 'development') {
  (window as any).getStoreErrorSummary = getStoreErrorSummary
}

// Three-phase progressive loading system
async function initializeApp() {
  try {
    // PHASE 1: Critical auth-only initialization (0-200ms target)
    await initializeCriticalPhase()
    
    // PHASE 2: Non-critical services with requestIdleCallback (200-600ms target)
    scheduleSecondaryPhase()
    
    // PHASE 3: Background services with setTimeout delay (600ms+ target)
    scheduleBackgroundPhase()
    
  } catch (error) {
    console.error('Failed to initialize app:', error)
    const errorObj = error instanceof Error ? error : new Error(String(error))
    logStoreError('app', 'general', errorObj)
    handleInitializationFailure(errorObj)
  }
}

// Phase 1: Critical auth-only initialization
async function initializeCriticalPhase() {
  const phaseStartTime = performance.now()
  
  try {
    // Skip auth initialization if not needed for initial render (ultra-fast path)
    const isAuthRequired = window.location.pathname.includes('/auth') || 
                          window.location.pathname.includes('/login') ||
                          localStorage.getItem('auth_token')
    
    if (!isAuthRequired) {
      console.log('🚀 Skipping auth initialization for faster startup')
      safeMarkStoreInitComplete()
      const phaseTime = performance.now() - phaseStartTime
      console.log(`Phase 1 (Critical - Fast Path) completed in ${phaseTime.toFixed(2)}ms`)
      return
    }
    
    // Import only critical auth store with aggressive timeout
    let authStore = null
    
    // Use lazy loading service with ultra-fast timeout
    const { lazyLoadingService } = await Promise.race([
      import('./services/lazyLoadingService'),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Lazy loading service timeout after 100ms')), 100)
      )
    ]) as any
    
    const authResult = await Promise.race([
      lazyLoadingService.loadAuthStore(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Auth store load timeout after 150ms')), 150)
      )
    ]) as any
    
    if (authResult.success && authResult.module) {
      authStore = authResult.module.useAuthStore()
      
      if (authResult.fallbackUsed) {
        console.warn('⚠️ Auth store loaded using fallback mechanism')
        // Dispatch event for UI notification
        window.dispatchEvent(new CustomEvent('auth-fallback-mode', {
          detail: { reason: 'import-failure', attempts: authResult.attempts }
        }))
      } else {
        console.log('✅ Auth store imported successfully')
      }
    } else {
      console.error('❌ Failed to load auth store:', authResult.error?.message)
      logStoreError('auth', 'import', authResult.error || new Error('Unknown import error'))
      await initializeGuestMode()
      return
    }
    
    // Initialize auth with timeout and detailed error handling
    try {
      await Promise.race([
        authStore.initializeAuth(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Auth initialization timeout after 3000ms')), 3000)
        )
      ])
      
      console.log('✅ Auth store initialized successfully')
      
      // Mark store initialization complete with error handling
      safeMarkStoreInitComplete()
      
    } catch (initError) {
      console.warn('⚠️ Auth initialization failed, implementing graceful degradation:', initError)
      logStoreError('auth', 'initialization', initError)
      
      // Determine error type and handle accordingly
      const errorMessage = initError instanceof Error ? initError.message : String(initError)
      if (errorMessage.includes('timeout')) {
        console.log('🔄 Auth timeout detected - continuing with cached credentials if available')
        await handleAuthTimeout(authStore)
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        console.log('🌐 Network error detected - enabling offline mode')
        await handleNetworkError(authStore)
      } else {
        console.log('🚫 Auth service error - initializing guest mode')
        await initializeGuestMode()
      }
    }
    
  } catch (phaseError) {
    console.error('❌ Critical phase initialization failed:', phaseError)
    logStoreError('critical-phase', 'general', phaseError)
    
    // Ensure app continues even if critical phase fails
    await initializeGuestMode()
  }
  
  const phaseTime = performance.now() - phaseStartTime
  if (import.meta.env.MODE === 'development') {
    console.log(`Phase 1 (Critical) completed in ${phaseTime.toFixed(2)}ms`)
  }
}

// Phase 2: Non-critical services using optimized requestIdleCallback
function scheduleSecondaryPhase() {
  const scheduleSecondary = () => {
    const phaseStartTime = performance.now()
    
    // Define callback function outside to be accessible in both then and catch blocks
    const callback = async (deadline: IdleDeadline) => {
      try {
        // Import non-critical stores and services using lazy loading service with fallbacks
        const { lazyLoadingService } = await import('./services/lazyLoadingService')
        
        // Load modules in parallel with individual fallback handling
        const moduleResults = await lazyLoadingService.loadModules({
          settings: () => import('./stores/settings'),
          cacheService: () => import('./services/cacheService'),
          performanceService: () => import('./services/performanceService')
        })
        
        // Process each module result with detailed error handling and fallback support
        let useSettingsStore = null
        let cacheService = null
        let performanceService = null
        
        // Handle settings store result
        const settingsResult = moduleResults.settings
        if (settingsResult.success && settingsResult.module) {
          useSettingsStore = settingsResult.module.useSettingsStore
          
          if (settingsResult.fallbackUsed) {
            console.warn('⚠️ Settings store loaded using fallback mechanism')
            window.dispatchEvent(new CustomEvent('settings-fallback-mode', {
              detail: { attempts: settingsResult.attempts, error: settingsResult.error?.message }
            }))
          } else {
            console.log('✅ Settings store imported successfully')
          }
        } else {
          console.warn('⚠️ Failed to load settings store:', settingsResult.error?.message)
          logStoreError('settings', 'import', settingsResult.error || new Error('Unknown import error'))
        }
        
        // Handle cache service result
        const cacheResult = moduleResults.cacheService
        if (cacheResult.success && cacheResult.module) {
          cacheService = cacheResult.module.cacheService
          
          if (cacheResult.fallbackUsed) {
            console.warn('⚠️ Cache service loaded using fallback mechanism')
            window.dispatchEvent(new CustomEvent('cache-fallback-mode', {
              detail: { attempts: cacheResult.attempts, error: cacheResult.error?.message }
            }))
          } else {
            console.log('✅ Cache service imported successfully')
          }
        } else {
          console.warn('⚠️ Failed to load cache service:', cacheResult.error?.message)
          logStoreError('cache', 'import', cacheResult.error || new Error('Unknown import error'))
        }
        
        // Handle performance service result
        const performanceResult = moduleResults.performanceService
        if (performanceResult.success && performanceResult.module) {
          performanceService = performanceResult.module.performanceService
          
          if (performanceResult.fallbackUsed) {
            console.warn('⚠️ Performance service loaded using fallback mechanism')
            window.dispatchEvent(new CustomEvent('performance-fallback-mode', {
              detail: { attempts: performanceResult.attempts, error: performanceResult.error?.message }
            }))
          } else {
            console.log('✅ Performance service imported successfully')
          }
        } else {
          console.warn('⚠️ Failed to load performance service:', performanceResult.error?.message)
          logStoreError('performance', 'import', performanceResult.error || new Error('Unknown import error'))
        }
        
        // Initialize settings store with comprehensive error handling
        if (useSettingsStore) {
          try {
            const settingsStore = useSettingsStore()
            await Promise.race([
              settingsStore.initializeSettings(),
              new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Settings initialization timeout after 2000ms')), 2000)
              )
            ])
            console.log('✅ Settings store initialized successfully')
          } catch (settingsError) {
            console.warn('⚠️ Settings store initialization failed:', settingsError)
            logStoreError('settings', 'initialization', settingsError)
            
            // Graceful degradation for settings
            await handleSettingsFailure(settingsError)
          }
        } else {
          console.warn('⚠️ Settings store not available - using default settings')
          await initializeDefaultSettings()
        }
        
        // Initialize cache service with error handling
        if (cacheService) {
          try {
            await Promise.race([
              cacheService.preloadCriticalData?.(),
              new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Cache preload timeout after 1500ms')), 1500)
              )
            ])
            console.log('✅ Cache service initialized successfully')
          } catch (cacheError) {
            console.warn('⚠️ Cache service initialization failed:', cacheError)
            logStoreError('cache', 'initialization', cacheError)
            
            // Continue without cache - app should work without it
            console.log('📝 Continuing without cache service - performance may be reduced')
          }
        } else {
          console.warn('⚠️ Cache service not available - continuing without caching')
        }
        
        // Initialize performance service with error handling
        if (performanceService) {
          try {
            // Performance service should not block app initialization
            performanceService.trackUserInteraction?.('app-init', 'application', startTime)
            console.log('✅ Performance service initialized successfully')
          } catch (perfError) {
            console.warn('⚠️ Performance service initialization failed:', perfError)
            logStoreError('performance', 'initialization', perfError)
            // Performance tracking failure should not affect app functionality
          }
        }
        
        // Mark DOM ready and track initialization completion with error handling
        safeMarkDOMReady()
        
        // Track app initialization time
        const totalInitTime = performance.now() - startTime
        
        // Setup network and navigation handlers with error handling
        try {
          setupNetworkHandlers()
          setupNavigationHandlers()
          console.log('✅ Event handlers initialized successfully')
        } catch (handlerError) {
          console.warn('⚠️ Event handler setup failed:', handlerError)
          logStoreError('event-handlers', 'initialization', handlerError)
          // Continue without event handlers - core functionality should work
        }
        
        const phaseTime = performance.now() - phaseStartTime
        if (import.meta.env.MODE === 'development') {
          console.log(`Phase 2 (Secondary) completed in ${phaseTime.toFixed(2)}ms`)
          console.log(`Total app initialization: ${totalInitTime.toFixed(2)}ms`)
        }
        
      } catch (phaseError) {
        console.error('❌ Secondary phase initialization failed:', phaseError)
        logStoreError('secondary-phase', 'general', phaseError)
        
        // Ensure app continues even if secondary phase fails
        console.log('🔄 Continuing with minimal functionality due to secondary phase failure')
      }
    }
    
    // Import optimized scheduling utilities
    import('./utils/optimizedScheduling').then(({ scheduleIdleTask }) => {
        // Use optimized requestIdleCallback with better timing
        try {
          scheduleIdleTask(callback, { 
            timeout: 800, // Reduced from 1000ms for faster secondary phase
            priority: 'normal'
          })
        } catch (schedulingError) {
          console.error('❌ Failed to schedule secondary phase:', schedulingError)
          logStoreError('scheduling', 'requestIdleCallback', schedulingError)
          
          // Fallback to immediate execution
          setTimeout(() => callback({ didTimeout: true, timeRemaining: () => 0 } as IdleDeadline), 100)
        }
      }).catch(error => {
        console.warn('⚠️ Failed to load optimized scheduling, using fallback:', error)
        
        // Fallback to basic requestIdleCallback
        try {
          if (window.requestIdleCallback) {
            window.requestIdleCallback(callback, { timeout: 800 })
          } else {
            setTimeout(() => callback({ didTimeout: false, timeRemaining: () => 50 } as IdleDeadline), 0)
          }
        } catch (fallbackError) {
          console.error('❌ Fallback scheduling failed:', fallbackError)
          setTimeout(() => callback({ didTimeout: true, timeRemaining: () => 0 } as IdleDeadline), 100)
        }
      })
  }
  
  scheduleSecondary()
}

// Phase 3: Background services with setTimeout delay
function scheduleBackgroundPhase() {
  setTimeout(async () => {
    const phaseStartTime = performance.now()
    
    try {
      console.log('🔄 Starting background phase initialization...')
      
      // Import and initialize background services with individual error handling
      const backgroundTasks = []
      
      // Use lazy loading service for background modules
      const { lazyLoadingService } = await import('./services/lazyLoadingService')
      
      // Additional performance monitoring with lazy loading and fallback
      backgroundTasks.push(
        lazyLoadingService.loadPerformanceService()
          .then((result) => {
            if (result.success && result.module) {
              try {
                const performanceService = result.module.performanceService
                // Note: startContinuousMonitoring method may not exist in current implementation
                const monitoringResult = performanceService?.exportPerformanceData?.()
                
                if (result.fallbackUsed) {
                  console.warn('⚠️ Performance monitoring using fallback - limited functionality')
                } else {
                  console.log('✅ Performance monitoring service started')
                }
                
                return monitoringResult
              } catch (error) {
                console.warn('⚠️ Performance monitoring startup failed:', error)
                logStoreError('performance', 'startup', error)
                return null
              }
            } else {
              console.warn('⚠️ Performance monitoring service unavailable:', result.error?.message)
              logStoreError('performance', 'load', result.error || new Error('Load failed'))
              return null
            }
          })
      )
      
      // Deferred service worker registration with lazy loading and comprehensive error handling
      if (import.meta.env.PROD) {
        backgroundTasks.push(
          lazyLoadingService.loadServiceWorker()
            .then((result) => {
              if (result.success && result.module) {
                try {
                  const { registerServiceWorker } = result.module
                  
                  return registerServiceWorker({
                    onUpdate: () => {
                      console.log('🔄 New app version available')
                      // Dispatch event for UI notification
                      window.dispatchEvent(new CustomEvent('sw-update-available'))
                    },
                    onSuccess: () => {
                      console.log('✅ App is ready for offline use')
                      window.dispatchEvent(new CustomEvent('sw-ready'))
                    },
                    onError: (error: any) => {
                      console.warn('⚠️ Service worker error:', error)
                      logStoreError('service-worker', 'runtime', error)
                    }
                  })
                } catch (error) {
                  console.warn('⚠️ Service worker registration setup failed:', error)
                  logStoreError('service-worker', 'setup', error)
                  return null
                }
              } else {
                console.warn('⚠️ Service worker import failed:', result.error?.message)
                logStoreError('service-worker', 'import', result.error || new Error('Import failed'))
                
                // Dispatch event to inform app that SW is not available
                window.dispatchEvent(new CustomEvent('sw-unavailable', {
                  detail: { 
                    reason: 'import-failed', 
                    error: result.error?.message,
                    attempts: result.attempts
                  }
                }))
                return null
              }
            })
        )
      } else {
        console.log('📝 Service worker registration skipped in development mode')
      }
      
      // Analytics service (if available) - graceful handling if not implemented
      backgroundTasks.push(
        import('./services/analyticsService')
          .then(({ analyticsService }) => {
            try {
              const result = analyticsService?.initialize?.()
              console.log('✅ Analytics service initialized')
              return result
            } catch (error) {
              console.warn('⚠️ Analytics service initialization failed:', error)
              logStoreError('analytics', 'initialization', error)
              return null
            }
          })
          .catch(error => {
            // Analytics service might not exist - this is acceptable
            console.log('📝 Analytics service not available (this is optional)')
            return null
          })
      )
      
      // Wait for all background tasks with timeout and detailed results
      const results = await Promise.race([
        Promise.allSettled(backgroundTasks),
        new Promise<PromiseSettledResult<any>[]>(resolve => 
          setTimeout(() => {
            console.warn('⏰ Background tasks timeout after 2000ms')
            resolve([])
          }, 2000)
        )
      ])
      
      // Log results of background tasks
      if (results.length > 0) {
        results.forEach((result, index) => {
          const taskNames = ['performance-monitoring', 'service-worker', 'analytics']
          const taskName = taskNames[index] || `task-${index}`
          
          if (result.status === 'fulfilled') {
            console.log(`✅ Background task ${taskName} completed successfully`)
          } else {
            console.warn(`⚠️ Background task ${taskName} failed:`, result.reason)
            logStoreError('background-task', 'execution', result.reason, { taskName })
          }
        })
      }
      
      const phaseTime = performance.now() - phaseStartTime
      if (import.meta.env.MODE === 'development') {
        console.log(`Phase 3 (Background) completed in ${phaseTime.toFixed(2)}ms`)
      }
      
      // Dispatch event to signal background initialization complete
      window.dispatchEvent(new CustomEvent('background-init-complete', {
        detail: { 
          duration: phaseTime,
          tasksCompleted: results.filter(r => r.status === 'fulfilled').length,
          tasksFailed: results.filter(r => r.status === 'rejected').length
        }
      }))
      
    } catch (phaseError) {
      console.error('❌ Background phase initialization failed:', phaseError)
      logStoreError('background-phase', 'general', phaseError)
      
      // Background phase failure should not affect app functionality
      console.log('🔄 App continues to function despite background phase failure')
      
      // Dispatch event to inform about background failure
      window.dispatchEvent(new CustomEvent('background-init-failed', {
        detail: { error: phaseError instanceof Error ? phaseError.message : String(phaseError) }
      }))
    }
  }, 1000) // 1 second delay for background services
}

// Initialize guest mode when auth fails or times out
function initializeGuestMode() {
  console.warn('Initializing app in guest mode due to auth failure/timeout')
  
  // Clear any existing auth tokens
  localStorage.removeItem('auth_token')
  localStorage.removeItem('refresh_token')
  
  // Dispatch event for UI components to handle guest mode
  window.dispatchEvent(new CustomEvent('guest-mode-initialized', {
    detail: { reason: 'auth-timeout-or-failure' }
  }))
  
  return Promise.resolve() // Return resolved promise to continue initialization
}

// Handle initialization failures gracefully
function handleInitializationFailure(error: Error) {
  console.warn('⚠️ App running with reduced functionality due to initialization failure')
  
  // Log detailed error information
  logStoreError('app-initialization', 'general', error)
  
  // Show user notification about reduced functionality
  if (import.meta.env.MODE === 'development') {
    console.error('🔍 Initialization error details:', {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    })
  }
  
  // Try to recover with minimal functionality
  try {
    console.log('🔄 Attempting to recover with minimal functionality...')
    
    // Ensure basic theme is applied
    const html = document.documentElement
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    html.classList.add(prefersDark ? 'dark' : 'light')
    
    // Initialize guest mode as fallback
    initializeGuestMode()
    
    // Apply default settings
    initializeDefaultSettings()
    
    console.log('✅ Minimal functionality recovery completed')
    
  } catch (recoveryError) {
    console.error('❌ Failed to recover with minimal functionality:', recoveryError)
    logStoreError('app-recovery', 'general', recoveryError)
  }
  
  // Dispatch comprehensive event for UI components to handle
  window.dispatchEvent(new CustomEvent('app-init-failure', { 
    detail: { 
      error: error.message,
      timestamp: Date.now(),
      recoveryAttempted: true,
      errorSummary: getStoreErrorSummary()
    } 
  }))
}

// Network event handlers
function setupNetworkHandlers() {
  window.addEventListener('online', () => {
    if (import.meta.env.MODE === 'development') {
      console.log('App is back online')
    }
    // Trigger cache sync if needed
    import('./services/cacheService').then(({ cacheService }) => {
      // Note: syncOfflineChanges method may not exist in current implementation
      console.log('Cache service back online')
    })
  })

  window.addEventListener('offline', () => {
    if (import.meta.env.MODE === 'development') {
      console.log('App is now offline')
    }
  })

  // Handle service worker updates
  window.addEventListener('sw-update-available', () => {
    if (import.meta.env.MODE === 'development') {
      console.log('Service worker update available')
    }
    // Show update notification to user
  })
}

// Navigation event handlers for store management
function setupNavigationHandlers() {
  // Listen for navigation events to manage store state
  window.addEventListener('popstate', () => {
    if (import.meta.env.MODE === 'development') {
      console.log('Navigation detected, checking store state...')
    }
  })

  // Listen for route changes from Vue Router
  window.addEventListener('router-navigation', () => {
    if (import.meta.env.MODE === 'development') {
      console.log('Router navigation detected, checking store state...')
    }
  })

  // Listen for user authentication events
  window.addEventListener('user-logged-in', (event) => {
    if (import.meta.env.MODE === 'development') {
      console.log('User logged in, resetting store initializations for fresh start')
    }
    // Reset store initializations to allow fresh initialization after login
    import('./services/storeInitializer').then(({ storeInitializer }) => {
      storeInitializer.resetAllInitializations()
    })
  })

  window.addEventListener('user-logged-out', () => {
    if (import.meta.env.MODE === 'development') {
      console.log('User logged out, resetting store initializations')
    }
    // Reset store initializations after logout
    import('./services/storeInitializer').then(({ storeInitializer }) => {
      storeInitializer.resetAllInitializations()
    })
  })

  // Listen for authentication expiration events
  window.addEventListener('auth-expired', (event) => {
    const { endpoint } = (event as CustomEvent).detail
    console.log('Authentication expired for endpoint:', endpoint)
    
    // Show user-friendly notification instead of immediate redirect
    if (import.meta.env.MODE === 'development') {
      console.log('Auth expired - user will be notified to re-login')
    }
    
    // Dispatch event for UI components to show login prompt
    window.dispatchEvent(new CustomEvent('show-login-prompt', {
      detail: { reason: 'session-expired', endpoint }
    }))
  })

  // Listen for navigation to dashboard to ensure stores are ready
  window.addEventListener('router-navigation', (event) => {
    const { to } = (event as CustomEvent).detail
    if (to === '/dashboard') {
      if (import.meta.env.MODE === 'development') {
        console.log('Navigation to dashboard detected, checking store readiness...')
      }
      
      // Check if stores need to be re-initialized for dashboard
      import('./services/storeInitializer').then(({ storeInitializer, defaultStoreConfigs }) => {
        if (storeInitializer.needsInitialization(defaultStoreConfigs)) {
          console.log('Stores need initialization for dashboard navigation')
          // Force store initialization for dashboard with longer timeout
          Promise.race([
            storeInitializer.initializeStores(defaultStoreConfigs),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Dashboard store init timeout')), 2000)
            )
          ]).catch(error => {
            console.warn('Failed to initialize stores for dashboard:', error)
          })
        } else {
          console.log('Stores are already ready for dashboard')
        }
      })
    }
  })
}

// Service worker registration is now deferred to Phase 3 background services

// Mount app and initialize with router readiness check
const mountApp = async () => {
  try {
    // Wait for router to be ready before mounting
    await router.isReady()
    console.log('Router ready, mounting app')
  } catch (error) {
    console.warn('Router readiness check failed, mounting anyway:', error)
  }
  
  // Mark initialization end and collect performance metrics with error handling
  safeMarkInitEnd()
  
  // Collect and report metrics after a short delay to ensure all measurements are captured
  setTimeout(() => {
    try {
      safeCollectAndReportMetrics()
    } catch (error) {
      console.warn('⚠️ Failed to collect final performance metrics:', error)
      // Don't let performance monitoring errors affect the app
    }
  }, 1000)
  
  initializeApp()
}

// Don't call mountApp here - it's called from mountFullApp

// Simple test script to verify group management API
const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

// Test user credentials (these should exist from seed data)
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

let authToken = '';
let groupId = '';

async function runTests() {
  try {
    console.log('🧪 Testing Group Management API...\n');

    // 1. Login to get auth token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, testUser);
    authToken = loginResponse.data.token;
    console.log('✅ Login successful\n');

    // 2. Create a group
    console.log('2. Creating a group...');
    const groupData = {
      name: 'Test Group',
      description: 'A test group for API testing',
      settings: {
        allowMemberInvites: true,
        defaultNotePermissions: 'view',
        maxMembers: 10
      }
    };

    const createGroupResponse = await axios.post(`${BASE_URL}/groups`, groupData, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    groupId = createGroupResponse.data.group.id;
    console.log('✅ Group created:', createGroupResponse.data.group.name);
    console.log('   Group ID:', groupId);
    console.log('   Members:', createGroupResponse.data.group.memberCount, '\n');

    // 3. Get user's groups
    console.log('3. Fetching user groups...');
    const groupsResponse = await axios.get(`${BASE_URL}/groups`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Found', groupsResponse.data.count, 'groups');
    groupsResponse.data.groups.forEach(group => {
      console.log('   -', group.name, `(${group.memberCount} members)`);
    });
    console.log('');

    // 4. Get specific group
    console.log('4. Fetching specific group...');
    const groupResponse = await axios.get(`${BASE_URL}/groups/${groupId}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Group details:');
    console.log('   Name:', groupResponse.data.group.name);
    console.log('   Description:', groupResponse.data.group.description);
    console.log('   Members:', groupResponse.data.group.members.length);
    console.log('   Settings:', JSON.stringify(groupResponse.data.group.settings, null, 2));
    console.log('');

    // 5. Update group
    console.log('5. Updating group...');
    const updateData = {
      name: 'Updated Test Group',
      description: 'Updated description for testing'
    };

    const updateResponse = await axios.put(`${BASE_URL}/groups/${groupId}`, updateData, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Group updated:', updateResponse.data.group.name);
    console.log('   New description:', updateResponse.data.group.description, '\n');

    // 6. Try to invite a user (this will fail since the email doesn't exist, but tests the validation)
    console.log('6. Testing user invitation...');
    try {
      const inviteData = {
        email: '<EMAIL>',
        role: 'viewer'
      };

      await axios.post(`${BASE_URL}/groups/${groupId}/invite`, inviteData, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      console.log('✅ Invitation sent (this should work even if user doesn\'t exist yet)');
    } catch (error) {
      if (error.response && error.response.status === 201) {
        console.log('✅ Invitation sent successfully');
      } else {
        console.log('⚠️  Invitation failed (expected for non-existent user):', error.response?.data?.error || error.message);
      }
    }
    console.log('');

    // 7. Get group invitations
    console.log('7. Fetching group invitations...');
    try {
      const invitationsResponse = await axios.get(`${BASE_URL}/groups/${groupId}/invitations`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      
      console.log('✅ Found', invitationsResponse.data.invitations.length, 'pending invitations');
      invitationsResponse.data.invitations.forEach(inv => {
        console.log('   -', inv.email, 'as', inv.role);
      });
    } catch (error) {
      console.log('⚠️  Could not fetch invitations:', error.response?.data?.error || error.message);
    }
    console.log('');

    // 8. Delete group
    console.log('8. Deleting group...');
    await axios.delete(`${BASE_URL}/groups/${groupId}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Group deleted successfully\n');

    console.log('🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

// Check if axios is available
try {
  require('axios');
  runTests();
} catch (error) {
  console.error('❌ axios is required to run this test. Install it with: npm install axios');
  console.error('Or run: node -e "const http = require(\'http\'); console.log(\'Use a proper HTTP client for testing\')"');
}
# Troubleshooting Guide - CSS Architecture & Theme System

## Common Issues and Solutions

### CSS Loading Issues

#### Issue: Styles not loading or appearing broken
**Symptoms:**
- Components appear unstyled
- Layout is broken
- Colors are missing

**Solutions:**
1. **Check import order in main.css**:
   ```css
   /* Correct order */
   @import './base/variables.css';  /* Must be first */
   @import './base/reset.css';
   @import './base/typography.css';
   /* ... other imports */
   ```

2. **Verify file paths**:
   ```bash
   # Check if CSS files exist
   ls -la frontend/src/styles/base/
   ls -la frontend/src/styles/components/
   ```

3. **Check browser console for 404 errors**:
   - Open DevTools → Console
   - Look for failed CSS imports
   - Verify file paths are correct

4. **Clear browser cache**:
   ```bash
   # Hard refresh
   Ctrl+Shift+R (Windows/Linux)
   Cmd+Shift+R (Mac)
   ```

#### Issue: CSS changes not reflecting
**Solutions:**
1. **Check Vite dev server**:
   ```bash
   # Restart dev server
   npm run dev
   ```

2. **Verify file watching**:
   - Check if files are being watched by Vite
   - Look for compilation errors in terminal

3. **Check CSS syntax**:
   ```css
   /* Invalid - missing semicolon */
   .component {
     color: red
     background: blue;
   }
   
   /* Valid */
   .component {
     color: red;
     background: blue;
   }
   ```

### Theme System Issues

#### Issue: Theme not switching or applying
**Symptoms:**
- Theme selector shows themes but colors don't change
- Some components don't update with theme
- Theme persists incorrectly

**Solutions:**
1. **Check theme attribute on HTML element**:
   ```javascript
   // In browser console
   console.log(document.documentElement.getAttribute('data-theme'))
   
   // Should show current theme name
   // If null or incorrect, theme switching is broken
   ```

2. **Verify CSS custom properties**:
   ```javascript
   // Check if custom properties are defined
   const styles = getComputedStyle(document.documentElement)
   console.log(styles.getPropertyValue('--color-primary'))
   
   // Should return the theme's primary color
   ```

3. **Check theme CSS file loading**:
   ```javascript
   // Check if theme CSS is loaded
   const themeStylesheets = Array.from(document.styleSheets)
     .filter(sheet => sheet.href && sheet.href.includes('theme-'))
   console.log(themeStylesheets)
   ```

4. **Verify theme registration**:
   ```typescript
   // In Vue DevTools or console
   // Check if theme is in available themes list
   const { useTheme } = await import('./src/composables/useTheme')
   const { availableThemes } = useTheme()
   console.log(availableThemes.value)
   ```

#### Issue: Custom theme not appearing in selector
**Solutions:**
1. **Check theme registration**:
   ```typescript
   // Ensure theme is added to availableThemes
   const customTheme = {
     name: 'my-theme',
     displayName: 'My Theme',
     // ... other properties
   }
   availableThemes.value.push(customTheme)
   ```

2. **Verify theme CSS file**:
   ```css
   /* Ensure proper theme selector */
   [data-theme="my-theme"] {
     --color-primary: #your-color;
     /* ... other properties */
   }
   ```

3. **Check file import**:
   ```css
   /* Add to main.css or load dynamically */
   @import './themes/bulmaswatch/my-theme.css';
   ```

### Component Styling Issues

#### Issue: Component styles not applying correctly
**Symptoms:**
- Components look different than expected
- Styles work in some themes but not others
- Inconsistent appearance across browsers

**Solutions:**
1. **Check CSS custom property usage**:
   ```css
   /* Incorrect - hardcoded colors */
   .my-component {
     background: #ffffff;
     color: #000000;
   }
   
   /* Correct - using custom properties */
   .my-component {
     background: var(--color-background);
     color: var(--color-text);
   }
   ```

2. **Verify component CSS import**:
   ```css
   /* In main.css */
   @import './components/my-component.css';
   ```

3. **Check CSS specificity**:
   ```css
   /* Low specificity - might be overridden */
   .component { color: red; }
   
   /* Higher specificity */
   .app .component { color: red; }
   
   /* Use more specific selectors if needed */
   ```

4. **Test across themes**:
   ```bash
   # Test component in different themes
   # Switch themes and verify appearance
   ```

#### Issue: Responsive styles not working
**Solutions:**
1. **Check media query syntax**:
   ```css
   /* Correct */
   @media screen and (max-width: 768px) {
     .component { padding: 1rem; }
   }
   
   /* Incorrect - missing screen */
   @media (max-width: 768px) {
     .component { padding: 1rem; }
   }
   ```

2. **Verify breakpoint values**:
   ```css
   /* Use consistent breakpoints */
   --breakpoint-mobile: 768px;
   --breakpoint-tablet: 1024px;
   --breakpoint-desktop: 1200px;
   ```

3. **Test on actual devices**:
   - Use browser DevTools device simulation
   - Test on real mobile devices
   - Check different screen orientations

### Performance Issues

#### Issue: Slow CSS loading or large bundle sizes
**Symptoms:**
- Slow initial page load
- Large CSS bundle sizes
- Poor performance scores

**Solutions:**
1. **Analyze bundle sizes**:
   ```bash
   npm run build:analyze
   npm run bundle-analyze
   ```

2. **Check for unused CSS**:
   ```bash
   # Use PurgeCSS or similar tools
   npm install --save-dev @fullhuman/postcss-purgecss
   ```

3. **Optimize imports**:
   ```css
   /* Avoid importing entire libraries */
   @import 'library/everything.css';  /* ❌ */
   
   /* Import only what you need */
   @import 'library/components/button.css';  /* ✅ */
   ```

4. **Enable CSS code splitting**:
   ```typescript
   // In vite.config.ts
   export default defineConfig({
     build: {
       cssCodeSplit: true,
       rollupOptions: {
         output: {
           manualChunks: {
             'theme-default': ['./src/styles/themes/bulmaswatch/default.css'],
             // ... other chunks
           }
         }
       }
     }
   })
   ```

#### Issue: Theme switching is slow
**Solutions:**
1. **Implement theme preloading**:
   ```typescript
   // Preload commonly used themes
   const preloadTheme = async (themeName: string) => {
     const link = document.createElement('link')
     link.rel = 'preload'
     link.as = 'style'
     link.href = `/themes/${themeName}.css`
     document.head.appendChild(link)
   }
   ```

2. **Use CSS custom properties efficiently**:
   ```css
   /* Efficient - changes only color values */
   [data-theme="dark"] {
     --color-primary: #new-color;
   }
   
   /* Inefficient - redefines entire rules */
   [data-theme="dark"] .button {
     background: #new-color;
     /* ... all other properties */
   }
   ```

3. **Optimize theme CSS**:
   ```css
   /* Group related properties */
   [data-theme="my-theme"] {
     /* Colors */
     --color-primary: #value;
     --color-secondary: #value;
     
     /* Spacing */
     --spacing-1: 0.25rem;
     --spacing-2: 0.5rem;
   }
   ```

### Browser Compatibility Issues

#### Issue: Styles not working in older browsers
**Solutions:**
1. **Check CSS custom property support**:
   ```css
   /* Provide fallbacks for older browsers */
   .component {
     background: #3273dc; /* Fallback */
     background: var(--color-primary, #3273dc); /* With fallback */
   }
   ```

2. **Use autoprefixer**:
   ```css
   /* Autoprefixer will add vendor prefixes */
   .component {
     transform: translateX(10px);
     /* Becomes: */
     /* -webkit-transform: translateX(10px); */
     /* transform: translateX(10px); */
   }
   ```

3. **Test in target browsers**:
   - Use BrowserStack or similar services
   - Test in actual browser versions
   - Check caniuse.com for feature support

#### Issue: CSS Grid or Flexbox not working
**Solutions:**
1. **Provide fallbacks**:
   ```css
   .layout {
     /* Fallback for older browsers */
     display: block;
     
     /* Modern layout */
     display: grid;
     grid-template-columns: 1fr 3fr;
   }
   ```

2. **Use feature queries**:
   ```css
   @supports (display: grid) {
     .layout {
       display: grid;
       grid-template-columns: 1fr 3fr;
     }
   }
   
   @supports not (display: grid) {
     .layout {
       display: flex;
     }
   }
   ```

### Development Environment Issues

#### Issue: Hot reload not working for CSS changes
**Solutions:**
1. **Check Vite configuration**:
   ```typescript
   // vite.config.ts
   export default defineConfig({
     css: {
       devSourcemap: true, // Enable CSS source maps
     },
     server: {
       hmr: true, // Enable hot module replacement
     }
   })
   ```

2. **Restart development server**:
   ```bash
   # Stop server (Ctrl+C) and restart
   npm run dev
   ```

3. **Check file watching**:
   ```bash
   # Ensure files are being watched
   # Check terminal for file change notifications
   ```

#### Issue: Build failing with CSS errors
**Solutions:**
1. **Check CSS syntax**:
   ```bash
   # Use CSS linter
   npm install --save-dev stylelint
   npx stylelint "src/**/*.css"
   ```

2. **Verify import paths**:
   ```css
   /* Check if all imported files exist */
   @import './non-existent-file.css'; /* Will cause build error */
   ```

3. **Check PostCSS configuration**:
   ```javascript
   // postcss.config.js
   module.exports = {
     plugins: [
       require('autoprefixer'),
       // ... other plugins
     ]
   }
   ```

## Debugging Tools and Techniques

### Browser DevTools

1. **Inspect CSS custom properties**:
   ```javascript
   // In console
   const element = document.querySelector('.my-component')
   const styles = getComputedStyle(element)
   console.log(styles.getPropertyValue('--color-primary'))
   ```

2. **Check CSS cascade**:
   - Use Elements panel
   - Look at Computed styles
   - Check which rules are being applied/overridden

3. **Debug responsive styles**:
   - Use device simulation
   - Resize viewport
   - Check media query application

### CSS Debugging Utilities

```css
/* Debug borders - add to any element */
.debug-border * {
  border: 1px solid red !important;
}

/* Debug spacing */
.debug-spacing * {
  background: rgba(255, 0, 0, 0.1) !important;
}

/* Debug custom properties */
.debug-vars::before {
  content: 'Primary: ' var(--color-primary) ', BG: ' var(--color-background);
  position: fixed;
  top: 0;
  left: 0;
  background: black;
  color: white;
  padding: 10px;
  z-index: 9999;
}
```

### Performance Debugging

```bash
# Analyze CSS bundle
npm run build:analyze

# Check CSS performance
npm run lighthouse -- --only-categories=performance

# Monitor CSS loading
# Use Network tab in DevTools
```

## Getting Help

### Before Asking for Help

1. **Check this troubleshooting guide**
2. **Review browser console for errors**
3. **Test in different browsers**
4. **Verify your changes against working examples**
5. **Check if issue exists in production build**

### Information to Include

When reporting issues, include:

1. **Environment details**:
   - Browser and version
   - Operating system
   - Node.js version
   - Development vs production

2. **Steps to reproduce**:
   - Exact steps taken
   - Expected vs actual behavior
   - Screenshots if applicable

3. **Code samples**:
   - Relevant CSS code
   - Component code if applicable
   - Configuration files

4. **Error messages**:
   - Console errors
   - Build errors
   - Network errors

### Resources

- **Documentation**: Check `docs/` folder for detailed guides
- **Examples**: Review existing component implementations
- **Community**: Ask questions in project discussions
- **Issues**: Report bugs in project issue tracker

## Prevention Tips

### Code Quality

1. **Use linting tools**:
   ```bash
   npm install --save-dev stylelint
   npm install --save-dev stylelint-config-standard
   ```

2. **Follow naming conventions**:
   - Use consistent class naming
   - Follow BEM methodology
   - Use semantic custom property names

3. **Test thoroughly**:
   - Test across browsers
   - Test responsive behavior
   - Test theme switching
   - Test accessibility

### Performance

1. **Monitor bundle sizes**:
   ```bash
   # Set up automated bundle size monitoring
   npm run build:analyze
   ```

2. **Optimize imports**:
   - Import only what you need
   - Use tree shaking
   - Split CSS by components

3. **Use efficient CSS**:
   - Avoid overly specific selectors
   - Use CSS custom properties
   - Minimize nesting

### Maintenance

1. **Keep documentation updated**:
   - Document new components
   - Update theme guides
   - Maintain troubleshooting info

2. **Regular audits**:
   - Review unused CSS
   - Check performance metrics
   - Update dependencies

3. **Version control**:
   - Use meaningful commit messages
   - Tag releases
   - Maintain changelog
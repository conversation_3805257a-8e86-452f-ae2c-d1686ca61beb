/* Auth Pages and Forms */

.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary-dark) 100%);
  padding: 1.5rem;
  position: relative;
}

.auth-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.auth-container {
  max-width: 480px;
  width: 100%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.brand-name {
  font-weight: var(--font-weight-bold);
  color: white;
  letter-spacing: var(--letter-spacing-wide);
  margin-bottom: 0.25rem;
}

.auth-title {
  font-size: var(--font-size-2xl);
  color: white;
  margin-bottom: 0.25rem;
}

.auth-subtitle {
  color: rgba(255, 255, 255, 0.9);
}

.auth-form-container {
  background: var(--card-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: 1.5rem;
}

.auth-form {
  width: 100%;
}

.form-content {
  padding: 2rem;
}

/* Cards inside auth flows */
.auth-layout .card,
.card.auth-card {
  max-width: 400px;
  margin: 0 auto;
}

/* Google Sign-in button */
.google-signin-container {
  position: relative;
}

.google-signin-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid var(--color-border);
  background-color: var(--color-background);
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.google-signin-button:hover:not(:disabled) {
  background-color: var(--color-surface);
  border-color: var(--color-border-hover);
  box-shadow: var(--shadow-sm);
}

.google-signin-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.google-signin-button .icon {
  margin: 0;
}

.google-signin-button .icon svg,
.google-logo {
  width: 18px;
  height: 18px;
}

.password-requirements {
  font-size: var(--font-size-xs);
  margin-top: 0.5rem;
}

.requirement {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}

.requirement .icon {
  margin-right: 0.5rem;
}

.requirement.is-success {
  color: var(--color-success);
}


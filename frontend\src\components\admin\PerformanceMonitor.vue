<template>
  <div class="performance-monitor">
    <div class="columns">
      <!-- Real-time Metrics -->
      <div class="column is-4">
        <div class="card">
          <header class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-tachometer-alt"></i>
              </span>
              <span>Real-time Metrics</span>
            </p>
          </header>
          <div class="card-content">
            <div class="metrics-grid">
              <div class="metric-item">
                <div class="metric-value">{{ formatMs(averageResponseTime) }}</div>
                <div class="metric-label">Avg Response Time</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ activeConnections }}</div>
                <div class="metric-label">Active Connections</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ formatBytes(memoryUsage) }}</div>
                <div class="metric-label">Memory Usage</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ cpuUsage }}%</div>
                <div class="metric-label">CPU Usage</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Chart -->
      <div class="column is-8">
        <div class="card">
          <header class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-chart-line"></i>
              </span>
              <span>Response Time Trend</span>
            </p>
            <div class="card-header-icon">
              <div class="field is-grouped">
                <div class="control">
                  <div class="select is-small">
                    <select v-model="timeRange" @change="updateChart">
                      <option value="1h">Last Hour</option>
                      <option value="6h">Last 6 Hours</option>
                      <option value="24h">Last 24 Hours</option>
                      <option value="7d">Last 7 Days</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </header>
          <div class="card-content">
            <canvas ref="chartCanvas" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Database Performance -->
    <div class="columns">
      <div class="column is-6">
        <div class="card">
          <header class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-database"></i>
              </span>
              <span>Database Performance</span>
            </p>
          </header>
          <div class="card-content">
            <div class="table-container">
              <table class="table is-fullwidth is-striped">
                <thead>
                  <tr>
                    <th>Query Type</th>
                    <th>Count</th>
                    <th>Avg Time</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="query in topQueries" :key="query.type">
                    <td>{{ query.type }}</td>
                    <td>{{ query.count }}</td>
                    <td>{{ formatMs(query.avgTime) }}</td>
                    <td>
                      <span 
                        class="tag is-small"
                        :class="getQueryStatusClass(query.avgTime)"
                      >
                        {{ getQueryStatus(query.avgTime) }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Bundle Analysis -->
      <div class="column is-6">
        <div class="card">
          <header class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-cube"></i>
              </span>
              <span>Bundle Analysis</span>
            </p>
          </header>
          <div class="card-content">
            <div class="bundle-stats">
              <div class="stat-item">
                <div class="stat-label">Total Bundle Size</div>
                <div class="stat-value">{{ formatBytes(bundleStats.totalSize) }}</div>
                <div class="stat-progress">
                  <progress 
                    class="progress is-small"
                    :class="getBundleSizeClass(bundleStats.totalSize)"
                    :value="bundleStats.totalSize" 
                    :max="bundleStats.budget"
                  ></progress>
                </div>
              </div>
              
              <div class="stat-item" v-for="chunk in bundleStats.chunks" :key="chunk.name">
                <div class="stat-label">{{ chunk.name }}</div>
                <div class="stat-value">{{ formatBytes(chunk.size) }}</div>
                <div class="stat-progress">
                  <progress 
                    class="progress is-small"
                    :class="getChunkSizeClass(chunk.size, chunk.budget)"
                    :value="chunk.size" 
                    :max="chunk.budget"
                  ></progress>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Recommendations -->
    <div class="card" v-if="recommendations.length > 0">
      <header class="card-header">
        <p class="card-header-title">
          <span class="icon">
            <i class="fas fa-lightbulb"></i>
          </span>
          <span>Performance Recommendations</span>
        </p>
      </header>
      <div class="card-content">
        <div class="recommendations">
          <div 
            v-for="rec in recommendations" 
            :key="rec.id"
            class="notification"
            :class="getRecommendationClass(rec.priority)"
          >
            <button class="delete" @click="dismissRecommendation(rec.id)"></button>
            <div class="recommendation-content">
              <div class="recommendation-title">{{ rec.title }}</div>
              <div class="recommendation-description">{{ rec.description }}</div>
              <div class="recommendation-impact">
                <span class="tag is-small">{{ rec.impact }} impact</span>
                <span class="tag is-small">{{ rec.effort }} effort</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Chart, createChart, destroyChart, performanceChartOptions } from '@/utils/chartConfig'
import { performanceApiService } from '../../services/performanceApiService'

// Reactive state
const chartCanvas = ref<HTMLCanvasElement>()
const timeRange = ref('1h')
const averageResponseTime = ref(0)
const activeConnections = ref(0)
const memoryUsage = ref(0)
const cpuUsage = ref(0)
const topQueries = ref<any[]>([])
type BundleChunk = { name: string; size: number; budget: number }
const bundleStats = ref<{ totalSize: number; budget: number; chunks: BundleChunk[] }>({
  totalSize: 0,
  budget: 2000000, // 2MB
  chunks: []
})
const recommendations = ref<any[]>([])

let chart: Chart | null = null
let updateInterval: ReturnType<typeof setInterval> | null = null

// Methods
const formatMs = (ms: number) => {
  if (ms < 1000) return `${Math.round(ms)}ms`
  return `${(ms / 1000).toFixed(1)}s`
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
}

const getQueryStatusClass = (avgTime: number) => {
  if (avgTime < 50) return 'is-success'
  if (avgTime < 200) return 'is-warning'
  return 'is-danger'
}

const getQueryStatus = (avgTime: number) => {
  if (avgTime < 50) return 'Fast'
  if (avgTime < 200) return 'Slow'
  return 'Critical'
}

const getBundleSizeClass = (size: number) => {
  const percentage = (size / bundleStats.value.budget) * 100
  if (percentage < 70) return 'is-success'
  if (percentage < 90) return 'is-warning'
  return 'is-danger'
}

const getChunkSizeClass = (size: number, budget: number) => {
  const percentage = (size / budget) * 100
  if (percentage < 70) return 'is-success'
  if (percentage < 90) return 'is-warning'
  return 'is-danger'
}

const getRecommendationClass = (priority: string) => {
  switch (priority) {
    case 'high': return 'is-danger is-light'
    case 'medium': return 'is-warning is-light'
    case 'low': return 'is-info is-light'
    default: return 'is-light'
  }
}

const updateMetrics = async () => {
  try {
    const metrics = await performanceApiService.getMetrics()
    
    averageResponseTime.value = metrics.averageResponseTime
    activeConnections.value = metrics.activeConnections
    memoryUsage.value = metrics.memoryUsage
    cpuUsage.value = metrics.cpuUsage
    topQueries.value = metrics.topQueries
    
    // Update chart data
    if (chart) {
      chart.data.labels?.push(new Date().toLocaleTimeString())
      chart.data.datasets[0].data.push(metrics.averageResponseTime)
      
      // Keep only last 20 data points
      if (chart.data.labels && chart.data.labels.length > 20) {
        chart.data.labels.shift()
        chart.data.datasets[0].data.shift()
      }
      
      chart.update('none')
    }
  } catch (error) {
    console.error('Failed to update metrics:', error)
  }
}

const updateChart = async () => {
  try {
    const data = await performanceApiService.getHistoricalData(timeRange.value)
    
    if (chart) {
      chart.data.labels = data.labels
      chart.data.datasets[0].data = data.responseTime
      chart.update()
    }
  } catch (error) {
    console.error('Failed to update chart:', error)
  }
}

const loadBundleStats = async () => {
  try {
    const stats = await performanceApiService.getBundleStats()
    bundleStats.value = stats
  } catch (error) {
    console.error('Failed to load bundle stats:', error)
  }
}

const loadRecommendations = async () => {
  try {
    const recs = await performanceApiService.getRecommendations()
    recommendations.value = recs
  } catch (error) {
    console.error('Failed to load recommendations:', error)
  }
}

const dismissRecommendation = (id: string) => {
  recommendations.value = recommendations.value.filter(r => r.id !== id)
}

const initChart = () => {
  if (!chartCanvas.value) return
  
  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return
  
  chart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [{
        label: 'Response Time (ms)',
        data: [],
        borderColor: '#3273dc',
        backgroundColor: 'rgba(50, 115, 220, 0.1)',
        tension: 0.4,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Response Time (ms)'
          }
        },
        x: {
          title: {
            display: true,
            text: 'Time'
          }
        }
      },
      plugins: {
        legend: {
          display: false
        }
      }
    }
  })
}

// Lifecycle
onMounted(async () => {
  initChart()
  await updateMetrics()
  await updateChart()
  await loadBundleStats()
  await loadRecommendations()
  
  // Update metrics every 30 seconds
  updateInterval = setInterval(updateMetrics, 30000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
  if (chart) {
    chart.destroy()
  }
})
</script>

<style scoped>
.performance-monitor {
  padding: 1rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.metric-item {
  text-align: center;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #363636;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.bundle-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.stat-value {
  font-weight: 600;
  color: #363636;
}

.stat-progress {
  width: 100%;
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.recommendation-title {
  font-weight: 600;
  color: #363636;
}

.recommendation-description {
  font-size: 0.875rem;
  color: #6c757d;
}

.recommendation-impact {
  display: flex;
  gap: 0.5rem;
}

@media screen and (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .columns {
    display: block;
  }
  
  .column {
    width: 100%;
    margin-bottom: 1rem;
  }
}
</style>
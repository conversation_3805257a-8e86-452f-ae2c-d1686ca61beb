#!/usr/bin/env node

// Performance regression check for <500ms initialization target
// Validates that performance optimizations meet the target requirements

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Performance targets for <500ms initialization
const PERFORMANCE_TARGETS = {
  initTime: 500, // App initialization time (ms)
  storeInitTime: 200, // Store initialization time (ms)
  domReadyTime: 150, // DOM ready time (ms)
  bundleSize: 1200, // Total bundle size (KB)
  initialBundle: 250, // Initial bundle size (KB)
  coreWebVitals: {
    fcp: 1000, // First Contentful Paint (ms)
    lcp: 1500, // Largest Contentful Paint (ms)
    tti: 2000, // Time to Interactive (ms)
    cls: 0.05, // Cumulative Layout Shift
    fid: 100, // First Input Delay (ms)
  },
};

function loadPerformanceMetrics() {
  const possiblePaths = [
    path.join(__dirname, '../dist/performance-metrics.json'),
    path.join(__dirname, '../performance-metrics.json'),
    path.join(process.cwd(), 'performance-metrics.json'),
    path.join(__dirname, '../dist/performance-baseline.json'),
  ];

  for (const metricsPath of possiblePaths) {
    if (fs.existsSync(metricsPath)) {
      try {
        const metrics = JSON.parse(fs.readFileSync(metricsPath, 'utf8'));
        console.log(`📊 Found performance metrics at: ${metricsPath}`);
        return metrics;
      } catch (error) {
        console.warn(`⚠️  Could not read metrics from ${metricsPath}:`, error.message);
      }
    }
  }

  return null;
}

function loadBaselineMetrics() {
  const baselinePath = path.join(__dirname, '../performance-baseline.json');

  if (fs.existsSync(baselinePath)) {
    try {
      const baseline = JSON.parse(fs.readFileSync(baselinePath, 'utf8'));
      console.log(`📈 Found performance baseline at: ${baselinePath}`);
      return baseline;
    } catch (error) {
      console.warn(`⚠️  Could not read baseline from ${baselinePath}:`, error.message);
    }
  }

  return null;
}

function validateInitializationTime(metrics) {
  console.log('\n⏱️  Initialization Time Validation\n');
  console.log('Metric'.padEnd(25) + 'Actual'.padEnd(12) + 'Target'.padEnd(12) + 'Status');
  console.log('-'.repeat(60));

  let allPassed = true;

  // App initialization time
  const initTime = metrics.initialization?.duration || metrics.initializationTime || 0;
  const initStatus = initTime <= PERFORMANCE_TARGETS.initTime ? '✅ PASS' : '❌ FAIL';
  if (initTime > PERFORMANCE_TARGETS.initTime) allPassed = false;

  console.log(
    'App Initialization'.padEnd(25) +
      `${initTime.toFixed(0)}ms`.padEnd(12) +
      `${PERFORMANCE_TARGETS.initTime}ms`.padEnd(12) +
      initStatus
  );

  // Store initialization time
  const storeInitTime = metrics.initialization?.storeInitTime || 0;
  if (storeInitTime > 0) {
    const storeStatus = storeInitTime <= PERFORMANCE_TARGETS.storeInitTime ? '✅ PASS' : '❌ FAIL';
    if (storeInitTime > PERFORMANCE_TARGETS.storeInitTime) allPassed = false;

    console.log(
      'Store Initialization'.padEnd(25) +
        `${storeInitTime.toFixed(0)}ms`.padEnd(12) +
        `${PERFORMANCE_TARGETS.storeInitTime}ms`.padEnd(12) +
        storeStatus
    );
  }

  // DOM ready time
  const domReadyTime = metrics.initialization?.domReadyTime || 0;
  if (domReadyTime > 0) {
    const domStatus = domReadyTime <= PERFORMANCE_TARGETS.domReadyTime ? '✅ PASS' : '❌ FAIL';
    if (domReadyTime > PERFORMANCE_TARGETS.domReadyTime) allPassed = false;

    console.log(
      'DOM Ready'.padEnd(25) +
        `${domReadyTime.toFixed(0)}ms`.padEnd(12) +
        `${PERFORMANCE_TARGETS.domReadyTime}ms`.padEnd(12) +
        domStatus
    );
  }

  return allPassed;
}

function validateCoreWebVitals(metrics) {
  console.log('\n🌐 Core Web Vitals Validation\n');
  console.log('Metric'.padEnd(25) + 'Actual'.padEnd(12) + 'Target'.padEnd(12) + 'Status');
  console.log('-'.repeat(60));

  let allPassed = true;

  if (metrics.coreWebVitals) {
    const vitals = metrics.coreWebVitals;
    const targets = PERFORMANCE_TARGETS.coreWebVitals;

    Object.entries(targets).forEach(([metric, target]) => {
      const value = vitals[metric] || 0;
      const status = value <= target ? '✅ PASS' : '❌ FAIL';
      if (value > target) allPassed = false;

      const unit = metric === 'cls' ? '' : 'ms';
      console.log(
        metric.toUpperCase().padEnd(25) +
          `${value.toFixed(2)}${unit}`.padEnd(12) +
          `${target}${unit}`.padEnd(12) +
          status
      );
    });
  } else {
    console.log('No Core Web Vitals data available');
    return true; // Don't fail if no data
  }

  return allPassed;
}

function validateBundleSize(baseline) {
  console.log('\n📦 Bundle Size Validation\n');
  console.log('Metric'.padEnd(25) + 'Actual'.padEnd(12) + 'Target'.padEnd(12) + 'Status');
  console.log('-'.repeat(60));

  let allPassed = true;

  if (baseline && baseline.buildSize) {
    const totalSize = baseline.buildSize.totalKB;
    const target = PERFORMANCE_TARGETS.bundleSize;
    const status = totalSize <= target ? '✅ PASS' : '❌ FAIL';
    if (totalSize > target) allPassed = false;

    console.log(
      'Total Bundle Size'.padEnd(25) +
        `${totalSize}KB`.padEnd(12) +
        `${target}KB`.padEnd(12) +
        status
    );

    // Check individual file sizes
    if (baseline.files) {
      const largeFiles = [
        ...baseline.files.css.filter(f => f.sizeKB > 200),
        ...baseline.files.js.filter(f => f.sizeKB > 300),
      ];

      if (largeFiles.length > 0) {
        console.log('\n⚠️  Large Files Detected:');
        largeFiles.forEach(file => {
          console.log(`   • ${file.name}: ${file.sizeKB}KB`);
        });
        allPassed = false;
      }
    }
  } else {
    console.log('No bundle size data available');
    return true; // Don't fail if no data
  }

  return allPassed;
}

function generateRegressionReport(metrics, baseline, results) {
  const report = {
    timestamp: new Date().toISOString(),
    targets: PERFORMANCE_TARGETS,
    results: {
      initializationPassed: results.initialization,
      coreWebVitalsPassed: results.coreWebVitals,
      bundleSizePassed: results.bundleSize,
      overallPassed: results.initialization && results.coreWebVitals && results.bundleSize,
    },
    metrics: metrics || {},
    baseline: baseline || {},
    recommendations: [],
  };

  // Generate recommendations based on failures
  if (!results.initialization) {
    report.recommendations.push('Optimize store initialization and DOM operations');
    report.recommendations.push('Implement more aggressive lazy loading');
    report.recommendations.push('Review requestIdleCallback timing');
  }

  if (!results.coreWebVitals) {
    report.recommendations.push('Optimize critical rendering path');
    report.recommendations.push('Reduce layout shifts and improve paint timing');
    report.recommendations.push('Implement progressive loading strategies');
  }

  if (!results.bundleSize) {
    report.recommendations.push('Further reduce bundle sizes through code splitting');
    report.recommendations.push('Remove unused dependencies and code');
    report.recommendations.push('Optimize CSS and implement tree shaking');
  }

  return report;
}

function main() {
  console.log('🔍 Performance Regression Check - <500ms Target\n');

  const metrics = loadPerformanceMetrics();
  const baseline = loadBaselineMetrics();

  const results = {
    initialization: true,
    coreWebVitals: true,
    bundleSize: true,
  };

  // Validate initialization time
  if (metrics) {
    results.initialization = validateInitializationTime(metrics);
  } else {
    console.log('\n⏱️  Initialization Time Validation');
    console.log('No performance metrics found - skipping initialization validation');
  }

  // Validate Core Web Vitals
  if (metrics) {
    results.coreWebVitals = validateCoreWebVitals(metrics);
  } else {
    console.log('\n🌐 Core Web Vitals Validation');
    console.log('No Core Web Vitals data found - skipping validation');
  }

  // Validate bundle size
  if (baseline) {
    results.bundleSize = validateBundleSize(baseline);
  } else {
    console.log('\n📦 Bundle Size Validation');
    console.log('No bundle size data found - skipping validation');
  }

  // Generate regression report
  const report = generateRegressionReport(metrics, baseline, results);

  const reportPath = path.join(__dirname, '../performance-regression-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  // Final results
  console.log('\n📊 Performance Regression Summary\n');
  console.log('Test Category'.padEnd(25) + 'Status');
  console.log('-'.repeat(40));
  console.log('Initialization Time'.padEnd(25) + (results.initialization ? '✅ PASS' : '❌ FAIL'));
  console.log('Core Web Vitals'.padEnd(25) + (results.coreWebVitals ? '✅ PASS' : '❌ FAIL'));
  console.log('Bundle Size'.padEnd(25) + (results.bundleSize ? '✅ PASS' : '❌ FAIL'));
  console.log('-'.repeat(40));
  console.log('Overall Result'.padEnd(25) + (report.results.overallPassed ? '✅ PASS' : '❌ FAIL'));

  if (report.recommendations.length > 0) {
    console.log('\n🔧 Recommendations:');
    report.recommendations.forEach(rec => console.log(`   • ${rec}`));
  }

  console.log(`\n📄 Detailed report saved to: ${reportPath}`);

  // Exit with appropriate code
  process.exit(report.results.overallPassed ? 0 : 1);
}

// Handle command line arguments
if (process.argv.includes('--update-baseline')) {
  console.log('📝 Updating performance baseline...');
  // This would update the baseline - for now just run the check
}

main();

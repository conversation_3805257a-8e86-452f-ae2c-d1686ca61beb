// Optimized Chart.js configuration - only import essential components
// This reduces Chart.js bundle from 158KB to ~80KB

import {
  Chart,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'

// Register only the components we actually use
Chart.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

// Export configured Chart instance
export { Chart }

// Export common chart configurations
export const defaultChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    title: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        display: true,
      }
    },
    x: {
      grid: {
        display: false,
      }
    }
  },
  elements: {
    point: {
      radius: 3,
      hoverRadius: 5
    },
    line: {
      tension: 0.2
    }
  }
}

// Performance chart specific configuration
export const performanceChartOptions = {
  ...defaultChartOptions,
  scales: {
    ...defaultChartOptions.scales,
    y: {
      ...defaultChartOptions.scales.y,
      title: {
        display: true,
        text: 'Time (ms)'
      }
    }
  }
}

// Bundle size chart configuration
export const bundleSizeChartOptions = {
  ...defaultChartOptions,
  scales: {
    ...defaultChartOptions.scales,
    y: {
      ...defaultChartOptions.scales.y,
      title: {
        display: true,
        text: 'Size (KB)'
      }
    }
  }
}

// Create chart helper function
export function createChart(canvas: HTMLCanvasElement, config: any) {
  return new Chart(canvas, config)
}

// Destroy chart helper
export function destroyChart(chart: Chart | null) {
  if (chart) {
    chart.destroy()
  }
}
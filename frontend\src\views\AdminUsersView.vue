<template>
  <div class="admin-users">
    <div class="admin-container">
      <!-- Header -->
      <div class="admin-header">
        <div class="header-content">
          <div class="header-info">
            <router-link 
              to="/dashboard"
              class="button is-light home-button"
              title="Back to Dashboard"
            >
              <span class="icon">
                <i class="fas fa-home"></i>
              </span>
              <span>Home</span>
            </router-link>
            <div class="header-text">
              <h1 class="page-title">User Management</h1>
              <p class="page-subtitle">Manage user accounts and permissions</p>
            </div>
          </div>
          <div class="header-actions">
            <button 
              class="button is-primary" 
              @click="loadUsers"
              :class="{ 'is-loading': isLoading }"
            >
              <span class="icon">
                <i class="fas fa-sync-alt"></i>
              </span>
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="filters-card">
        <div class="filters-content">
          <div class="filter-group">
            <div class="filter-item search-filter">
              <label class="filter-label">Search</label>
              <div class="search-input">
                <input 
                  v-model="searchQuery" 
                  class="input" 
                  type="text" 
                  placeholder="Search by email or name..."
                  @input="debouncedSearch"
                >
                <span class="search-icon">
                  <i class="fas fa-search"></i>
                </span>
              </div>
            </div>
            <div class="filter-item">
              <label class="filter-label">Status</label>
              <div class="select-wrapper">
                <select v-model="statusFilter" @change="loadUsers" class="filter-select">
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="suspended">Suspended</option>
                  <option value="banned">Banned</option>
                </select>
              </div>
            </div>
            <div class="filter-item">
              <label class="filter-label">Sort By</label>
              <div class="select-wrapper">
                <select v-model="sortBy" @change="loadUsers" class="filter-select">
                  <option value="created_at">Created Date</option>
                  <option value="email">Email</option>
                  <option value="display_name">Name</option>
                  <option value="updated_at">Last Updated</option>
                </select>
              </div>
            </div>
            <div class="filter-item">
              <label class="filter-label">Order</label>
              <div class="select-wrapper">
                <select v-model="sortOrder" @change="loadUsers" class="filter-select">
                  <option value="desc">Descending</option>
                  <option value="asc">Ascending</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="error-notification">
        <div class="error-content">
          <span class="icon">
            <i class="fas fa-exclamation-circle"></i>
          </span>
          <p>{{ error }}</p>
          <button class="button is-small" @click="error = null">Dismiss</button>
        </div>
      </div>

      <!-- Users Content -->
      <div class="users-card">
        <!-- Loading state -->
        <div v-if="isLoading && users.length === 0" class="loading-state">
          <div class="loader"></div>
          <p>Loading users...</p>
        </div>

        <!-- Empty state -->
        <div v-else-if="users.length === 0" class="empty-state">
          <div class="empty-content">
            <span class="icon">
              <i class="fas fa-users"></i>
            </span>
            <h3>No users found</h3>
            <p>No users match your current filters.</p>
          </div>
        </div>

        <!-- Users list -->
        <div v-else class="users-list">
          <div v-for="user in users" :key="user.id" class="user-card">
            <div class="user-info">
              <div class="user-avatar">
                <img 
                  v-if="user.avatar_url" 
                  :src="user.avatar_url" 
                  :alt="user.display_name"
                  class="avatar-image"
                >
                <div v-else class="avatar-placeholder">
                  {{ user.display_name.charAt(0).toUpperCase() }}
                </div>
              </div>
              <div class="user-details">
                <div class="user-name-section">
                  <h4 class="user-name">{{ user.display_name }}</h4>
                  <div class="user-badges">
                    <span v-if="user.isAdmin" class="badge admin-badge">Admin</span>
                    <span v-if="user.oauth_provider" class="badge oauth-badge">
                      {{ user.oauth_provider }}
                    </span>
                  </div>
                </div>
                <p class="user-email">{{ user.email }}</p>
                <div class="user-meta">
                  <div class="status-section">
                    <span class="status-badge" :class="{
                      'status-active': user.status === 'active',
                      'status-suspended': user.status === 'suspended',
                      'status-banned': user.status === 'banned'
                    }">
                      {{ user.status }}
                    </span>
                    <span v-if="user.email_verified" class="verification-badge verified">
                      <i class="fas fa-check"></i>
                      Verified
                    </span>
                    <span v-else class="verification-badge unverified">
                      <i class="fas fa-exclamation-triangle"></i>
                      Unverified
                    </span>
                  </div>
                  <div class="stats-section">
                    <span class="stat-item">{{ user.note_count }} notes</span>
                    <span class="stat-item">{{ user.group_count }} groups</span>
                  </div>
                  <div class="date-section">
                    <span class="created-date">{{ formatDate(user.created_at) }}</span>
                    <span class="relative-date">{{ formatRelativeDate(user.created_at) }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="user-actions">
              <div class="dropdown" :class="{ 'is-active': activeDropdown === user.id }">
                <div class="dropdown-trigger">
                  <button 
                    class="button is-small is-ghost" 
                    @click="toggleDropdown(user.id)"
                  >
                    <span class="icon">
                      <i class="fas fa-ellipsis-v"></i>
                    </span>
                  </button>
                </div>
                <div class="dropdown-menu">
                  <div class="dropdown-content">
                    <a 
                      v-if="user.status === 'active'" 
                      class="dropdown-item" 
                      @click="showStatusModal(user, 'suspended')"
                    >
                      <span class="icon">
                        <i class="fas fa-pause"></i>
                      </span>
                      <span>Suspend User</span>
                    </a>
                    <a 
                      v-if="user.status === 'suspended'" 
                      class="dropdown-item" 
                      @click="updateUserStatus(user.id, 'active')"
                    >
                      <span class="icon">
                        <i class="fas fa-play"></i>
                      </span>
                      <span>Activate User</span>
                    </a>
                    <a 
                      v-if="user.status !== 'banned'" 
                      class="dropdown-item has-text-danger" 
                      @click="showStatusModal(user, 'banned')"
                    >
                      <span class="icon">
                        <i class="fas fa-ban"></i>
                      </span>
                      <span>Ban User</span>
                    </a>
                    <hr class="dropdown-divider">
                    <a 
                      class="dropdown-item" 
                      @click="toggleUserAdmin(user)"
                    >
                      <span class="icon">
                        <i class="fas" :class="user.isAdmin ? 'fa-user-minus' : 'fa-user-plus'"></i>
                      </span>
                      <span>{{ user.isAdmin ? 'Remove Admin' : 'Make Admin' }}</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="usersPagination.totalPages > 1" class="pagination-wrapper">
          <div class="pagination-controls">
            <button 
              class="button is-light" 
              :disabled="usersPagination.page <= 1"
              @click="changePage(usersPagination.page - 1)"
            >
              <span class="icon">
                <i class="fas fa-chevron-left"></i>
              </span>
              <span>Previous</span>
            </button>
            <div class="pagination-info">
              Page {{ usersPagination.page }} of {{ usersPagination.totalPages }}
            </div>
            <button 
              class="button is-light" 
              :disabled="usersPagination.page >= usersPagination.totalPages"
              @click="changePage(usersPagination.page + 1)"
            >
              <span>Next</span>
              <span class="icon">
                <i class="fas fa-chevron-right"></i>
              </span>
            </button>
          </div>
          <div class="pagination-pages">
            <button 
              v-for="page in visiblePages" 
              :key="page"
              v-if="page !== '...'"
              class="page-button" 
              :class="{ 'is-current': page === usersPagination.page }"
              @click="changePage(page as number)"
            >
              {{ page }}
            </button>
            <span v-else class="page-ellipsis">…</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Change Modal -->
    <div class="modal" :class="{ 'is-active': showStatusChangeModal }">
      <div class="modal-background" @click="closeStatusModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            {{ statusModalData.action === 'suspended' ? 'Suspend' : 'Ban' }} User
          </p>
          <button class="delete" @click="closeStatusModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="modal-content">
            <p class="modal-message">
              Are you sure you want to {{ statusModalData.action }} 
              <strong>{{ statusModalData.user?.display_name }}</strong>?
            </p>
            <div class="form-field">
              <label class="field-label">Reason (optional)</label>
              <textarea 
                v-model="statusModalData.reason" 
                class="field-textarea" 
                placeholder="Enter reason for this action..."
                rows="3"
              ></textarea>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-danger" 
            @click="confirmStatusChange"
            :class="{ 'is-loading': isLoading }"
          >
            {{ statusModalData.action === 'suspended' ? 'Suspend' : 'Ban' }} User
          </button>
          <button class="button is-light" @click="closeStatusModal">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAdminStore } from '../stores/admin'
import { storeToRefs } from 'pinia'

const adminStore = useAdminStore()
const { users, usersPagination, isLoading, error } = storeToRefs(adminStore)

// Filters and search
const searchQuery = ref('')
const statusFilter = ref('')
const sortBy = ref('created_at')
const sortOrder = ref('desc')

// UI state
const activeDropdown = ref<string | null>(null)
const showStatusChangeModal = ref(false)
const statusModalData = ref({
  user: null as any,
  action: '',
  reason: ''
})

// Debounced search
let searchTimeout: number
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    loadUsers()
  }, 500)
}

const loadUsers = () => {
  adminStore.loadUsers({
    page: usersPagination.value.page,
    limit: usersPagination.value.limit,
    search: searchQuery.value,
    status: statusFilter.value,
    sortBy: sortBy.value,
    sortOrder: sortOrder.value
  })
}

const changePage = (page: number) => {
  adminStore.loadUsers({
    page,
    limit: usersPagination.value.limit,
    search: searchQuery.value,
    status: statusFilter.value,
    sortBy: sortBy.value,
    sortOrder: sortOrder.value
  })
}

const visiblePages = computed(() => {
  const current = usersPagination.value.page
  const total = usersPagination.value.totalPages
  const pages: (number | string)[] = []
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    pages.push(1)
    if (current > 4) pages.push('...')
    
    const start = Math.max(2, current - 2)
    const end = Math.min(total - 1, current + 2)
    
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
    
    if (current < total - 3) pages.push('...')
    pages.push(total)
  }
  
  return pages
})

const toggleDropdown = (userId: string) => {
  activeDropdown.value = activeDropdown.value === userId ? null : userId
}

const showStatusModal = (user: any, action: string) => {
  statusModalData.value = {
    user,
    action,
    reason: ''
  }
  showStatusChangeModal.value = true
  activeDropdown.value = null
}

const closeStatusModal = () => {
  showStatusChangeModal.value = false
  statusModalData.value = {
    user: null,
    action: '',
    reason: ''
  }
}

const confirmStatusChange = async () => {
  if (!statusModalData.value.user) return
  
  await updateUserStatus(
    statusModalData.value.user.id, 
    statusModalData.value.action, 
    statusModalData.value.reason
  )
  closeStatusModal()
}

const updateUserStatus = async (userId: string, status: string, reason?: string) => {
  const result = await adminStore.updateUserStatus(userId, status, reason)
  if (result.success) {
    // Optionally show success message
  }
}

const toggleUserAdmin = async (user: any) => {
  const result = await adminStore.toggleAdminStatus(user.id, !user.isAdmin)
  if (result.success) {
    // Optionally show success message
  }
  activeDropdown.value = null
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const formatRelativeDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Yesterday'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`
  return `${Math.floor(diffDays / 365)} years ago`
}

// Close dropdown when clicking outside
document.addEventListener('click', (e) => {
  if (!(e.target as Element).closest('.dropdown')) {
    activeDropdown.value = null
  }
})

onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.admin-users {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 0;
}

.admin-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

/* Header */
.admin-header {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 2rem;
}

.header-info {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  flex: 1;
}

.home-button {
  background: #28a745;
  color: white;
  border-color: #28a745;
  border-radius: 6px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.home-button:hover {
  background: #218838;
  border-color: #1e7e34;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #363636;
  margin: 0 0 0.5rem 0;
}

.page-subtitle {
  color: #6c757d;
  font-size: 1.125rem;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  flex-shrink: 0;
}

.button {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.button.is-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.button.is-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.button.is-light {
  background: #f8f9fa;
  color: #6c757d;
  border-color: #e9ecef;
}

.button.is-light:hover {
  background: #e9ecef;
  color: #495057;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

/* Filters */
.filters-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.filters-content {
  display: flex;
  flex-direction: column;
}

.filter-group {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 1.5rem;
  align-items: end;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.search-filter {
  position: relative;
}

.filter-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #363636;
}

.search-input {
  position: relative;
}

.input {
  border: 2px solid #e9ecef;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  padding-left: 2.5rem;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  outline: none;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.select-wrapper {
  position: relative;
}

.filter-select {
  width: 100%;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  outline: none;
}

/* Error notification */
.error-notification {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #dc3545;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.error-content .icon {
  color: #dc3545;
  font-size: 1.25rem;
}

.error-content p {
  color: #363636;
  margin: 0;
  flex: 1;
}

/* Users card */
.users-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Loading state */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.loader {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: #6c757d;
  margin: 0;
}

/* Empty state */
.empty-state {
  padding: 3rem;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.empty-content .icon {
  color: #6c757d;
  font-size: 3rem;
}

.empty-content h3 {
  color: #363636;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.empty-content p {
  color: #6c757d;
  margin: 0;
}

/* Users list */
.users-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
  background: #f8f9fa;
}

/* User card */
.user-card {
  background: white;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
}

.user-card:hover {
  background: #f8f9fa;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
}

.user-details {
  flex: 1;
}

.user-name-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.25rem;
}

.user-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #363636;
  margin: 0;
}

.user-badges {
  display: flex;
  gap: 0.5rem;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.admin-badge {
  background: #ffc107;
  color: #212529;
}

.oauth-badge {
  background: #17a2b8;
  color: white;
}

.user-email {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0 0 0.75rem 0;
}

.user-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.status-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-active {
  background: #28a745;
  color: white;
}

.status-suspended {
  background: #ffc107;
  color: #212529;
}

.status-banned {
  background: #dc3545;
  color: white;
}

.verification-badge {
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.verified {
  background: #d4edda;
  color: #155724;
}

.unverified {
  background: #fff3cd;
  color: #856404;
}

.stats-section {
  display: flex;
  gap: 0.75rem;
}

.stat-item {
  color: #6c757d;
  font-size: 0.8rem;
}

.date-section {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.created-date {
  color: #363636;
  font-size: 0.8rem;
  font-weight: 500;
}

.relative-date {
  color: #6c757d;
  font-size: 0.75rem;
}

/* User actions */
.user-actions {
  flex-shrink: 0;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  z-index: 20;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border: 1px solid #e9ecef;
  overflow: hidden;
  min-width: 180px;
}

.dropdown-content {
  padding: 0.5rem 0;
  background: white;
}

.dropdown-item {
  padding: 0.75rem 1rem;
  color: #495057;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #363636;
}

.dropdown-item.has-text-danger {
  color: #dc3545;
}

.dropdown-item.has-text-danger:hover {
  background-color: #f8d7da;
  color: #721c24;
}

.dropdown-divider {
  margin: 0.5rem 0;
  border-color: #e9ecef;
}

.button.is-ghost {
  background: transparent;
  border: none;
  border-radius: 6px;
  padding: 0.5rem;
  transition: all 0.2s ease;
  color: #6c757d;
  cursor: pointer;
}

.button.is-ghost:hover {
  background-color: #f8f9fa;
  color: #495057;
}

/* Pagination */
.pagination-wrapper {
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.pagination-info {
  color: #6c757d;
  font-size: 0.9rem;
}

.pagination-pages {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.page-button {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-button:hover {
  background: #f8f9fa;
  border-color: #dee2e6;
}

.page-button.is-current {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

.page-ellipsis {
  padding: 0.5rem 0.75rem;
  color: #6c757d;
}

/* Modal styling */
.modal-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-card-head {
  background: #007bff;
  color: white;
  border: none;
  padding: 1.5rem 2rem;
}

.modal-card-title {
  color: white;
  font-weight: 600;
}

.modal-card-body {
  padding: 2rem;
  background: white;
}

.modal-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.modal-message {
  color: #363636;
  margin: 0;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #363636;
}

.field-textarea {
  border: 2px solid #e9ecef;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 1rem;
  resize: vertical;
  transition: all 0.2s ease;
}

.field-textarea:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  outline: none;
}

.modal-card-foot {
  background: #f8f9fa;
  border: none;
  padding: 1.5rem 2rem;
  justify-content: flex-end;
  gap: 1rem;
}

.button.is-danger {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

.button.is-danger:hover {
  background: #c82333;
  border-color: #bd2130;
}

/* Responsive design */
@media (max-width: 768px) {
  .admin-container {
    padding: 1rem;
  }
  
  .admin-header {
    padding: 1.5rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .header-info {
    flex-direction: column;
    gap: 1rem;
  }
  
  .header-actions {
    width: 100%;
  }
  
  .button {
    flex: 1;
    justify-content: center;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .page-subtitle {
    font-size: 1rem;
  }
  
  .filter-group {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .user-card {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .user-info {
    width: 100%;
  }
  
  .user-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .user-actions {
    align-self: flex-end;
  }
  
  .pagination-controls {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .admin-container {
    padding: 0.75rem;
  }
  
  .admin-header {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 1.25rem;
  }
  
  .user-card {
    padding: 0.75rem;
  }
  
  .user-avatar {
    width: 40px;
    height: 40px;
  }
  
  .avatar-placeholder {
    font-size: 1rem;
  }
  
  .user-name {
    font-size: 1rem;
  }
}
</style>
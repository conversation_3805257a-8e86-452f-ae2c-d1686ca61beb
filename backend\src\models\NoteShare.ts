import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';

export type AccessLevel = 'private' | 'shared' | 'unlisted' | 'public';
export type Permission = 'view' | 'comment' | 'edit';

export interface ShareSettings {
  accessLevel: AccessLevel;
  permissions: Permission[];
  expiresAt?: Date;
  passwordProtected: boolean;
  password?: string;
  allowedIps?: string[];
  watermark?: boolean;
}

export interface NoteShare {
  id: string;
  noteId: string;
  shareToken: string;
  accessLevel: AccessLevel;
  permissions: Permission[];
  expiresAt?: Date;
  passwordHash?: string;
  allowedIps?: string[];
  watermark: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  accessCount: number;
  lastAccessedAt?: Date;
}

export interface ShareAccess {
  id: string;
  shareId: string;
  ipAddress: string;
  userAgent: string;
  accessedAt: Date;
  userId?: string;
}

export interface CreateShareData {
  noteId: string;
  accessLevel: AccessLevel;
  permissions: Permission[];
  expiresAt?: Date;
  password?: string;
  allowedIps?: string[];
  watermark?: boolean;
  createdBy: string;
}

export interface UpdateShareData {
  accessLevel?: AccessLevel;
  permissions?: Permission[];
  expiresAt?: Date;
  password?: string;
  allowedIps?: string[];
  watermark?: boolean;
}

export interface ShareFilters {
  noteId?: string;
  createdBy?: string;
  accessLevel?: AccessLevel;
  isExpired?: boolean;
}

export class NoteShareModel {
  static generateId(): string {
    return uuidv4();
  }

  static generateShareToken(): string {
    // Generate a secure random token for sharing
    return crypto.randomBytes(32).toString('hex');
  }

  static hashPassword(password: string): string {
    return crypto.createHash('sha256').update(password).digest('hex');
  }

  static verifyPassword(password: string, hash: string): boolean {
    const passwordHash = this.hashPassword(password);
    return crypto.timingSafeEqual(Buffer.from(passwordHash), Buffer.from(hash));
  }

  static validateAccessLevel(accessLevel: string): boolean {
    return ['private', 'shared', 'unlisted', 'public'].includes(accessLevel);
  }

  static validatePermissions(permissions: string[]): boolean {
    const validPermissions = ['view', 'comment', 'edit'];
    return permissions.every(permission => validPermissions.includes(permission));
  }

  static validateShareData(data: CreateShareData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.noteId) {
      errors.push('Note ID is required');
    }

    if (!data.createdBy) {
      errors.push('Creator ID is required');
    }

    if (!this.validateAccessLevel(data.accessLevel)) {
      errors.push('Invalid access level');
    }

    if (!data.permissions || !Array.isArray(data.permissions) || data.permissions.length === 0) {
      errors.push('At least one permission is required');
    } else if (!this.validatePermissions(data.permissions)) {
      errors.push('Invalid permissions');
    }

    if (data.expiresAt && new Date(data.expiresAt) <= new Date()) {
      errors.push('Expiration date must be in the future');
    }

    if (data.password && data.password.length < 6) {
      errors.push('Password must be at least 6 characters long');
    }

    if (data.allowedIps && Array.isArray(data.allowedIps)) {
      for (const ip of data.allowedIps) {
        if (!this.validateIpAddress(ip)) {
          errors.push(`Invalid IP address: ${ip}`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static validateIpAddress(ip: string): boolean {
    // Basic IP validation (IPv4 and IPv6)
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    const ipv4CidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/;
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip) || ipv4CidrRegex.test(ip);
  }

  static isShareExpired(share: NoteShare): boolean {
    if (!share.expiresAt) return false;
    return new Date() > new Date(share.expiresAt);
  }

  static canAccess(share: NoteShare, ipAddress?: string, password?: string): { canAccess: boolean; reason?: string } {
    // Check if share is expired
    if (this.isShareExpired(share)) {
      return { canAccess: false, reason: 'Share has expired' };
    }

    // Check IP restrictions
    if (share.allowedIps && share.allowedIps.length > 0 && ipAddress) {
      const isAllowed = share.allowedIps.some(allowedIp => {
        if (allowedIp.includes('/')) {
          // CIDR notation - simplified check
          const [network, prefix] = allowedIp.split('/');
          return ipAddress.startsWith(network.split('.').slice(0, parseInt(prefix) / 8).join('.'));
        }
        return allowedIp === ipAddress;
      });

      if (!isAllowed) {
        return { canAccess: false, reason: 'IP address not allowed' };
      }
    }

    // Check password protection
    if (share.passwordHash && !password) {
      return { canAccess: false, reason: 'Password required' };
    }

    if (share.passwordHash && password && !this.verifyPassword(password, share.passwordHash)) {
      return { canAccess: false, reason: 'Invalid password' };
    }

    return { canAccess: true };
  }

  static getShareUrl(baseUrl: string, shareToken: string): string {
    return `${baseUrl}/shared/${shareToken}`;
  }

  static sanitizeShareForResponse(share: NoteShare): Omit<NoteShare, 'passwordHash'> {
    const { passwordHash, ...sanitizedShare } = share;
    return sanitizedShare;
  }

  static getDefaultShareSettings(): ShareSettings {
    return {
      accessLevel: 'private',
      permissions: ['view'],
      passwordProtected: false,
      watermark: false
    };
  }
}
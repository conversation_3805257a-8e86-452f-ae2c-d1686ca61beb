import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SearchService } from '../../services/SearchService';
import { getDatabase } from '../../config/database';

// Mock database
vi.mock('../../config/database');

describe('SearchService', () => {
  let mockDb: any;

  beforeEach(() => {
    mockDb = {
      all: vi.fn(),
      get: vi.fn(),
      prepare: vi.fn(() => ({
        all: vi.fn(),
        get: vi.fn()
      }))
    };
    vi.mocked(getDatabase).mockReturnValue(mockDb);
  });

  describe('search', () => {
    it('should perform full-text search with query', async () => {
      const mockResults = [
        {
          id: 'note-1',
          title: 'JavaScript Tutorial',
          content: 'Learn JavaScript fundamentals',
          note_type: 'richtext',
          user_id: 'user-123',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];

      mockDb.all.mockResolvedValue(mockResults);

      const filters = {
        userId: 'user-123',
        query: 'JavaScript'
      };

      const options = {
        page: 1,
        limit: 10
      };

      const result = await SearchService.search(filters, options);

      expect(result.results).toHaveLength(1);
      expect(result.results[0].note.title).toBe('JavaScript Tutorial');
      expect(result.total).toBe(1);
      expect(result.searchTime).toBeGreaterThan(0);
    });

    it('should filter by note type', async () => {
      const mockResults = [
        {
          id: 'note-1',
          title: 'Markdown Note',
          content: '# Header',
          note_type: 'markdown',
          user_id: 'user-123',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];

      mockDb.all.mockResolvedValue(mockResults);

      const filters = {
        userId: 'user-123',
        noteType: 'markdown' as const
      };

      const options = {
        page: 1,
        limit: 10
      };

      const result = await SearchService.search(filters, options);

      expect(result.results).toHaveLength(1);
      expect(result.results[0].note.note_type).toBe('markdown');
    });

    it('should handle empty search results', async () => {
      mockDb.all.mockResolvedValue([]);

      const filters = {
        userId: 'user-123',
        query: 'nonexistent'
      };

      const options = {
        page: 1,
        limit: 10
      };

      const result = await SearchService.search(filters, options);

      expect(result.results).toHaveLength(0);
      expect(result.total).toBe(0);
    });

    it('should handle database errors', async () => {
      mockDb.all.mockRejectedValue(new Error('Database connection failed'));

      const filters = {
        userId: 'user-123',
        query: 'test'
      };

      const options = {
        page: 1,
        limit: 10
      };

      await expect(SearchService.search(filters, options)).rejects.toThrow('Database connection failed');
    });
  });

  describe('getSuggestions', () => {
    it('should return search suggestions', async () => {
      const mockSuggestions = [
        { suggestion: 'JavaScript' },
        { suggestion: 'JavaScript Tutorial' },
        { suggestion: 'JavaScript React' }
      ];

      mockDb.all.mockResolvedValue(mockSuggestions);

      const suggestions = await SearchService.getSuggestions('user-123', 'java');

      expect(suggestions).toHaveLength(3);
      expect(suggestions).toContain('JavaScript');
      expect(suggestions).toContain('JavaScript Tutorial');
    });

    it('should handle empty suggestions', async () => {
      mockDb.all.mockResolvedValue([]);

      const suggestions = await SearchService.getSuggestions('user-123', 'nonexistent');

      expect(suggestions).toHaveLength(0);
    });
  });

  describe('performance requirements', () => {
    it('should complete search within 500ms', async () => {
      // Mock a large dataset
      const mockResults = Array.from({ length: 100 }, (_, i) => ({
        id: `note-${i}`,
        title: `Note ${i}`,
        content: `Content for note ${i}`,
        note_type: 'richtext',
        user_id: 'user-123',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      mockDb.all.mockResolvedValue(mockResults);

      const filters = {
        userId: 'user-123',
        query: 'note'
      };

      const options = {
        page: 1,
        limit: 100
      };

      const startTime = Date.now();
      const result = await SearchService.search(filters, options);
      const endTime = Date.now();

      const searchTime = endTime - startTime;
      expect(searchTime).toBeLessThan(500); // Should be under 500ms
      expect(result.results).toHaveLength(100);
    });
  });
});
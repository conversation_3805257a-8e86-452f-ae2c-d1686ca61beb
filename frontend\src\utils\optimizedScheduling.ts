// Optimized scheduling utilities for better performance
// Fine-tuned requestIdleCallback timing and fallbacks

export interface SchedulingOptions {
  timeout?: number
  priority?: 'high' | 'normal' | 'low'
  defer?: boolean
}

// Enhanced requestIdleCallback with better timing control
export function scheduleIdleTask(
  callback: (deadline: IdleDeadline) => void,
  options: SchedulingOptions = {}
): number {
  const { timeout = 1000, priority = 'normal', defer = false } = options
  
  // Determine optimal timeout based on priority
  const priorityTimeouts = {
    high: 500,    // High priority tasks get shorter timeout
    normal: 1000, // Normal priority
    low: 2000     // Low priority tasks can wait longer
  }
  
  const finalTimeout = timeout || priorityTimeouts[priority]
  
  // If defer is true, add a small delay before scheduling
  if (defer) {
    return setTimeout(() => {
      scheduleIdleTaskImmediate(callback, finalTimeout)
    }, 16) // One frame delay
  }
  
  return scheduleIdleTaskImmediate(callback, finalTimeout)
}

function scheduleIdleTaskImmediate(
  callback: (deadline: IdleDeadline) => void,
  timeout: number
): number {
  if ('requestIdleCallback' in window) {
    return window.requestIdleCallback(callback, { timeout })
  } else {
    // Fallback for browsers without requestIdleCallback
    return setTimeout(() => {
      const start = performance.now()
      callback({
        didTimeout: false,
        timeRemaining: () => Math.max(0, 50 - (performance.now() - start))
      } as IdleDeadline)
    }, 0)
  }
}

// Optimized task batching for better performance
export class TaskBatcher {
  private tasks: Array<() => void> = []
  private isScheduled = false
  private batchSize: number
  private timeout: number
  
  constructor(batchSize = 5, timeout = 1000) {
    this.batchSize = batchSize
    this.timeout = timeout
  }
  
  addTask(task: () => void): void {
    this.tasks.push(task)
    
    if (!this.isScheduled) {
      this.scheduleExecution()
    }
  }
  
  private scheduleExecution(): void {
    this.isScheduled = true
    
    scheduleIdleTask((deadline) => {
      this.executeBatch(deadline)
    }, { timeout: this.timeout, priority: 'normal' })
  }
  
  private executeBatch(deadline: IdleDeadline): void {
    let executed = 0
    
    while (
      this.tasks.length > 0 && 
      executed < this.batchSize && 
      (deadline.timeRemaining() > 5 || deadline.didTimeout)
    ) {
      const task = this.tasks.shift()
      if (task) {
        try {
          task()
          executed++
        } catch (error) {
          console.warn('Task execution failed:', error)
        }
      }
    }
    
    // If there are more tasks, schedule another batch
    if (this.tasks.length > 0) {
      this.scheduleExecution()
    } else {
      this.isScheduled = false
    }
  }
  
  clear(): void {
    this.tasks = []
    this.isScheduled = false
  }
  
  get pendingTasks(): number {
    return this.tasks.length
  }
}

// Optimized progressive loading with better timing
export function scheduleProgressiveLoad(
  phases: Array<{
    name: string
    task: () => Promise<void> | void
    priority: 'critical' | 'important' | 'normal' | 'low'
    delay?: number
  }>
): Promise<void> {
  return new Promise((resolve) => {
    let completedPhases = 0
    const totalPhases = phases.length
    
    // Sort phases by priority
    const priorityOrder = { critical: 0, important: 1, normal: 2, low: 3 }
    const sortedPhases = [...phases].sort((a, b) => 
      priorityOrder[a.priority] - priorityOrder[b.priority]
    )
    
    function executeNextPhase(index: number): void {
      if (index >= sortedPhases.length) {
        resolve()
        return
      }
      
      const phase = sortedPhases[index]
      const delay = phase.delay || 0
      
      const executePhase = async () => {
        try {
          console.log(`🔄 Executing phase: ${phase.name} (priority: ${phase.priority})`)
          const startTime = performance.now()
          
          await phase.task()
          
          const duration = performance.now() - startTime
          console.log(`✅ Phase ${phase.name} completed in ${duration.toFixed(2)}ms`)
          
          completedPhases++
          
          // Schedule next phase based on priority
          if (phase.priority === 'critical') {
            // Critical phases execute immediately
            executeNextPhase(index + 1)
          } else if (phase.priority === 'important') {
            // Important phases use short delay
            setTimeout(() => executeNextPhase(index + 1), 16)
          } else {
            // Normal and low priority phases use idle scheduling
            scheduleIdleTask(() => {
              executeNextPhase(index + 1)
            }, { 
              priority: phase.priority === 'normal' ? 'normal' : 'low',
              timeout: phase.priority === 'normal' ? 1000 : 2000
            })
          }
          
        } catch (error) {
          console.error(`❌ Phase ${phase.name} failed:`, error)
          // Continue with next phase even if current fails
          executeNextPhase(index + 1)
        }
      }
      
      if (delay > 0) {
        setTimeout(executePhase, delay)
      } else {
        executePhase()
      }
    }
    
    executeNextPhase(0)
  })
}

// Performance-aware timeout with automatic adjustment
export function createAdaptiveTimeout<T>(
  operation: () => Promise<T>,
  baseTimeout: number,
  options: {
    maxRetries?: number
    backoffMultiplier?: number
    performanceAware?: boolean
  } = {}
): Promise<T> {
  const { maxRetries = 3, backoffMultiplier = 1.5, performanceAware = true } = options
  
  return new Promise((resolve, reject) => {
    let attempts = 0
    
    function attemptOperation(): void {
      attempts++
      
      // Adjust timeout based on device performance if enabled
      let adjustedTimeout = baseTimeout
      if (performanceAware && 'hardwareConcurrency' in navigator) {
        const cores = navigator.hardwareConcurrency || 4
        // Increase timeout on devices with fewer cores
        if (cores < 4) {
          adjustedTimeout *= 1.5
        }
      }
      
      // Apply backoff for retries
      if (attempts > 1) {
        adjustedTimeout *= Math.pow(backoffMultiplier, attempts - 1)
      }
      
      const timeoutId = setTimeout(() => {
        if (attempts < maxRetries) {
          console.warn(`Operation timeout (attempt ${attempts}/${maxRetries}), retrying...`)
          attemptOperation()
        } else {
          reject(new Error(`Operation timed out after ${maxRetries} attempts`))
        }
      }, adjustedTimeout)
      
      operation()
        .then((result) => {
          clearTimeout(timeoutId)
          resolve(result)
        })
        .catch((error) => {
          clearTimeout(timeoutId)
          if (attempts < maxRetries) {
            console.warn(`Operation failed (attempt ${attempts}/${maxRetries}), retrying:`, error)
            setTimeout(attemptOperation, 100 * attempts) // Progressive delay
          } else {
            reject(error)
          }
        })
    }
    
    attemptOperation()
  })
}
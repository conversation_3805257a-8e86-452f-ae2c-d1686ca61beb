/**
 * Theme Preloader - <PERSON>les intelligent theme preloading and caching
 */

export interface PreloadStrategy {
  immediate: string[]    // Themes to load immediately
  priority: string[]     // Themes to load with high priority
  background: string[]   // Themes to load in background
}

export interface PreloadProgress {
  total: number
  loaded: number
  current: string | null
  errors: string[]
}

export class ThemePreloader {
  private loadedThemes = new Set<string>()
  private loadingPromises = new Map<string, Promise<void>>()
  private progressCallbacks = new Set<(progress: PreloadProgress) => void>()
  private serviceWorkerRegistration: ServiceWorkerRegistration | null = null

  constructor() {
    this.initializeServiceWorker()
  }

  /**
   * Initialize service worker for offline caching
   */
  private async initializeServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        this.serviceWorkerRegistration = await navigator.serviceWorker.register('/sw.js')
        console.log('Theme preloader: Service worker registered')
      } catch (error) {
        console.warn('Theme preloader: Service worker registration failed:', error)
      }
    }
  }

  /**
   * Get intelligent preload strategy based on user preferences and system
   */
  getPreloadStrategy(): PreloadStrategy {
    const savedTheme = localStorage.getItem('theme-name') || 'default'
    const savedMode = localStorage.getItem('theme-mode') || 'auto'
    const systemPreference = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    
    // Determine immediate themes (critical for first paint)
    const immediate: string[] = []
    if (savedMode === 'auto') {
      immediate.push(systemPreference === 'dark' ? 'darkly' : 'default')
    } else {
      immediate.push(savedTheme)
    }

    // High priority themes (likely to be used) - reduced in development to prevent timeouts
    const priority: string[] = []
    
    // In development, skip priority preloading to avoid timeouts
    if (!import.meta.env.DEV) {
      if (savedMode === 'dark' || (savedMode === 'auto' && systemPreference === 'dark')) {
        // User prefers dark, preload light theme as backup
        priority.push('default')
      } else {
        // User prefers light, preload dark theme as backup
        priority.push('darkly')
      }
      
      // Add saved theme if it's not already in immediate or priority
      if (!immediate.includes(savedTheme) && !priority.includes(savedTheme)) {
        priority.push(savedTheme)
      }
    }

    // Background themes (nice to have) - disabled in development to prevent timeouts
    const background = import.meta.env.DEV ? [] : ['flatly', 'cerulean']

    return {
      immediate: [...new Set(immediate)],
      priority: [...new Set(priority.filter(t => !immediate.includes(t)))],
      background: [...new Set(background.filter(t => !immediate.includes(t) && !priority.includes(t)))]
    }
  }

  /**
   * Execute preload strategy with progress tracking
   */
  async executePreloadStrategy(
    strategy: PreloadStrategy,
    themeManager: any
  ): Promise<void> {
    const allThemes = [...strategy.immediate, ...strategy.priority, ...strategy.background]
    const progress: PreloadProgress = {
      total: allThemes.length,
      loaded: 0,
      current: null,
      errors: []
    }

    // Notify initial progress
    this.notifyProgress(progress)

    try {
      // Phase 1: Immediate themes (blocking)
      for (const theme of strategy.immediate) {
        progress.current = theme
        this.notifyProgress(progress)
        
        try {
          await this.preloadTheme(theme, themeManager)
          progress.loaded++
        } catch (error) {
          progress.errors.push(`${theme}: ${error}`)
        }
        
        this.notifyProgress(progress)
      }

      // Phase 2: Priority themes (high priority, non-blocking)
      const priorityPromises = strategy.priority.map(async theme => {
        progress.current = theme
        this.notifyProgress(progress)
        
        try {
          await this.preloadTheme(theme, themeManager)
          progress.loaded++
        } catch (error) {
          progress.errors.push(`${theme}: ${error}`)
        }
        
        this.notifyProgress(progress)
      })

      await Promise.allSettled(priorityPromises)

      // Phase 3: Background themes (low priority, with delays)
      for (let i = 0; i < strategy.background.length; i++) {
        const theme = strategy.background[i]
        
        // Add delay to prevent blocking
        if (i > 0) {
          await new Promise(resolve => setTimeout(resolve, 200))
        }

        progress.current = theme
        this.notifyProgress(progress)
        
        try {
          await this.preloadTheme(theme, themeManager)
          progress.loaded++
        } catch (error) {
          progress.errors.push(`${theme}: ${error}`)
        }
        
        this.notifyProgress(progress)
      }

    } finally {
      progress.current = null
      this.notifyProgress(progress)
    }
  }

  /**
   * Preload a single theme with caching
   */
  private async preloadTheme(themeName: string, themeManager: any): Promise<void> {
    if (this.loadedThemes.has(themeName)) {
      return
    }

    if (this.loadingPromises.has(themeName)) {
      return this.loadingPromises.get(themeName)!
    }

    const loadPromise = this.loadThemeWithCache(themeName, themeManager)
    this.loadingPromises.set(themeName, loadPromise)

    try {
      await loadPromise
      this.loadedThemes.add(themeName)
    } finally {
      this.loadingPromises.delete(themeName)
    }
  }

  /**
   * Load theme with intelligent caching
   */
  private async loadThemeWithCache(themeName: string, themeManager: any): Promise<void> {
    try {
      // Try to preload via service worker first
      if (this.serviceWorkerRegistration) {
        await this.preloadViaServiceWorker(themeName)
      }

      // Load theme through theme manager
      await themeManager.preloadTheme(themeName)
    } catch (error) {
      console.warn(`Failed to preload theme ${themeName}:`, error)
      throw error
    }
  }

  /**
   * Preload theme via service worker
   */
  private async preloadViaServiceWorker(themeName: string): Promise<void> {
    if (!this.serviceWorkerRegistration?.active) {
      return
    }

    const themeUrls = [
      `/src/styles/themes/bulmaswatch/${themeName}.json`,
      `/src/styles/themes/bulmaswatch/${themeName}.css`
    ]

    this.serviceWorkerRegistration.active.postMessage({
      type: 'PRELOAD_THEMES',
      themes: themeUrls
    })
  }

  /**
   * Add progress callback
   */
  onProgress(callback: (progress: PreloadProgress) => void): void {
    this.progressCallbacks.add(callback)
  }

  /**
   * Remove progress callback
   */
  offProgress(callback: (progress: PreloadProgress) => void): void {
    this.progressCallbacks.delete(callback)
  }

  /**
   * Notify all progress callbacks
   */
  private notifyProgress(progress: PreloadProgress): void {
    this.progressCallbacks.forEach(callback => {
      try {
        callback({ ...progress })
      } catch (error) {
        console.error('Error in preload progress callback:', error)
      }
    })
  }

  /**
   * Get preload statistics
   */
  getStats(): { loaded: number; loading: number; total: number } {
    return {
      loaded: this.loadedThemes.size,
      loading: this.loadingPromises.size,
      total: this.loadedThemes.size + this.loadingPromises.size
    }
  }

  /**
   * Clear preload cache
   */
  clear(): void {
    this.loadedThemes.clear()
    this.loadingPromises.clear()
  }

  /**
   * Preload themes based on user behavior patterns
   */
  async preloadBasedOnUsage(): Promise<void> {
    // Get usage statistics from localStorage
    const usageStats = this.getUsageStats()
    
    // Sort themes by usage frequency
    const sortedThemes = Object.entries(usageStats)
      .sort(([, a], [, b]) => b - a)
      .map(([theme]) => theme)
      .slice(0, 5) // Top 5 most used themes

    // Create strategy based on usage
    const strategy: PreloadStrategy = {
      immediate: sortedThemes.slice(0, 1),
      priority: sortedThemes.slice(1, 3),
      background: sortedThemes.slice(3, 5)
    }

    // Note: Would need themeManager instance to execute
    console.log('Usage-based preload strategy:', strategy)
    return Promise.resolve()
  }

  /**
   * Get theme usage statistics
   */
  private getUsageStats(): Record<string, number> {
    try {
      const stats = localStorage.getItem('theme-usage-stats')
      return stats ? JSON.parse(stats) : {}
    } catch {
      return {}
    }
  }

  /**
   * Record theme usage for future optimization
   */
  recordThemeUsage(themeName: string): void {
    try {
      const stats = this.getUsageStats()
      stats[themeName] = (stats[themeName] || 0) + 1
      localStorage.setItem('theme-usage-stats', JSON.stringify(stats))
    } catch (error) {
      console.warn('Failed to record theme usage:', error)
    }
  }
}

// Export singleton instance
export const themePreloader = new ThemePreloader()
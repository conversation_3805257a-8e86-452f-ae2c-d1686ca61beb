import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import TagInput from '../TagInput.vue'
import type { Tag } from '../../../services/noteService'

describe('TagInput', () => {
  const mockTags: Tag[] = [
    { id: '1', name: 'work', userId: 'user1', createdAt: '2023-01-01' },
    { id: '2', name: 'personal', userId: 'user1', createdAt: '2023-01-01' },
    { id: '3', name: 'ideas', userId: 'user1', createdAt: '2023-01-01' }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly with empty tags', () => {
    const wrapper = mount(TagInput, {
      props: {
        modelValue: [],
        suggestions: mockTags
      }
    })

    expect(wrapper.find('.tag-input-field').exists()).toBe(true)
    expect(wrapper.find('.tag-input-field').attributes('placeholder')).toBe('Add tags...')
  })

  it('displays selected tags', () => {
    const selectedTags = [mockTags[0], mockTags[1]]
    const wrapper = mount(TagInput, {
      props: {
        modelValue: selectedTags,
        suggestions: mockTags
      }
    })

    const tagElements = wrapper.findAll('.tag')
    expect(tagElements).toHaveLength(2)
    expect(tagElements[0].text()).toContain('work')
    expect(tagElements[1].text()).toContain('personal')
  })

  it('shows suggestions when typing', async () => {
    const wrapper = mount(TagInput, {
      props: {
        modelValue: [],
        suggestions: mockTags
      }
    })

    const input = wrapper.find('.tag-input-field')
    await input.setValue('wo')
    await input.trigger('input')

    // Wait for debounce
    await new Promise(resolve => setTimeout(resolve, 350))

    expect(wrapper.find('.dropdown').exists()).toBe(true)
    expect(wrapper.text()).toContain('work')
  })

  it('emits update:modelValue when tag is added', async () => {
    const wrapper = mount(TagInput, {
      props: {
        modelValue: [],
        suggestions: mockTags
      }
    })

    const input = wrapper.find('.tag-input-field')
    await input.setValue('work')
    await input.trigger('keydown', { key: 'Enter' })

    const emitted = wrapper.emitted('update:modelValue')
    expect(emitted).toBeTruthy()
    expect(emitted![0][0]).toEqual([mockTags[0]])
  })

  it('removes tag when delete button is clicked', async () => {
    const selectedTags = [mockTags[0], mockTags[1]]
    const wrapper = mount(TagInput, {
      props: {
        modelValue: selectedTags,
        suggestions: mockTags
      }
    })

    const deleteButton = wrapper.find('.tag .delete')
    await deleteButton.trigger('click')

    const emitted = wrapper.emitted('update:modelValue')
    expect(emitted).toBeTruthy()
    expect(emitted![0][0]).toEqual([mockTags[1]])
  })

  it('creates new tag when allowCreate is true', async () => {
    const wrapper = mount(TagInput, {
      props: {
        modelValue: [],
        suggestions: mockTags,
        allowCreate: true
      }
    })

    const input = wrapper.find('.tag-input-field')
    await input.setValue('newtag')
    await input.trigger('input')
    
    // Wait for debounce
    await new Promise(resolve => setTimeout(resolve, 350))
    
    await input.trigger('keydown', { key: 'Enter' })

    const emitted = wrapper.emitted('tag-created')
    expect(emitted).toBeTruthy()
    expect(emitted![0][0]).toBe('newtag')
  })

  it('validates tag names correctly', async () => {
    const wrapper = mount(TagInput, {
      props: {
        modelValue: [],
        suggestions: mockTags,
        allowCreate: true,
        validateTag: (name: string) => name.length >= 3 || 'Tag must be at least 3 characters'
      }
    })

    const input = wrapper.find('.tag-input-field')
    await input.setValue('ab')
    await input.trigger('input')
    
    // Wait for debounce
    await new Promise(resolve => setTimeout(resolve, 350))
    
    await input.trigger('keydown', { key: 'Enter' })

    // Should not create tag due to validation
    const emitted = wrapper.emitted('tag-created')
    expect(emitted).toBeFalsy()
  })

  it('handles paste events correctly', async () => {
    const wrapper = mount(TagInput, {
      props: {
        modelValue: [],
        suggestions: mockTags,
        allowCreate: true
      }
    })

    const input = wrapper.find('.tag-input-field')
    
    // Mock clipboard data
    const clipboardData = {
      getData: vi.fn().mockReturnValue('tag1,tag2,tag3')
    }
    
    await input.trigger('paste', {
      clipboardData
    })

    const emitted = wrapper.emitted('tag-created')
    expect(emitted).toBeTruthy()
    expect(emitted!.length).toBe(3) // Should create 3 tags
  })
})
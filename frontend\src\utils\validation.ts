export interface ValidationRule {
  test: (value: string) => boolean
  message: string
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

export const emailRules: ValidationRule[] = [
  {
    test: (value: string) => !!value.trim(),
    message: 'Email is required'
  },
  {
    test: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    message: 'Please enter a valid email address'
  }
]

export const passwordRules: ValidationRule[] = [
  {
    test: (value: string) => !!value,
    message: 'Password is required'
  },
  {
    test: (value: string) => value.length >= 8,
    message: 'Password must be at least 8 characters long'
  },
  {
    test: (value: string) => /[A-Z]/.test(value),
    message: 'Password must contain at least one uppercase letter'
  },
  {
    test: (value: string) => /[a-z]/.test(value),
    message: 'Password must contain at least one lowercase letter'
  },
  {
    test: (value: string) => /\d/.test(value),
    message: 'Password must contain at least one number'
  },
  {
    test: (value: string) => /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?~`]/.test(value),
    message: 'Password must contain at least one special character'
  }
]

export const displayNameRules: ValidationRule[] = [
  {
    test: (value: string) => !!value.trim(),
    message: 'Display name is required'
  },
  {
    test: (value: string) => value.trim().length >= 2,
    message: 'Display name must be at least 2 characters long'
  },
  {
    test: (value: string) => value.trim().length <= 50,
    message: 'Display name must be less than 50 characters'
  }
]

export const confirmPasswordRule = (originalPassword: string): ValidationRule => ({
  test: (value: string) => value === originalPassword,
  message: 'Passwords do not match'
})

export const validateField = (value: string, rules: ValidationRule[]): ValidationResult => {
  const errors: string[] = []
  
  for (const rule of rules) {
    if (!rule.test(value)) {
      errors.push(rule.message)
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validateForm = (fields: Record<string, { value: string; rules: ValidationRule[] }>): Record<string, ValidationResult> => {
  const results: Record<string, ValidationResult> = {}
  
  for (const [fieldName, field] of Object.entries(fields)) {
    results[fieldName] = validateField(field.value, field.rules)
  }
  
  return results
}
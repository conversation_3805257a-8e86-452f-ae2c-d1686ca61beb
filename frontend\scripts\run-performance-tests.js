#!/usr/bin/env node

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Run a command and return a promise
 */
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 Running: ${command} ${args.join(' ')}`);

    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options,
    });

    child.on('close', code => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', error => {
      reject(error);
    });
  });
}

/**
 * Check if build exists
 */
function checkBuildExists() {
  const distPath = path.join(__dirname, '../dist');
  return fs.existsSync(distPath);
}

/**
 * Run performance test suite
 */
async function runPerformanceTests(options = {}) {
  console.log('🚀 Starting Comprehensive Performance Test Suite\n');

  const results = {
    build: false,
    bundleSize: false,
    budgetCheck: false,
    regressionCheck: false,
    unitTests: false,
    errors: [],
  };

  try {
    // Step 1: Build the application
    if (!checkBuildExists() || options.forceBuild) {
      console.log('📦 Building application...');
      await runCommand('npm', ['run', 'build']);
      results.build = true;
      console.log('✅ Build completed\n');
    } else {
      console.log('📦 Using existing build\n');
      results.build = true;
    }

    // Step 2: Run bundle size analysis
    console.log('📊 Running bundle size analysis...');
    try {
      await runCommand('node', ['scripts/check-bundle-size.js']);
      results.bundleSize = true;
      console.log('✅ Bundle size check passed\n');
    } catch (error) {
      results.errors.push('Bundle size check failed');
      console.log('❌ Bundle size check failed\n');
    }

    // Step 3: Run performance budget check
    console.log('💰 Running performance budget check...');
    try {
      await runCommand('node', ['scripts/performance-budget-check.js']);
      results.budgetCheck = true;
      console.log('✅ Performance budget check passed\n');
    } catch (error) {
      results.errors.push('Performance budget check failed');
      console.log('❌ Performance budget check failed\n');
    }

    // Step 4: Run regression check
    console.log('🔍 Running performance regression check...');
    try {
      const regressionArgs = ['scripts/performance-regression-check.js'];
      if (options.updateBaseline) {
        regressionArgs.push('--update-baseline');
      }
      if (options.noFailOnRegression) {
        regressionArgs.push('--no-fail');
      }

      await runCommand('node', regressionArgs);
      results.regressionCheck = true;
      console.log('✅ Performance regression check passed\n');
    } catch (error) {
      results.errors.push('Performance regression check failed');
      console.log('❌ Performance regression check failed\n');
    }

    // Step 5: Run performance unit tests
    if (options.runTests) {
      console.log('🧪 Running performance unit tests...');
      try {
        await runCommand('npm', ['run', 'test:unit', '--', '--run', 'src/__tests__/performance/']);
        results.unitTests = true;
        console.log('✅ Performance unit tests passed\n');
      } catch (error) {
        results.errors.push('Performance unit tests failed');
        console.log('❌ Performance unit tests failed\n');
      }
    }

    // Generate summary report
    generateSummaryReport(results, options);

    return results;
  } catch (error) {
    console.error('❌ Performance test suite failed:', error.message);
    results.errors.push(error.message);
    return results;
  }
}

/**
 * Generate summary report
 */
function generateSummaryReport(results, options) {
  console.log('📋 Performance Test Summary\n');
  console.log('='.repeat(50));

  const tests = [
    { name: 'Application Build', status: results.build },
    { name: 'Bundle Size Analysis', status: results.bundleSize },
    { name: 'Performance Budget Check', status: results.budgetCheck },
    { name: 'Regression Detection', status: results.regressionCheck },
  ];

  if (options.runTests) {
    tests.push({ name: 'Performance Unit Tests', status: results.unitTests });
  }

  tests.forEach(test => {
    const status = test.status ? '✅ PASSED' : '❌ FAILED';
    console.log(`${test.name.padEnd(30)} ${status}`);
  });

  console.log('='.repeat(50));

  const passedTests = tests.filter(t => t.status).length;
  const totalTests = tests.length;

  if (passedTests === totalTests) {
    console.log(`🎉 All ${totalTests} performance tests PASSED!`);
  } else {
    console.log(`⚠️  ${passedTests}/${totalTests} performance tests passed`);

    if (results.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      results.errors.forEach(error => {
        console.log(`   • ${error}`);
      });
    }
  }

  // Performance recommendations
  if (results.errors.length > 0) {
    console.log('\n💡 Recommendations:');

    if (results.errors.some(e => e.includes('Bundle size'))) {
      console.log('   • Review bundle size and implement code splitting');
      console.log('   • Remove unused dependencies');
      console.log('   • Optimize images and assets');
    }

    if (results.errors.some(e => e.includes('budget'))) {
      console.log('   • Optimize initialization time with parallel loading');
      console.log('   • Implement progressive loading strategies');
      console.log('   • Defer non-critical operations');
    }

    if (results.errors.some(e => e.includes('regression'))) {
      console.log('   • Review recent changes for performance impact');
      console.log('   • Profile the application to identify bottlenecks');
      console.log('   • Consider updating performance baselines if changes are intentional');
    }
  }

  console.log('\n📊 For detailed analysis, check:');
  console.log('   • Bundle analyzer: npm run bundle-analyze');
  console.log('   • Performance metrics: Check browser dev tools');
  console.log('   • Lighthouse audit: npm run lighthouse (if configured)');
}

/**
 * CLI interface
 */
function main() {
  const args = process.argv.slice(2);

  const options = {
    forceBuild: args.includes('--force-build') || args.includes('-f'),
    updateBaseline: args.includes('--update-baseline') || args.includes('-u'),
    noFailOnRegression: args.includes('--no-fail-regression'),
    runTests: args.includes('--run-tests') || args.includes('-t'),
    help: args.includes('--help') || args.includes('-h'),
  };

  if (options.help) {
    console.log('Performance Test Suite\n');
    console.log('Usage: node run-performance-tests.js [options]\n');
    console.log('Options:');
    console.log('  --force-build, -f           Force rebuild even if dist exists');
    console.log('  --update-baseline, -u       Update performance baselines');
    console.log("  --no-fail-regression        Don't fail on performance regressions");
    console.log('  --run-tests, -t             Run performance unit tests');
    console.log('  --help, -h                  Show this help message\n');
    console.log('Examples:');
    console.log(
      '  node run-performance-tests.js                    # Run basic performance checks'
    );
    console.log(
      '  node run-performance-tests.js -f -t              # Force build and run all tests'
    );
    console.log('  node run-performance-tests.js -u                 # Update baselines');
    return;
  }

  runPerformanceTests(options)
    .then(results => {
      const hasErrors = results.errors.length > 0;
      const allPassed =
        results.build && results.bundleSize && results.budgetCheck && results.regressionCheck;

      if (hasErrors || !allPassed) {
        process.exit(1);
      } else {
        process.exit(0);
      }
    })
    .catch(error => {
      console.error('❌ Performance test suite crashed:', error);
      process.exit(1);
    });
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { runPerformanceTests };

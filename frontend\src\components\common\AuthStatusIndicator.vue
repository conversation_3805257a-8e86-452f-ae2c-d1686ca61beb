<template>
    <div v-if="showIndicator" class="auth-status-indicator" :class="statusClass">
        <div class="status-content">
            <i class="fas fa-circle-notch fa-spin" v-if="isInitializing"></i>
            <i class="fas fa-exclamation-triangle" v-else-if="hasTimeout"></i>
            <i class="fas fa-wifi-slash" v-else-if="isOffline"></i>

            <span class="status-text">{{ statusText }}</span>

            <button v-if="canRetry" @click="retryAuth" class="retry-button" :disabled="isRetrying">
                <i class="fas fa-redo" :class="{ 'fa-spin': isRetrying }"></i>
                Retry
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '../../stores/auth'

const authStore = useAuthStore()

const isInitializing = ref(false)
const hasTimeout = ref(false)
const isOffline = ref(false)
const isRetrying = ref(false)
const showIndicator = ref(false)

const statusClass = computed(() => ({
    'is-loading': isInitializing.value,
    'is-warning': hasTimeout.value,
    'is-offline': isOffline.value
}))

const statusText = computed(() => {
    if (isRetrying.value) return 'Retrying authentication...'
    if (isInitializing.value) return 'Initializing authentication...'
    if (hasTimeout.value) return 'Authentication taking longer than expected'
    if (isOffline.value) return 'Working offline with cached data'
    return ''
})

const canRetry = computed(() => {
    return (hasTimeout.value || isOffline.value) && !isRetrying.value && authStore.token
})

const retryAuth = async () => {
    if (isRetrying.value) return

    isRetrying.value = true
    hasTimeout.value = false
    isOffline.value = false

    try {
        await authStore.retryInitialization()
        hideIndicator()
    } catch (error) {
        console.warn('Auth retry failed:', error)
        hasTimeout.value = true
    } finally {
        isRetrying.value = false
    }
}

const hideIndicator = () => {
    showIndicator.value = false
    isInitializing.value = false
    hasTimeout.value = false
    isOffline.value = false
}

const handleAuthTimeout = () => {
    if (authStore.token && !authStore.user) {
        hasTimeout.value = true
        isInitializing.value = false
        showIndicator.value = true

        // Auto-hide after 10 seconds if user doesn't interact
        setTimeout(() => {
            if (hasTimeout.value && !isRetrying.value) {
                hideIndicator()
            }
        }, 10000)
    }
}

const handleOfflineMode = () => {
    isOffline.value = true
    isInitializing.value = false
    showIndicator.value = true

    // Auto-hide after 5 seconds for offline mode
    setTimeout(() => {
        if (isOffline.value && !isRetrying.value) {
            hideIndicator()
        }
    }, 5000)
}

const handleAuthSuccess = () => {
    hideIndicator()
}

onMounted(() => {
    // Show indicator if auth is currently initializing
    if (authStore.token && !authStore.user && !authStore.isInitialized) {
        isInitializing.value = true
        showIndicator.value = true
    }

    // Listen for auth events
    window.addEventListener('auth-timeout', handleAuthTimeout)
    window.addEventListener('app-offline-mode', handleOfflineMode)
    window.addEventListener('user-logged-in', handleAuthSuccess)
    window.addEventListener('auth-logout-complete', hideIndicator)
})

onUnmounted(() => {
    window.removeEventListener('auth-timeout', handleAuthTimeout)
    window.removeEventListener('app-offline-mode', handleOfflineMode)
    window.removeEventListener('user-logged-in', handleAuthSuccess)
    window.removeEventListener('auth-logout-complete', hideIndicator)
})
</script>

<style scoped>
.auth-status-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 300px;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.auth-status-indicator.is-loading {
    background: rgba(50, 115, 220, 0.9);
    color: white;
    border-left: 4px solid #3273dc;
}

.auth-status-indicator.is-warning {
    background: rgba(255, 221, 87, 0.9);
    color: #8a6d3b;
    border-left: 4px solid #ffdd57;
}

.auth-status-indicator.is-offline {
    background: rgba(169, 169, 169, 0.9);
    color: white;
    border-left: 4px solid #a9a9a9;
}

.status-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-text {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
}

.retry-button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: inherit;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.retry-button:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.retry-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .auth-status-indicator.is-warning {
        background: rgba(255, 193, 7, 0.9);
        color: #212529;
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .auth-status-indicator {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .status-content {
        flex-wrap: wrap;
    }

    .retry-button {
        margin-left: auto;
    }
}
</style>
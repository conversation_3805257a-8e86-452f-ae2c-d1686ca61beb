<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Group Page Styling Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Test styles to simulate the improved group page styling */
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .group-detail {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .header-card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e8e8e8;
            margin-bottom: 2rem;
        }

        .title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #6c757d;
            margin-bottom: 1rem;
        }

        .tags {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .tag {
            border-radius: 6px;
            font-weight: 500;
            padding: 0.5rem 0.75rem;
        }

        .tag.is-light {
            background-color: #f8f9fa;
            color: #495057;
            border: 1px solid #e9ecef;
        }

        .tag.is-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .buttons {
            display: flex;
            gap: 0.75rem;
        }

        .button {
            border-radius: 6px;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }

        .button.is-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .button.is-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .button.is-light {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            color: #495057;
        }

        .button.is-light:hover {
            background-color: #e9ecef;
            transform: translateY(-1px);
        }

        .tabs-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e8e8e8;
            overflow: hidden;
        }

        .tabs {
            margin: 0;
        }

        .tabs ul {
            border-bottom: none;
            margin: 0;
        }

        .tabs li a {
            border: none;
            padding: 1.25rem 2rem;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .tabs li a:hover {
            background-color: #f8f9fa;
            color: #495057;
        }

        .tabs li.is-active a {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            padding: 2rem;
            background: white;
            min-height: 400px;
        }

        .members-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .table {
            margin: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #495057;
            font-weight: 600;
            padding: 1.25rem 1.5rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table tbody td {
            padding: 1.5rem;
            border: none;
            border-bottom: 1px solid #f1f3f4;
        }

        .member-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.75rem;
            margin-right: 1rem;
        }

        .member-info {
            display: flex;
            align-items: center;
        }

        .member-details strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .member-details small {
            color: #6c757d;
            font-size: 0.85rem;
        }

        @media (max-width: 768px) {
            .group-detail {
                padding: 1rem 0.5rem;
            }
            
            .header-card {
                padding: 1.5rem;
            }
            
            .buttons {
                flex-direction: column;
            }
            
            .button {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="group-detail">
        <!-- Header -->
        <div class="header-card">
            <div class="level">
                <div class="level-left">
                    <div class="level-item">
                        <div>
                            <h1 class="title is-3">Development Team</h1>
                            <p class="subtitle is-5">
                                Collaborative workspace for our development projects and discussions
                            </p>
                            <div class="tags">
                                <span class="tag is-light">
                                    <span class="icon">
                                        <i class="fas fa-users"></i>
                                    </span>
                                    <span>5 members</span>
                                </span>
                                <span class="tag is-primary">
                                    Admin
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="level-right">
                    <div class="level-item">
                        <div class="buttons">
                            <button class="button is-primary">
                                <span class="icon">
                                    <i class="fas fa-user-plus"></i>
                                </span>
                                <span>Invite Member</span>
                            </button>
                            <button class="button is-light">
                                <span class="icon">
                                    <i class="fas fa-cog"></i>
                                </span>
                                <span>Settings</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="tabs-container">
            <div class="tabs">
                <ul>
                    <li class="is-active">
                        <a>
                            <span class="icon is-small">
                                <i class="fas fa-users"></i>
                            </span>
                            <span>Members</span>
                        </a>
                    </li>
                    <li>
                        <a>
                            <span class="icon is-small">
                                <i class="fas fa-sticky-note"></i>
                            </span>
                            <span>Notes</span>
                        </a>
                    </li>
                    <li>
                        <a>
                            <span class="icon is-small">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <span>Invitations</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <div class="members-table">
                    <table class="table is-fullwidth is-hoverable">
                        <thead>
                            <tr>
                                <th>Member</th>
                                <th>Role</th>
                                <th>Joined</th>
                                <th class="has-text-right">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="member-info">
                                        <div class="member-avatar">JD</div>
                                        <div class="member-details">
                                            <strong>John Doe</strong><br>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="tag is-primary">Admin</span>
                                    <span class="tag is-light ml-2">Owner</span>
                                </td>
                                <td>
                                    <span class="has-text-grey">Jan 15, 2024</span>
                                </td>
                                <td class="has-text-right">
                                    <button class="button is-small is-ghost">
                                        <span class="icon">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </span>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="member-info">
                                        <div class="member-avatar">AS</div>
                                        <div class="member-details">
                                            <strong>Alice Smith</strong><br>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="tag is-info">Editor</span>
                                </td>
                                <td>
                                    <span class="has-text-grey">Jan 20, 2024</span>
                                </td>
                                <td class="has-text-right">
                                    <button class="button is-small is-ghost">
                                        <span class="icon">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </span>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="member-info">
                                        <div class="member-avatar">BJ</div>
                                        <div class="member-details">
                                            <strong>Bob Johnson</strong><br>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="tag is-success">Viewer</span>
                                </td>
                                <td>
                                    <span class="has-text-grey">Feb 1, 2024</span>
                                </td>
                                <td class="has-text-right">
                                    <button class="button is-small is-ghost">
                                        <span class="icon">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </span>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple tab switching functionality for demo
        document.querySelectorAll('.tabs li a').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Remove active class from all tabs
                document.querySelectorAll('.tabs li').forEach(li => li.classList.remove('is-active'));
                
                // Add active class to clicked tab
                e.target.closest('li').classList.add('is-active');
            });
        });

        // Button hover effects
        document.querySelectorAll('.button').forEach(button => {
            button.addEventListener('mouseenter', () => {
                button.style.transform = 'translateY(-1px)';
            });
            
            button.addEventListener('mouseleave', () => {
                button.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
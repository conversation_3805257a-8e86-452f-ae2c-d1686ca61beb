/* Form Component Styles */

/* Field Container */
.field {
  margin-bottom: var(--spacing-3);
}

.field:last-child {
  margin-bottom: 0;
}

.field.has-addons {
  display: flex;
}

.field.has-addons .control {
  flex: 1;
}

.field.has-addons .control:not(:last-child) {
  margin-right: -1px;
}

.field.has-addons .control:not(:last-child) .input,
.field.has-addons .control:not(:last-child) .select select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.field.has-addons .control:not(:first-child) .input,
.field.has-addons .control:not(:first-child) .select select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.field.is-grouped {
  display: flex;
  gap: var(--spacing-2);
}

.field.is-grouped .control {
  flex-shrink: 0;
}

.field.is-grouped.is-grouped-centered {
  justify-content: center;
}

.field.is-grouped.is-grouped-right {
  justify-content: flex-end;
}

/* Control Container */
.control {
  position: relative;
  text-align: inherit;
}

.control.has-icons-left,
.control.has-icons-right {
  position: relative;
}

.control.has-icons-left .input,
.control.has-icons-left .select select {
  padding-left: 2.5em;
}

.control.has-icons-right .input,
.control.has-icons-right .select select {
  padding-right: 2.5em;
}

.control.has-icons-left .icon.is-left,
.control.has-icons-right .icon.is-right {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 4;
}

.control.has-icons-left .icon.is-left {
  left: 0.75em;
}

.control.has-icons-right .icon.is-right {
  right: 0.75em;
}

/* Labels */
.label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-strong);
  font-size: var(--font-size-sm);
}

.label.is-required::after {
  content: ' *';
  color: var(--color-danger);
}

/* Input Fields */
.input,
.textarea,
.select select {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--input-border);
  border-radius: var(--radius);
  font-size: var(--font-size-base);
  background: var(--input-background);
  color: var(--color-text);
  transition: var(--transition-fast);
  appearance: none;
}

.input:focus,
.textarea:focus,
.select select:focus {
  outline: none;
  border-color: var(--input-focus-border);
  box-shadow: 0 0 0 2px var(--input-focus-shadow);
}

.input:hover,
.textarea:hover,
.select select:hover {
  border-color: var(--input-border-hover);
}

.input:disabled,
.textarea:disabled,
.select select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--color-surface);
}

.input::placeholder,
.textarea::placeholder {
  color: var(--color-text-muted);
  opacity: 1;
}

/* Input Sizes */
.input.is-small,
.textarea.is-small,
.select.is-small select {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-sm);
}

.input.is-medium,
.textarea.is-medium,
.select.is-medium select {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-lg);
}

.input.is-large,
.textarea.is-large,
.select.is-large select {
  padding: var(--spacing-4) var(--spacing-5);
  font-size: var(--font-size-xl);
}

/* Input States */
.input.is-success,
.textarea.is-success,
.select.is-success select {
  border-color: var(--color-success);
}

.input.is-success:focus,
.textarea.is-success:focus,
.select.is-success select:focus {
  box-shadow: 0 0 0 2px rgba(35, 209, 96, 0.2);
}

.input.is-danger,
.textarea.is-danger,
.select.is-danger select {
  border-color: var(--color-danger);
}

.input.is-danger:focus,
.textarea.is-danger:focus,
.select.is-danger select:focus {
  box-shadow: 0 0 0 2px rgba(255, 56, 96, 0.2);
}

.input.is-warning,
.textarea.is-warning,
.select.is-warning select {
  border-color: var(--color-warning);
}

.input.is-warning:focus,
.textarea.is-warning:focus,
.select.is-warning select:focus {
  box-shadow: 0 0 0 2px rgba(255, 221, 87, 0.2);
}

/* Textarea Specific */
.textarea {
  resize: vertical;
  min-height: 120px;
  line-height: var(--line-height-relaxed);
}

.textarea.has-fixed-size {
  resize: none;
}

/* Select Dropdown */
.select {
  display: inline-block;
  position: relative;
  vertical-align: top;
  width: 100%;
}

.select select {
  padding-right: 2.5em;
  cursor: pointer;
}

.select::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 1em;
  transform: translateY(-50%);
  border: 0.35em solid transparent;
  border-top-color: var(--color-text-muted);
  pointer-events: none;
  z-index: 2;
}

.select:hover::after {
  border-top-color: var(--color-text);
}

.select.is-multiple select {
  height: auto;
  padding-right: var(--spacing-3);
}

.select.is-multiple::after {
  display: none;
}

/* Checkbox and Radio */
.checkbox,
.radio {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  position: relative;
}

.checkbox input[type='checkbox'],
.radio input[type='radio'] {
  margin-right: var(--spacing-2);
  cursor: pointer;
}

.checkbox:hover,
.radio:hover {
  color: var(--color-text-strong);
}

.checkbox input[type='checkbox']:disabled,
.radio input[type='radio']:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.checkbox:has(input:disabled),
.radio:has(input:disabled) {
  cursor: not-allowed;
  opacity: 0.6;
}

/* File Input */
.file {
  display: inline-flex;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.file-input {
  position: absolute;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  z-index: -1;
}

.file-cta {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  background: var(--color-surface);
  color: var(--color-text);
  cursor: pointer;
  transition: var(--transition-fast);
}

.file:hover .file-cta {
  background: var(--color-surface-hover);
  border-color: var(--color-border-hover);
}

.file-icon {
  margin-right: var(--spacing-2);
}

.file-label {
  font-weight: var(--font-weight-medium);
}

.file-name {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--color-border);
  border-left: none;
  background: var(--input-background);
  color: var(--color-text-muted);
  max-width: 16em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Help Text */
.help {
  display: block;
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
}

.help.is-success {
  color: var(--color-success);
}

.help.is-danger {
  color: var(--color-danger);
}

.help.is-warning {
  color: var(--color-warning-dark);
}

.help.is-info {
  color: var(--color-info);
}

/* Form Validation */
.field.has-error .input,
.field.has-error .textarea,
.field.has-error .select select {
  border-color: var(--color-danger);
}

.field.has-error .input:focus,
.field.has-error .textarea:focus,
.field.has-error .select select:focus {
  box-shadow: 0 0 0 2px rgba(255, 56, 96, 0.2);
}

.field.has-success .input,
.field.has-success .textarea,
.field.has-success .select select {
  border-color: var(--color-success);
}

.field.has-success .input:focus,
.field.has-success .textarea:focus,
.field.has-success .select select:focus {
  box-shadow: 0 0 0 2px rgba(35, 209, 96, 0.2);
}

/* Responsive Form Adjustments */
@media screen and (max-width: 768px) {
  .field.is-grouped {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .field.is-grouped .control {
    width: 100%;
  }

  .field.has-addons {
    flex-direction: column;
  }

  .field.has-addons .control:not(:last-child) {
    margin-right: 0;
    margin-bottom: -1px;
  }

  .field.has-addons .control:not(:last-child) .input,
  .field.has-addons .control:not(:last-child) .select select {
    border-radius: var(--radius);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .field.has-addons .control:not(:first-child) .input,
  .field.has-addons .control:not(:first-child) .select select {
    border-radius: var(--radius);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

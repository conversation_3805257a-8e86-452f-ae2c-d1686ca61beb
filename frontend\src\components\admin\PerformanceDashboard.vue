<template>
  <div class="performance-dashboard">
    <div class="columns is-multiline">
      <!-- Performance Overview -->
      <div class="column is-12">
        <div class="card">
          <header class="card-header">
            <p class="card-header-title">
              <i class="fas fa-tachometer-alt mr-2"></i>
              Performance Overview
            </p>
            <button class="card-header-icon" @click="refreshData">
              <span class="icon">
                <i class="fas fa-sync-alt" :class="{ 'fa-spin': isRefreshing }"></i>
              </span>
            </button>
          </header>
          <div class="card-content">
            <div class="columns is-multiline">
              <div class="column is-3">
                <div class="has-text-centered">
                  <p class="heading">Page Load Time</p>
                  <p class="title is-4" :class="getPerformanceClass(performanceMetrics.pageLoadTime, 2000)">
                    {{ formatTime(performanceMetrics.pageLoadTime) }}
                  </p>
                </div>
              </div>
              <div class="column is-3">
                <div class="has-text-centered">
                  <p class="heading">First Contentful Paint</p>
                  <p class="title is-4" :class="getPerformanceClass(performanceMetrics.firstContentfulPaint, 1500)">
                    {{ formatTime(performanceMetrics.firstContentfulPaint) }}
                  </p>
                </div>
              </div>
              <div class="column is-3">
                <div class="has-text-centered">
                  <p class="heading">Largest Contentful Paint</p>
                  <p class="title is-4" :class="getPerformanceClass(performanceMetrics.largestContentfulPaint, 2500)">
                    {{ formatTime(performanceMetrics.largestContentfulPaint) }}
                  </p>
                </div>
              </div>
              <div class="column is-3">
                <div class="has-text-centered">
                  <p class="heading">First Input Delay</p>
                  <p class="title is-4" :class="getPerformanceClass(performanceMetrics.firstInputDelay, 100)">
                    {{ formatTime(performanceMetrics.firstInputDelay) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- API Performance -->
      <div class="column is-6">
        <div class="card">
          <header class="card-header">
            <p class="card-header-title">
              <i class="fas fa-exchange-alt mr-2"></i>
              API Performance
            </p>
          </header>
          <div class="card-content">
            <div v-if="apiStats">
              <div class="field is-grouped is-grouped-multiline">
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag">Total Calls</span>
                    <span class="tag is-info">{{ apiStats.totalCalls }}</span>
                  </div>
                </div>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag">Cache Hit Rate</span>
                    <span class="tag is-success">{{ apiStats.cacheHitRate.toFixed(1) }}%</span>
                  </div>
                </div>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag">Avg Duration</span>
                    <span class="tag" :class="getPerformanceClass(parseFloat(apiStats.averageDuration), 500)">
                      {{ apiStats.averageDuration }}ms
                    </span>
                  </div>
                </div>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag">Slow Calls</span>
                    <span class="tag is-warning">{{ apiStats.slowCallRate.toFixed(1) }}%</span>
                  </div>
                </div>
              </div>

              <!-- Top Endpoints -->
              <div class="mt-4">
                <p class="subtitle is-6">Top Endpoints</p>
                <div class="table-container">
                  <table class="table is-fullwidth is-striped">
                    <thead>
                      <tr>
                        <th>Endpoint</th>
                        <th>Calls</th>
                        <th>Avg Duration</th>
                        <th>Error Rate</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="endpoint in apiStats.endpointStats.slice(0, 5)" :key="endpoint.endpoint">
                        <td>{{ endpoint.endpoint }}</td>
                        <td>{{ endpoint.count }}</td>
                        <td :class="getPerformanceClass(parseFloat(endpoint.averageDuration), 500)">
                          {{ endpoint.averageDuration }}ms
                        </td>
                        <td>
                          <span class="tag" :class="endpoint.errorRate > 5 ? 'is-danger' : 'is-success'">
                            {{ endpoint.errorRate.toFixed(1) }}%
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div v-else class="has-text-centered">
              <p class="has-text-grey">No API performance data available</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Memory Usage -->
      <div class="column is-6">
        <div class="card">
          <header class="card-header">
            <p class="card-header-title">
              <i class="fas fa-memory mr-2"></i>
              Memory Usage
            </p>
          </header>
          <div class="card-content">
            <div v-if="memoryStats">
              <div class="field is-grouped is-grouped-multiline">
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag">Used</span>
                    <span class="tag is-info">{{ memoryStats.currentUsage.used }}</span>
                  </div>
                </div>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag">Total</span>
                    <span class="tag">{{ memoryStats.currentUsage.total }}</span>
                  </div>
                </div>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag">Usage</span>
                    <span class="tag" :class="getMemoryUsageClass(parseFloat(memoryStats.currentUsage.percentage))">
                      {{ memoryStats.currentUsage.percentage }}%
                    </span>
                  </div>
                </div>
              </div>

              <!-- Memory Usage Progress Bar -->
              <div class="mt-4">
                <p class="subtitle is-6">Memory Usage</p>
                <progress 
                  class="progress" 
                  :class="getMemoryUsageClass(parseFloat(memoryStats.currentUsage.percentage))"
                  :value="memoryStats.currentUsage.percentage" 
                  max="100"
                >
                  {{ memoryStats.currentUsage.percentage }}%
                </progress>
              </div>

              <!-- Memory Trend -->
              <div class="mt-4">
                <p class="subtitle is-6">Memory Trend</p>
                <div class="tags has-addons">
                  <span class="tag">Trend</span>
                  <span class="tag" :class="memoryStats.trend > 1.2 ? 'is-warning' : 'is-success'">
                    {{ memoryStats.trend > 1 ? '↗' : '↘' }} {{ ((memoryStats.trend - 1) * 100).toFixed(1) }}%
                  </span>
                </div>
              </div>
            </div>
            <div v-else class="has-text-centered">
              <p class="has-text-grey">Memory API not supported</p>
            </div>
          </div>
        </div>
      </div>

      <!-- User Interactions -->
      <div class="column is-6">
        <div class="card">
          <header class="card-header">
            <p class="card-header-title">
              <i class="fas fa-mouse-pointer mr-2"></i>
              User Interactions
            </p>
          </header>
          <div class="card-content">
            <div v-if="interactionStats">
              <div class="field is-grouped is-grouped-multiline">
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag">Total</span>
                    <span class="tag is-info">{{ interactionStats.totalInteractions }}</span>
                  </div>
                </div>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag">Avg Duration</span>
                    <span class="tag">{{ interactionStats.averageDuration }}ms</span>
                  </div>
                </div>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag">Slow Rate</span>
                    <span class="tag is-warning">{{ interactionStats.slowInteractionRate.toFixed(1) }}%</span>
                  </div>
                </div>
              </div>

              <!-- Top Actions -->
              <div class="mt-4">
                <p class="subtitle is-6">Top Actions</p>
                <div class="table-container">
                  <table class="table is-fullwidth is-striped">
                    <thead>
                      <tr>
                        <th>Action</th>
                        <th>Count</th>
                        <th>Avg Duration</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="action in interactionStats.actionStats.slice(0, 5)" :key="action.action">
                        <td>{{ action.action }}</td>
                        <td>{{ action.count }}</td>
                        <td :class="getPerformanceClass(parseFloat(action.averageDuration), 100)">
                          {{ action.averageDuration }}ms
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div v-else class="has-text-centered">
              <p class="has-text-grey">No interaction data available</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Cache Statistics -->
      <div class="column is-6">
        <div class="card">
          <header class="card-header">
            <p class="card-header-title">
              <i class="fas fa-database mr-2"></i>
              Cache Statistics
            </p>
            <button class="card-header-icon" @click="clearCache">
              <span class="icon">
                <i class="fas fa-trash"></i>
              </span>
            </button>
          </header>
          <div class="card-content">
            <div class="field is-grouped is-grouped-multiline">
              <div class="control">
                <div class="tags has-addons">
                  <span class="tag">Entries</span>
                  <span class="tag is-info">{{ cacheStats.totalEntries }}</span>
                </div>
              </div>
              <div class="control">
                <div class="tags has-addons">
                  <span class="tag">Expired</span>
                  <span class="tag is-warning">{{ cacheStats.expiredEntries }}</span>
                </div>
              </div>
              <div class="control">
                <div class="tags has-addons">
                  <span class="tag">Size</span>
                  <span class="tag">{{ formatBytes(cacheStats.approximateSize) }}</span>
                </div>
              </div>
            </div>

            <!-- Cache Actions -->
            <div class="mt-4">
              <div class="buttons">
                <button class="button is-small is-warning" @click="clearExpiredCache">
                  <span class="icon">
                    <i class="fas fa-broom"></i>
                  </span>
                  <span>Clear Expired</span>
                </button>
                <button class="button is-small is-danger" @click="clearAllCache">
                  <span class="icon">
                    <i class="fas fa-trash-alt"></i>
                  </span>
                  <span>Clear All</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Export Data -->
    <div class="mt-5">
      <div class="card">
        <header class="card-header">
          <p class="card-header-title">
            <i class="fas fa-download mr-2"></i>
            Export Performance Data
          </p>
        </header>
        <div class="card-content">
          <div class="buttons">
            <button class="button is-primary" @click="exportData">
              <span class="icon">
                <i class="fas fa-file-export"></i>
              </span>
              <span>Export JSON</span>
            </button>
            <button class="button is-info" @click="exportCSV">
              <span class="icon">
                <i class="fas fa-file-csv"></i>
              </span>
              <span>Export CSV</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { performanceService } from '../../services/performanceService'
import { cacheService } from '../../services/cacheService'

// Reactive data
const isRefreshing = ref(false)
const performanceMetrics = ref({
  pageLoadTime: 0,
  firstContentfulPaint: 0,
  largestContentfulPaint: 0,
  firstInputDelay: 0,
  cumulativeLayoutShift: 0,
  timeToInteractive: 0
})
const apiStats = ref<any>(null)
const memoryStats = ref<any>(null)
const interactionStats = ref<any>(null)
const cacheStats = ref({
  totalEntries: 0,
  expiredEntries: 0,
  approximateSize: 0,
  hitRate: 0
})

let refreshInterval: number | null = null

// Methods
const refreshData = async () => {
  isRefreshing.value = true
  
  try {
    performanceMetrics.value = performanceService.getPerformanceMetrics()
    apiStats.value = performanceService.getApiPerformanceStats()
    memoryStats.value = performanceService.getMemoryStats()
    interactionStats.value = performanceService.getUserInteractionStats()
    cacheStats.value = cacheService.getStats()
  } catch (error) {
    console.error('Failed to refresh performance data:', error)
  } finally {
    isRefreshing.value = false
  }
}

const getPerformanceClass = (value: number, threshold: number) => {
  if (value === 0) return ''
  if (value < threshold * 0.5) return 'has-text-success'
  if (value < threshold) return 'has-text-warning'
  return 'has-text-danger'
}

const getMemoryUsageClass = (percentage: number) => {
  if (percentage < 50) return 'is-success'
  if (percentage < 80) return 'is-warning'
  return 'is-danger'
}

const formatTime = (ms: number) => {
  if (ms === 0) return 'N/A'
  if (ms < 1000) return `${ms.toFixed(0)}ms`
  return `${(ms / 1000).toFixed(2)}s`
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

const clearCache = () => {
  cacheService.clear()
  refreshData()
}

const clearExpiredCache = () => {
  // This would need to be implemented in the cache service
  console.log('Clearing expired cache entries...')
  refreshData()
}

const clearAllCache = () => {
  if (confirm('Are you sure you want to clear all cache data?')) {
    cacheService.clear()
    cacheService.clearServiceWorkerCache()
    refreshData()
  }
}

const exportData = () => {
  const data = performanceService.exportPerformanceData()
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-data-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const exportCSV = () => {
  // This would need to be implemented to convert performance data to CSV
  console.log('Exporting CSV data...')
}

// Lifecycle
onMounted(() => {
  refreshData()
  
  // Auto-refresh every 30 seconds
  refreshInterval = window.setInterval(refreshData, 30000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>

<style scoped>
.performance-dashboard {
  padding: 1rem;
}

.progress {
  height: 1rem;
}

.table-container {
  max-height: 300px;
  overflow-y: auto;
}

.tags {
  margin-bottom: 0.5rem;
}
</style>
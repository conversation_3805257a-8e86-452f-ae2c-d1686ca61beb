import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ExportService } from '../../services/ExportService';

// Mock puppeteer and marked
vi.mock('puppeteer');
vi.mock('marked');

describe('ExportService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('exportNote', () => {
    const mockNote = {
      id: 'note-1',
      title: 'Test Note',
      content: 'This is test content',
      note_type: 'richtext' as const,
      user_id: 'user-123',
      created_at: new Date(),
      updated_at: new Date()
    };

    it('should export note to PDF format', async () => {
      const puppeteer = await import('puppeteer');
      const mockBrowser = {
        newPage: vi.fn(),
        close: vi.fn()
      };
      const mockPage = {
        setContent: vi.fn(),
        pdf: vi.fn().mockResolvedValue(Buffer.from('PDF content'))
      };

      vi.mocked(puppeteer.launch).mockResolvedValue(mockBrowser as any);
      mockBrowser.newPage.mockResolvedValue(mockPage);

      const result = await ExportService.exportNote(mockNote, { format: 'pdf' });

      expect(result).toBeInstanceOf(Buffer);
      expect(mockPage.setContent).toHaveBeenCalled();
      expect(mockPage.pdf).toHaveBeenCalled();
      expect(mockBrowser.close).toHaveBeenCalled();
    });

    it('should export note to HTML format', async () => {
      const result = await ExportService.exportNote(mockNote, { format: 'html' });

      expect(result).toBeInstanceOf(Buffer);
      expect(result.toString()).toContain('Test Note');
      expect(result.toString()).toContain('This is test content');
    });

    it('should export note to Markdown format', async () => {
      const result = await ExportService.exportNote(mockNote, { format: 'markdown' });

      expect(result).toBeInstanceOf(Buffer);
      expect(result.toString()).toContain('# Test Note');
      expect(result.toString()).toContain('This is test content');
    });

    it('should handle markdown notes correctly', async () => {
      const markdownNote = {
        ...mockNote,
        content: '# Header\n\nThis is **bold** text',
        note_type: 'markdown' as const
      };

      const marked = await import('marked');
      vi.mocked(marked.parse).mockReturnValue('<h1>Header</h1><p>This is <strong>bold</strong> text</p>');

      const result = await ExportService.exportNote(markdownNote, { format: 'html' });

      expect(marked.parse).toHaveBeenCalledWith('# Header\n\nThis is **bold** text');
      expect(result.toString()).toContain('<h1>Header</h1>');
    });

    it('should include metadata when requested', async () => {
      const result = await ExportService.exportNote(mockNote, { 
        format: 'html',
        includeMetadata: true 
      });

      expect(result.toString()).toContain('Created:');
      expect(result.toString()).toContain('Updated:');
    });

    it('should throw error for unsupported format', async () => {
      await expect(
        ExportService.exportNote(mockNote, { format: 'invalid' as any })
      ).rejects.toThrow('Unsupported export format: invalid');
    });
  });

  describe('exportMultipleNotes', () => {
    const mockNotes = [
      {
        id: 'note-1',
        title: 'First Note',
        content: 'First content',
        note_type: 'richtext' as const,
        user_id: 'user-123',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 'note-2',
        title: 'Second Note',
        content: 'Second content',
        note_type: 'markdown' as const,
        user_id: 'user-123',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    it('should export multiple notes to single file', async () => {
      const result = await ExportService.exportMultipleNotes(mockNotes, { format: 'html' });

      expect(result).toBeInstanceOf(Buffer);
      expect(result.toString()).toContain('First Note');
      expect(result.toString()).toContain('Second Note');
    });

    it('should handle empty notes array', async () => {
      await expect(
        ExportService.exportMultipleNotes([], { format: 'html' })
      ).rejects.toThrow('No notes to export');
    });

    it('should export single note when array has one item', async () => {
      const result = await ExportService.exportMultipleNotes([mockNotes[0]], { format: 'html' });

      expect(result).toBeInstanceOf(Buffer);
      expect(result.toString()).toContain('First Note');
    });
  });

  describe('createExportJob', () => {
    it('should create export job and return job ID', async () => {
      const jobId = await ExportService.createExportJob('user-123', ['note-1', 'note-2'], 'pdf');

      expect(jobId).toBeDefined();
      expect(typeof jobId).toBe('string');
    });

    it('should store job in memory', async () => {
      const jobId = await ExportService.createExportJob('user-123', ['note-1'], 'html');

      const job = await ExportService.getExportJob(jobId);
      expect(job).toBeDefined();
      expect(job!.userId).toBe('user-123');
      expect(job!.noteIds).toEqual(['note-1']);
      expect(job!.format).toBe('html');
      expect(job!.status).toBe('pending');
    });
  });

  describe('getExportJob', () => {
    it('should return export job by ID', async () => {
      const jobId = await ExportService.createExportJob('user-123', ['note-1'], 'pdf');

      const job = await ExportService.getExportJob(jobId);

      expect(job).toBeDefined();
      expect(job!.id).toBe(jobId);
      expect(job!.userId).toBe('user-123');
    });

    it('should return null for non-existent job', async () => {
      const job = await ExportService.getExportJob('non-existent-job');
      expect(job).toBeNull();
    });
  });

  describe('error handling', () => {
    it('should handle PDF generation errors', async () => {
      const mockNote = {
        id: 'note-1',
        title: 'Test Note',
        content: 'Content',
        note_type: 'richtext' as const,
        user_id: 'user-123',
        created_at: new Date(),
        updated_at: new Date()
      };

      const puppeteer = await import('puppeteer');
      vi.mocked(puppeteer.launch).mockRejectedValue(new Error('Puppeteer failed'));

      await expect(
        ExportService.exportNote(mockNote, { format: 'pdf' })
      ).rejects.toThrow('Puppeteer failed');
    });

    it('should handle browser cleanup on error', async () => {
      const mockNote = {
        id: 'note-1',
        title: 'Test Note',
        content: 'Content',
        note_type: 'richtext' as const,
        user_id: 'user-123',
        created_at: new Date(),
        updated_at: new Date()
      };

      const puppeteer = await import('puppeteer');
      const mockBrowser = {
        newPage: vi.fn(),
        close: vi.fn()
      };
      const mockPage = {
        setContent: vi.fn(),
        pdf: vi.fn().mockRejectedValue(new Error('PDF generation failed'))
      };

      vi.mocked(puppeteer.launch).mockResolvedValue(mockBrowser as any);
      mockBrowser.newPage.mockResolvedValue(mockPage);

      await expect(
        ExportService.exportNote(mockNote, { format: 'pdf' })
      ).rejects.toThrow('PDF generation failed');

      expect(mockBrowser.close).toHaveBeenCalled();
    });
  });
});
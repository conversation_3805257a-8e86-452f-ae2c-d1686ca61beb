// Notification Service for User Feedback
// Handles user notifications for lazy loading failures and other system events

export interface NotificationOptions {
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  duration?: number
  persistent?: boolean
  actions?: NotificationAction[]
}

export interface NotificationAction {
  label: string
  action: () => void
  style?: 'primary' | 'secondary' | 'danger'
}

export interface Notification extends NotificationOptions {
  id: string
  timestamp: number
  dismissed: boolean
}

class NotificationService {
  private static instance: NotificationService
  private notifications: Notification[] = []
  private listeners: ((notifications: Notification[]) => void)[] = []
  private notificationId = 0

  private constructor() {
    this.setupLazyLoadingListeners()
  }

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance
  }

  // Setup listeners for lazy loading events
  private setupLazyLoadingListeners() {
    // Listen for lazy loading fallback events
    window.addEventListener('lazy-load-fallback', (event: Event) => {
      const customEvent = event as CustomEvent
      const { moduleName, attempts, error } = customEvent.detail
      
      this.show({
        type: 'warning',
        title: 'Service Degraded',
        message: `${this.getModuleDisplayName(moduleName)} is running with limited functionality due to loading issues.`,
        duration: 8000,
        actions: [
          {
            label: 'Retry',
            action: () => this.retryModuleLoad(moduleName),
            style: 'primary'
          },
          {
            label: 'Details',
            action: () => this.showErrorDetails(moduleName, error, attempts),
            style: 'secondary'
          }
        ]
      })
    })

    // Listen for complete lazy loading failures
    window.addEventListener('lazy-load-failure', (event: Event) => {
      const customEvent = event as CustomEvent
      const { moduleName, attempts, error, hasFallback } = customEvent.detail
      
      if (!hasFallback) {
        this.show({
          type: 'error',
          title: 'Service Unavailable',
          message: `${this.getModuleDisplayName(moduleName)} could not be loaded. Some features may not work properly.`,
          persistent: true,
          actions: [
            {
              label: 'Retry',
              action: () => this.retryModuleLoad(moduleName),
              style: 'primary'
            },
            {
              label: 'Continue',
              action: () => this.dismissByType('error'),
              style: 'secondary'
            }
          ]
        })
      }
    })

    // Listen for app initialization failures
    window.addEventListener('app-init-failure', (event: Event) => {
      const customEvent = event as CustomEvent
      const { error, recoveryAttempted } = customEvent.detail
      
      this.show({
        type: 'error',
        title: 'App Initialization Issue',
        message: recoveryAttempted 
          ? 'The app is running with reduced functionality due to initialization issues.'
          : 'The app failed to initialize properly. Please refresh the page.',
        persistent: !recoveryAttempted,
        actions: recoveryAttempted ? [
          {
            label: 'Refresh Page',
            action: () => window.location.reload(),
            style: 'primary'
          }
        ] : [
          {
            label: 'Refresh Now',
            action: () => window.location.reload(),
            style: 'danger'
          }
        ]
      })
    })

    // Listen for offline mode events
    window.addEventListener('app-offline-mode', (event: Event) => {
      const customEvent = event as CustomEvent
      const { reason, hasAuth } = customEvent.detail
      
      this.show({
        type: 'info',
        title: 'Offline Mode',
        message: hasAuth 
          ? 'You\'re now in offline mode. Some features may be limited.'
          : 'You\'re now in offline mode without authentication. Functionality is limited.',
        duration: 6000
      })
    })
  }

  // Show notification
  show(options: NotificationOptions): string {
    const notification: Notification = {
      ...options,
      id: `notification-${++this.notificationId}`,
      timestamp: Date.now(),
      dismissed: false
    }

    this.notifications.push(notification)
    this.notifyListeners()

    // Auto-dismiss if not persistent and has duration
    if (!options.persistent && options.duration) {
      setTimeout(() => {
        this.dismiss(notification.id)
      }, options.duration)
    }

    return notification.id
  }

  // Dismiss notification by ID
  dismiss(id: string): boolean {
    const index = this.notifications.findIndex(n => n.id === id)
    if (index !== -1) {
      this.notifications[index].dismissed = true
      this.notifications.splice(index, 1)
      this.notifyListeners()
      return true
    }
    return false
  }

  // Dismiss all notifications of a specific type
  dismissByType(type: NotificationOptions['type']): number {
    const dismissed = this.notifications.filter(n => n.type === type)
    this.notifications = this.notifications.filter(n => n.type !== type)
    this.notifyListeners()
    return dismissed.length
  }

  // Clear all notifications
  clear(): void {
    this.notifications = []
    this.notifyListeners()
  }

  // Get all active notifications
  getAll(): Notification[] {
    return [...this.notifications]
  }

  // Subscribe to notification changes
  subscribe(listener: (notifications: Notification[]) => void): () => void {
    this.listeners.push(listener)
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index !== -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  // Private helper methods
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener([...this.notifications])
      } catch (error) {
        console.error('Error in notification listener:', error)
      }
    })
  }

  private getModuleDisplayName(moduleName: string): string {
    const displayNames: Record<string, string> = {
      'auth': 'Authentication Service',
      'settings': 'Settings Service',
      'cacheService': 'Cache Service',
      'performanceService': 'Performance Monitoring',
      'serviceWorker': 'Offline Support',
      'analytics': 'Analytics Service'
    }
    
    return displayNames[moduleName] || moduleName
  }

  private retryModuleLoad(moduleName: string): void {
    console.log(`🔄 Retrying load for ${moduleName}`)
    
    // Clear any cached failed attempts
    import('./lazyLoadingService').then(({ lazyLoadingService }) => {
      lazyLoadingService.clearCache()
      
      // Dispatch event to trigger reload
      window.dispatchEvent(new CustomEvent('retry-module-load', {
        detail: { moduleName }
      }))
    })
    
    // Dismiss current notifications for this module
    this.notifications = this.notifications.filter(n => 
      !n.message.includes(this.getModuleDisplayName(moduleName))
    )
    this.notifyListeners()
  }

  private showErrorDetails(moduleName: string, error: string, attempts: number): void {
    this.show({
      type: 'info',
      title: 'Error Details',
      message: `Module: ${moduleName}\nAttempts: ${attempts}\nError: ${error}`,
      duration: 10000
    })
  }

  // Convenience methods for common notification types
  showSuccess(title: string, message: string, duration = 4000): string {
    return this.show({ type: 'success', title, message, duration })
  }

  showInfo(title: string, message: string, duration = 6000): string {
    return this.show({ type: 'info', title, message, duration })
  }

  showWarning(title: string, message: string, duration = 8000): string {
    return this.show({ type: 'warning', title, message, duration })
  }

  showError(title: string, message: string, persistent = false): string {
    return this.show({ type: 'error', title, message, persistent })
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance()

// Types are already exported above with the interfaces
{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "vitest --run", "test:watch": "vitest", "lint": "eslint src --ext .ts --fix", "format": "prettier --write src/"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/redis": "^4.0.10", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-slow-down": "^2.1.0", "express-validator": "^7.2.0", "google-auth-library": "^10.2.1", "helmet": "^8.1.0", "html-pdf-node": "^1.0.8", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "marked": "^16.2.0", "morgan": "^1.10.1", "puppeteer": "^24.17.0", "redis": "^5.8.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.10", "@types/marked": "^5.0.2", "@types/morgan": "^1.9.10", "@types/node": "^24.3.0", "@types/puppeteer": "^5.4.7", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "axios": "^1.11.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "nodemon": "^3.1.10", "prettier": "^3.6.2", "supertest": "^7.1.4", "ts-node": "^10.9.2", "typescript": "^5.9.2", "vitest": "^3.2.4"}}
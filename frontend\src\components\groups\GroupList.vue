<template>
  <div class="group-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h2 class="title is-4">My Groups</h2>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button 
            class="button is-primary"
            @click="showCreateModal = true"
          >
            <span class="icon">
              <i class="fas fa-plus"></i>
            </span>
            <span>Create Group</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="has-text-centered">
      <div class="loader"></div>
      <p class="mt-2">Loading groups...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="clearError"></button>
      {{ error }}
    </div>

    <!-- Empty state -->
    <div v-else-if="groups.length === 0" class="has-text-centered py-6">
      <div class="icon is-large has-text-grey-light mb-4">
        <i class="fas fa-users fa-3x"></i>
      </div>
      <h3 class="title is-5 has-text-grey">No groups yet</h3>
      <p class="has-text-grey">Create your first group to start collaborating with others.</p>
      <button 
        class="button is-primary mt-4"
        @click="showCreateModal = true"
      >
        <span class="icon">
          <i class="fas fa-plus"></i>
        </span>
        <span>Create Your First Group</span>
      </button>
    </div>

    <!-- Groups grid -->
    <div v-else class="columns is-multiline">
      <div 
        v-for="group in groups" 
        :key="group.id"
        class="column is-one-third"
      >
        <div class="card group-card" @click="selectGroup(group)">
          <div class="card-content">
            <div class="media">
              <div class="media-left">
                <figure class="image is-48x48">
                  <div class="has-background-primary has-text-white is-flex is-align-items-center is-justify-content-center" style="width: 48px; height: 48px; border-radius: 6px;">
                    <span class="icon">
                      <i class="fas fa-users"></i>
                    </span>
                  </div>
                </figure>
              </div>
              <div class="media-content">
                <p class="title is-5">{{ group.name }}</p>
                <p class="subtitle is-6 has-text-grey">
                  {{ group.memberCount }} member{{ group.memberCount !== 1 ? 's' : '' }}
                </p>
              </div>
              <div class="media-right">
                <span 
                  class="tag"
                  :class="getRoleColor(getUserRole(group) ?? 'viewer')"
                >
                  {{ getRoleDisplayName(getUserRole(group) ?? 'viewer') }}
                </span>
              </div>
            </div>

            <div class="content" v-if="group.description">
              <p>{{ group.description }}</p>
            </div>

            <div class="content">
              <small class="has-text-grey">
                Created {{ formatDate(group.createdAt) }}
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Group Modal -->
    <CreateGroupModal 
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @created="onGroupCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useGroupsStore } from '../../stores/groups';
import { useAuthStore } from '../../stores/auth';
import { getRoleDisplayName, getRoleColor } from '../../types/group';
import type { GroupWithMembers, UserRole } from '../../types/group';
import CreateGroupModal from './CreateGroupModal.vue';

const router = useRouter();
const groupsStore = useGroupsStore();
const authStore = useAuthStore();

const showCreateModal = ref(false);

// Computed properties
const groups = computed(() => groupsStore.groups);
const loading = computed(() => groupsStore.loading);
const error = computed(() => groupsStore.error);

// Methods
const clearError = () => {
  groupsStore.clearError();
};

const selectGroup = (group: GroupWithMembers) => {
  router.push(`/groups/${group.id}`);
};

const getUserRole = (group: GroupWithMembers): UserRole | null => {
  if (!authStore.user) return null;
  return groupsStore.getUserRole(group.id, authStore.user.id) as UserRole;
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

const onGroupCreated = (group: GroupWithMembers) => {
  showCreateModal.value = false;
  router.push(`/groups/${group.id}`);
};

// Lifecycle
onMounted(async () => {
  try {
    await groupsStore.loadGroups();
  } catch (error) {
    console.error('Failed to load groups:', error);
  }
});
</script>

<style scoped>
.group-card {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.group-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
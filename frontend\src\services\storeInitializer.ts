// Store Initialization Orchestrator
// Manages parallel store initialization with dependency resolution

export interface StoreInitializationResult {
  success: boolean
  error?: string
  duration: number
  storeName: string
}

export interface StoreConfig {
  name: string
  importPath: string
  initMethod?: string
  dependencies?: string[]
  timeout?: number
  critical?: boolean
}

export class StoreInitializer {
  private stores: Map<string, any> = new Map()
  private initResults: Map<string, StoreInitializationResult> = new Map()
  private startTime: number = 0
  private _hasInitialized: boolean = false

  constructor() {
    this.startTime = performance.now()
  }

  // Initialize stores with dependency management and parallel execution
  async initializeStores(configs: StoreConfig[]): Promise<Map<string, StoreInitializationResult>> {
    console.log('Starting parallel store initialization...')
    console.log('Store configs:', configs.map(c => ({ name: c.name, critical: c.critical })))
    
    // Check if store initializer has already been used and all stores are initialized
    if (this._hasInitialized && this.areStoresAlreadyInitialized(configs)) {
      console.log('Store initializer already used and all stores initialized, skipping...')
      return this.initResults
    }
    
    // Check if all stores are already initialized
    if (this.areStoresAlreadyInitialized(configs)) {
      console.log('All stores already initialized, skipping initialization...')
      return this.initResults
    }
    
    // Group stores by dependency level
    const dependencyGroups = this.resolveDependencies(configs)
    
    // Initialize stores level by level
    for (const group of dependencyGroups) {
      await this.initializeStoreGroup(group)
    }
    
    const totalTime = performance.now() - this.startTime
    console.log(`Store initialization completed in ${totalTime.toFixed(2)}ms`)
    
    this._hasInitialized = true
    return this.initResults
  }

  // Initialize independent stores in parallel
  private async initializeStoreGroup(configs: StoreConfig[]): Promise<void> {
    const promises = configs.map(config => this.initializeStore(config))
    await Promise.all(promises)
  }

  // Initialize a single store with error handling and timeout
  private async initializeStore(config: StoreConfig): Promise<void> {
    const startTime = performance.now()
    
    try {
      console.log(`Initializing store: ${config.name}`)
      
      // Import the store module
      const module = await this.importStoreModule(config)
      
      // Get store instance
      const store = this.getStoreInstance(module, config)
      this.stores.set(config.name, store)
      
      // Check if store is already initialized (for stores that support this)
      if (store.isInitialized !== undefined && store.isInitialized) {
        console.log(`Store ${config.name} already initialized, skipping...`)
        const duration = performance.now() - startTime
        this.initResults.set(config.name, {
          success: true,
          duration,
          storeName: config.name
        })
        return
      }
      
      // Initialize the store if it has an init method
      if (config.initMethod && store[config.initMethod]) {
        await this.executeStoreInitialization(store, config)
      }
      
      const duration = performance.now() - startTime
      this.initResults.set(config.name, {
        success: true,
        duration,
        storeName: config.name
      })
      
      console.log(`Store ${config.name} initialized successfully in ${duration.toFixed(2)}ms`)
      
    } catch (error) {
      const duration = performance.now() - startTime
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      this.initResults.set(config.name, {
        success: false,
        error: errorMessage,
        duration,
        storeName: config.name
      })
      
      console.error(`Store ${config.name} initialization failed:`, errorMessage)
      console.log(`Store ${config.name} critical setting:`, config.critical)
      
      // Re-throw if it's a critical store
      if (config.critical) {
        throw new Error(`Critical store ${config.name} failed to initialize: ${errorMessage}`)
      } else {
        console.log(`Non-critical store ${config.name} failed, continuing...`)
      }
    }
  }

  // Import store module with error handling
  private async importStoreModule(config: StoreConfig): Promise<any> {
    try {
      // Use static imports for known modules to help Vite with analysis
      switch (config.importPath) {
        case '../stores/auth':
          return await import('../stores/auth')
        case '../stores/settings':
          return await import('../stores/settings')
        case '../services/cacheService':
          return await import('../services/cacheService')
        case '../services/performanceService':
          return await import('../services/performanceService')
        default:
          // For unknown paths, use dynamic import with vite-ignore
          return await import(/* @vite-ignore */ config.importPath)
      }
    } catch (error) {
      throw new Error(`Failed to import store module ${config.importPath}: ${error}`)
    }
  }

  // Get store instance from imported module
  private getStoreInstance(module: any, config: StoreConfig): any {
    // Try common store factory patterns
    const possibleFactories = [
      `use${config.name.charAt(0).toUpperCase() + config.name.slice(1)}Store`,
      `use${config.name}Store`,
      'default',
      config.name
    ]
    
    for (const factoryName of possibleFactories) {
      if (module[factoryName] && typeof module[factoryName] === 'function') {
        return module[factoryName]()
      }
    }
    
    // If no factory found, return the module itself
    return module
  }

  // Execute store initialization with timeout
  private async executeStoreInitialization(store: any, config: StoreConfig): Promise<void> {
    const timeout = config.timeout || 3000
    const initMethod = config.initMethod!
    
    const initPromise = store[initMethod]()
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error(`Store ${config.name} initialization timeout`)), timeout)
    )
    
    await Promise.race([initPromise, timeoutPromise])
  }

  // Resolve store dependencies and group by initialization level
  private resolveDependencies(configs: StoreConfig[]): StoreConfig[][] {
    const groups: StoreConfig[][] = []
    const processed = new Set<string>()
    const configMap = new Map(configs.map(config => [config.name, config]))
    
    // Find stores with no dependencies (level 0)
    const noDependencies = configs.filter(config => !config.dependencies || config.dependencies.length === 0)
    if (noDependencies.length > 0) {
      groups.push(noDependencies)
      noDependencies.forEach(config => processed.add(config.name))
    }
    
    // Process remaining stores level by level
    while (processed.size < configs.length) {
      const currentLevel: StoreConfig[] = []
      
      for (const config of configs) {
        if (processed.has(config.name)) continue
        
        // Check if all dependencies are processed
        const dependenciesMet = config.dependencies?.every(dep => processed.has(dep)) ?? true
        
        if (dependenciesMet) {
          currentLevel.push(config)
        }
      }
      
      if (currentLevel.length === 0) {
        // Circular dependency or missing dependency
        const remaining = configs.filter(config => !processed.has(config.name))
        console.warn('Circular or missing dependencies detected for stores:', remaining.map(c => c.name))
        // Add remaining stores to current level to prevent infinite loop
        currentLevel.push(...remaining)
      }
      
      groups.push(currentLevel)
      currentLevel.forEach(config => processed.add(config.name))
    }
    
    return groups
  }

  // Get initialized store by name
  getStore(name: string): any {
    return this.stores.get(name)
  }

  // Get initialization results
  getInitializationResults(): Map<string, StoreInitializationResult> {
    return this.initResults
  }

  // Check if all stores are already initialized
  private areStoresAlreadyInitialized(configs: StoreConfig[]): boolean {
    for (const config of configs) {
      const store = this.stores.get(config.name)
      if (!store || store.isInitialized === undefined || !store.isInitialized) {
        return false
      }
    }
    return true
  }

  // Check if any store needs initialization
  needsInitialization(configs: StoreConfig[]): boolean {
    return !this.areStoresAlreadyInitialized(configs)
  }

  // Get current initialization status of all stores
  getInitializationStatus(configs: StoreConfig[]): Record<string, boolean> {
    const status: Record<string, boolean> = {}
    for (const config of configs) {
      const store = this.stores.get(config.name)
      status[config.name] = store?.isInitialized === true
    }
    return status
  }

  // Force re-initialization of specific stores
  forceReinitializeStores(storeNames: string[]): void {
    for (const storeName of storeNames) {
      const store = this.stores.get(storeName)
      if (store && store.resetInitialization && typeof store.resetInitialization === 'function') {
        store.resetInitialization()
        console.log(`Forced re-initialization of store: ${storeName}`)
      }
    }
    // Clear results for forced stores
    for (const storeName of storeNames) {
      this.initResults.delete(storeName)
    }
  }

  // Get initialization status
  get hasInitialized(): boolean {
    return this._hasInitialized
  }

  // Check if store initializer should be used
  shouldInitialize(configs: StoreConfig[]): boolean {
    // Don't initialize if already done and all stores are ready
    if (this._hasInitialized && this.areStoresAlreadyInitialized(configs)) {
      return false
    }
    
    // Don't initialize if all stores are already initialized
    if (this.areStoresAlreadyInitialized(configs)) {
      return false
    }
    
    return true
  }

  // Reset all store initializations
  resetAllInitializations(): void {
    for (const store of this.stores.values()) {
      if (store.resetInitialization && typeof store.resetInitialization === 'function') {
        store.resetInitialization()
      }
    }
    this.initResults.clear()
    this._hasInitialized = false
    console.log('All store initializations reset')
  }

  // Get initialization statistics
  getStats(): any {
    const results = Array.from(this.initResults.values())
    const successful = results.filter(r => r.success)
    const failed = results.filter(r => !r.success)
    const totalTime = Math.max(...results.map(r => r.duration), 0)
    
    return {
      totalStores: results.length,
      successful: successful.length,
      failed: failed.length,
      totalTime,
      averageTime: results.length > 0 ? results.reduce((sum, r) => sum + r.duration, 0) / results.length : 0,
      failedStores: failed.map(r => ({ name: r.storeName, error: r.error }))
    }
  }
}

// Default store configurations  
export const defaultStoreConfigs: StoreConfig[] = [
  {
    name: 'auth',
    importPath: '../stores/auth', 
    initMethod: 'initializeAuthWithTimeout',
    timeout: 6000, // Increased timeout for better reliability
    critical: false // Non-critical to allow graceful degradation
  },
  {
    name: 'settings',
    importPath: '../stores/settings',
    initMethod: 'initializeSettings',
    dependencies: [], // Independent of auth for parallel execution
    timeout: 2000,
    critical: false
  },
  {
    name: 'cache',
    importPath: '../services/cacheService',
    initMethod: 'preloadCriticalData',
    dependencies: [], // Independent for parallel execution
    timeout: 2000,
    critical: false
  }
]

// Create singleton instance
export const storeInitializer = new StoreInitializer()
import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import AppLayout from '../../../components/layout/AppLayout.vue'

// Mock components to avoid complex dependencies
const mockComponents = {
  Sidebar: {
    template: '<div data-testid="sidebar">Sidebar</div>',
    props: ['isCollapsed'],
    emits: ['toggle-collapse', 'close-mobile']
  },
  NoteList: {
    template: '<div data-testid="notelist">NoteList</div>',
    emits: ['note-selected', 'toggle-editor']
  },
  EditorPanel: {
    template: '<div data-testid="editor">EditorPanel</div>',
    props: ['selectedNote', 'isFullscreen'],
    emits: ['toggle-fullscreen']
  }
}

describe('AppLayout', () => {
  let router: any
  let pinia: any

  beforeEach(() => {
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } }
      ]
    })
    pinia = createPinia()
  })

  it('renders the main layout structure', () => {
    const wrapper = mount(AppLayout, {
      global: {
        plugins: [router, pinia],
        components: mockComponents
      }
    })

    // Check that main layout elements are present
    expect(wrapper.find('.app-layout').exists()).toBe(true)
    expect(wrapper.find('.layout-container').exists()).toBe(true)
    expect(wrapper.find('.sidebar-panel').exists()).toBe(true)
    expect(wrapper.find('.notelist-panel').exists()).toBe(true)
    expect(wrapper.find('.editor-panel').exists()).toBe(true)
  })

  it('shows mobile header on mobile screens', () => {
    const wrapper = mount(AppLayout, {
      global: {
        plugins: [router, pinia],
        components: mockComponents
      }
    })

    const mobileHeader = wrapper.find('.mobile-header')
    expect(mobileHeader.exists()).toBe(true)
    expect(mobileHeader.classes()).toContain('is-hidden-tablet')
  })

  it('renders child components', () => {
    const wrapper = mount(AppLayout, {
      global: {
        plugins: [router, pinia],
        components: mockComponents,
        stubs: {
          Sidebar: mockComponents.Sidebar,
          NoteList: mockComponents.NoteList,
          EditorPanel: mockComponents.EditorPanel
        }
      }
    })

    // Check that the component structure exists
    expect(wrapper.find('.sidebar-panel').exists()).toBe(true)
    expect(wrapper.find('.notelist-panel').exists()).toBe(true)
    expect(wrapper.find('.editor-panel').exists()).toBe(true)
  })

  it('applies responsive layout classes', () => {
    const wrapper = mount(AppLayout, {
      global: {
        plugins: [router, pinia],
        components: mockComponents
      }
    })

    const appLayout = wrapper.find('.app-layout')
    expect(appLayout.exists()).toBe(true)
    
    // Should have responsive classes based on screen width
    // Note: In tests, we can't easily test actual responsive behavior
    // but we can verify the class structure exists
    expect(appLayout.classes()).toContain('app-layout')
  })

  it('handles mobile sidebar toggle', async () => {
    const wrapper = mount(AppLayout, {
      global: {
        plugins: [router, pinia],
        components: mockComponents
      }
    })

    const burgerButton = wrapper.find('.navbar-burger')
    expect(burgerButton.exists()).toBe(true)

    // Initially sidebar should not be active
    const sidebarPanel = wrapper.find('.sidebar-panel')
    expect(sidebarPanel.classes()).not.toContain('is-active')

    // Click burger to open sidebar
    await burgerButton.trigger('click')
    expect(sidebarPanel.classes()).toContain('is-active')

    // Click overlay to close sidebar
    const overlay = wrapper.find('.mobile-overlay')
    expect(overlay.exists()).toBe(true)
    await overlay.trigger('click')
    expect(sidebarPanel.classes()).not.toContain('is-active')
  })
})
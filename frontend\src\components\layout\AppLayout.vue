<template>
  <div class="app-layout" :class="layoutClasses">
    <!-- Mobile Header -->
    <header class="mobile-header mobile-only">
      <nav class="navbar is-primary">
        <div class="navbar-brand">
          <div class="navbar-item is-clickable" @click="goToDashboard">
            <h1 class="title is-5 has-text-white">Notes</h1>
          </div>
          <div class="navbar-burger" @click="toggleMobileSidebar">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </nav>
    </header>

    <!-- Main Layout Container -->
    <div class="layout-container">
      <!-- Sidebar Panel -->
      <aside class="sidebar-panel" :class="{ 'is-active': isSidebarOpen }">
        <Sidebar :is-collapsed="isSidebarCollapsed || (screenWidth < 769 && isSidebarOpen)" :current-section="currentSection" @toggle-collapse="toggleSidebarCollapse"
          @close-mobile="closeMobileSidebar" @create-note="handleCreateNote" @open-settings="openSettingsModal"
          @create-group="openCreateGroupModal" @show-dashboard="handleShowDashboard" @filter-by-tags="handleTagFilter"
          @update-tags="handleUpdateTags" @section-change="handleSectionChange" />
      </aside>

      <!-- Note List Panel -->
      <section class="notelist-panel" :class="{ 'is-hidden': isEditorFullscreen, 'is-visible-mobile': isSidebarOpen }">
        <!-- Breadcrumb Navigation - Only show in 1-2 panel mode -->
        <BreadcrumbNavigation :show-breadcrumb="isSidebarCollapsed || screenWidth < 769" />

        <!-- Always show Note List -->
        <NoteList :section="currentSection" :tag-filter="activeTagFilter" @note-selected="handleNoteSelected"
          @create-note="handleCreateNote" @share-note="handleShareNote" />
      </section>

      <!-- Editor Panel -->
      <main class="editor-panel" :class="{ 'is-fullscreen': isEditorFullscreen }">
        <EditorPanel :selected-note="selectedNote" :is-fullscreen="isEditorFullscreen"
          :show-dashboard="currentSection === 'dashboard'" @toggle-fullscreen="toggleEditorFullscreen"
          @create-note="handleCreateNote" @note-saved="handleNoteSaved" @share-note="handleShareNote"
          @create-group="openCreateGroupModal" @open-search="openSearchModal" @open-settings="openSettingsModal" />
      </main>
    </div>

    <!-- Mobile Overlay -->
    <div v-if="isSidebarOpen" class="mobile-overlay mobile-only" @click="closeMobileSidebar"></div>

    <!-- Global Components -->
    <KeyboardShortcuts @open-search="openSearchModal" @create-note="createNote" @toggle-sidebar="toggleMobileSidebar"
      @focus-editor="focusEditor" @save-note="saveCurrentNote" />

    <!-- Search Modal -->
    <SearchModal :is-open="isSearchModalOpen" @close="closeSearchModal" @note-selected="handleSearchNoteSelected"
      @create-note="handleCreateNote" />

    <!-- Share Modal -->
    <ShareModal :is-open="isShareModalOpen" :note="noteToShare" @close="closeShareModal"
      @share-created="handleShareCreated" />

    <!-- Settings Modal -->
    <SettingsModal :is-open="isSettingsModalOpen" @close="closeSettingsModal" />

    <!-- Create Group Modal -->
    <CreateGroupModal v-if="isCreateGroupModalOpen" @close="closeCreateGroupModal" @created="handleGroupCreated" />

    <!-- Auth Status Indicator -->
    <AuthStatusIndicator />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useSettingsStore } from '../../stores/settings'
import { useTheme } from '../../composables/useTheme'
import Sidebar from './Sidebar.vue'
import NoteList from './NoteList.vue'
import EditorPanel from './EditorPanel.vue'
import BreadcrumbNavigation from '../navigation/BreadcrumbNavigation.vue'
import KeyboardShortcuts from '../navigation/KeyboardShortcuts.vue'
import SearchModal from '../navigation/SearchModal.vue'
import ShareModal from '../sharing/ShareModal.vue'
import SettingsModal from '../settings/SettingsModal.vue'
import CreateGroupModal from '../groups/CreateGroupModal.vue'
import AuthStatusIndicator from '../common/AuthStatusIndicator.vue'

// Composables - with safe initialization
let route: any = null
let router: any = null
const authStore = useAuthStore()
const settingsStore = useSettingsStore()
const {
  currentTheme,
  currentMode,
  isLoading: isThemeLoading,
  error: themeError,
  initializeThemeManager,
  onThemeChange,
  offThemeChange
} = useTheme()

// Reactive state
const isSidebarOpen = ref(false)
const isSidebarCollapsed = ref(false)
const isEditorFullscreen = ref(false)
const selectedNote = ref<any>(null)
const screenWidth = ref(window.innerWidth)
const isSearchModalOpen = ref(false)
const isShareModalOpen = ref(false)
const isSettingsModalOpen = ref(false)
const isCreateGroupModalOpen = ref(false)
const noteToShare = ref<any>(null)
const routerReady = ref(false)
const activeTagFilter = ref<{ selectedTags: string[], filterMode: 'any' | 'all' } | null>(null)

// Local section state to prevent sidebar reloading
const currentSectionState = ref<string>('dashboard')

// Theme management state
const themeInitialized = ref(false)
const themeLoadingState = ref(false)

// Safe router initialization
const initializeRouter = () => {
  try {
    route = useRoute()
    router = useRouter()

    if (route && router) {
      routerReady.value = true
      console.log('Router initialized successfully')
    } else {
      console.warn('Router composables not fully available')
    }
  } catch (error) {
    console.warn('Router initialization error:', error)
    // Continue without router - use window.location as fallback
  }
}

// Try to initialize router immediately
try {
  initializeRouter()
} catch (error) {
  console.warn('Initial router setup failed, will retry:', error)
}

// Check if any modal should be opened based on route or user preferences
const checkModalStates = () => {
  try {
    // Check route first
    let routePath = window.location.pathname

    if (routerReady.value && route?.path) {
      routePath = route.path
    } else if (router?.currentRoute?.value?.path) {
      routePath = router.currentRoute.value.path
    }

    // Check if we should open settings modal
    if (routePath.includes('/settings')) {
      isSettingsModalOpen.value = true
      return
    }

    // Don't open modals automatically after login unless specifically requested
    // This prevents the settings modal from opening automatically after login
    const isLoginPath = routePath.includes('/login') || routePath.includes('/register') || routePath.includes('/auth')
    if (isLoginPath) {
      return
    }

    // Load user preferences for modals
    const modalPreferences = authStore.loadModalPreferences()

    // Check if settings modal should be opened
    if (modalPreferences.settingsModalOpen) {
      // Only open if not on login/register page
      isSettingsModalOpen.value = true
    } else if (modalPreferences.searchModalOpen) {
      isSearchModalOpen.value = true
    } else if (modalPreferences.shareModalOpen && noteToShare.value) {
      isShareModalOpen.value = true
    }
  } catch (error) {
    console.warn('Failed to check modal states:', error)
  }
}

// Watch route changes
const routeWatcher = watch(
  () => routerReady.value && route?.path,
  (newPath) => {
    if (newPath && newPath.includes('/settings')) {
      openSettingsModal()
    }
  }
)

// Computed properties
const layoutClasses = computed(() => ({
  'is-mobile': screenWidth.value < 769,
  'is-tablet': screenWidth.value >= 769 && screenWidth.value < 1024,
  'is-desktop': screenWidth.value >= 1024,
  'sidebar-collapsed': isSidebarCollapsed.value,
  'editor-fullscreen': isEditorFullscreen.value
}))

const currentSection = computed(() => {
  // Use local state to prevent sidebar reloading
  return currentSectionState.value
})

// Methods
const toggleMobileSidebar = () => {
  console.log('Toggle mobile sidebar clicked. Current state:', isSidebarOpen.value)
  isSidebarOpen.value = !isSidebarOpen.value
  console.log('New state:', isSidebarOpen.value)
}

const closeMobileSidebar = () => {
  isSidebarOpen.value = false
}

const toggleSidebarCollapse = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

const toggleEditorFullscreen = () => {
  isEditorFullscreen.value = !isEditorFullscreen.value
}

const handleNoteSelected = (note: any) => {
  selectedNote.value = note
  // On mobile, close sidebar when note is selected
  if (screenWidth.value < 769) {
    closeMobileSidebar()
  }
}

const handleResize = () => {
  screenWidth.value = window.innerWidth

  // Auto-collapse sidebar on tablets
  if (screenWidth.value >= 769 && screenWidth.value < 1024) {
    isSidebarCollapsed.value = true
  } else if (screenWidth.value >= 1024) {
    isSidebarCollapsed.value = false
  }
}

const handleCreateNote = (type?: string) => {
  createNote(type)
}

const createNote = async (type: string = 'richtext') => {
  // Create new note logic
  console.log('Creating new note:', type)

  // This would typically call the notes store to create a new note
  // For now, just create a placeholder
  const newNote = {
    id: `new-${Date.now()}`,
    title: 'New Note',
    content: '',
    noteType: type,
    isNew: true,
    isCreating: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }

  // Set the newly created note as selected
  selectedNote.value = newNote
}

const handleNoteSaved = (note: any) => {
  // Update the selected note with saved data
  selectedNote.value = note
}

const focusEditor = () => {
  // Focus the editor if a note is selected
  if (selectedNote.value) {
    const editorElement = document.querySelector('.editor-area, .editor-textarea')
    if (editorElement) {
      (editorElement as HTMLElement).focus()
    }
  }
}

const saveCurrentNote = () => {
  // Save the current note if one is selected
  if (selectedNote.value) {
    console.log('Save current note:', selectedNote.value.id)
    // This would trigger the save functionality
  }
}

const handleShareNote = (note: any) => {
  noteToShare.value = note
  isShareModalOpen.value = true
  // Save state to user preferences
  authStore.saveModalPreferences({ shareModalOpen: true })
}

const closeShareModal = () => {
  isShareModalOpen.value = false
  noteToShare.value = null
  // Save state to user preferences
  authStore.saveModalPreferences({ shareModalOpen: false })
}

const handleShareCreated = (share: any) => {
  console.log('Share created:', share)
  // You might want to show a success notification here
}

const openSettingsModal = () => {
  isSettingsModalOpen.value = true
  // Save state to user preferences
  authStore.saveModalPreferences({ settingsModalOpen: true })
}

const closeSettingsModal = () => {
  isSettingsModalOpen.value = false
  // Save state to user preferences
  authStore.saveModalPreferences({ settingsModalOpen: false })
}

const openCreateGroupModal = () => {
  isCreateGroupModalOpen.value = true
}

const closeCreateGroupModal = () => {
  isCreateGroupModalOpen.value = false
}

const handleGroupCreated = (group: any) => {
  closeCreateGroupModal()
  // Navigate to the newly created group
  if (router && router.push) {
    router.push(`/groups/${group.id}`)
  }
}

const handleShowDashboard = () => {
  // Clear selected note to show dashboard in editor panel
  selectedNote.value = null
}

const handleSectionChange = (section: string) => {
  console.log('Handling section change to:', section)
  
  // Clear selected note to show dashboard in editor panel
  selectedNote.value = null
  
  // Update local state to prevent sidebar reloading
  currentSectionState.value = section
  
  // Update the URL without triggering a full route change
  // This allows bookmarking while preventing sidebar reloading
  if (router && router.replace) {
    // Use replace to avoid adding to browser history
    router.replace(`/dashboard/${section}`)
  } else {
    // Fallback: update URL without page reload
    const newUrl = `/dashboard/${section}`
    if (window.history && window.history.replaceState) {
      window.history.replaceState({ path: newUrl }, '', newUrl)
    }
  }
}

// Reset all modal states
const resetModalStates = () => {
  try {
    // Close all modals
    isSettingsModalOpen.value = false
    isSearchModalOpen.value = false
    isShareModalOpen.value = false
    isCreateGroupModalOpen.value = false
    noteToShare.value = null

    // Clear all modal states from user preferences
    authStore.saveModalPreferences({
      settingsModalOpen: false,
      searchModalOpen: false,
      shareModalOpen: false
    })
  } catch (error) {
    console.warn('Failed to reset modal states:', error)
  }
}

// Initialize theme system
const initializeTheme = async () => {
  if (themeInitialized.value) {
    console.log('Theme already initialized, skipping...')
    return
  }

  themeLoadingState.value = true

  try {
    // Initialize theme manager
    await initializeThemeManager()

    // Initialize settings store (which loads theme preferences)
    await settingsStore.initializeSettings()

    // Load theme preferences from auth store
    const themePrefs = authStore.loadThemePreference()

    // Apply theme based on preferences (without syncing to backend during initialization)
    const { setTheme, setThemeMode } = useTheme()

    if (themePrefs.mode === 'auto') {
      await setThemeMode('auto')
      settingsStore.setLocalThemeState('auto')
    } else if (themePrefs.themeName) {
      await setTheme(themePrefs.themeName)
      // Determine the correct mode for the theme
      const darkThemes = ['darkly', 'cyborg', 'slate', 'superhero', 'vapor']
      const mode = darkThemes.includes(themePrefs.themeName) ? 'dark' : 'light'
      settingsStore.setLocalThemeState(mode, themePrefs.themeName)
    } else {
      await setThemeMode(themePrefs.mode)
      settingsStore.setLocalThemeState(themePrefs.mode)
    }

    themeInitialized.value = true
    console.log('Theme system initialized successfully')
  } catch (error) {
    console.error('Failed to initialize theme system:', error)
    settingsStore.handleThemeError('Failed to initialize theme system')

    // Fallback to basic theme application
    applyBasicTheme()
    themeInitialized.value = true
  } finally {
    themeLoadingState.value = false
  }
}

// Fallback theme application (legacy method)
const applyBasicTheme = () => {
  try {
    const themePrefs = authStore.loadThemePreference()
    const html = document.documentElement

    // Use specific theme name if available, otherwise use fallback
    const themeName = themePrefs.themeName || (themePrefs.mode === 'dark' ? 'darkly' : 'default')

    if (themePrefs.mode === 'dark') {
      html.classList.add('dark')
      html.classList.remove('light')
      html.setAttribute('data-theme', themeName)
    } else if (themePrefs.mode === 'light') {
      html.classList.add('light')
      html.classList.remove('dark')
      html.setAttribute('data-theme', themeName)
    } else {
      // Auto mode - use system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      html.classList.toggle('dark', prefersDark)
      html.classList.toggle('light', !prefersDark)
      html.setAttribute('data-theme', prefersDark ? (themePrefs.themeName || 'darkly') : (themePrefs.themeName || 'default'))
    }

    console.log(`Fallback theme applied: ${html.getAttribute('data-theme')} (mode: ${themePrefs.mode})`)
  } catch (error) {
    console.warn('Failed to apply basic theme:', error)
    // Default to light theme on error
    const html = document.documentElement
    html.classList.add('light')
    html.classList.remove('dark')
    html.setAttribute('data-theme', 'default')
  }
}

// Handle theme changes
const handleThemeChange = (newTheme: string) => {
  console.log('Theme changed to:', newTheme)

  // Update document classes and attributes
  const html = document.documentElement
  const theme = settingsStore.availableThemes.find(t => t.name === newTheme)

  if (theme) {
    html.classList.toggle('dark', theme.isDark)
    html.classList.toggle('light', !theme.isDark)
    html.setAttribute('data-theme', newTheme)
  } else {
    // Fallback for unknown themes
    const isDark = newTheme === 'darkly' || newTheme.includes('dark')
    html.classList.toggle('dark', isDark)
    html.classList.toggle('light', !isDark)
    html.setAttribute('data-theme', newTheme)
  }

  console.log(`Theme change applied: data-theme="${newTheme}", classes="${html.className}"`)
}

// Handle theme loading states
const handleThemeLoadingState = (loading: boolean) => {
  themeLoadingState.value = loading

  if (loading) {
    // Add loading indicator class
    document.documentElement.classList.add('theme-loading')
  } else {
    // Remove loading indicator class
    document.documentElement.classList.remove('theme-loading')
  }
}

// Handle theme errors
const handleThemeErrorState = (error: string | null) => {
  if (error) {
    console.error('Theme error in AppLayout:', error)
    // Could show a user notification here
  }
}

// Emergency navigation function
const goToDashboard = () => {
  try {
    // Reset all modal states
    resetModalStates()

    // Ensure auth store is properly initialized before navigation
    if (!authStore.isInitialized && authStore.token) {
      console.log('Initializing auth before dashboard navigation...')
      authStore.initializeAuth()
    }

    if (router && router.push) {
      router.push('/dashboard')
    } else {
      // Fallback: direct navigation
      window.location.href = '/dashboard'
    }
  } catch (error) {
    console.error('Navigation error, using fallback:', error)
    window.location.href = '/dashboard'
  }
}

const openSearchModal = () => {
  isSearchModalOpen.value = true
  // Save state to user preferences
  authStore.saveModalPreferences({ searchModalOpen: true })
}

const closeSearchModal = () => {
  isSearchModalOpen.value = false
  // Save state to user preferences
  authStore.saveModalPreferences({ searchModalOpen: false })
}

const handleSearchNoteSelected = (note: any) => {
  selectedNote.value = note
  closeSearchModal()
}

const handleTagFilter = (filter: { selectedTags: string[], filterMode: 'any' | 'all' }) => {
  activeTagFilter.value = filter.selectedTags.length > 0 ? filter : null
  console.log('Tag filter applied:', filter)
}

const handleUpdateTags = (tags: string[]) => {
  // This would be called when the navigation changes to update available tags
  console.log('Tags updated from navigation:', tags)
}



// Lifecycle
onMounted(async () => {
  window.addEventListener('resize', handleResize)

  // Set initial sidebar state based on screen size
  if (screenWidth.value >= 1024) {
    isSidebarCollapsed.value = false
  } else if (screenWidth.value >= 769) {
    isSidebarCollapsed.value = true
  }

  // Initialize router if not already done
  if (!routerReady.value) {
    nextTick(() => {
      initializeRouter()
    })
  }

  // Initialize section state based on current route
  nextTick(() => {
    try {
      let routePath = window.location.pathname
      
      // Try to get from Vue Router if available
      if (routerReady.value && route?.path) {
        routePath = route.path
      } else if (router?.currentRoute?.value?.path) {
        routePath = router.currentRoute.value.path
      }

      // Set initial section based on route
      if (routePath.includes('/recent')) {
        currentSectionState.value = 'recent'
      } else if (routePath.includes('/favorites')) {
        currentSectionState.value = 'favorites'
      } else if (routePath.includes('/archived')) {
        currentSectionState.value = 'archived'
      } else if (routePath.includes('/shared')) {
        currentSectionState.value = 'shared'
      } else if (routePath.includes('/notes')) {
        currentSectionState.value = 'all-notes'
      } else {
        currentSectionState.value = 'dashboard'
      }
      
      console.log('Initialized section state:', currentSectionState.value)
    } catch (error) {
      console.warn('Failed to initialize section state:', error)
      currentSectionState.value = 'dashboard'
    }
  })

  // Initialize theme system
  await initializeTheme()

  // Set up theme change listeners
  onThemeChange(handleThemeChange)

  // Watch for theme loading state changes
  watch(() => isThemeLoading.value, handleThemeLoadingState)
  watch(() => settingsStore.themeError, handleThemeErrorState)

  // Listen for system theme changes
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  const systemThemeHandler = () => {
    if ((currentMode.value as string) === 'auto') {
      // Theme system will handle this automatically
      console.log('System theme changed, auto mode will update')
    }
  }
  mediaQuery.addEventListener('change', systemThemeHandler)

  // Check if any modal should be opened
  nextTick(() => {
    checkModalStates()
  })

  // Listen for logout events to clear modal states
  const logoutHandler = () => {
    try {
      // Clear all modal states from localStorage
      localStorage.removeItem('settingsModalOpen')
      localStorage.removeItem('searchModalOpen')
      localStorage.removeItem('shareModalOpen')

      // Close all modals
      isSettingsModalOpen.value = false
      isSearchModalOpen.value = false
      isShareModalOpen.value = false
      noteToShare.value = null

      // Reset theme initialization flag to allow re-initialization on next login
      themeInitialized.value = false
    } catch (error) {
      console.warn('Failed to clear modal states on logout:', error)
    }
  }

  window.addEventListener('auth-logout-complete', logoutHandler)

  // Listen for theme errors
  const themeErrorHandler = (event: CustomEvent) => {
    console.error('Theme error event:', event.detail.message)
    // Could show user notification here
  }

  window.addEventListener('theme-error', themeErrorHandler as EventListener)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  // Clean up theme listeners
  offThemeChange(handleThemeChange)

  // Clean up system theme listener
  // Note: systemThemeHandler cleanup is handled in onMounted
})
</script>

<style scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background: var(--color-surface);
}

/* Mobile Header */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 40;
  background: var(--color-primary);
  min-height: 3.25rem;
}

.mobile-header .navbar {
  background: var(--color-primary);
  border-bottom: none;
  padding: 0.5rem 1rem;
}

.mobile-header .navbar-brand .title {
  color: white;
  font-size: 1.1rem;
}

.mobile-header .navbar-burger span {
  background: white;
}

/* Layout Container */
.layout-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  padding-top: 3.25rem;
  /* Height of mobile header */
}

@media (min-width: 769px) {
  .layout-container {
    padding-top: 0;
  }

  .mobile-header {
    display: none;
  }
}

/* Sidebar Panel */
.sidebar-panel {
  width: 260px;
  height: 100%;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  overflow-y: auto;
  transition: width 0.3s ease;
  flex-shrink: 0;
  z-index: 30;
}

/* Mobile sidebar behavior */
@media (max-width: 768px) {
  .sidebar-panel {
    position: fixed;
    top: 3.25rem;
    left: -60px;
    bottom: 0;
    width: 60px;
    z-index: 40;
    transition: left 0.3s ease;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }

  .sidebar-panel.is-active {
    left: 0;
  }

  .notelist-panel.is-visible-mobile {
    display: block;
    position: fixed;
    top: 3.25rem;
    left: 60px;
    width: 80vw;
    bottom: 0;
    z-index: 39;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.08);
  }

  /* Ensure overlay covers full screen on mobile for easy dismissal */
  .mobile-overlay {
    left: 0;
  }
}

/* Collapsed sidebar */
.sidebar-collapsed .sidebar-panel {
  width: 60px;
}

/* Note List Panel */
.notelist-panel {
  width: 360px;
  height: 100%;
  background: var(--color-background);
  border-right: 1px solid var(--color-border);
  overflow-y: auto;
  flex-shrink: 0;
  transition: width 0.3s ease;
  position: relative;
  z-index: 10;
}

/* Hide note list on mobile */
@media (max-width: 768px) {
  .notelist-panel {
    display: none;
  }
}

/* Hide note list when editor is fullscreen */
.notelist-panel.is-hidden {
  display: none;
}

/* Editor Panel */
.editor-panel {
  flex: 1;
  height: 100%;
  background: var(--color-background);
  overflow-y: auto;
  transition: all 0.3s ease;
}

/* Fullscreen editor */
.editor-panel.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
  background: var(--color-background);
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 3.25rem;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 25;
}

@media (max-width: 768px) {
  .mobile-overlay {
    top: 3.25rem;
  }
}

/* Responsive adjustments */
@media (min-width: 769px) and (max-width: 1023px) {
  .notelist-panel {
    width: 300px;
  }

  .sidebar-panel {
    width: 200px;
  }

  .sidebar-collapsed .sidebar-panel {
    width: 60px;
  }
}

@media (min-width: 1024px) {
  .notelist-panel {
    width: 360px;
  }

  .sidebar-panel {
    width: 260px;
  }
}

/* Scrollbar styling */
.sidebar-panel::-webkit-scrollbar,
.notelist-panel::-webkit-scrollbar,
.editor-panel::-webkit-scrollbar {
  width: 6px;
}

.sidebar-panel::-webkit-scrollbar-track,
.notelist-panel::-webkit-scrollbar-track,
.editor-panel::-webkit-scrollbar-track {
  background: var(--color-surface-hover);
  border-radius: 3px;
}

.sidebar-panel::-webkit-scrollbar-thumb,
.notelist-panel::-webkit-scrollbar-thumb,
.editor-panel::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.sidebar-panel::-webkit-scrollbar-thumb:hover,
.notelist-panel::-webkit-scrollbar-thumb:hover,
.editor-panel::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .app-layout {
    background: var(--color-background);
  }

  .sidebar-panel {
    background: var(--color-surface);
    border-color: var(--color-border);
  }

  .notelist-panel {
    background: var(--color-surface);
    border-color: var(--color-border);
  }

  .editor-panel {
    background: var(--color-surface);
  }

  .editor-panel.is-fullscreen {
    background: var(--color-surface);
  }

  .mobile-header {
    background: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
  }

  .mobile-header .navbar {
    background: var(--color-surface);
  }

  .mobile-header .navbar-brand .title {
    color: var(--color-text);
  }

  .mobile-overlay {
    background: rgba(0, 0, 0, 0.7);
  }
}

/* Smooth transitions for layout changes */
.layout-container>* {
  transition: all 0.3s ease;
}

/* Ensure proper touch scrolling on mobile */
.sidebar-panel,
.notelist-panel,
.editor-panel {
  -webkit-overflow-scrolling: touch;
}

/* Prevent content shift when scrollbars appear */
.layout-container {
  overflow: hidden;
}

/* Global dropdown fixes */
:deep(.dropdown-menu) {
  z-index: 10000 !important;
  position: absolute !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid var(--color-border) !important;
  background: var(--color-background) !important;
}

:deep(.dropdown.is-active .dropdown-menu) {
  display: block !important;
  z-index: 10000 !important;
}

:deep(.dropdown.is-right .dropdown-menu) {
  right: 0 !important;
  left: auto !important;
}

/* Ensure note list dropdowns appear above everything */
:deep(.notelist-panel .dropdown) {
  z-index: 10000 !important;
}

:deep(.notelist-panel .dropdown-menu) {
  z-index: 10000 !important;
}

/* Force dropdowns to escape overflow containers */
:deep(.notelist-panel .dropdown-menu) {
  position: absolute !important;
  z-index: 10000 !important;
  /* Ensure dropdowns can escape overflow containers */
  transform: translateZ(0) !important;
  will-change: transform !important;
}

:deep(.notelist-panel .dropdown.is-active) {
  z-index: 10000 !important;
}

/* Override any parent overflow settings for dropdowns */
:deep(.notelist-panel .dropdown.is-active .dropdown-menu),
:deep(.notelist-panel .dropdown.is-hoverable:hover .dropdown-menu) {
  position: absolute !important;
  z-index: 10000 !important;
  /* Force the dropdown to be above everything */
  isolation: isolate !important;
}

/* Improved mobile experience */
@media (max-width: 768px) {
  .app-layout {
    position: relative;
  }

  .layout-container {
    position: relative;
  }

  /* Ensure modals work properly on mobile */
  :deep(.modal-card) {
    margin: 1rem;
    width: calc(100% - 2rem);
  }

  :deep(.modal-card-body) {
    max-height: 60vh;
  }
}

/* Theme loading state */
.theme-loading {
  transition: none !important;
}

.theme-loading * {
  transition: none !important;
}

/* Theme transition improvements */
.app-layout {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-panel,
.notelist-panel,
.editor-panel {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Print styles */
@media print {

  .mobile-header,
  .sidebar-panel,
  .notelist-panel,
  .mobile-overlay {
    display: none !important;
  }

  .layout-container {
    padding-top: 0 !important;
  }

  .editor-panel {
    width: 100% !important;
    height: auto !important;
    overflow: visible !important;
  }
}
</style>

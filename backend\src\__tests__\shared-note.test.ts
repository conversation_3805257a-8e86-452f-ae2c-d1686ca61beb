import request from 'supertest';
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import express from 'express';
import { initializeDatabase } from '../config/database';
import noteRoutes from '../routes/notes';

const app = express();
app.use(express.json());
app.use('/api', noteRoutes);

describe('Shared Note API', () => {
  beforeAll(async () => {
    await initializeDatabase();
  });

  it('should return 404 for non-existent share token', async () => {
    const response = await request(app)
      .get('/api/shared/nonexistent-token')
      .expect(404);

    expect(response.body.error).toBe('Shared note not found');
  });

  it('should return 404 for non-existent share token via POST', async () => {
    const response = await request(app)
      .post('/api/shared/nonexistent-token')
      .send({})
      .expect(404);

    expect(response.body.error).toBe('Shared note not found');
  });
});
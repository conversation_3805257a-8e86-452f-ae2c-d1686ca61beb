import { Router } from 'express';
import { TagController } from '../controllers/TagController';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// GET /api/tags - Get all tags for the authenticated user
router.get('/', TagController.getTags);

// POST /api/tags - Create a new custom tag
router.post('/', TagController.createTag);

// POST /api/tags/initialize - Initialize predefined tags for user
router.post('/initialize', TagController.initializePredefinedTags);

// GET /api/tags/:id - Get a specific tag
router.get('/:id', TagController.getTag);

// PUT /api/tags/:id - Update a tag
router.put('/:id', TagController.updateTag);

// DELETE /api/tags/:id - Delete a custom tag
router.delete('/:id', TagController.deleteTag);

export default router;
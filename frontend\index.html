<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CF Notes Pro</title>
    <style>
      /* Critical path CSS - inline in HTML for <500ms target */
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #fff;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
      }
      .navbar {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #fff;
        border-bottom: 1px solid #eee;
      }
      .navbar-brand {
        font-weight: 700;
        font-size: 1.25rem;
      }
      .navbar-menu {
        display: flex;
        margin-left: auto;
      }
      .navbar-item {
        padding: 0.5rem 1rem;
        text-decoration: none;
        color: #333;
      }
      .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: #fff;
        color: #333;
        text-decoration: none;
        cursor: pointer;
        font-size: 0.875rem;
      }
      .button.is-primary {
        background: #3273dc;
        color: #fff;
        border-color: #3273dc;
      }
      .is-loading {
        opacity: 0.7;
        pointer-events: none;
      }
      .is-hidden {
        display: none !important;
      }
      @media (max-width: 768px) {
        .container {
          padding: 0 0.5rem;
        }
        .navbar-menu {
          flex-direction: column;
          width: 100%;
        }
      }
    </style>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>

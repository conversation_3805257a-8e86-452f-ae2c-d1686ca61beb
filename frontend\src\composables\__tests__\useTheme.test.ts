import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { nextTick } from 'vue'
import { useTheme } from '../useTheme'
import { ThemeManager } from '@/utils/ThemeManager'
import { themePreloader } from '@/utils/ThemePreloader'
import type { BulmaswatchTheme } from '@/types/theme'

// Mock dependencies
vi.mock('@/utils/ThemeManager')
vi.mock('@/utils/ThemePreloader')

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

// Mock matchMedia
const mockMatchMedia = vi.fn()
Object.defineProperty(window, 'matchMedia', {
  value: mockMatchMedia
})

// Mock themes data
const mockThemes: BulmaswatchTheme[] = [
  {
    name: 'default',
    displayName: 'Default Light',
    description: 'Clean light theme',
    cssFile: 'default.css',
    isDark: false,
    category: 'light',
    preview: {
      primary: '#3273dc',
      background: '#ffffff',
      surface: '#f8f9fa',
      text: '#363636',
      accent: '#209cee'
    },
    colors: {
      primary: '#3273dc',
      link: '#3273dc',
      info: '#209cee',
      success: '#23d160',
      warning: '#ffdd57',
      danger: '#ff3860',
      background: '#ffffff',
      surface: '#f8f9fa',
      text: '#363636',
      textMuted: '#6b7280',
      border: '#dbdbdb'
    }
  },
  {
    name: 'darkly',
    displayName: 'Darkly',
    description: 'Professional dark theme',
    cssFile: 'darkly.css',
    isDark: true,
    category: 'dark',
    preview: {
      primary: '#4f46e5',
      background: '#1a1a1a',
      surface: '#2d2d2d',
      text: '#e0e0e0',
      accent: '#6366f1'
    },
    colors: {
      primary: '#4f46e5',
      link: '#4f46e5',
      info: '#6366f1',
      success: '#10b981',
      warning: '#f59e0b',
      danger: '#ef4444',
      background: '#1a1a1a',
      surface: '#2d2d2d',
      text: '#e0e0e0',
      textMuted: '#9ca3af',
      border: '#404040'
    }
  }
]

describe('useTheme', () => {
  let mockThemeManager: any
  let mockPreloader: any
  let mediaQueryList: any

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
    
    // Setup media query mock
    mediaQueryList = {
      matches: false,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    }
    mockMatchMedia.mockReturnValue(mediaQueryList)

    // Setup ThemeManager mock
    mockThemeManager = {
      initialize: vi.fn().mockResolvedValue(undefined),
      getAvailableThemes: vi.fn().mockReturnValue(mockThemes),
      getSystemPreference: vi.fn().mockReturnValue('light'),
      setTheme: vi.fn().mockResolvedValue(undefined),
      getCurrentTheme: vi.fn().mockReturnValue('default'),
      getTheme: vi.fn().mockImplementation((name: string) => 
        mockThemes.find(t => t.name === name)
      ),
      getThemePreview: vi.fn().mockImplementation((name: string) => 
        mockThemes.find(t => t.name === name)?.preview || null
      ),
      preloadTheme: vi.fn().mockResolvedValue(undefined),
      onThemeChange: vi.fn(),
      offThemeChange: vi.fn()
    }
    
    // Mock ThemeManager constructor
    vi.mocked(ThemeManager).mockImplementation(() => mockThemeManager)

    // Setup themePreloader mock
    mockPreloader = {
      getPreloadStrategy: vi.fn().mockReturnValue({
        immediate: ['default'],
        priority: ['darkly'],
        lazy: []
      }),
      executePreloadStrategy: vi.fn().mockResolvedValue(undefined),
      onProgress: vi.fn(),
      recordThemeUsage: vi.fn(),
      getStats: vi.fn().mockReturnValue({
        totalThemes: 2,
        preloadedThemes: 1,
        cacheHits: 0,
        cacheMisses: 0
      })
    }
    
    // Mock themePreloader object
    Object.assign(themePreloader, mockPreloader)

    // Reset localStorage mock
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('initialization', () => {
    it('should initialize theme manager on first use', async () => {
      const { initializeThemeManager } = useTheme()
      
      await initializeThemeManager()
      
      expect(ThemeManager).toHaveBeenCalledOnce()
      expect(mockThemeManager.initialize).toHaveBeenCalledOnce()
    })

    it('should handle initialization errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockThemeManager.initialize.mockRejectedValue(new Error('Init failed'))
      
      const { initializeThemeManager, error } = useTheme()
      
      await initializeThemeManager()
      
      expect(error.value).toBe('Init failed')
      expect(consoleSpy).toHaveBeenCalledWith('Theme manager initialization failed:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })

    it('should not reinitialize theme manager if already initialized', async () => {
      const { initializeThemeManager } = useTheme()
      
      await initializeThemeManager()
      await initializeThemeManager()
      
      expect(ThemeManager).toHaveBeenCalledOnce()
      expect(mockThemeManager.initialize).toHaveBeenCalledOnce()
    })
  })

  describe('theme mode management', () => {
    it('should set theme mode to light', async () => {
      const { setThemeMode, currentMode, currentTheme } = useTheme()
      
      await setThemeMode('light')
      
      expect(mockThemeManager.setTheme).toHaveBeenCalledWith('light')
      expect(currentMode.value).toBe('light')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('theme-mode', 'light')
    })

    it('should set theme mode to dark', async () => {
      const { setThemeMode, currentMode } = useTheme()
      
      await setThemeMode('dark')
      
      expect(mockThemeManager.setTheme).toHaveBeenCalledWith('dark')
      expect(currentMode.value).toBe('dark')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('theme-mode', 'dark')
    })

    it('should set theme mode to auto', async () => {
      const { setThemeMode, currentMode } = useTheme()
      
      await setThemeMode('auto')
      
      expect(mockThemeManager.setTheme).toHaveBeenCalledWith('auto')
      expect(currentMode.value).toBe('auto')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('theme-mode', 'auto')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('theme-name')
    })

    it('should handle theme mode setting errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockThemeManager.setTheme.mockRejectedValue(new Error('Theme set failed'))
      
      const { setThemeMode, error } = useTheme()
      
      await setThemeMode('dark')
      
      expect(error.value).toBe('Theme set failed')
      expect(consoleSpy).toHaveBeenCalledWith('Failed to set theme mode:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })
  })

  describe('specific theme management', () => {
    it('should set specific theme by name', async () => {
      const { setTheme, currentTheme, currentMode } = useTheme()
      
      await setTheme('darkly')
      
      expect(mockThemeManager.setTheme).toHaveBeenCalledWith('darkly')
      expect(currentTheme.value).toBe('darkly')
      expect(currentMode.value).toBe('dark') // Should update mode based on theme
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('theme-name', 'darkly')
    })

    it('should record theme usage when setting theme', async () => {
      const { setTheme } = useTheme()
      
      await setTheme('darkly')
      
      expect(mockPreloader.recordThemeUsage).toHaveBeenCalledWith('darkly')
    })

    it('should handle theme setting errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockThemeManager.setTheme.mockRejectedValue(new Error('Theme not found'))
      
      const { setTheme, error } = useTheme()
      
      await setTheme('nonexistent')
      
      expect(error.value).toBe('Theme not found')
      expect(consoleSpy).toHaveBeenCalledWith('Failed to set theme:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })
  })

  describe('theme persistence', () => {
    it('should load saved theme mode from localStorage', async () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'theme-mode') return 'dark'
        if (key === 'theme-name') return 'darkly'
        return null
      })
      
      const { loadSavedTheme, currentMode } = useTheme()
      
      await loadSavedTheme()
      
      expect(mockThemeManager.setTheme).toHaveBeenCalledWith('darkly')
      expect(currentMode.value).toBe('dark')
    })

    it('should default to auto mode when no preference is saved', async () => {
      const { loadSavedTheme, currentMode } = useTheme()
      
      await loadSavedTheme()
      
      expect(currentMode.value).toBe('auto')
      expect(mockThemeManager.setTheme).toHaveBeenCalledWith('auto')
    })

    it('should handle auto mode correctly when saved', async () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'theme-mode') return 'auto'
        return null
      })
      
      const { loadSavedTheme, currentMode } = useTheme()
      
      await loadSavedTheme()
      
      expect(currentMode.value).toBe('auto')
      expect(mockThemeManager.setTheme).toHaveBeenCalledWith('auto')
    })
  })

  describe('system preference detection', () => {
    it('should detect system preference changes in auto mode', async () => {
      const { setThemeMode } = useTheme()
      
      // Set to auto mode first
      await setThemeMode('auto')
      
      // Simulate system preference change
      const changeHandler = mediaQueryList.addEventListener.mock.calls[0][1]
      changeHandler({ matches: true })
      
      // Should trigger theme change when in auto mode
      expect(mediaQueryList.addEventListener).toHaveBeenCalledWith('change', expect.any(Function))
    })

    it('should initialize system preference watcher', () => {
      useTheme()
      
      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)')
      expect(mediaQueryList.addEventListener).toHaveBeenCalledWith('change', expect.any(Function))
    })

    it('should handle missing matchMedia gracefully', () => {
      // Remove matchMedia
      delete (window as any).matchMedia
      
      expect(() => useTheme()).not.toThrow()
      
      // Restore matchMedia
      Object.defineProperty(window, 'matchMedia', {
        value: mockMatchMedia
      })
    })
  })

  describe('theme preloading', () => {
    it('should preload themes', async () => {
      const { preloadThemes } = useTheme()
      
      await preloadThemes(['darkly', 'cerulean'])
      
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledWith('darkly')
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledWith('cerulean')
    })

    it('should handle preload errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      mockThemeManager.preloadTheme.mockRejectedValue(new Error('Preload failed'))
      
      const { preloadThemes } = useTheme()
      
      await preloadThemes(['darkly'])
      
      expect(consoleSpy).toHaveBeenCalledWith('Failed to preload theme darkly:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })

    it('should execute preload strategy', async () => {
      const { executePreloadStrategy } = useTheme()
      
      await executePreloadStrategy()
      
      expect(mockPreloader.getPreloadStrategy).toHaveBeenCalled()
      expect(mockPreloader.executePreloadStrategy).toHaveBeenCalled()
    })
  })

  describe('computed properties', () => {
    it('should filter light themes correctly', () => {
      const { lightThemes, availableThemes } = useTheme()
      
      // Trigger initialization to populate availableThemes
      availableThemes.value = mockThemes
      
      expect(lightThemes.value).toHaveLength(1)
      expect(lightThemes.value[0].name).toBe('default')
    })

    it('should filter dark themes correctly', () => {
      const { darkThemes, availableThemes } = useTheme()
      
      // Trigger initialization to populate availableThemes
      availableThemes.value = mockThemes
      
      expect(darkThemes.value).toHaveLength(1)
      expect(darkThemes.value[0].name).toBe('darkly')
    })

    it('should determine if current theme is dark', () => {
      const { isDarkTheme, currentTheme } = useTheme()
      
      currentTheme.value = 'darkly'
      
      expect(isDarkTheme.value).toBe(true)
    })

    it('should get current theme object', () => {
      const { getCurrentThemeObject, currentTheme } = useTheme()
      
      currentTheme.value = 'default'
      
      expect(getCurrentThemeObject.value).toEqual(mockThemes[0])
    })
  })

  describe('theme callbacks', () => {
    it('should register theme change callbacks', () => {
      const { onThemeChange } = useTheme()
      const callback = vi.fn()
      
      onThemeChange(callback)
      
      expect(mockThemeManager.onThemeChange).toHaveBeenCalledWith(callback)
    })

    it('should unregister theme change callbacks', () => {
      const { offThemeChange } = useTheme()
      const callback = vi.fn()
      
      offThemeChange(callback)
      
      expect(mockThemeManager.offThemeChange).toHaveBeenCalledWith(callback)
    })
  })

  describe('utility methods', () => {
    it('should get theme preview', () => {
      const { getThemePreview } = useTheme()
      
      const preview = getThemePreview('default')
      
      expect(preview).toEqual(mockThemes[0].preview)
      expect(mockThemeManager.getThemePreview).toHaveBeenCalledWith('default')
    })

    it('should clear error', () => {
      const { clearError, error } = useTheme()
      
      error.value = 'Test error'
      clearError()
      
      expect(error.value).toBeNull()
    })

    it('should reset to default theme', async () => {
      const { resetToDefault } = useTheme()
      
      await resetToDefault()
      
      expect(mockThemeManager.setTheme).toHaveBeenCalledWith('default')
    })

    it('should get theme settings', () => {
      const { getThemeSettings, currentMode, currentTheme, availableThemes } = useTheme()
      
      currentMode.value = 'dark'
      currentTheme.value = 'darkly'
      availableThemes.value = mockThemes
      
      const settings = getThemeSettings()
      
      expect(settings).toEqual({
        mode: 'dark',
        selectedTheme: 'darkly',
        customThemes: mockThemes,
        transitionDuration: 300,
        preloadThemes: true
      })
    })

    it('should apply theme settings', async () => {
      const { applyThemeSettings } = useTheme()
      
      await applyThemeSettings({
        mode: 'dark',
        selectedTheme: 'darkly'
      })
      
      expect(mockThemeManager.setTheme).toHaveBeenCalledWith('dark')
      expect(mockThemeManager.setTheme).toHaveBeenCalledWith('darkly')
    })

    it('should get preload stats', () => {
      const { getPreloadStats } = useTheme()
      
      const stats = getPreloadStats()
      
      expect(stats).toEqual({
        totalThemes: 2,
        preloadedThemes: 1,
        cacheHits: 0,
        cacheMisses: 0
      })
    })
  })

  describe('error handling', () => {
    it('should handle theme manager initialization failure', async () => {
      mockThemeManager.initialize.mockRejectedValue(new Error('Network error'))
      
      const { initializeThemeManager, error, isLoading } = useTheme()
      
      await initializeThemeManager()
      
      expect(error.value).toBe('Network error')
      expect(isLoading.value).toBe(false)
    })

    it('should handle theme setting failure with proper error message', async () => {
      mockThemeManager.setTheme.mockRejectedValue(new Error('Theme not found'))
      
      const { setTheme, error } = useTheme()
      
      await setTheme('invalid-theme')
      
      expect(error.value).toBe('Theme not found')
    })

    it('should handle non-Error objects in catch blocks', async () => {
      mockThemeManager.setTheme.mockRejectedValue('String error')
      
      const { setTheme, error } = useTheme()
      
      await setTheme('theme')
      
      expect(error.value).toBe('Failed to set theme')
    })
  })

  describe('loading states', () => {
    it('should manage loading state during theme operations', async () => {
      let resolvePromise: () => void
      const promise = new Promise<void>((resolve) => {
        resolvePromise = resolve
      })
      
      mockThemeManager.setTheme.mockReturnValue(promise)
      
      const { setTheme, isLoading } = useTheme()
      
      const setThemePromise = setTheme('darkly')
      
      // Should be loading
      expect(isLoading.value).toBe(true)
      
      resolvePromise!()
      await setThemePromise
      
      // Should not be loading anymore
      expect(isLoading.value).toBe(false)
    })

    it('should manage preloading state', async () => {
      let resolvePromise: () => void
      const promise = new Promise<void>((resolve) => {
        resolvePromise = resolve
      })
      
      mockPreloader.executePreloadStrategy.mockReturnValue(promise)
      
      const { executePreloadStrategy, isPreloading } = useTheme()
      
      const preloadPromise = executePreloadStrategy()
      
      // Should be preloading
      expect(isPreloading.value).toBe(true)
      
      resolvePromise!()
      await preloadPromise
      
      // Should not be preloading anymore
      expect(isPreloading.value).toBe(false)
    })
  })
})
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import ShareModal from '../ShareModal.vue'
import type { Note } from '../../../services/noteService'

// Mock the note shares store
const mockNoteSharesStore = {
  isLoading: false,
  error: null,
  getSharesForNote: vi.fn(() => []),
  createShare: vi.fn(),
  updateShare: vi.fn(),
  deleteShare: vi.fn(),
  copyShareUrl: vi.fn(),
  clearError: vi.fn(),
  loadNoteShares: vi.fn()
}

vi.mock('../../../stores/noteShares', () => ({
  useNoteSharesStore: () => mockNoteSharesStore
}))

describe('ShareModal', () => {
  let wrapper: any
  const mockNote: Note = {
    id: '1',
    title: 'Test Note',
    content: 'Test content',
    noteType: 'markdown',
    tags: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    userId: 'user1',
    isArchived: false,
    metadata: {}
  }

  beforeEach(() => {
    wrapper = mount(ShareModal, {
      props: {
        isOpen: true,
        note: mockNote
      },
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })
  })

  it('renders when modal is open', () => {
    expect(wrapper.find('.modal.is-active').exists()).toBe(true)
    expect(wrapper.find('.modal-card-title').text()).toContain('Share Note')
  })

  it('displays note information', () => {
    expect(wrapper.text()).toContain('Test Note')
    expect(wrapper.text()).toContain('Markdown')
  })

  it('emits close event when close button is clicked', async () => {
    await wrapper.find('.delete').trigger('click')
    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('does not render when modal is closed', async () => {
    await wrapper.setProps({ isOpen: false })
    expect(wrapper.find('.modal.is-active').exists()).toBe(false)
  })

  it('shows create new share form', () => {
    expect(wrapper.text()).toContain('Note Visibility')
    expect(wrapper.text()).toContain('Permissions')
    expect(wrapper.text()).toContain('Advanced Options')
    expect(wrapper.find('input[type="radio"]').exists()).toBe(true)
    expect(wrapper.find('input[type="checkbox"]').exists()).toBe(true)
  })
})
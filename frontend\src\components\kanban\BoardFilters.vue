<template>
  <div class="board-filters">
    <!-- Search Bar -->
    <div class="search-section">
      <div class="field has-addons">
        <div class="control has-icons-left is-expanded">
          <input
            v-model="localFilters.search"
            @input="debouncedSearch"
            class="input"
            type="text"
            placeholder="Search cards..."
          />
          <span class="icon is-left">
            <i class="fas fa-search"></i>
          </span>
        </div>
        <div class="control">
          <button
            @click="clearSearch"
            class="button"
            :disabled="!localFilters.search"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Filter Toggles -->
    <div class="filter-toggles">
      <button
        @click="showFilters = !showFilters"
        class="button is-small"
        :class="{ 'is-active': hasActiveFilters }"
      >
        <i class="fas fa-filter"></i>
        <span>Filters</span>
        <span v-if="activeFilterCount > 0" class="tag is-small is-primary">
          {{ activeFilterCount }}
        </span>
      </button>
      
      <button
        @click="clearAllFilters"
        class="button is-small is-light"
        :disabled="!hasActiveFilters"
      >
        <i class="fas fa-times"></i>
        <span>Clear</span>
      </button>
    </div>

    <!-- Filter Panel -->
    <div v-if="showFilters" class="filter-panel">
      <div class="filter-grid">
        <!-- Labels Filter -->
        <div class="filter-group">
          <h5 class="filter-title">Labels</h5>
          <div class="label-filters">
            <label
              v-for="label in availableLabels"
              :key="label.id"
              class="checkbox label-checkbox"
            >
              <input
                v-model="localFilters.labels"
                :value="label.id"
                type="checkbox"
                @change="updateFilters"
              />
              <span
                class="tag label-tag"
                :style="{ backgroundColor: label.color }"
              >
                {{ label.name }}
              </span>
            </label>
          </div>
        </div>

        <!-- Assignees Filter -->
        <div class="filter-group">
          <h5 class="filter-title">Assignees</h5>
          <div class="assignee-filters">
            <label
              v-for="assignee in availableAssignees"
              :key="assignee.id"
              class="checkbox assignee-checkbox"
            >
              <input
                v-model="localFilters.assignees"
                :value="assignee.id"
                type="checkbox"
                @change="updateFilters"
              />
              <div class="assignee-item">
                <div class="assignee-avatar">
                  <img
                    v-if="assignee.avatar"
                    :src="assignee.avatar"
                    :alt="assignee.name"
                    class="avatar"
                  />
                  <span v-else class="avatar-placeholder">
                    {{ assignee.name.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <span class="assignee-name">{{ assignee.name }}</span>
              </div>
            </label>
          </div>
        </div>

        <!-- Due Date Filter -->
        <div class="filter-group">
          <h5 class="filter-title">Due Date</h5>
          <div class="field">
            <div class="control">
              <div class="select is-small is-fullwidth">
                <select v-model="localFilters.dueDate" @change="updateFilters">
                  <option value="">All cards</option>
                  <option value="overdue">Overdue</option>
                  <option value="today">Due today</option>
                  <option value="week">Due this week</option>
                  <option value="month">Due this month</option>
                  <option value="none">No due date</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Priority Filter -->
        <div class="filter-group">
          <h5 class="filter-title">Priority</h5>
          <div class="priority-filters">
            <label
              v-for="priority in priorityOptions"
              :key="priority.value"
              class="checkbox priority-checkbox"
            >
              <input
                v-model="localFilters.priority"
                :value="priority.value"
                type="checkbox"
                @change="updateFilters"
              />
              <span
                class="tag priority-tag"
                :class="priority.class"
              >
                <i :class="priority.icon"></i>
                {{ priority.label }}
              </span>
            </label>
          </div>
        </div>

        <!-- Additional Filters -->
        <div class="filter-group">
          <h5 class="filter-title">Additional</h5>
          <div class="additional-filters">
            <label class="checkbox">
              <input
                v-model="localFilters.hasAttachments"
                type="checkbox"
                @change="updateFilters"
              />
              <span class="filter-label">
                <i class="fas fa-paperclip"></i>
                Has attachments
              </span>
            </label>
            
            <label class="checkbox">
              <input
                v-model="localFilters.hasComments"
                type="checkbox"
                @change="updateFilters"
              />
              <span class="filter-label">
                <i class="fas fa-comment"></i>
                Has comments
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Active Filters Display -->
    <div v-if="hasActiveFilters && !showFilters" class="active-filters">
      <div class="active-filter-tags">
        <!-- Search -->
        <span v-if="localFilters.search" class="tag is-light">
          <i class="fas fa-search"></i>
          "{{ localFilters.search }}"
          <button
            @click="clearSearch"
            class="delete is-small"
          ></button>
        </span>

        <!-- Labels -->
        <span
          v-for="labelId in localFilters.labels"
          :key="`label-${labelId}`"
          class="tag is-light"
        >
          <i class="fas fa-tag"></i>
          {{ getLabelName(labelId) }}
          <button
            @click="removeLabel(labelId)"
            class="delete is-small"
          ></button>
        </span>

        <!-- Assignees -->
        <span
          v-for="assigneeId in localFilters.assignees"
          :key="`assignee-${assigneeId}`"
          class="tag is-light"
        >
          <i class="fas fa-user"></i>
          {{ getAssigneeName(assigneeId) }}
          <button
            @click="removeAssignee(assigneeId)"
            class="delete is-small"
          ></button>
        </span>

        <!-- Due Date -->
        <span v-if="localFilters.dueDate" class="tag is-light">
          <i class="fas fa-clock"></i>
          {{ getDueDateLabel(localFilters.dueDate) }}
          <button
            @click="clearDueDate"
            class="delete is-small"
          ></button>
        </span>

        <!-- Priority -->
        <span
          v-for="priority in localFilters.priority"
          :key="`priority-${priority}`"
          class="tag is-light"
        >
          <i class="fas fa-exclamation"></i>
          {{ getPriorityLabel(priority) }}
          <button
            @click="removePriority(priority)"
            class="delete is-small"
          ></button>
        </span>

        <!-- Additional -->
        <span v-if="localFilters.hasAttachments" class="tag is-light">
          <i class="fas fa-paperclip"></i>
          Has attachments
          <button
            @click="localFilters.hasAttachments = false; updateFilters()"
            class="delete is-small"
          ></button>
        </span>

        <span v-if="localFilters.hasComments" class="tag is-light">
          <i class="fas fa-comment"></i>
          Has comments
          <button
            @click="localFilters.hasComments = false; updateFilters()"
            class="delete is-small"
          ></button>
        </span>
      </div>
    </div>

    <!-- Search Results -->
    <div v-if="searchResults.length > 0 && localFilters.search" class="search-results">
      <h5 class="search-results-title">
        Search Results ({{ searchResults.length }})
      </h5>
      <div class="search-results-list">
        <div
          v-for="result in searchResults"
          :key="result.card.id"
          @click="$emit('card-selected', result.card.id)"
          class="search-result-item"
        >
          <div class="result-header">
            <h6 class="result-title">{{ result.card.title }}</h6>
            <span class="result-column">{{ result.columnTitle }}</span>
          </div>
          <div class="result-match">
            <span class="match-type">{{ result.matchType }}:</span>
            <span class="match-text">{{ result.matchText }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { debounce } from 'lodash-es'
import type { 
  KanbanFilters, 
  KanbanLabel, 
  KanbanAssignee, 
  CardSearchResult 
} from '../../types/kanban'

interface Props {
  filters: KanbanFilters
  availableLabels: KanbanLabel[]
  availableAssignees: KanbanAssignee[]
  searchResults: CardSearchResult[]
}

interface Emits {
  (e: 'update:filters', filters: KanbanFilters): void
  (e: 'search', query: string): void
  (e: 'card-selected', cardId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const showFilters = ref(false)
const localFilters = ref<KanbanFilters>({ ...props.filters })

// Priority options
const priorityOptions = [
  { value: 'low', label: 'Low', class: 'is-success', icon: 'fas fa-arrow-down' },
  { value: 'medium', label: 'Medium', class: 'is-warning', icon: 'fas fa-minus' },
  { value: 'high', label: 'High', class: 'is-danger', icon: 'fas fa-arrow-up' },
  { value: 'urgent', label: 'Urgent', class: 'is-danger', icon: 'fas fa-exclamation-triangle' }
]

// Computed
const hasActiveFilters = computed(() => {
  return !!(
    localFilters.value.search ||
    (localFilters.value.labels && localFilters.value.labels.length > 0) ||
    (localFilters.value.assignees && localFilters.value.assignees.length > 0) ||
    localFilters.value.dueDate ||
    (localFilters.value.priority && localFilters.value.priority.length > 0) ||
    localFilters.value.hasAttachments ||
    localFilters.value.hasComments
  )
})

const activeFilterCount = computed(() => {
  let count = 0
  if (localFilters.value.search) count++
  if (localFilters.value.labels?.length) count += localFilters.value.labels.length
  if (localFilters.value.assignees?.length) count += localFilters.value.assignees.length
  if (localFilters.value.dueDate) count++
  if (localFilters.value.priority?.length) count += localFilters.value.priority.length
  if (localFilters.value.hasAttachments) count++
  if (localFilters.value.hasComments) count++
  return count
})

// Debounced search
const debouncedSearch = debounce(() => {
  emit('search', localFilters.value.search || '')
  updateFilters()
}, 300)

// Methods
const updateFilters = () => {
  emit('update:filters', { ...localFilters.value })
}

const clearAllFilters = () => {
  localFilters.value = {
    search: '',
    labels: [],
    assignees: [],
    dueDate: undefined,
    priority: [],
    hasAttachments: false,
    hasComments: false
  }
  updateFilters()
  emit('search', '')
}

const clearSearch = () => {
  localFilters.value.search = ''
  updateFilters()
  emit('search', '')
}

const clearDueDate = () => {
  localFilters.value.dueDate = undefined
  updateFilters()
}

const removeLabel = (labelId: string) => {
  if (localFilters.value.labels) {
    localFilters.value.labels = localFilters.value.labels.filter(id => id !== labelId)
    updateFilters()
  }
}

const removeAssignee = (assigneeId: string) => {
  if (localFilters.value.assignees) {
    localFilters.value.assignees = localFilters.value.assignees.filter(id => id !== assigneeId)
    updateFilters()
  }
}

const removePriority = (priority: string) => {
  if (localFilters.value.priority) {
    localFilters.value.priority = localFilters.value.priority.filter(p => p !== priority)
    updateFilters()
  }
}

// Helper methods
const getLabelName = (labelId: string) => {
  const label = props.availableLabels.find(l => l.id === labelId)
  return label?.name || 'Unknown Label'
}

const getAssigneeName = (assigneeId: string) => {
  const assignee = props.availableAssignees.find(a => a.id === assigneeId)
  return assignee?.name || 'Unknown Assignee'
}

const getDueDateLabel = (dueDate: string) => {
  const labels = {
    overdue: 'Overdue',
    today: 'Due today',
    week: 'Due this week',
    month: 'Due this month',
    none: 'No due date'
  }
  return labels[dueDate as keyof typeof labels] || dueDate
}

const getPriorityLabel = (priority: string) => {
  const option = priorityOptions.find(p => p.value === priority)
  return option?.label || priority
}

// Initialize filters
const initializeFilters = () => {
  if (!localFilters.value.labels) localFilters.value.labels = []
  if (!localFilters.value.assignees) localFilters.value.assignees = []
  if (!localFilters.value.priority) localFilters.value.priority = []
}

// Watch for external filter changes
watch(() => props.filters, (newFilters) => {
  localFilters.value = { ...newFilters }
  initializeFilters()
}, { deep: true, immediate: true })

// Initialize on mount
initializeFilters()
</script>

<style scoped>
.board-filters {
  background: white;
  border-bottom: 1px solid #dbdbdb;
  padding: 1rem;
}

.search-section {
  margin-bottom: 1rem;
}

.filter-toggles {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  margin-bottom: 1rem;
}

.filter-toggles .button.is-active {
  background: #3273dc;
  color: white;
}

.filter-panel {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  color: #666;
}

/* Label Filters */
.label-filters {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.label-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.label-tag {
  color: white;
  font-weight: 500;
  font-size: 0.8rem;
}

/* Assignee Filters */
.assignee-filters {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.assignee-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.assignee-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.assignee-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #3273dc;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.7rem;
}

.assignee-name {
  font-size: 0.9rem;
}

/* Priority Filters */
.priority-filters {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.priority-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.priority-tag {
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Additional Filters */
.additional-filters {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

/* Active Filters */
.active-filters {
  margin-bottom: 1rem;
}

.active-filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.active-filter-tags .tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Search Results */
.search-results {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 1rem;
}

.search-results-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #666;
}

.search-results-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.search-result-item {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-result-item:hover {
  border-color: #3273dc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.result-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
}

.result-column {
  font-size: 0.8rem;
  color: #666;
  background: #f0f0f0;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

.result-match {
  font-size: 0.8rem;
  color: #666;
}

.match-type {
  font-weight: 500;
  text-transform: capitalize;
}

.match-text {
  font-style: italic;
}

/* Responsive */
@media (max-width: 768px) {
  .filter-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-toggles {
    flex-wrap: wrap;
  }
  
  .active-filter-tags {
    gap: 0.25rem;
  }
  
  .active-filter-tags .tag {
    font-size: 0.8rem;
  }
}
</style>
import { Request, Response, NextFunction } from 'express';
import { CacheService } from '../services/CacheService';
import crypto from 'crypto';

export interface CacheMiddlewareOptions {
  ttl?: number;
  keyGenerator?: (req: Request) => string;
  condition?: (req: Request, res: Response) => boolean;
  tags?: string[] | ((req: Request) => string[]);
  skipCache?: (req: Request) => boolean;
}

// Generate cache key from request
function generateCacheKey(req: Request, customGenerator?: (req: Request) => string): string {
  if (customGenerator) {
    return customGenerator(req);
  }

  const userId = (req as any).user?.id || 'anonymous';
  const method = req.method;
  const path = req.path;
  const query = JSON.stringify(req.query);
  const body = method === 'GET' ? '' : JSON.stringify(req.body);
  
  const keyData = `${method}:${path}:${query}:${body}:${userId}`;
  const hash = crypto.createHash('md5').update(keyData).digest('hex');
  
  return `api:${hash}`;
}

// Cache middleware factory
export function cacheMiddleware(options: CacheMiddlewareOptions = {}) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip caching for non-GET requests by default
    if (req.method !== 'GET' && !options.condition) {
      return next();
    }

    // Skip cache if condition is provided and returns false
    if (options.skipCache && options.skipCache(req)) {
      return next();
    }

    const cacheKey = generateCacheKey(req, options.keyGenerator);
    
    try {
      // Try to get cached response
      const cachedResponse = await CacheService.get(cacheKey);
      
      if (cachedResponse) {
        // Set cache headers
        res.set('X-Cache', 'HIT');
        res.set('X-Cache-Key', cacheKey);
        
        // Send cached response
        const response = cachedResponse as { status?: number; data: any };
        return res.status(response.status || 200).json(response.data);
      }

      // Cache miss - continue to route handler
      res.set('X-Cache', 'MISS');
      res.set('X-Cache-Key', cacheKey);

      // Override res.json to cache the response
      const originalJson = res.json.bind(res);
      res.json = function(data: any) {
        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          const shouldCache = !options.condition || options.condition(req, res);
          
          if (shouldCache) {
            const responseData = {
              status: res.statusCode,
              data: data
            };

            const tags = typeof options.tags === 'function' 
              ? options.tags(req) 
              : options.tags;

            CacheService.set(cacheKey, responseData, {
              ttl: options.ttl || 300, // Default 5 minutes
              tags: tags
            }).catch(err => {
              console.error('Failed to cache response:', err);
            });
          }
        }

        return originalJson(data);
      };

      next();
    } catch (error) {
      console.error('Cache middleware error:', error);
      next();
    }
  };
}

// Specific cache middleware for different endpoints
export const cacheNotes = cacheMiddleware({
  ttl: 300, // 5 minutes
  keyGenerator: (req) => {
    const userId = (req as any).user?.id;
    const page = req.query.page || '1';
    const limit = req.query.limit || '20';
    const search = req.query.search || '';
    const type = req.query.type || '';
    const tags = req.query.tags || '';
    return `notes:${userId}:${page}:${limit}:${search}:${type}:${tags}`;
  },
  tags: (req) => ['notes', `user:${(req as any).user?.id}`],
  condition: (req, res) => res.statusCode === 200
});

export const cacheNote = cacheMiddleware({
  ttl: 600, // 10 minutes
  keyGenerator: (req) => {
    const noteId = req.params.id;
    const userId = (req as any).user?.id;
    return `note:${noteId}:${userId}`;
  },
  tags: (req) => [`note:${req.params.id}`, `user:${(req as any).user?.id}`],
  condition: (req, res) => res.statusCode === 200
});

export const cacheSearch = cacheMiddleware({
  ttl: 600, // 10 minutes
  keyGenerator: (req) => {
    const userId = (req as any).user?.id;
    const query = req.query.q || '';
    const type = req.query.type || '';
    const tags = req.query.tags || '';
    return `search:${userId}:${Buffer.from(query as string).toString('base64')}:${type}:${tags}`;
  },
  tags: (req) => ['search', `user:${(req as any).user?.id}`],
  condition: (req, res) => res.statusCode === 200 && !!req.query.q
});

export const cacheTags = cacheMiddleware({
  ttl: 1800, // 30 minutes
  keyGenerator: (req) => {
    const userId = (req as any).user?.id;
    return `tags:${userId}`;
  },
  tags: (req) => ['tags', `user:${(req as any).user?.id}`],
  condition: (req, res) => res.statusCode === 200
});

export const cacheGroups = cacheMiddleware({
  ttl: 900, // 15 minutes
  keyGenerator: (req) => {
    const userId = (req as any).user?.id;
    return `groups:${userId}`;
  },
  tags: (req) => ['groups', `user:${(req as any).user?.id}`],
  condition: (req, res) => res.statusCode === 200
});

export const cacheUserSettings = cacheMiddleware({
  ttl: 1800, // 30 minutes
  keyGenerator: (req) => {
    const userId = (req as any).user?.id;
    return `user_settings:${userId}`;
  },
  tags: (req) => ['settings', `user:${(req as any).user?.id}`],
  condition: (req, res) => res.statusCode === 200
});

// Cache invalidation middleware
export function invalidateCacheMiddleware(tags: string[] | ((req: Request) => string[])) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Store original end function
    const originalEnd = res.end.bind(res);
    
    res.end = function(chunk?: any, encoding?: any) {
      // Only invalidate on successful operations
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const tagsToInvalidate = typeof tags === 'function' ? tags(req) : tags;
        
        CacheService.invalidateByTags(tagsToInvalidate).catch(err => {
          console.error('Failed to invalidate cache:', err);
        });
      }
      
      return originalEnd(chunk, encoding);
    };
    
    next();
  };
}

// Specific invalidation middleware
export const invalidateNotesCache = invalidateCacheMiddleware((req) => {
  const userId = (req as any).user?.id;
  return ['notes', `user:${userId}`];
});

export const invalidateNoteCache = invalidateCacheMiddleware((req) => {
  const noteId = req.params.id;
  const userId = (req as any).user?.id;
  return [`note:${noteId}`, `user:${userId}`, 'notes'];
});

export const invalidateSearchCache = invalidateCacheMiddleware((req) => {
  const userId = (req as any).user?.id;
  return ['search', `user:${userId}`];
});

export const invalidateTagsCache = invalidateCacheMiddleware((req) => {
  const userId = (req as any).user?.id;
  return ['tags', `user:${userId}`];
});

export const invalidateGroupsCache = invalidateCacheMiddleware((req) => {
  const userId = (req as any).user?.id;
  return ['groups', `user:${userId}`];
});

export const invalidateUserCache = invalidateCacheMiddleware((req) => {
  const userId = (req as any).user?.id;
  return [`user:${userId}`, 'settings'];
});
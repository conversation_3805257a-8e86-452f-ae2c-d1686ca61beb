<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FontAwesome Theme Integration Test</title>
    <link rel="stylesheet" href="src/styles/main.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--color-background);
            color: var(--color-text);
            transition: var(--transition-theme);
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .theme-selector {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--color-surface);
            border-radius: var(--radius);
            border: 1px solid var(--color-border);
        }

        .theme-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .theme-button {
            padding: 8px 16px;
            border: 1px solid var(--color-border);
            background: var(--color-background);
            color: var(--color-text);
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition-fast);
        }

        .theme-button:hover {
            background: var(--color-primary);
            color: white;
        }

        .theme-button.active {
            background: var(--color-primary);
            color: white;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--color-surface);
            border-radius: var(--radius);
            border: 1px solid var(--color-border);
        }

        .test-section h3 {
            margin-top: 0;
            color: var(--color-text-strong);
        }

        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .icon-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: var(--color-background);
            border-radius: var(--radius);
            border: 1px solid var(--color-border);
        }

        .icon-item .fas,
        .icon-item .far,
        .icon-item .fab {
            width: 20px;
            text-align: center;
        }

        .navbar-demo {
            background: var(--navbar-background, var(--color-surface));
            padding: 15px;
            border-radius: var(--radius);
            margin-bottom: 15px;
        }

        .navbar-item {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            margin-right: 10px;
            border-radius: var(--radius);
            color: var(--navbar-text, var(--color-text));
            text-decoration: none;
            transition: var(--transition-fast);
        }

        .navbar-item:hover {
            background: var(--color-primary);
            color: white;
        }

        .navbar-item.is-active {
            background: var(--color-primary);
            color: white;
        }

        .button-demo {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid var(--color-border);
            background: var(--color-background);
            color: var(--color-text);
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition-fast);
            text-decoration: none;
        }

        .button:hover {
            background: var(--color-surface);
        }

        .button.is-primary {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        .button.is-success {
            background: var(--color-success);
            color: white;
            border-color: var(--color-success);
        }

        .button.is-danger {
            background: var(--color-danger);
            color: white;
            border-color: var(--color-danger);
        }

        .status-icons {
            display: flex;
            gap: 20px;
            margin-top: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: var(--radius);
            background: var(--color-background);
        }

        .performance-stats {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius);
            padding: 15px;
            font-size: 12px;
            font-family: monospace;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-palette"></i> FontAwesome Theme Integration Test</h1>
        
        <div class="theme-selector">
            <h3>Theme Selector</h3>
            <div class="theme-buttons">
                <button class="theme-button active" data-theme="default">Default</button>
                <button class="theme-button" data-theme="darkly">Darkly</button>
                <button class="theme-button" data-theme="flatly">Flatly</button>
                <button class="theme-button" data-theme="cerulean">Cerulean</button>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-bars"></i> Navigation Icons</h3>
            <div class="navbar-demo">
                <a href="#" class="navbar-item">
                    <i class="fas fa-home"></i>
                    Home
                </a>
                <a href="#" class="navbar-item">
                    <i class="fas fa-file-alt"></i>
                    Notes
                </a>
                <a href="#" class="navbar-item is-active">
                    <i class="fas fa-users"></i>
                    Groups
                </a>
                <a href="#" class="navbar-item">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-mouse-pointer"></i> Button Icons</h3>
            <div class="button-demo">
                <button class="button">
                    <i class="fas fa-plus"></i>
                    Add Note
                </button>
                <button class="button is-primary">
                    <i class="fas fa-save"></i>
                    Save
                </button>
                <button class="button is-success">
                    <i class="fas fa-check"></i>
                    Complete
                </button>
                <button class="button is-danger">
                    <i class="fas fa-trash"></i>
                    Delete
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-info-circle"></i> Status Icons</h3>
            <div class="status-icons">
                <div class="status-item">
                    <i class="fas fa-check-circle has-text-success"></i>
                    Success
                </div>
                <div class="status-item">
                    <i class="fas fa-exclamation-triangle has-text-warning"></i>
                    Warning
                </div>
                <div class="status-item">
                    <i class="fas fa-times-circle has-text-danger"></i>
                    Error
                </div>
                <div class="status-item">
                    <i class="fas fa-info-circle has-text-info"></i>
                    Info
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-icons"></i> Common Icons</h3>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fas fa-search"></i>
                    Search
                </div>
                <div class="icon-item">
                    <i class="fas fa-edit"></i>
                    Edit
                </div>
                <div class="icon-item">
                    <i class="fas fa-share-alt"></i>
                    Share
                </div>
                <div class="icon-item">
                    <i class="fas fa-download"></i>
                    Download
                </div>
                <div class="icon-item">
                    <i class="fas fa-upload"></i>
                    Upload
                </div>
                <div class="icon-item">
                    <i class="fas fa-heart"></i>
                    Favorite
                </div>
                <div class="icon-item">
                    <i class="fas fa-star"></i>
                    Star
                </div>
                <div class="icon-item">
                    <i class="fas fa-bookmark"></i>
                    Bookmark
                </div>
                <div class="icon-item">
                    <i class="fas fa-tag"></i>
                    Tag
                </div>
                <div class="icon-item">
                    <i class="fas fa-calendar"></i>
                    Calendar
                </div>
                <div class="icon-item">
                    <i class="fas fa-clock"></i>
                    Time
                </div>
                <div class="icon-item">
                    <i class="fas fa-bell"></i>
                    Notifications
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fab fa-font-awesome"></i> Brand Icons</h3>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fab fa-google"></i>
                    Google
                </div>
                <div class="icon-item">
                    <i class="fab fa-github"></i>
                    GitHub
                </div>
                <div class="icon-item">
                    <i class="fab fa-twitter"></i>
                    Twitter
                </div>
                <div class="icon-item">
                    <i class="fab fa-facebook"></i>
                    Facebook
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-spinner fa-spin"></i> Animated Icons</h3>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fas fa-spinner fa-spin"></i>
                    Loading
                </div>
                <div class="icon-item">
                    <i class="fas fa-circle-notch fa-spin"></i>
                    Processing
                </div>
                <div class="icon-item">
                    <i class="fas fa-heart fa-pulse"></i>
                    Pulse
                </div>
                <div class="icon-item">
                    <i class="fas fa-sync fa-spin"></i>
                    Syncing
                </div>
            </div>
        </div>
    </div>

    <div class="performance-stats" id="performance-stats">
        <div>Theme: <span id="current-theme">default</span></div>
        <div>Icons: <span id="icon-count">0</span></div>
        <div>Load Time: <span id="load-time">0ms</span></div>
    </div>

    <script type="module">
        const startTime = performance.now();
        
        // Theme switching functionality
        const themeButtons = document.querySelectorAll('.theme-button');
        const currentThemeSpan = document.getElementById('current-theme');
        
        themeButtons.forEach(button => {
            button.addEventListener('click', () => {
                const theme = button.dataset.theme;
                
                // Update active button
                themeButtons.forEach(b => b.classList.remove('active'));
                button.classList.add('active');
                
                // Apply theme
                document.documentElement.setAttribute('data-theme', theme);
                document.body.className = `theme-${theme}`;
                
                // Update display
                currentThemeSpan.textContent = theme;
                
                console.log(`Theme switched to: ${theme}`);
            });
        });
        
        // Count icons and measure performance
        function updateStats() {
            const iconCount = document.querySelectorAll('.fas, .far, .fab').length;
            const loadTime = Math.round(performance.now() - startTime);
            
            document.getElementById('icon-count').textContent = iconCount;
            document.getElementById('load-time').textContent = loadTime + 'ms';
        }
        
        // Initialize stats
        document.addEventListener('DOMContentLoaded', updateStats);
        
        // Test FontAwesome optimization if available
        if (window.fontAwesomeOptimizer) {
            window.fontAwesomeOptimizer.initialize().then(() => {
                console.log('FontAwesome optimizer initialized');
                updateStats();
            });
        }
        
        // Test theme switching performance
        let switchCount = 0;
        function performanceTest() {
            const themes = ['default', 'darkly', 'flatly', 'cerulean'];
            const startTime = performance.now();
            
            themes.forEach((theme, index) => {
                setTimeout(() => {
                    document.documentElement.setAttribute('data-theme', theme);
                    document.body.className = `theme-${theme}`;
                    currentThemeSpan.textContent = theme;
                    
                    if (index === themes.length - 1) {
                        const totalTime = performance.now() - startTime;
                        console.log(`Theme switching performance test completed in ${totalTime.toFixed(2)}ms`);
                    }
                }, index * 500);
            });
        }
        
        // Add performance test button
        const perfButton = document.createElement('button');
        perfButton.textContent = 'Run Performance Test';
        perfButton.className = 'theme-button';
        perfButton.onclick = performanceTest;
        document.querySelector('.theme-buttons').appendChild(perfButton);
        
        console.log('FontAwesome theme integration test loaded');
    </script>
</body>
</html>
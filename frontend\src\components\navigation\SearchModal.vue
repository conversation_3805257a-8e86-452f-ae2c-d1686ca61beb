<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="close"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">
          <span class="icon">
            <i class="fas fa-search"></i>
          </span>
          <span>Search Notes</span>
        </p>
        <button class="delete" aria-label="close" @click="close"></button>
      </header>

      <section class="modal-card-body">
        <!-- Search Input -->
        <div class="field">
          <div class="control has-icons-left has-icons-right">
            <input ref="searchInput" class="input is-large" type="text" placeholder="Search notes, tags, or content..."
              v-model="searchQuery" @input="handleSearch" @keydown.enter="selectFirstResult"
              @keydown.down.prevent="navigateResults(1)" @keydown.up.prevent="navigateResults(-1)"
              @keydown.escape="close">
            <span class="icon is-small is-left">
              <i class="fas fa-search"></i>
            </span>
            <span v-if="isSearching" class="icon is-small is-right">
              <i class="fas fa-spinner fa-pulse"></i>
            </span>
          </div>
        </div>

        <!-- Search Filters -->
        <div class="search-filters" v-if="searchQuery">
          <div class="field is-grouped is-grouped-multiline">
            <div class="control">
              <div class="tags has-addons">
                <span class="tag">Type:</span>
                <div class="dropdown is-hoverable">
                  <div class="dropdown-trigger">
                    <span class="tag is-link">
                      {{ selectedType || 'All' }}
                      <span class="icon is-small">
                        <i class="fas fa-angle-down"></i>
                      </span>
                    </span>
                  </div>
                  <div class="dropdown-menu">
                    <div class="dropdown-content">
                      <a class="dropdown-item" @click="setTypeFilter('')">All Types</a>
                      <a class="dropdown-item" @click="setTypeFilter('richtext')">Rich Text</a>
                      <a class="dropdown-item" @click="setTypeFilter('markdown')">Markdown</a>
                      <a class="dropdown-item" @click="setTypeFilter('kanban')">Kanban</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="control">
              <div class="tags has-addons">
                <span class="tag">Date:</span>
                <div class="dropdown is-hoverable">
                  <div class="dropdown-trigger">
                    <span class="tag is-info">
                      {{ selectedDateRange || 'Any time' }}
                      <span class="icon is-small">
                        <i class="fas fa-angle-down"></i>
                      </span>
                    </span>
                  </div>
                  <div class="dropdown-menu">
                    <div class="dropdown-content">
                      <a class="dropdown-item" @click="setDateFilter('')">Any time</a>
                      <a class="dropdown-item" @click="setDateFilter('today')">Today</a>
                      <a class="dropdown-item" @click="setDateFilter('week')">This week</a>
                      <a class="dropdown-item" @click="setDateFilter('month')">This month</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Search Results -->
        <div class="search-results" v-if="searchQuery">
          <!-- Loading State -->
          <div v-if="isSearching" class="search-loading">
            <div class="has-text-centered">
              <span class="icon is-large">
                <i class="fas fa-spinner fa-pulse"></i>
              </span>
              <p>Searching...</p>
            </div>
          </div>

          <!-- No Results -->
          <div v-else-if="searchResults.length === 0" class="search-empty">
            <div class="has-text-centered">
              <span class="icon is-large has-text-grey-light">
                <i class="fas fa-search"></i>
              </span>
              <h4 class="title is-5 has-text-grey">No results found</h4>
              <p class="has-text-grey">Try adjusting your search terms or filters</p>
            </div>
          </div>

          <!-- Results List -->
          <div v-else class="results-list">
            <div v-for="(result, index) in searchResults" :key="result.id" class="result-item"
              :class="{ 'is-selected': selectedResultIndex === index }" @click="selectResult(result)"
              @mouseenter="selectedResultIndex = index">
              <div class="result-icon">
                <span class="icon" :class="getNoteTypeClass(result.noteType)">
                  <i :class="getNoteTypeIcon(result.noteType)"></i>
                </span>
              </div>

              <div class="result-content">
                <h5 class="result-title" v-html="highlightMatch(result.title, searchQuery)"></h5>
                <p class="result-preview" v-html="highlightMatch(result.preview, searchQuery)"></p>

                <div class="result-meta">
                  <span class="result-type">{{ getNoteTypeName(result.noteType) }}</span>
                  <span class="result-date">{{ formatDate(result.updatedAt) }}</span>
                  <div class="result-tags" v-if="result.tags.length > 0">
                    <span v-for="tag in result.tags.slice(0, 3)" :key="tag" class="tag is-small">
                      {{ tag }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="result-actions">
                <span class="icon is-small has-text-grey-light">
                  <i class="fas fa-arrow-right"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Searches -->
        <div class="recent-searches" v-if="!searchQuery && recentSearches.length > 0">
          <h4 class="title is-6">Recent Searches</h4>
          <div class="recent-list">
            <a v-for="recent in recentSearches" :key="recent" class="recent-item" @click="searchQuery = recent">
              <span class="icon is-small">
                <i class="fas fa-history"></i>
              </span>
              <span>{{ recent }}</span>
            </a>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions" v-if="!searchQuery">
          <h4 class="title is-6">Quick Actions</h4>
          <div class="action-buttons">
            <button class="button is-fullwidth is-light" @click="createNote">
              <span class="icon">
                <i class="fas fa-plus"></i>
              </span>
              <span>Create New Note</span>
            </button>
          </div>
        </div>
      </section>

      <footer class="modal-card-foot">
        <div class="search-shortcuts">
          <span class="shortcut-hint">
            <kbd>↑</kbd><kbd>↓</kbd> to navigate
          </span>
          <span class="shortcut-hint">
            <kbd>Enter</kbd> to select
          </span>
          <span class="shortcut-hint">
            <kbd>Esc</kbd> to close
          </span>
        </div>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'

// Props
interface Props {
  isOpen: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'close': []
  'note-selected': [noteId: string]
  'create-note': []
}>()

// Composables
const router = useRouter()

// Reactive state
const searchQuery = ref('')
const isSearching = ref(false)
const selectedResultIndex = ref(0)
const selectedType = ref('')
const selectedDateRange = ref('')
const searchInput = ref<HTMLInputElement>()

// Mock data (will be replaced with real search service)
const searchResults = ref<any[]>([])
const recentSearches = ref(['project ideas', 'meeting notes', 'todo'])

// Computed
const filteredResults = computed(() => {
  let results = searchResults.value

  if (selectedType.value) {
    results = results.filter(result => result.noteType === selectedType.value)
  }

  if (selectedDateRange.value) {
    const now = new Date()
    const filterDate = new Date()

    switch (selectedDateRange.value) {
      case 'today':
        filterDate.setHours(0, 0, 0, 0)
        break
      case 'week':
        filterDate.setDate(now.getDate() - 7)
        break
      case 'month':
        filterDate.setMonth(now.getMonth() - 1)
        break
    }

    results = results.filter(result => new Date(result.updatedAt) >= filterDate)
  }

  return results
})

// Methods
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    return
  }

  isSearching.value = true
  selectedResultIndex.value = 0

  // Simulate API call
  setTimeout(() => {
    // Mock search results
    searchResults.value = [
      {
        id: '1',
        title: 'Project Ideas for Q4',
        preview: 'Some great ideas for the upcoming quarter including mobile app development...',
        noteType: 'richtext',
        tags: ['work', 'ideas', 'q4'],
        updatedAt: new Date('2024-01-20')
      },
      {
        id: '2',
        title: 'Meeting Notes - Team Sync',
        preview: 'Discussed project timeline and resource allocation for the next sprint...',
        noteType: 'markdown',
        tags: ['meeting', 'team'],
        updatedAt: new Date('2024-01-19')
      }
    ].filter(note =>
      note.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      note.preview.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      note.tags.some(tag => tag.toLowerCase().includes(searchQuery.value.toLowerCase()))
    )

    isSearching.value = false
  }, 300)
}

const navigateResults = (direction: number) => {
  if (searchResults.value.length === 0) return

  selectedResultIndex.value += direction

  if (selectedResultIndex.value < 0) {
    selectedResultIndex.value = searchResults.value.length - 1
  } else if (selectedResultIndex.value >= searchResults.value.length) {
    selectedResultIndex.value = 0
  }
}

const selectFirstResult = () => {
  if (searchResults.value.length > 0) {
    selectResult(searchResults.value[selectedResultIndex.value])
  }
}

const selectResult = (result: any) => {
  // Add to recent searches
  if (!recentSearches.value.includes(searchQuery.value)) {
    recentSearches.value.unshift(searchQuery.value)
    recentSearches.value = recentSearches.value.slice(0, 5)
  }

  emit('note-selected', result.id)
  close()
}

const setTypeFilter = (type: string) => {
  selectedType.value = type
  handleSearch()
}

const setDateFilter = (range: string) => {
  selectedDateRange.value = range
  handleSearch()
}

const close = () => {
  emit('close')
  searchQuery.value = ''
  searchResults.value = []
  selectedResultIndex.value = 0
  selectedType.value = ''
  selectedDateRange.value = ''
}

const createNote = () => {
  close()
  // Emit create note event to parent
  emit('create-note')
}

const highlightMatch = (text: string, query: string) => {
  if (!query) return text

  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const getNoteTypeIcon = (type: string) => {
  switch (type) {
    case 'richtext': return 'fas fa-align-left'
    case 'markdown': return 'fab fa-markdown'
    case 'kanban': return 'fas fa-columns'
    default: return 'fas fa-file-alt'
  }
}

const getNoteTypeClass = (type: string) => {
  switch (type) {
    case 'richtext': return 'has-text-info'
    case 'markdown': return 'has-text-success'
    case 'kanban': return 'has-text-warning'
    default: return 'has-text-grey'
  }
}

const getNoteTypeName = (type: string) => {
  switch (type) {
    case 'richtext': return 'Rich Text'
    case 'markdown': return 'Markdown'
    case 'kanban': return 'Kanban'
    default: return 'Note'
  }
}

const formatDate = (date: Date) => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return 'Today'
  } else if (diffDays === 1) {
    return 'Yesterday'
  } else if (diffDays < 7) {
    return `${diffDays} days ago`
  } else {
    return date.toLocaleDateString()
  }
}

// Watch for modal open/close
watch(() => props.isOpen, async (isOpen) => {
  if (isOpen) {
    await nextTick()
    searchInput.value?.focus()
  }
})
</script>

<style scoped>
.modal-card {
  width: 90vw;
  max-width: 600px;
  max-height: 80vh;
}

.modal-card-body {
  max-height: 60vh;
  overflow-y: auto;
}

.search-filters {
  margin-bottom: 1rem;
}

.search-results {
  margin-top: 1rem;
}

.search-loading,
.search-empty {
  padding: 2rem;
  text-align: center;
}

.results-list {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.result-item:hover,
.result-item.is-selected {
  background: #f8f9fa;
  border-color: #007bff;
}

.result-icon {
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-size: 1rem;
  font-weight: 600;
  color: #363636;
  margin: 0 0 0.25rem 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.result-preview {
  font-size: 0.875rem;
  color: #6c757d;
  line-height: 1.4;
  margin: 0 0 0.5rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.75rem;
  color: #6c757d;
}

.result-type {
  font-weight: 500;
}

.result-tags {
  display: flex;
  gap: 0.25rem;
}

.result-actions {
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.recent-searches,
.quick-actions {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  color: #6c757d;
  text-decoration: none;
  transition: all 0.2s ease;
}

.recent-item:hover {
  background: #f8f9fa;
  color: #007bff;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.search-shortcuts {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.shortcut-hint {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

kbd {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
  font-family: monospace;
}

mark {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
  padding: 0.125rem 0.25rem;
  border-radius: 2px;
}

/* Responsive */
@media screen and (max-width: 768px) {
  .modal-card {
    width: 95vw;
    margin: 1rem;
  }

  .result-item {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .result-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .search-shortcuts {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
import { getDatabase } from '../config/database';
import { Tag, CreateTagData, UpdateTagData, TagFilters, TagModel, PREDEFINED_TAGS } from '../models/Tag';

export class TagRepository {
  private static getDb() {
    return getDatabase();
  }

  static async create(data: CreateTagData): Promise<Tag> {
    return new Promise((resolve, reject) => {
      const db = this.getDb();
      const id = TagModel.generateId();
      const now = new Date();
      const normalizedName = TagModel.normalizeTagName(data.name);

      const sql = `
        INSERT INTO tags (id, user_id, name, icon, color, is_predefined, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        id,
        data.userId,
        normalizedName,
        data.icon || TagModel.getDefaultIcon(),
        data.color || TagModel.getDefaultColor(),
        data.isPredefined ? 1 : 0,
        now.toISOString(),
        now.toISOString()
      ];

      db.run(sql, params, function(err) {
        if (err) {
          if (err.message.includes('UNIQUE constraint failed')) {
            reject(new Error('Tag with this name already exists'));
          } else {
            reject(err);
          }
        } else {
          resolve({
            id,
            userId: data.userId,
            name: normalizedName,
            icon: data.icon || TagModel.getDefaultIcon(),
            color: data.color || TagModel.getDefaultColor(),
            isPredefined: data.isPredefined || false,
            createdAt: now,
            updatedAt: now
          });
        }
      });
    });
  }

  static async findById(id: string): Promise<Tag | null> {
    return new Promise((resolve, reject) => {
      const db = this.getDb();
      const sql = `
        SELECT id, user_id, name, icon, color, is_predefined, created_at, updated_at
        FROM tags
        WHERE id = ?
      `;

      db.get(sql, [id], (err, row: any) => {
        if (err) {
          reject(err);
        } else if (!row) {
          resolve(null);
        } else {
          resolve({
            id: row.id,
            userId: row.user_id,
            name: row.name,
            icon: row.icon,
            color: row.color,
            isPredefined: Boolean(row.is_predefined),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
          });
        }
      });
    });
  }

  static async findByUserIdAndName(userId: string, name: string): Promise<Tag | null> {
    return new Promise((resolve, reject) => {
      const db = this.getDb();
      const normalizedName = TagModel.normalizeTagName(name);
      const sql = `
        SELECT id, user_id, name, icon, color, is_predefined, created_at, updated_at
        FROM tags
        WHERE user_id = ? AND name = ?
      `;

      db.get(sql, [userId, normalizedName], (err, row: any) => {
        if (err) {
          reject(err);
        } else if (!row) {
          resolve(null);
        } else {
          resolve({
            id: row.id,
            userId: row.user_id,
            name: row.name,
            icon: row.icon,
            color: row.color,
            isPredefined: Boolean(row.is_predefined),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
          });
        }
      });
    });
  }

  static async findByFilters(filters: TagFilters): Promise<Tag[]> {
    return new Promise((resolve, reject) => {
      const db = this.getDb();
      let sql = `
        SELECT t.id, t.user_id, t.name, t.icon, t.color, t.is_predefined, 
               t.created_at, t.updated_at,
               COUNT(nt.note_id) as usage_count
        FROM tags t
        LEFT JOIN note_tags nt ON t.id = nt.tag_id
        WHERE t.user_id = ?
      `;
      
      const params: any[] = [filters.userId];

      if (filters.isPredefined !== undefined) {
        sql += ' AND t.is_predefined = ?';
        params.push(filters.isPredefined ? 1 : 0);
      }

      if (filters.search) {
        sql += ' AND t.name LIKE ?';
        params.push(`%${filters.search}%`);
      }

      sql += ' GROUP BY t.id ORDER BY t.is_predefined DESC, usage_count DESC, t.name ASC';

      db.all(sql, params, (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const tags = rows.map(row => ({
            id: row.id,
            userId: row.user_id,
            name: row.name,
            icon: row.icon,
            color: row.color,
            isPredefined: Boolean(row.is_predefined),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
          }));
          resolve(tags);
        }
      });
    });
  }

  static async update(id: string, data: UpdateTagData): Promise<Tag | null> {
    return new Promise((resolve, reject) => {
      const db = this.getDb();
      const updates: string[] = [];
      const params: any[] = [];

      if (data.name !== undefined) {
        updates.push('name = ?');
        params.push(TagModel.normalizeTagName(data.name));
      }

      if (data.icon !== undefined) {
        updates.push('icon = ?');
        params.push(data.icon);
      }

      if (data.color !== undefined) {
        updates.push('color = ?');
        params.push(data.color);
      }

      if (updates.length === 0) {
        this.findById(id).then(resolve).catch(reject);
        return;
      }

      updates.push('updated_at = ?');
      params.push(new Date().toISOString());
      params.push(id);

      const sql = `UPDATE tags SET ${updates.join(', ')} WHERE id = ?`;

      db.run(sql, params, (err) => {
        if (err) {
          if (err.message.includes('UNIQUE constraint failed')) {
            reject(new Error('Tag with this name already exists'));
          } else {
            reject(err);
          }
        } else {
          this.findById(id).then(resolve).catch(reject);
        }
      });
    });
  }

  static async delete(id: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const db = this.getDb();
      const sql = 'DELETE FROM tags WHERE id = ? AND is_predefined = 0';

      db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  static async getTagUsageCounts(userId: string): Promise<Record<string, number>> {
    return new Promise((resolve, reject) => {
      const db = this.getDb();
      const sql = `
        SELECT t.name, COUNT(nt.note_id) as count
        FROM tags t
        LEFT JOIN note_tags nt ON t.id = nt.tag_id
        WHERE t.user_id = ?
        GROUP BY t.id, t.name
      `;

      db.all(sql, [userId], (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const counts: Record<string, number> = {};
          rows.forEach(row => {
            counts[row.name] = row.count;
          });
          resolve(counts);
        }
      });
    });
  }

  static async initializePredefinedTags(userId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const db = this.getDb();
      // Check if user already has predefined tags
      const checkSql = 'SELECT COUNT(*) as count FROM tags WHERE user_id = ? AND is_predefined = 1';
      
      db.get(checkSql, [userId], (err, row: any) => {
        if (err) {
          reject(err);
          return;
        }

        if (row.count > 0) {
          resolve(); // Already initialized
          return;
        }

        // Create predefined tags
        const predefinedTags = TagModel.createPredefinedTags(userId);
        const insertPromises = predefinedTags.map(tag => 
          this.create({
            userId: tag.userId,
            name: tag.name,
            icon: tag.icon,
            color: tag.color,
            isPredefined: true
          })
        );

        Promise.all(insertPromises)
          .then(() => resolve())
          .catch(reject);
      });
    });
  }
}
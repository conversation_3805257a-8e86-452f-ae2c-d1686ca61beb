<template>
  <div class="group-notes">
    <div class="notes-header">
      <div class="header-content">
        <h3 class="section-title">Group Notes</h3>
        <button 
          v-if="canCreateNotes"
          class="button is-primary"
          @click="createNote"
        >
          <span class="icon">
            <i class="fas fa-plus"></i>
          </span>
          <span>New Note</span>
        </button>
      </div>
    </div>

    <!-- Empty state -->
    <div class="empty-state">
      <div class="empty-content">
        <span class="icon">
          <i class="fas fa-sticky-note"></i>
        </span>
        <h4>Group Notes Coming Soon</h4>
        <p>
          Group note functionality will be available once note-group integration is implemented.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { getPermissions } from '../../types/group';
import type { GroupWithMembers, UserRole } from '../../types/group';

// Props
const props = defineProps<{
  group: GroupWithMembers;
  userRole: UserRole | null;
}>();

const router = useRouter();

// Computed properties
const permissions = computed(() => {
  return props.userRole ? getPermissions(props.userRole) : null;
});

const canCreateNotes = computed(() => {
  return permissions.value?.canEditNotes || false;
});

// Methods
const createNote = () => {
  // Navigate to note creation with group context
  router.push(`/notes/new?groupId=${props.group.id}`);
};
</script>

<style scoped>
.group-notes {
  padding: 1.5rem;
}

/* Header */
.notes-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #363636;
  margin: 0;
}

/* Button styling */
.button.is-primary {
  background: #007bff;
  color: white;
  border: 1px solid #007bff;
  border-radius: 6px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
}

.button.is-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Empty state */
.empty-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 3rem 2rem;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.empty-content .icon {
  color: #6c757d;
  font-size: 3rem;
}

.empty-content h4 {
  color: #363636;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.empty-content p {
  color: #6c757d;
  line-height: 1.5;
  max-width: 400px;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .group-notes {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .button {
    width: 100%;
    justify-content: center;
  }
  
  .empty-state {
    padding: 2rem 1.5rem;
  }
  
  .section-title {
    font-size: 1.125rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .group-notes {
    padding: 0.75rem;
  }
  
  .empty-state {
    padding: 1.5rem 1rem;
  }
  
  .section-title {
    font-size: 1rem;
  }
  
  .empty-content h4 {
    font-size: 1.125rem;
  }
}
</style>
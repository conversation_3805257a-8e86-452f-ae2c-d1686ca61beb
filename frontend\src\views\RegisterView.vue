<template>
  <div class="auth-layout">
    <div class="auth-container">
      <div class="auth-header">
        <div class="auth-logo">
          <img src="/logo_icon_only.png" alt="CF Notes Pro" class="logo-image" />
        </div>
        <div class="brand-name">CF Notes Pro</div>
        <h1 class="auth-title">Create Account</h1>
        <p class="auth-subtitle">Join us and start organizing your notes</p>
      </div>
      <div class="auth-form-container">
        <RegisterForm />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import RegisterForm from '../components/auth/RegisterForm.vue'
</script>

<style scoped>
</style>
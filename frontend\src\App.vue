<script setup lang="ts">
import { onMounted, onErrorCaptured } from 'vue'

// Error handling for router issues
onErrorCaptured((error, instance, info) => {
  console.error('App error captured:', error, info)
  
  // If it's a router-related error, try to recover
  if (error.message?.includes('route') || error.message?.includes('router')) {
    console.warn('Router error detected, attempting recovery...')
    // Don't propagate router errors to prevent app crash
    return false
  }
  
  // Let other errors propagate
  return true
})

onMounted(() => {
  // Add global error handler for unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    // Prevent router-related rejections from crashing the app
    if (event.reason?.message?.includes('route') || event.reason?.message?.includes('router')) {
      event.preventDefault()
    }
  })
})
</script>

<template>
  <div id="app">
    <router-view v-slot="{ Component, route }">
      <component :is="Component" :key="route?.path || 'default'" />
    </router-view>
  </div>
</template>

<style>
#app {
  min-height: 100vh;
}

/* All moved to external CSS files under src/styles */
</style>

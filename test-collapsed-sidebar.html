<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Collapsed Sidebar Test</title>
    <style>
        /* CSS Variables */
        :root {
            --color-surface: #f5f5f5;
            --color-surface-hover: #eeeeee;
            --color-text: #4a4a4a;
            --color-text-muted: #7a7a7a;
            --color-primary: #3273dc;
            --color-border: #dbdbdb;
            --color-background: #ffffff;
            --color-card: #ffffff;
            --radius: 4px;
            --transition-fast: all 0.15s ease;
            --spacing-3: 0.75rem;
            --spacing-4: 1rem;
        }

        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: var(--color-background);
        }

        .app-sidebar {
            width: 260px;
            height: 100vh;
            background: var(--color-surface);
            border-right: 1px solid var(--color-border);
            transition: width 0.3s ease;
            position: fixed;
            left: 0;
            top: 0;
        }

        .app-sidebar.is-collapsed {
            width: 60px;
            background: transparent;
            border-right: none;
        }

        /* Remove hover effects and make backgrounds transparent in collapsed state */
        .app-sidebar.is-collapsed .menu-link:hover {
            background: transparent;
            color: var(--color-text-muted);
        }

        .app-sidebar.is-collapsed .menu-link.is-active {
            background: transparent;
            color: var(--color-primary);
        }

        .app-sidebar.is-collapsed .menu-link.is-active:hover {
            background: transparent;
            color: var(--color-primary);
        }

        /* Make user section background transparent in collapsed state */
        .app-sidebar.is-collapsed .user-section {
            background: transparent;
            border: none;
            margin: 0.5rem;
            padding: 0.5rem;
        }

        /* Make actions section background transparent in collapsed state */
        .app-sidebar.is-collapsed .actions-section {
            background: transparent;
            border: none;
            margin: 0.5rem;
            padding: 0.5rem;
        }

        /* Remove hover effects for buttons in collapsed state */
        .app-sidebar.is-collapsed .button:hover {
            background: transparent;
            border-color: var(--color-border);
            color: var(--color-text-muted);
            transform: none;
            box-shadow: none;
        }

        /* Ensure icons have good contrast in collapsed state */
        .app-sidebar.is-collapsed .menu-link .icon {
            margin: 0 auto;
            color: var(--color-text-muted);
            opacity: 0.8;
        }

        .app-sidebar.is-collapsed .menu-link:hover .icon {
            color: var(--color-text);
            opacity: 1;
        }

        .app-sidebar.is-collapsed .menu-link.is-active .icon {
            color: var(--color-primary);
            opacity: 1;
        }

        /* Sidebar sections */
        .sidebar-section {
            padding: var(--spacing-4);
            border-bottom: 1px solid var(--color-border);
        }

        .app-sidebar.is-collapsed .sidebar-section {
            padding: 0.5rem;
            border-bottom: none;
        }

        /* User section */
        .user-section {
            background: var(--color-card);
            border-radius: var(--radius);
            margin: var(--spacing-4);
            padding: var(--spacing-4);
            border-bottom: none;
        }

        /* Actions section */
        .actions-section {
            border-bottom: 1px solid var(--color-border);
            margin: 0 1rem;
            padding: 0.75rem;
            background: var(--color-card);
            border-radius: var(--radius);
        }

        /* Menu items */
        .sidebar-menu {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .menu-item {
            margin-bottom: 0.25rem;
        }

        .menu-link {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            padding: var(--spacing-3);
            border-radius: var(--radius);
            color: var(--color-text-muted);
            text-decoration: none;
            transition: var(--transition-fast);
            cursor: pointer;
        }

        .menu-link:hover {
            background: var(--color-surface-hover);
            color: var(--color-text);
        }

        .app-sidebar.is-collapsed .menu-link {
            justify-content: center;
            padding: 0.5rem;
        }

        .menu-text {
            flex: 1;
            font-weight: 500;
        }

        .app-sidebar.is-collapsed .menu-text {
            display: none;
        }

        /* Buttons */
        .button {
            border-radius: var(--radius);
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            color: var(--color-text-muted);
            transition: var(--transition-fast);
            padding: 0.5rem;
            cursor: pointer;
        }

        .button.is-primary {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        .button:hover {
            background: var(--color-surface-hover);
            border-color: var(--color-primary);
            color: var(--color-primary);
            transform: translateY(-1px);
            box-shadow: 0 1px 2px rgba(10, 10, 10, 0.1);
        }

        /* Toggle button */
        .toggle-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        /* Content area */
        .content {
            margin-left: 260px;
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        .content.sidebar-collapsed {
            margin-left: 60px;
        }
    </style>
</head>
<body>
    <button class="toggle-btn button" onclick="toggleSidebar()">Toggle Sidebar</button>
    
    <div class="app-sidebar" id="sidebar">
        <!-- User Section -->
        <div class="sidebar-section user-section">
            <div class="user-profile">
                <div class="user-avatar">
                    <span class="icon">👤</span>
                </div>
                <div class="user-info">
                    <p class="user-name">John Doe</p>
                    <p class="user-email"><EMAIL></p>
                </div>
            </div>
        </div>

        <!-- Actions Section -->
        <div class="sidebar-section actions-section">
            <div class="quick-actions">
                <button class="button is-primary">+ New Note</button>
            </div>
        </div>

        <!-- Navigation Section -->
        <div class="sidebar-section nav-section">
            <ul class="sidebar-menu">
                <li class="menu-item">
                    <a href="#" class="menu-link">
                        <span class="icon">📊</span>
                        <span class="menu-text">Dashboard</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link is-active">
                        <span class="icon">📝</span>
                        <span class="menu-text">Notes</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link">
                        <span class="icon">🏷️</span>
                        <span class="menu-text">Tags</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link">
                        <span class="icon">⚙️</span>
                        <span class="menu-text">Settings</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="content" id="content">
        <h1>Collapsed Sidebar Test</h1>
        <p>This page tests the collapsed sidebar behavior with transparent backgrounds and no hover effects.</p>
        <p>Click the toggle button to collapse/expand the sidebar.</p>
        <p>When collapsed, the sidebar should have:</p>
        <ul>
            <li>Transparent background</li>
            <li>No hover effects on menu items</li>
            <li>Better icon contrast</li>
            <li>No background color changes on hover</li>
        </ul>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');
            
            sidebar.classList.toggle('is-collapsed');
            content.classList.toggle('sidebar-collapsed');
        }
    </script>
</body>
</html>

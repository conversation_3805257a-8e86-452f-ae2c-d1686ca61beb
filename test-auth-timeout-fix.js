#!/usr/bin/env node

/**
 * Test script to verify auth timeout fixes
 * This script simulates the auth initialization process and checks for timeout handling
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Auth Timeout Fixes...\n');

// Test 1: Check if auth store has proper timeout handling
function testAuthStoreTimeouts() {
  console.log('Test 1: Checking auth store timeout configuration...');
  
  const authStorePath = path.join(__dirname, 'frontend/src/stores/auth.ts');
  
  if (!fs.existsSync(authStorePath)) {
    console.log('❌ Auth store file not found');
    return false;
  }
  
  const authStoreContent = fs.readFileSync(authStorePath, 'utf8');
  
  // Check for increased timeout values
  const hasIncreasedTimeout = authStoreContent.includes('timeoutMs: number = 5000');
  const hasWaitingLogic = authStoreContent.includes('waiting for completion');
  const hasImprovedTokenRefresh = authStoreContent.includes('setTimeout(() => reject(new Error(\'Token refresh timeout\')), 5000)');
  const hasImprovedProfileFetch = authStoreContent.includes('setTimeout(() => reject(new Error(\'Profile fetch timeout\')), 3000)');
  
  console.log(`  ✅ Increased default timeout: ${hasIncreasedTimeout ? 'YES' : 'NO'}`);
  console.log(`  ✅ Concurrent initialization handling: ${hasWaitingLogic ? 'YES' : 'NO'}`);
  console.log(`  ✅ Improved token refresh timeout: ${hasImprovedTokenRefresh ? 'YES' : 'NO'}`);
  console.log(`  ✅ Improved profile fetch timeout: ${hasImprovedProfileFetch ? 'YES' : 'NO'}`);
  
  return hasIncreasedTimeout && hasWaitingLogic && hasImprovedTokenRefresh && hasImprovedProfileFetch;
}

// Test 2: Check router guard timeout improvements
function testRouterTimeouts() {
  console.log('\nTest 2: Checking router timeout configuration...');
  
  const routerPath = path.join(__dirname, 'frontend/src/router/index.ts');
  
  if (!fs.existsSync(routerPath)) {
    console.log('❌ Router file not found');
    return false;
  }
  
  const routerContent = fs.readFileSync(routerPath, 'utf8');
  
  // Check for increased timeout values and initialization checks
  const hasIncreasedProtectedRouteTimeout = routerContent.includes('initializeAuthWithTimeout(8000)');
  const hasIncreasedDashboardTimeout = routerContent.includes('initializeAuthWithTimeout(6000)');
  const hasInitializingCheck = routerContent.includes('!authStore.isInitializing');
  
  console.log(`  ✅ Increased protected route timeout: ${hasIncreasedProtectedRouteTimeout ? 'YES' : 'NO'}`);
  console.log(`  ✅ Increased dashboard timeout: ${hasIncreasedDashboardTimeout ? 'YES' : 'NO'}`);
  console.log(`  ✅ Initialization state check: ${hasInitializingCheck ? 'YES' : 'NO'}`);
  
  return hasIncreasedProtectedRouteTimeout && hasIncreasedDashboardTimeout && hasInitializingCheck;
}

// Test 3: Check store initializer timeout improvements
function testStoreInitializerTimeouts() {
  console.log('\nTest 3: Checking store initializer timeout configuration...');
  
  const storeInitializerPath = path.join(__dirname, 'frontend/src/services/storeInitializer.ts');
  
  if (!fs.existsSync(storeInitializerPath)) {
    console.log('❌ Store initializer file not found');
    return false;
  }
  
  const storeInitializerContent = fs.readFileSync(storeInitializerPath, 'utf8');
  
  // Check for increased timeout values
  const hasIncreasedAuthTimeout = storeInitializerContent.includes('timeout: 6000, // Increased timeout for better reliability');
  
  console.log(`  ✅ Increased auth store timeout: ${hasIncreasedAuthTimeout ? 'YES' : 'NO'}`);
  
  return hasIncreasedAuthTimeout;
}

// Test 4: Check for potential race conditions
function testRaceConditionPrevention() {
  console.log('\nTest 4: Checking race condition prevention...');
  
  const authStorePath = path.join(__dirname, 'frontend/src/stores/auth.ts');
  const authStoreContent = fs.readFileSync(authStorePath, 'utf8');
  
  // Check for proper initialization state management
  const hasInitializingFlag = authStoreContent.includes('isInitializing.value');
  const hasWaitingLoop = authStoreContent.includes('while (isInitializing.value && attempts < maxAttempts)');
  const hasAttemptLimit = authStoreContent.includes('maxAttempts');
  
  console.log(`  ✅ Initialization flag usage: ${hasInitializingFlag ? 'YES' : 'NO'}`);
  console.log(`  ✅ Waiting loop for concurrent calls: ${hasWaitingLoop ? 'YES' : 'NO'}`);
  console.log(`  ✅ Attempt limit to prevent infinite loops: ${hasAttemptLimit ? 'YES' : 'NO'}`);
  
  return hasInitializingFlag && hasWaitingLoop && hasAttemptLimit;
}

// Run all tests
function runAllTests() {
  const test1 = testAuthStoreTimeouts();
  const test2 = testRouterTimeouts();
  const test3 = testStoreInitializerTimeouts();
  const test4 = testRaceConditionPrevention();
  
  const allPassed = test1 && test2 && test3 && test4;
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 Test Results Summary:');
  console.log('='.repeat(50));
  console.log(`Auth Store Timeouts: ${test1 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Router Timeouts: ${test2 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Store Initializer Timeouts: ${test3 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Race Condition Prevention: ${test4 ? '✅ PASS' : '❌ FAIL'}`);
  console.log('='.repeat(50));
  console.log(`Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎉 Auth timeout fixes have been successfully implemented!');
    console.log('\nKey improvements:');
    console.log('• Increased timeout values for better reliability');
    console.log('• Added concurrent initialization handling');
    console.log('• Improved race condition prevention');
    console.log('• Better error handling and fallback mechanisms');
  } else {
    console.log('\n⚠️  Some fixes may not have been applied correctly.');
    console.log('Please review the failed tests above.');
  }
  
  return allPassed;
}

// Run the tests
runAllTests();
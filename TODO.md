
1. When pressing and selecting tags in the tags-container in the sidebar, it should only show the selected tags and not all tags in the note-list

2. The sidebar need to have a vertical scrolling function for smaller screens, like the note-list have.

3. Add a folder structure system below the tags section in the sidebar for better note organization - folders should complement the existing tag system and allow hierarchical organization of notes.

4. When refreshing the page at dashboard or anywhere it seems to return me to the login.
    - 4.1 Error code for this is:
    ```bash

    Store auth initialized successfully in 2007.00ms storeInitializer.ts:62:15
        Store initialization completed in 38941.00ms storeInitializer.ts:26:13
        Token refresh failed, clearing tokens auth.ts:268:19
        [vite] connecting... client:733:9
        GET
        ws://localhost:5173/?token=KVIJcFePAOO6
        NS_ERROR_WEBSOCKET_CONNECTION_REFUSED

        Firefox can’t establish a connection to the server at ws://localhost:5173/?token=KVIJcFePAOO6. client:745:27
        [vite] failed to connect to websocket (Error: WebSocket closed without opened.). client:772:13
        Uncaught (in promise) Error: WebSocket closed without opened.
            connect client:424
            connect client:422
            connect client:417
            connect client:751
            connect client:290
            connect client:374
            <anonymous> client:823
        client:424:30
        Router is ready main.ts:24:13
        Router ready, mounting app main.ts:240:13
        Request for font "Ubuntu" blocked at visibility level 2 (requires 3)
        login
        Ignoring unsupported entryTypes: layout-shift. performanceService.ts:72:32
        Performance monitoring initialized performanceService.ts:75:15
        Memory API not supported performanceService.ts:169:15
        Starting parallel store initialization... storeInitializer.ts:32:13
        Store configs: 
        Array(3) [ {…}, {…}, {…} ]
        storeInitializer.ts:33:13
        Initializing store: auth storeInitializer.ts:73:15
        Initializing store: settings storeInitializer.ts:73:15
        Initializing store: cache storeInitializer.ts:73:15
        No refresh token found, starting in guest mode auth.ts:364:15
        Auth initialization completed successfully auth.ts:387:15
        Store auth initialized successfully in 1.00ms storeInitializer.ts:106:15
        Store cache initialized successfully in 14.00ms storeInitializer.ts:106:15
        Service Worker registered: 
        ServiceWorkerRegistration { installing: null, waiting: null, active: ServiceWorker, navigationPreload: NavigationPreloadManager, scope: "http://localhost:5173/", updateViaCache: "imports", onupdatefound: null, pushManager: PushManager, cookies: CookieStoreManager }
        cacheService.ts:52:15
        🍍 "settings" store installed 🆕 pinia.js:4616:13
        Store settings initialized successfully in 52.00ms storeInitializer.ts:106:15
        Store initialization completed in 76.00ms storeInitializer.ts:56:13
        Auth store initialized successfully main.ts:65:19
        Slow interaction detected: app-init on application took 129.00ms performanceService.ts:162:15
        App initialized in 129.00ms main.ts:83:15
        Store initialization stats: 
        Object { totalStores: 3, successful: 3, failed: 0, totalTime: 52, averageTime: 22.333333333333332, failedStores: [] }
        main.ts:84:15
        Store initialization status: 
        Object { auth: true, settings: true, cache: false }
        main.ts:85:15
        [GSI_LOGGER]: Your client application uses one of the Google One Tap prompt UI status methods that may stop functioning when FedCM becomes mandatory. Refer to the migration guide to update your code accordingly and opt-in to FedCM to test your changes. Learn more: https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#display_moment and https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#skipped_moment client:74:460
        XHRGET
        https://accounts.google.com/gsi/status?client_id=************-uvo9q25h50b8no2aot0d50io10p27h2a.apps.googleusercontent.com&cas=c6DNQNQCP4RazzhNZ5IZeoI3tIZjI3oFrt8saAvP/uo&is_itp=true
        [HTTP/1.1 403  0ms]

        [GSI_LOGGER]: The given origin is not allowed for the given client ID. client:75:89
        XHRGET
        https://accounts.google.com/gsi/status?client_id=************-uvo9q25h50b8no2aot0d50io10p27h2a.apps.googleusercontent.com&cas=c6DNQNQCP4RazzhNZ5IZeoI3tIZjI3oFrt8saAvP/uo&is_itp=true
        [HTTP/1.1 403  0ms]

        [GSI_LOGGER]: The given origin is not allowed for the given client ID. client:75:89

        ```
​

5. When going from the Group page or Admin page back to the main dashboard (entering the root url http://localhost:5173/ ), it returns you to the login screen, this need to be fixed.
    - 5.1 Error code for this is:
        ```bash

            [vite] connecting... client:733:9
            Router guard: Initializing auth for navigation to /dashboard index.ts:209:15
            GET
            ws://localhost:5173/?token=KVIJcFePAOO6
            NS_ERROR_WEBSOCKET_CONNECTION_REFUSED

            Firefox can’t establish a connection to the server at ws://localhost:5173/?token=KVIJcFePAOO6. client:745:27
            [vite] failed to connect to websocket (Error: WebSocket closed without opened.). client:772:13
            Uncaught (in promise) Error: WebSocket closed without opened.
                connect client:424
                connect client:422
                connect client:417
                connect client:751
                connect client:290
                connect client:374
                <anonymous> client:823
            client:424:30
            Auth initialization failed in router guard, continuing: Error: Auth init timeout in router
                <anonymous> index.ts:215
                setTimeout handler* index.ts:215
                <anonymous> index.ts:214
                guardReturn vue-router.mjs:2322
                runWithContext vue-router.mjs:2289
                guardToPromiseFn vue-router.mjs:2322
                guardToPromiseFn vue-router.mjs:2292
                runWithContext runtime-core.esm-bundler.js:4024
                runWithContext vue-router.mjs:3594
                runGuardQueue vue-router.js:2871
                promise callback*createRouter/runGuardQueue/< vue-router.js:2871
                runGuardQueue vue-router.js:2871
                navigate vue-router.mjs:3626
                promise callback*navigate vue-router.mjs:3619
                pushWithRedirect vue-router.mjs:3534
                pushWithRedirect vue-router.mjs:3503
                push vue-router.mjs:3440
                install vue-router.js:2836
                use runtime-core.esm-bundler.js:3887
                <anonymous> main.ts:20
            index.ts:219:17
            Router is ready main.ts:24:13
            Router ready, mounting app main.ts:240:13
            Request for font "Ubuntu" blocked at visibility level 2 (requires 3)
            login
            Ignoring unsupported entryTypes: layout-shift. performanceService.ts:72:32
            Performance monitoring initialized performanceService.ts:75:15
            Memory API not supported performanceService.ts:169:15
            Starting parallel store initialization... storeInitializer.ts:32:13
            Store configs: 
            Array(3) [ {…}, {…}, {…} ]
            storeInitializer.ts:33:13
            Initializing store: auth storeInitializer.ts:73:15
            Initializing store: settings storeInitializer.ts:73:15
            Initializing store: cache storeInitializer.ts:73:15
            No refresh token found, starting in guest mode auth.ts:364:15
            Auth initialization completed successfully auth.ts:387:15
            Store auth initialized successfully in 1.00ms storeInitializer.ts:106:15
            Store cache initialized successfully in 419.00ms storeInitializer.ts:106:15
            Service Worker registered: 
            ServiceWorkerRegistration { installing: null, waiting: null, active: ServiceWorker, navigationPreload: NavigationPreloadManager, scope: "http://localhost:5173/", updateViaCache: "imports", onupdatefound: null, pushManager: PushManager, cookies: CookieStoreManager }
            cacheService.ts:52:15
            🍍 "settings" store installed 🆕 pinia.js:4616:13
            Store settings initialized successfully in 433.00ms storeInitializer.ts:106:15
            Store initialization completed in 468.00ms storeInitializer.ts:56:13
            Auth store initialized successfully main.ts:65:19
            Slow interaction detected: app-init on application took 2009.00ms performanceService.ts:162:15
            App initialized in 2009.00ms main.ts:83:15
            Store initialization stats: 
            Object { totalStores: 3, successful: 3, failed: 0, totalTime: 433, averageTime: 284.3333333333333, failedStores: [] }
            main.ts:84:15
            Store initialization status: 
            Object { auth: true, settings: true, cache: false }
            main.ts:85:15
            [GSI_LOGGER]: Missing required parameter: client_id. client:75:89
            [GSI_LOGGER]: Your client application uses one of the Google One Tap prompt UI status methods that may stop functioning when FedCM becomes mandatory. Refer to the migration guide to update your code accordingly and opt-in to FedCM to test your changes. Learn more: https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#display_moment and https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#skipped_moment client:74:460
            XHRGET
            https://accounts.google.com/gsi/status?client_id=************-uvo9q25h50b8no2aot0d50io10p27h2a.apps.googleusercontent.com&cas=k3fC72CMNEuvMr6SMvJa4HObCTvSoAs6r0ucarw+UrY&is_itp=true
            [HTTP/1.1 403  0ms]

            [GSI_LOGGER]: The given origin is not allowed for the given client ID. client:75:89
            Google One Tap not displayed: unregistered_origin GoogleSignInButton.vue:100:17
            Auth initialized successfully with existing tokens auth.ts:356:19
            First Input Delay: 13ms
        First Contentful Paint: 1.00ms

        ```	
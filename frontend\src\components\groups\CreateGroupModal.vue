<template>
  <div class="modal is-active">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">Create New Group</p>
        <button class="delete" @click="$emit('close')"></button>
      </header>
      
      <section class="modal-card-body">
        <form @submit.prevent="handleSubmit">
          <!-- Group Name -->
          <div class="field">
            <label class="label">Group Name *</label>
            <div class="control">
              <input
                v-model="form.name"
                class="input"
                :class="{ 'is-danger': errors.name }"
                type="text"
                placeholder="Enter group name"
                maxlength="100"
                required
              />
            </div>
            <p v-if="errors.name" class="help is-danger">{{ errors.name }}</p>
          </div>

          <!-- Description -->
          <div class="field">
            <label class="label">Description</label>
            <div class="control">
              <textarea
                v-model="form.description"
                class="textarea"
                :class="{ 'is-danger': errors.description }"
                placeholder="Describe your group (optional)"
                maxlength="500"
                rows="3"
              ></textarea>
            </div>
            <p v-if="errors.description" class="help is-danger">{{ errors.description }}</p>
          </div>

          <!-- Settings -->
          <div class="field">
            <label class="label">Group Settings</label>
            
            <!-- Allow Member Invites -->
            <div class="field">
              <div class="control">
                <label class="checkbox">
                  <input
                    v-model="form.settings.allowMemberInvites"
                    type="checkbox"
                  />
                  Allow members to invite others
                </label>
              </div>
              <p class="help">When enabled, editors and viewers can invite new members to the group.</p>
            </div>

            <!-- Default Note Permissions -->
            <div class="field">
              <label class="label">Default Note Permissions</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="form.settings.defaultNotePermissions">
                    <option value="view">View Only</option>
                    <option value="edit">View and Edit</option>
                  </select>
                </div>
              </div>
              <p class="help">Default permission level for new members when accessing group notes.</p>
            </div>

            <!-- Max Members -->
            <div class="field">
              <label class="label">Maximum Members</label>
              <div class="control">
                <input
                  v-model.number="form.settings.maxMembers"
                  class="input"
                  :class="{ 'is-danger': errors.maxMembers }"
                  type="number"
                  min="1"
                  max="1000"
                  placeholder="50"
                />
              </div>
              <p v-if="errors.maxMembers" class="help is-danger">{{ errors.maxMembers }}</p>
              <p class="help">Maximum number of members allowed in this group (1-1000).</p>
            </div>

            <!-- Require Approval -->
            <div class="field">
              <div class="control">
                <label class="checkbox">
                  <input
                    v-model="form.settings.requireApprovalForJoin"
                    type="checkbox"
                  />
                  Require approval for new members
                </label>
              </div>
              <p class="help">When enabled, new member requests must be approved by an admin.</p>
            </div>
          </div>

          <!-- Error Display -->
          <div v-if="submitError" class="notification is-danger">
            {{ submitError }}
          </div>
        </form>
      </section>
      
      <footer class="modal-card-foot">
        <button 
          class="button is-primary"
          :class="{ 'is-loading': loading }"
          :disabled="loading || !isFormValid"
          @click="handleSubmit"
        >
          Create Group
        </button>
        <button class="button" @click="$emit('close')">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import { useGroupsStore } from '../../stores/groups';
import type { CreateGroupData, GroupWithMembers } from '../../types/group';

// Emits
const emit = defineEmits<{
  close: [];
  created: [group: GroupWithMembers];
}>();

const groupsStore = useGroupsStore();

// Form data
const form = reactive<CreateGroupData & { settings: any }>({
  name: '',
  description: '',
  settings: {
    allowMemberInvites: true,
    defaultNotePermissions: 'view',
    requireApprovalForJoin: false,
    maxMembers: 50
  }
});

// Form state
const loading = ref(false);
const submitError = ref<string | null>(null);
const errors = reactive({
  name: '',
  description: '',
  maxMembers: ''
});

// Computed
const isFormValid = computed(() => {
  return form.name.trim().length >= 3 && 
         form.name.trim().length <= 100 &&
         (!form.description || form.description.length <= 500) &&
         form.settings.maxMembers >= 1 && 
         form.settings.maxMembers <= 1000;
});

// Methods
const validateForm = (): boolean => {
  // Reset errors
  errors.name = '';
  errors.description = '';
  errors.maxMembers = '';

  let isValid = true;

  // Validate name
  if (!form.name.trim()) {
    errors.name = 'Group name is required';
    isValid = false;
  } else if (form.name.trim().length < 3) {
    errors.name = 'Group name must be at least 3 characters';
    isValid = false;
  } else if (form.name.trim().length > 100) {
    errors.name = 'Group name must be less than 100 characters';
    isValid = false;
  }

  // Validate description
  if (form.description && form.description.length > 500) {
    errors.description = 'Description must be less than 500 characters';
    isValid = false;
  }

  // Validate max members
  if (form.settings.maxMembers < 1) {
    errors.maxMembers = 'Maximum members must be at least 1';
    isValid = false;
  } else if (form.settings.maxMembers > 1000) {
    errors.maxMembers = 'Maximum members cannot exceed 1000';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    loading.value = true;
    submitError.value = null;

    const groupData: CreateGroupData = {
      name: form.name.trim(),
      description: form.description?.trim() || undefined,
      settings: form.settings
    };

    const newGroup = await groupsStore.createGroup(groupData);
    emit('created', newGroup);
  } catch (error: any) {
    console.error('Error creating group:', error);
    submitError.value = error.message || 'Failed to create group';
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.modal-card {
  width: 90%;
  max-width: 600px;
}

.help {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}
</style>
import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue'

export interface CoreWebVitalsMetrics {
  fcp: number // First Contentful Paint
  lcp: number // Largest Contentful Paint
  tti: number // Time to Interactive
  cls: number // Cumulative Layout Shift
  fid: number // First Input Delay
}

export interface InitializationMetrics {
  startTime: number
  endTime: number
  duration: number
  storeInitTime: number
  domReadyTime: number
}

export interface PerformanceBudgets {
  fcp: number
  lcp: number
  tti: number
  cls: number
  fid: number
  initTime: number
}

const DEFAULT_BUDGETS: PerformanceBudgets = {
  fcp: 1000,    // 1 second
  lcp: 1500,    // 1.5 seconds
  tti: 2000,    // 2 seconds
  cls: 0.05,    // 5% layout shift
  fid: 100,     // 100ms
  initTime: 600 // 600ms initialization
}

export function usePerformance(budgets: Partial<PerformanceBudgets> = {}) {
  const performanceBudgets = { ...DEFAULT_BUDGETS, ...budgets }
  const metrics = ref<CoreWebVitalsMetrics>({
    fcp: 0,
    lcp: 0,
    tti: 0,
    cls: 0,
    fid: 0
  })

  const initMetrics = ref<InitializationMetrics>({
    startTime: 0,
    endTime: 0,
    duration: 0,
    storeInitTime: 0,
    domReadyTime: 0
  })

  const isMonitoring = ref(false)
  const budgetViolations = ref<string[]>([])

  let performanceObserver: PerformanceObserver | null = null
  let initStartTime = 0

  // Core Web Vitals measurement functions
  const measureFCP = (): number => {
    const paintEntries = performance.getEntriesByType('paint')
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    return fcpEntry ? fcpEntry.startTime : 0
  }

  const measureLCP = (): number => {
    const lcpEntries = performance.getEntriesByType('largest-contentful-paint') as any[]
    return lcpEntries.length > 0 ? lcpEntries[lcpEntries.length - 1].startTime : 0
  }

  const measureTTI = (): number => {
    const navigation = performance.getEntriesByType('navigation')[0] as any
    return navigation ? navigation.domInteractive - navigation.fetchStart : 0
  }

  const measureCLS = (): number => {
    const clsEntries = performance.getEntriesByType('layout-shift') as any[]
    let clsValue = 0

    for (const entry of clsEntries) {
      if (!entry.hadRecentInput) {
        clsValue += entry.value
      }
    }

    return clsValue
  }

  const measureFID = (): number => {
    const fidEntries = performance.getEntriesByType('first-input') as any[]
    if (fidEntries.length === 0) return 0

    const fid = fidEntries[0]
    return fid.processingStart - fid.startTime
  }

  // Initialize performance monitoring with comprehensive error handling
  const startMonitoring = () => {
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported - performance monitoring disabled')
      return false
    }

    try {
      performanceObserver = new PerformanceObserver((list) => {
        try {
          for (const entry of list.getEntries()) {
            handlePerformanceEntry(entry)
          }
        } catch (entryError) {
          console.warn('Error processing performance entry:', entryError)
          // Continue monitoring despite individual entry errors
        }
      })

      // Try to observe different types of performance entries with fallback
      const entryTypes = ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift', 'navigation']
      let observedTypes: string[] = []

      // Try each entry type individually to handle partial support
      for (const entryType of entryTypes) {
        try {
          // Test if this entry type is supported
          const testObserver = new PerformanceObserver(() => { })
          testObserver.observe({ entryTypes: [entryType] })
          testObserver.disconnect()

          observedTypes.push(entryType)
        } catch (typeError) {
          console.warn(`Performance entry type '${entryType}' not supported:`, typeError)
        }
      }

      if (observedTypes.length > 0) {
        performanceObserver.observe({ entryTypes: observedTypes })
        isMonitoring.value = true
        console.log(`Performance monitoring started with entry types: ${observedTypes.join(', ')}`)
        return true
      } else {
        console.warn('No supported performance entry types found')
        return false
      }

    } catch (error) {
      console.error('Failed to start performance monitoring:', error)
      isMonitoring.value = false

      // Dispatch event for error tracking
      window.dispatchEvent(new CustomEvent('performance-observer-failed', {
        detail: { error: error instanceof Error ? error.message : String(error) }
      }))

      return false
    }
  }

  const handlePerformanceEntry = (entry: PerformanceEntry) => {
    try {
      switch (entry.entryType) {
        case 'paint':
          if (entry.name === 'first-contentful-paint') {
            metrics.value.fcp = entry.startTime
            checkBudget('fcp', entry.startTime)
          }
          break
        case 'largest-contentful-paint':
          metrics.value.lcp = (entry as any).startTime
          checkBudget('lcp', (entry as any).startTime)
          break
        case 'first-input':
          const fid = (entry as any).processingStart - entry.startTime
          metrics.value.fid = fid
          checkBudget('fid', fid)
          break
        case 'layout-shift':
          if (!(entry as any).hadRecentInput) {
            metrics.value.cls += (entry as any).value
            checkBudget('cls', metrics.value.cls)
          }
          break
        case 'navigation':
          const tti = (entry as any).domInteractive - (entry as any).fetchStart
          metrics.value.tti = tti
          checkBudget('tti', tti)
          break
        default:
          // Ignore unknown entry types
          break
      }
    } catch (error) {
      console.warn(`Error handling performance entry of type '${entry.entryType}':`, error)
      // Continue monitoring despite individual entry processing errors
    }
  }

  const checkBudget = (metric: keyof PerformanceBudgets, value: number) => {
    try {
      const budget = performanceBudgets[metric]
      if (typeof value === 'number' && !isNaN(value) && value > budget) {
        const violation = `${metric.toUpperCase()}: ${value.toFixed(2)} > ${budget} (budget exceeded)`
        if (!budgetViolations.value.includes(violation)) {
          budgetViolations.value.push(violation)
          console.warn(`Performance budget violation: ${violation}`)

          // Dispatch event for budget violations
          window.dispatchEvent(new CustomEvent('performance-budget-violation', {
            detail: { metric, value, budget, violation }
          }))
        }
      }
    } catch (error) {
      console.warn(`Error checking budget for metric '${metric}':`, error)
      // Don't let budget checking errors stop performance monitoring
    }
  }

  // Initialization tracking with error handling
  const markInitStart = () => {
    try {
      initStartTime = performance.now()
      performance.mark('app-init-start')
      initMetrics.value.startTime = initStartTime
    } catch (error) {
      console.warn('Failed to mark init start:', error)
      // Use fallback timing
      initStartTime = Date.now()
      initMetrics.value.startTime = initStartTime
    }
  }

  const markStoreInitComplete = () => {
    try {
      performance.mark('store-init-complete')
      const storeTime = performance.now() - initStartTime
      initMetrics.value.storeInitTime = storeTime
    } catch (error) {
      console.warn('Failed to mark store init complete:', error)
      // Use fallback timing
      const storeTime = Date.now() - initStartTime
      initMetrics.value.storeInitTime = storeTime
    }
  }

  const markDOMReady = () => {
    try {
      performance.mark('dom-ready')
      const domTime = performance.now() - initStartTime
      initMetrics.value.domReadyTime = domTime
    } catch (error) {
      console.warn('Failed to mark DOM ready:', error)
      // Use fallback timing
      const domTime = Date.now() - initStartTime
      initMetrics.value.domReadyTime = domTime
    }
  }

  const markInitEnd = () => {
    try {
      const endTime = performance.now()
      performance.mark('app-init-end')

      initMetrics.value.endTime = endTime
      initMetrics.value.duration = endTime - initStartTime

      checkBudget('initTime', initMetrics.value.duration)

      // Save metrics to localStorage for build-time analysis
      saveMetricsToStorage()
    } catch (error) {
      console.warn('Failed to mark init end:', error)
      // Use fallback timing and still save metrics
      const endTime = Date.now()
      initMetrics.value.endTime = endTime
      initMetrics.value.duration = endTime - initStartTime

      try {
        checkBudget('initTime', initMetrics.value.duration)
        saveMetricsToStorage()
      } catch (fallbackError) {
        console.warn('Failed to process metrics in fallback mode:', fallbackError)
      }
    }
  }

  const saveMetricsToStorage = () => {
    try {
      const allMetrics = {
        timestamp: Date.now(),
        initialization: initMetrics.value,
        coreWebVitals: metrics.value,
        budgetViolations: budgetViolations.value,
        buildInfo: {
          version: import.meta.env.VITE_APP_VERSION || '1.0.0',
          environment: import.meta.env.MODE,
          buildTime: Date.now()
        }
      }

      // Use the performance export utility with error handling
      import('../utils/performanceExport')
        .then(({ exportPerformanceMetrics }) => {
          try {
            exportPerformanceMetrics(allMetrics)
          } catch (exportError) {
            console.warn('Failed to export performance metrics:', exportError)
            // Fallback to localStorage
            localStorage.setItem('performance-metrics', JSON.stringify(allMetrics))
          }
        })
        .catch(error => {
          console.warn('Failed to import performance export utility:', error)
          // Fallback to localStorage only
          try {
            localStorage.setItem('performance-metrics', JSON.stringify(allMetrics))
          } catch (storageError) {
            console.warn('Failed to save metrics to localStorage:', storageError)
            // Performance metrics saving failed completely - not critical for app function
          }
        })

    } catch (error) {
      console.warn('Failed to prepare performance metrics for saving:', error)
      // Don't let metrics saving errors affect app functionality
    }
  }

  // Get current metrics
  const getCurrentMetrics = (): CoreWebVitalsMetrics => {
    return {
      fcp: measureFCP(),
      lcp: measureLCP(),
      tti: measureTTI(),
      cls: measureCLS(),
      fid: measureFID()
    }
  }

  // Stop monitoring with error handling
  const stopMonitoring = () => {
    try {
      if (performanceObserver) {
        performanceObserver.disconnect()
        performanceObserver = null
      }
      isMonitoring.value = false
      console.log('Performance monitoring stopped')
    } catch (error) {
      console.warn('Error stopping performance monitoring:', error)
      // Force cleanup
      performanceObserver = null
      isMonitoring.value = false
    }
  }

  // Real-time performance monitoring with console logging
  const logPerformanceMetric = (metric: string, value: number, budget: number, unit: string = 'ms') => {
    const status = value <= budget ? '✅ PASS' : '❌ FAIL'
    const percentage = ((value / budget) * 100).toFixed(1)

    console.log(`🚀 Performance: ${metric} = ${value.toFixed(2)}${unit} (${percentage}% of ${budget}${unit} budget) ${status}`)

    if (value > budget) {
      console.warn(`⚠️  Performance Budget Violation: ${metric} exceeded budget by ${(value - budget).toFixed(2)}${unit}`)
    }
  }

  // Enhanced real-time monitoring with error handling
  const enableRealTimeMonitoring = () => {
    try {
      if (!isMonitoring.value) {
        const started = startMonitoring()
        if (!started) {
          console.warn('Real-time monitoring could not be enabled - performance observer failed')
          return false
        }
      }

      console.log('🔍 Real-time performance monitoring enabled')
      return true

    } catch (error) {
      console.warn('Failed to enable real-time performance monitoring:', error)
      return false
    }
  }

  // Performance metrics collection and reporting
  const collectAndReportMetrics = () => {
    const currentMetrics = getCurrentMetrics()

    console.group('📊 Performance Metrics Report')
    console.log('Core Web Vitals:')
    logPerformanceMetric('FCP', currentMetrics.fcp, performanceBudgets.fcp)
    logPerformanceMetric('LCP', currentMetrics.lcp, performanceBudgets.lcp)
    logPerformanceMetric('TTI', currentMetrics.tti, performanceBudgets.tti)
    logPerformanceMetric('CLS', currentMetrics.cls, performanceBudgets.cls, '')
    logPerformanceMetric('FID', currentMetrics.fid, performanceBudgets.fid)

    if (initMetrics.value.duration > 0) {
      console.log('\nInitialization Metrics:')
      logPerformanceMetric('App Initialization', initMetrics.value.duration, performanceBudgets.initTime)
      logPerformanceMetric('Store Initialization', initMetrics.value.storeInitTime, 300)
      logPerformanceMetric('DOM Ready', initMetrics.value.domReadyTime, 200)
    }

    if (budgetViolations.value.length > 0) {
      console.warn('\n⚠️  Budget Violations:')
      budgetViolations.value.forEach(violation => console.warn(`   • ${violation}`))
    } else {
      console.log('\n✅ All performance budgets met!')
    }

    console.groupEnd()

    return currentMetrics
  }

  // Generate performance report
  const generateReport = () => {
    const currentMetrics = getCurrentMetrics()
    const violations = budgetViolations.value

    return {
      coreWebVitals: currentMetrics,
      initialization: initMetrics.value,
      budgets: performanceBudgets,
      violations,
      summary: {
        fcpStatus: currentMetrics.fcp <= performanceBudgets.fcp ? 'PASS' : 'FAIL',
        lcpStatus: currentMetrics.lcp <= performanceBudgets.lcp ? 'PASS' : 'FAIL',
        ttiStatus: currentMetrics.tti <= performanceBudgets.tti ? 'PASS' : 'FAIL',
        clsStatus: currentMetrics.cls <= performanceBudgets.cls ? 'PASS' : 'FAIL',
        fidStatus: currentMetrics.fid <= performanceBudgets.fid ? 'PASS' : 'FAIL',
        initStatus: initMetrics.value.duration <= performanceBudgets.initTime ? 'PASS' : 'FAIL'
      }
    }
  }

  // Lifecycle hooks - only use if in component context
  // Check if we're in a component context by checking for current instance
  const instance = getCurrentInstance()
  if (instance) {
    onMounted(() => {
      startMonitoring()
    })

    onUnmounted(() => {
      stopMonitoring()
    })
  } else {
    // Not in component context - caller needs to manually start/stop monitoring
    console.log('Performance monitoring initialized outside component context - manual lifecycle management required')
  }

  return {
    // Reactive state
    metrics,
    initMetrics,
    isMonitoring,
    budgetViolations,

    // Methods
    markInitStart,
    markStoreInitComplete,
    markDOMReady,
    markInitEnd,
    getCurrentMetrics,
    generateReport,
    startMonitoring,
    stopMonitoring,

    // Enhanced real-time monitoring
    enableRealTimeMonitoring,
    collectAndReportMetrics,
    logPerformanceMetric,

    // Computed
    budgets: performanceBudgets
  }
}
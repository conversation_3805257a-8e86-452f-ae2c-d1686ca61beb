import { io, Socket } from 'socket.io-client'
import { useAuthStore } from '@/stores/auth'

export interface WebSocketConfig {
  url?: string
  autoConnect?: boolean
  reconnection?: boolean
  reconnectionAttempts?: number
  reconnectionDelay?: number
}

export interface CollaborationEvent {
  noteId: string
  userId: string
  userEmail?: string
  timestamp: number
}

export interface NoteOperation extends CollaborationEvent {
  operation: {
    type: 'insert' | 'delete' | 'replace'
    position: number
    content?: string
    length?: number
  }
}

export interface CursorUpdate extends CollaborationEvent {
  position: number
  selection?: {
    start: number
    end: number
  }
}

export interface UserPresence extends CollaborationEvent {
  isTyping?: boolean
  isOnline?: boolean
}

class WebSocketService {
  private socket: Socket | null = null
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private eventListeners = new Map<string, Set<Function>>()

  constructor() {
    // Initialize with default config
    this.initialize()
  }

  private initialize(config: WebSocketConfig = {}) {
    const defaultConfig: WebSocketConfig = {
      url: import.meta.env.VITE_WEBSOCKET_URL || 'http://localhost:3001',
      autoConnect: false,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    }

    const finalConfig = { ...defaultConfig, ...config }

    if (this.socket) {
      this.disconnect()
    }

    this.socket = io(finalConfig.url!, {
      autoConnect: finalConfig.autoConnect,
      reconnection: finalConfig.reconnection,
      reconnectionAttempts: finalConfig.reconnectionAttempts,
      reconnectionDelay: finalConfig.reconnectionDelay,
      transports: ['websocket', 'polling'], // Fallback to polling if WebSocket fails
      auth: {
        token: this.getAuthToken()
      }
    })

    this.setupEventHandlers()
  }

  private getAuthToken(): string | null {
    try {
      const authStore = useAuthStore()
      return authStore.token
    } catch (error) {
      // Fallback to localStorage if store is not available
      return localStorage.getItem('auth_token')
    }
  }

  private setupEventHandlers() {
    if (!this.socket) return

    this.socket.on('connect', () => {
      console.log('WebSocket connected')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.emit('connection', { connected: true })
    })

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason)
      this.isConnected = false
      this.emit('connection', { connected: false, reason })
    })

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error)
      this.reconnectAttempts++
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('Max reconnection attempts reached')
        this.emit('connection_failed', { error, attempts: this.reconnectAttempts })
      }
    })

    // Collaboration events
    this.socket.on('user:joined', (data: CollaborationEvent) => {
      this.emit('user:joined', data)
    })

    this.socket.on('user:left', (data: CollaborationEvent) => {
      this.emit('user:left', data)
    })

    this.socket.on('note:operation', (data: NoteOperation) => {
      this.emit('note:operation', data)
    })

    this.socket.on('cursor:update', (data: CursorUpdate) => {
      this.emit('cursor:update', data)
    })

    this.socket.on('user:typing', (data: UserPresence) => {
      this.emit('user:typing', data)
    })
  }

  // Connection management
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        this.initialize({ autoConnect: true })
      }

      if (this.isConnected) {
        resolve()
        return
      }

      // Update auth token before connecting
      if (this.socket) {
        this.socket.auth = { token: this.getAuthToken() }
        this.socket.connect()
      }

      const onConnect = () => {
        this.socket?.off('connect', onConnect)
        this.socket?.off('connect_error', onError)
        resolve()
      }

      const onError = (error: Error) => {
        this.socket?.off('connect', onConnect)
        this.socket?.off('connect_error', onError)
        reject(error)
      }

      this.socket?.on('connect', onConnect)
      this.socket?.on('connect_error', onError)
    })
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    this.isConnected = false
  }

  // Note collaboration methods
  joinNote(noteId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('note:join', { noteId })
    }
  }

  leaveNote(noteId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('note:leave', { noteId })
    }
  }

  sendNoteOperation(noteId: string, operation: NoteOperation['operation']) {
    if (this.socket && this.isConnected) {
      this.socket.emit('note:edit', { noteId, operation })
    }
  }

  sendCursorUpdate(noteId: string, position: number, selection?: CursorUpdate['selection']) {
    if (this.socket && this.isConnected) {
      this.socket.emit('cursor:update', { noteId, position, selection })
    }
  }

  sendTypingStart(noteId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing:start', { noteId })
    }
  }

  sendTypingStop(noteId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing:stop', { noteId })
    }
  }

  // Event management
  on(event: string, callback: Function) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set())
    }
    this.eventListeners.get(event)!.add(callback)
  }

  off(event: string, callback?: Function) {
    if (!this.eventListeners.has(event)) return

    if (callback) {
      this.eventListeners.get(event)!.delete(callback)
    } else {
      this.eventListeners.get(event)!.clear()
    }
  }

  private emit(event: string, data: any) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event)!.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error)
        }
      })
    }
  }

  // Getters
  get connected(): boolean {
    return this.isConnected
  }

  get socketId(): string | undefined {
    return this.socket?.id
  }
}

// Export singleton instance
export const websocketService = new WebSocketService()
export default websocketService

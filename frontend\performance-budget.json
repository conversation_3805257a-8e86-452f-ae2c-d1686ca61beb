{"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "50kb", "maximumError": "100kb"}, {"type": "bundle", "name": "main", "maximumWarning": "200kb", "maximumError": "400kb"}, {"type": "bundle", "name": "styles-*", "maximumWarning": "100kb", "maximumError": "200kb"}, {"type": "bundle", "name": "theme-*", "maximumWarning": "30kb", "maximumError": "60kb"}], "thresholds": {"css": {"base": "20kb", "components": "80kb", "utilities": "30kb", "themes": "25kb", "vendor": "40kb"}, "js": {"main": "150kb", "vendor": "300kb", "chunks": "100kb"}}, "monitoring": {"enabled": true, "reportPath": "dist/performance-report.json", "failOnBudgetExceeded": false, "trackTrends": true}}
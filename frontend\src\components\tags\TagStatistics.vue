<template>
  <div class="tag-statistics">
    <div class="card">
      <div class="card-header">
        <div class="card-header-title">
          <span class="icon">
            <i class="fas fa-chart-line"></i>
          </span>
          <span>Tag Analytics</span>
        </div>
        <div class="card-header-icon">
          <div class="buttons are-small">
            <button
              class="button"
              :class="{ 'is-primary': activeTab === 'overview' }"
              @click="activeTab = 'overview'"
            >
              Overview
            </button>
            <button
              class="button"
              :class="{ 'is-primary': activeTab === 'trends' }"
              @click="activeTab = 'trends'"
            >
              Trends
            </button>
            <button
              class="button"
              :class="{ 'is-primary': activeTab === 'usage' }"
              @click="activeTab = 'usage'"
            >
              Usage
            </button>
          </div>
        </div>
      </div>

      <div class="card-content">
        <!-- Overview Tab -->
        <div v-if="activeTab === 'overview'" class="overview-tab">
          <!-- Summary Stats -->
          <div class="stats-grid mb-5">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-tags"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ totalTags }}</div>
                <div class="stat-label">Total Tags</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ usedTags }}</div>
                <div class="stat-label">Used Tags</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-eye-slash"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ unusedTags }}</div>
                <div class="stat-label">Unused Tags</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-calculator"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ averageUsage.toFixed(1) }}</div>
                <div class="stat-label">Avg Usage</div>
              </div>
            </div>
          </div>

          <!-- Top Tags -->
          <div class="top-tags mb-5">
            <h4 class="title is-6 mb-3">Most Used Tags</h4>
            <div class="top-tags-list">
              <div
                v-for="(tagData, index) in topTags"
                :key="tagData.tag.id"
                class="top-tag-item"
              >
                <div class="top-tag-rank">{{ index + 1 }}</div>
                <div class="top-tag-content">
                  <div class="top-tag-name">{{ tagData.tag.name }}</div>
                  <div class="top-tag-usage">{{ tagData.usage }} notes</div>
                </div>
                <div class="top-tag-bar">
                  <div
                    class="top-tag-fill"
                    :style="{ width: getUsagePercentage(tagData.usage) + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Tag Distribution -->
          <div class="tag-distribution">
            <h4 class="title is-6 mb-3">Usage Distribution</h4>
            <div class="distribution-chart">
              <div
                v-for="bucket in usageDistribution"
                :key="bucket.label"
                class="distribution-bar"
              >
                <div class="distribution-label">{{ bucket.label }}</div>
                <div class="distribution-bar-container">
                  <div
                    class="distribution-bar-fill"
                    :style="{ width: getDistributionPercentage(bucket.count) + '%' }"
                  ></div>
                </div>
                <div class="distribution-count">{{ bucket.count }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Trends Tab -->
        <div v-if="activeTab === 'trends'" class="trends-tab">
          <!-- Recent Activity -->
          <div class="recent-activity mb-5">
            <h4 class="title is-6 mb-3">Recent Tag Activity</h4>
            <div class="activity-list">
              <div
                v-for="activity in recentActivity"
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon">
                  <i :class="getActivityIcon(activity.type)"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-description">
                    {{ getActivityDescription(activity) }}
                  </div>
                  <div class="activity-time">
                    {{ formatRelativeTime(activity.timestamp) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Growth Chart -->
          <div class="growth-chart">
            <h4 class="title is-6 mb-3">Tag Growth Over Time</h4>
            <div class="chart-placeholder">
              <p class="has-text-grey">Chart visualization would go here</p>
              <p class="is-size-7 has-text-grey">
                Shows tag creation and usage trends over the last 30 days
              </p>
            </div>
          </div>
        </div>

        <!-- Usage Tab -->
        <div v-if="activeTab === 'usage'" class="usage-tab">
          <!-- Usage Patterns -->
          <div class="usage-patterns mb-5">
            <h4 class="title is-6 mb-3">Usage Patterns</h4>
            <div class="pattern-grid">
              <div class="pattern-card">
                <div class="pattern-header">
                  <span class="icon">
                    <i class="fas fa-fire"></i>
                  </span>
                  <span>Hot Tags</span>
                </div>
                <div class="pattern-content">
                  <div class="tags">
                    <span
                      v-for="tag in hotTags"
                      :key="tag.id"
                      class="tag is-danger"
                      @click="selectTag(tag)"
                    >
                      {{ tag.name }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="pattern-card">
                <div class="pattern-header">
                  <span class="icon">
                    <i class="fas fa-snowflake"></i>
                  </span>
                  <span>Cold Tags</span>
                </div>
                <div class="pattern-content">
                  <div class="tags">
                    <span
                      v-for="tag in coldTags"
                      :key="tag.id"
                      class="tag is-light"
                      @click="selectTag(tag)"
                    >
                      {{ tag.name }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="pattern-card">
                <div class="pattern-header">
                  <span class="icon">
                    <i class="fas fa-star"></i>
                  </span>
                  <span>New Tags</span>
                </div>
                <div class="pattern-content">
                  <div class="tags">
                    <span
                      v-for="tag in newTags"
                      :key="tag.id"
                      class="tag is-info"
                      @click="selectTag(tag)"
                    >
                      {{ tag.name }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="pattern-card">
                <div class="pattern-header">
                  <span class="icon">
                    <i class="fas fa-ghost"></i>
                  </span>
                  <span>Orphaned Tags</span>
                </div>
                <div class="pattern-content">
                  <div class="tags">
                    <span
                      v-for="tag in orphanedTags"
                      :key="tag.id"
                      class="tag is-warning"
                      @click="selectTag(tag)"
                    >
                      {{ tag.name }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recommendations -->
          <div class="recommendations">
            <h4 class="title is-6 mb-3">Recommendations</h4>
            <div class="recommendation-list">
              <div
                v-for="recommendation in recommendations"
                :key="recommendation.id"
                class="recommendation-item"
                :class="`is-${recommendation.type}`"
              >
                <div class="recommendation-icon">
                  <i :class="getRecommendationIcon(recommendation.type)"></i>
                </div>
                <div class="recommendation-content">
                  <div class="recommendation-title">{{ recommendation.title }}</div>
                  <div class="recommendation-description">{{ recommendation.description }}</div>
                </div>
                <div class="recommendation-action">
                  <button
                    class="button is-small"
                    :class="`is-${recommendation.type}`"
                    @click="executeRecommendation(recommendation)"
                  >
                    {{ recommendation.actionText }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useNotesStore } from '../../stores/notes'
import type { Tag } from '../../services/noteService'

interface TagData {
  tag: Tag
  usage: number
}

interface Activity {
  id: string
  type: 'created' | 'used' | 'removed'
  tagName: string
  noteTitle?: string
  timestamp: Date
}

interface Recommendation {
  id: string
  type: 'info' | 'warning' | 'success' | 'danger'
  title: string
  description: string
  actionText: string
  action: () => void
}

interface Emits {
  (e: 'tag-selected', tag: Tag): void
}

const emit = defineEmits<Emits>()

const notesStore = useNotesStore()

// Local state
const activeTab = ref<'overview' | 'trends' | 'usage'>('overview')

// Computed
const tags = computed(() => notesStore.tags)
const tagUsageCount = computed(() => notesStore.tagUsageCount)
const notes = computed(() => notesStore.notes)

const totalTags = computed(() => tags.value.length)

const usedTags = computed(() => {
  return tags.value.filter(tag => getTagUsage(tag.id) > 0).length
})

const unusedTags = computed(() => totalTags.value - usedTags.value)

const averageUsage = computed(() => {
  if (totalTags.value === 0) return 0
  const totalUsage = tags.value.reduce((sum, tag) => sum + getTagUsage(tag.id), 0)
  return totalUsage / totalTags.value
})

const maxUsage = computed(() => {
  return Math.max(...tags.value.map(tag => getTagUsage(tag.id)), 1)
})

const topTags = computed(() => {
  return tags.value
    .map(tag => ({ tag, usage: getTagUsage(tag.id) }))
    .filter(item => item.usage > 0)
    .sort((a, b) => b.usage - a.usage)
    .slice(0, 10)
})

const usageDistribution = computed(() => {
  const buckets = [
    { label: '0 uses', min: 0, max: 0, count: 0 },
    { label: '1-2 uses', min: 1, max: 2, count: 0 },
    { label: '3-5 uses', min: 3, max: 5, count: 0 },
    { label: '6-10 uses', min: 6, max: 10, count: 0 },
    { label: '11+ uses', min: 11, max: Infinity, count: 0 }
  ]

  tags.value.forEach(tag => {
    const usage = getTagUsage(tag.id)
    const bucket = buckets.find(b => usage >= b.min && usage <= b.max)
    if (bucket) bucket.count++
  })

  return buckets
})

const hotTags = computed(() => {
  const threshold = maxUsage.value * 0.7
  return tags.value
    .filter(tag => getTagUsage(tag.id) >= threshold)
    .slice(0, 5)
})

const coldTags = computed(() => {
  const threshold = Math.max(1, maxUsage.value * 0.1)
  return tags.value
    .filter(tag => {
      const usage = getTagUsage(tag.id)
      return usage > 0 && usage <= threshold
    })
    .slice(0, 5)
})

const newTags = computed(() => {
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  
  return tags.value
    .filter(tag => new Date(tag.createdAt) > thirtyDaysAgo)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5)
})

const orphanedTags = computed(() => {
  return tags.value
    .filter(tag => getTagUsage(tag.id) === 0)
    .slice(0, 5)
})

const recentActivity = computed(() => {
  // Mock recent activity data
  // In a real app, this would come from an activity log
  const activities: Activity[] = []
  
  // Add recent tag creations
  newTags.value.forEach(tag => {
    activities.push({
      id: `created-${tag.id}`,
      type: 'created',
      tagName: tag.name,
      timestamp: new Date(tag.createdAt)
    })
  })

  return activities
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, 10)
})

const recommendations = computed(() => {
  const recs: Recommendation[] = []

  // Recommend cleaning up unused tags
  if (orphanedTags.value.length > 5) {
    recs.push({
      id: 'cleanup-orphaned',
      type: 'warning',
      title: 'Clean up unused tags',
      description: `You have ${orphanedTags.value.length} tags that aren't used in any notes.`,
      actionText: 'Review Tags',
      action: () => emit('tag-selected', orphanedTags.value[0])
    })
  }

  // Recommend consolidating similar tags
  if (totalTags.value > 20) {
    recs.push({
      id: 'consolidate-tags',
      type: 'info',
      title: 'Consider consolidating tags',
      description: 'You have many tags. Consider merging similar ones for better organization.',
      actionText: 'Manage Tags',
      action: () => {}
    })
  }

  // Recommend using more tags
  const averageTagsPerNote = notes.value.length > 0 
    ? notes.value.reduce((sum, note) => sum + note.tags.length, 0) / notes.value.length 
    : 0

  if (averageTagsPerNote < 2 && notes.value.length > 5) {
    recs.push({
      id: 'use-more-tags',
      type: 'success',
      title: 'Use more tags for better organization',
      description: 'Your notes have few tags on average. Adding more tags can improve searchability.',
      actionText: 'Learn More',
      action: () => {}
    })
  }

  return recs
})

// Methods
const getTagUsage = (tagId: string): number => {
  const tag = tags.value.find(t => t.id === tagId)
  return tag ? (tagUsageCount.value.get(tag.name) || 0) : 0
}

const getUsagePercentage = (usage: number): number => {
  return maxUsage.value > 0 ? (usage / maxUsage.value) * 100 : 0
}

const getDistributionPercentage = (count: number): number => {
  return totalTags.value > 0 ? (count / totalTags.value) * 100 : 0
}

const selectTag = (tag: Tag) => {
  emit('tag-selected', tag)
}

const getActivityIcon = (type: string): string => {
  switch (type) {
    case 'created': return 'fas fa-plus-circle has-text-success'
    case 'used': return 'fas fa-tag has-text-info'
    case 'removed': return 'fas fa-minus-circle has-text-danger'
    default: return 'fas fa-circle'
  }
}

const getActivityDescription = (activity: Activity): string => {
  switch (activity.type) {
    case 'created':
      return `Tag "${activity.tagName}" was created`
    case 'used':
      return `Tag "${activity.tagName}" was added to "${activity.noteTitle}"`
    case 'removed':
      return `Tag "${activity.tagName}" was removed from "${activity.noteTitle}"`
    default:
      return 'Unknown activity'
  }
}

const getRecommendationIcon = (type: string): string => {
  switch (type) {
    case 'info': return 'fas fa-info-circle'
    case 'warning': return 'fas fa-exclamation-triangle'
    case 'success': return 'fas fa-check-circle'
    case 'danger': return 'fas fa-times-circle'
    default: return 'fas fa-lightbulb'
  }
}

const executeRecommendation = (recommendation: Recommendation) => {
  recommendation.action()
}

const formatRelativeTime = (date: Date): string => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMinutes < 60) {
    return `${diffMinutes} minutes ago`
  } else if (diffHours < 24) {
    return `${diffHours} hours ago`
  } else {
    return `${diffDays} days ago`
  }
}

// Lifecycle
onMounted(() => {
  // Load data if needed
  if (tags.value.length === 0) {
    notesStore.loadTags()
  }
  if (notes.value.length === 0) {
    notesStore.loadNotes()
  }
})
</script>

<style scoped>
.tag-statistics {
  max-width: 100%;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.stat-icon {
  font-size: 2rem;
  margin-right: 1rem;
  opacity: 0.8;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Top Tags */
.top-tags-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.top-tag-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.top-tag-item:hover {
  border-color: #3273dc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-tag-rank {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3273dc;
  color: white;
  border-radius: 50%;
  font-weight: bold;
  margin-right: 1rem;
}

.top-tag-content {
  flex: 1;
  margin-right: 1rem;
}

.top-tag-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.top-tag-usage {
  font-size: 0.9rem;
  color: #666;
}

.top-tag-bar {
  width: 100px;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.top-tag-fill {
  height: 100%;
  background: linear-gradient(90deg, #3273dc, #23d160);
  transition: width 0.3s ease;
}

/* Distribution Chart */
.distribution-chart {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.distribution-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.distribution-label {
  width: 80px;
  font-size: 0.9rem;
  color: #666;
}

.distribution-bar-container {
  flex: 1;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.distribution-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #feca57);
  transition: width 0.3s ease;
}

.distribution-count {
  width: 40px;
  text-align: right;
  font-weight: 600;
}

/* Activity List */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
}

.activity-icon {
  margin-right: 1rem;
  font-size: 1.2rem;
}

.activity-content {
  flex: 1;
}

.activity-description {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.8rem;
  color: #666;
}

/* Pattern Grid */
.pattern-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.pattern-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.pattern-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
}

.pattern-content {
  padding: 1rem;
}

.pattern-content .tags {
  margin-bottom: 0;
}

.pattern-content .tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.pattern-content .tag:hover {
  transform: scale(1.05);
}

/* Recommendations */
.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid;
}

.recommendation-item.is-info {
  background-color: #f0f7ff;
  border-left-color: #3273dc;
}

.recommendation-item.is-warning {
  background-color: #fffbf0;
  border-left-color: #ffdd57;
}

.recommendation-item.is-success {
  background-color: #f0fff4;
  border-left-color: #23d160;
}

.recommendation-item.is-danger {
  background-color: #fff5f5;
  border-left-color: #ff3860;
}

.recommendation-icon {
  margin-right: 1rem;
  font-size: 1.5rem;
}

.recommendation-content {
  flex: 1;
  margin-right: 1rem;
}

.recommendation-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.recommendation-description {
  font-size: 0.9rem;
  color: #666;
}

/* Chart Placeholder */
.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  background-color: #f8f9fa;
}

/* Responsive design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .pattern-grid {
    grid-template-columns: 1fr;
  }
  
  .top-tag-item,
  .recommendation-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .distribution-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .distribution-label {
    width: auto;
  }
}
</style>
<template>
  <div class="search-view">
    <div class="container is-fluid">
      <!-- Search Header -->
      <div class="search-header mb-5">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <h1 class="title is-3">
                <span class="icon">
                  <i class="fas fa-search"></i>
                </span>
                <span>Search Notes</span>
              </h1>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <div class="buttons">
                <button
                  class="button is-light"
                  @click="showSearchStats = !showSearchStats"
                  :class="{ 'is-primary': showSearchStats }"
                >
                  <span class="icon">
                    <i class="fas fa-chart-bar"></i>
                  </span>
                  <span>Stats</span>
                </button>
                <button
                  class="button is-light"
                  @click="showSearchHistory = !showSearchHistory"
                  :class="{ 'is-primary': showSearchHistory }"
                >
                  <span class="icon">
                    <i class="fas fa-history"></i>
                  </span>
                  <span>History</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Search Input -->
        <div class="search-input-section">
          <SearchInput
            ref="searchInputRef"
            :auto-focus="true"
            @search="handleSearch"
            @advanced-toggle="handleAdvancedToggle"
            @suggestion-select="handleSuggestionSelect"
          />
        </div>

        <!-- Advanced Search -->
        <AdvancedSearch
          :show="showAdvancedSearch"
          @close="handleAdvancedClose"
          @search="handleAdvancedSearch"
        />
      </div>

      <div class="columns">
        <!-- Main Content -->
        <div class="column" :class="sidebarVisible ? 'is-three-quarters' : 'is-full'">
          <!-- Search Results -->
          <SearchResults
            @note-select="handleNoteSelect"
            @tag-search="handleTagSearch"
            @clear-search="handleClearSearch"
            @share-note="handleShareNote"
          />
        </div>

        <!-- Sidebar -->
        <div v-if="sidebarVisible" class="column is-one-quarter">
          <!-- Search Statistics -->
          <div v-if="showSearchStats && searchStats" class="card mb-4">
            <div class="card-header">
              <div class="card-header-title">
                <span class="icon">
                  <i class="fas fa-chart-bar"></i>
                </span>
                <span>Search Statistics</span>
              </div>
            </div>
            <div class="card-content">
              <div class="content">
                <div class="field">
                  <label class="label is-small">Total Notes</label>
                  <p class="title is-4">{{ searchStats.totalNotes }}</p>
                </div>
                
                <div class="field">
                  <label class="label is-small">By Type</label>
                  <div class="tags">
                    <span class="tag is-info">
                      <span class="icon">
                        <i class="fas fa-font"></i>
                      </span>
                      <span>{{ searchStats.notesByType.richtext }} Rich Text</span>
                    </span>
                    <span class="tag is-success">
                      <span class="icon">
                        <i class="fab fa-markdown"></i>
                      </span>
                      <span>{{ searchStats.notesByType.markdown }} Markdown</span>
                    </span>
                    <span class="tag is-warning">
                      <span class="icon">
                        <i class="fas fa-columns"></i>
                      </span>
                      <span>{{ searchStats.notesByType.kanban }} Kanban</span>
                    </span>
                  </div>
                </div>

                <div class="field">
                  <label class="label is-small">Total Tags</label>
                  <p class="title is-4">{{ searchStats.totalTags }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Search History -->
          <div v-if="showSearchHistory && searchHistory.length > 0" class="card mb-4">
            <div class="card-header">
              <div class="card-header-title">
                <span class="icon">
                  <i class="fas fa-history"></i>
                </span>
                <span>Search History</span>
              </div>
              <div class="card-header-icon">
                <button
                  class="button is-small is-ghost"
                  @click="clearSearchHistory"
                  title="Clear History"
                >
                  <span class="icon">
                    <i class="fas fa-trash"></i>
                  </span>
                </button>
              </div>
            </div>
            <div class="card-content">
              <div class="content">
                <div
                  v-for="(query, index) in searchHistory.slice(0, 10)"
                  :key="`history-${index}`"
                  class="history-item"
                  @click="searchFromHistory(query)"
                >
                  <span class="icon is-small">
                    <i class="fas fa-search"></i>
                  </span>
                  <span>{{ query }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Filters -->
          <div class="card mb-4">
            <div class="card-header">
              <div class="card-header-title">
                <span class="icon">
                  <i class="fas fa-filter"></i>
                </span>
                <span>Quick Filters</span>
              </div>
            </div>
            <div class="card-content">
              <div class="content">
                <div class="field">
                  <label class="label is-small">By Type</label>
                  <div class="buttons are-small">
                    <button
                      class="button is-light"
                      @click="quickSearchByType('richtext')"
                    >
                      <span class="icon">
                        <i class="fas fa-font"></i>
                      </span>
                      <span>Rich Text</span>
                    </button>
                    <button
                      class="button is-light"
                      @click="quickSearchByType('markdown')"
                    >
                      <span class="icon">
                        <i class="fab fa-markdown"></i>
                      </span>
                      <span>Markdown</span>
                    </button>
                    <button
                      class="button is-light"
                      @click="quickSearchByType('kanban')"
                    >
                      <span class="icon">
                        <i class="fas fa-columns"></i>
                      </span>
                      <span>Kanban</span>
                    </button>
                  </div>
                </div>

                <div class="field">
                  <label class="label is-small">By Date</label>
                  <div class="buttons are-small">
                    <button
                      class="button is-light"
                      @click="quickSearchByDate('today')"
                    >
                      Today
                    </button>
                    <button
                      class="button is-light"
                      @click="quickSearchByDate('week')"
                    >
                      This Week
                    </button>
                    <button
                      class="button is-light"
                      @click="quickSearchByDate('month')"
                    >
                      This Month
                    </button>
                  </div>
                </div>

                <div class="field">
                  <label class="label is-small">Special</label>
                  <div class="buttons are-small">
                    <button
                      class="button is-light"
                      @click="quickSearchArchived"
                    >
                      <span class="icon">
                        <i class="fas fa-archive"></i>
                      </span>
                      <span>Archived</span>
                    </button>
                    <button
                      class="button is-light"
                      @click="quickSearchRecent"
                    >
                      <span class="icon">
                        <i class="fas fa-clock"></i>
                      </span>
                      <span>Recent</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Popular Tags -->
          <div v-if="popularTags.length > 0" class="card">
            <div class="card-header">
              <div class="card-header-title">
                <span class="icon">
                  <i class="fas fa-tags"></i>
                </span>
                <span>Popular Tags</span>
              </div>
            </div>
            <div class="card-content">
              <div class="content">
                <div class="tags">
                  <button
                    v-for="tag in popularTags"
                    :key="tag.name"
                    class="tag is-light"
                    @click="handleTagSearch(tag.name)"
                  >
                    {{ tag.name }}
                    <span class="tag is-primary is-small ml-1">{{ tag.count }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Action Button (Mobile) -->
    <button
      v-if="isMobile"
      class="button is-primary is-large floating-action-button"
      @click="toggleSidebar"
    >
      <span class="icon">
        <i :class="sidebarVisible ? 'fas fa-times' : 'fas fa-filter'"></i>
      </span>
    </button>

    <!-- Share Modal -->
    <ShareModal 
      :is-open="isShareModalOpen"
      :note="noteToShare"
      @close="closeShareModal"
      @share-created="handleShareCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useSearchStore } from '../stores/search'
import { useNotesStore } from '../stores/notes'
import SearchInput from '../components/search/SearchInput.vue'
import SearchResults from '../components/search/SearchResults.vue'
import AdvancedSearch from '../components/search/AdvancedSearch.vue'
import ShareModal from '../components/sharing/ShareModal.vue'
import type { Note } from '../services/noteService'
import type { SearchFilters } from '../services/searchService'

const router = useRouter()
const route = useRoute()
const searchStore = useSearchStore()
const notesStore = useNotesStore()

// Local state
const searchInputRef = ref()
const showSearchStats = ref(false)
const showSearchHistory = ref(false)
const sidebarVisible = ref(true)
const isMobile = ref(false)
const isShareModalOpen = ref(false)
const noteToShare = ref<Note | null>(null)

// Computed
const showAdvancedSearch = computed(() => searchStore.showAdvancedSearch)
const searchStats = computed(() => searchStore.searchStats)
const searchHistory = computed(() => searchStore.searchHistory)

const popularTags = computed(() => {
  const tagUsage = notesStore.tagUsageCount
  return Array.from(tagUsage.entries())
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10)
})

// Methods
const handleSearch = (query: string) => {
  // Update URL with search query
  if (query) {
    router.push({ query: { q: query } })
  } else {
    router.push({ query: {} })
  }
}

const handleAdvancedToggle = (show: boolean) => {
  // Advanced search toggle is handled by the store
}

const handleAdvancedClose = () => {
  searchStore.toggleAdvancedSearch()
}

const handleAdvancedSearch = (query: string, filters: SearchFilters) => {
  // Update URL with advanced search parameters
  const queryParams: any = {}
  
  if (query) queryParams.q = query
  if (filters.noteType) queryParams.type = filters.noteType
  if (filters.tags && filters.tags.length > 0) queryParams.tags = filters.tags.join(',')
  if (filters.dateFrom) queryParams.from = filters.dateFrom
  if (filters.dateTo) queryParams.to = filters.dateTo
  if (filters.archived) queryParams.archived = 'true'

  router.push({ query: queryParams })
}

const handleSuggestionSelect = (suggestion: string) => {
  // Suggestion selection is handled by SearchInput component
}

const handleNoteSelect = (note: Note) => {
  // Note selection is handled by SearchResults component
}

const handleShareNote = (note: Note) => {
  noteToShare.value = note
  isShareModalOpen.value = true
}

const closeShareModal = () => {
  isShareModalOpen.value = false
  noteToShare.value = null
}

const handleShareCreated = (share: any) => {
  console.log('Share created:', share)
  // You might want to show a success notification here
}

const handleTagSearch = (tagName: string) => {
  searchStore.searchByTag(tagName)
  router.push({ query: { tags: tagName } })
}

const handleClearSearch = () => {
  searchStore.resetSearch()
  router.push({ query: {} })
}

const searchFromHistory = (query: string) => {
  searchStore.setSearchQuery(query)
  searchStore.search(query)
  router.push({ query: { q: query } })
}

const clearSearchHistory = () => {
  searchStore.clearSearchHistory()
}

const quickSearchByType = (noteType: 'richtext' | 'markdown' | 'kanban') => {
  searchStore.searchByNoteType(noteType)
  router.push({ query: { type: noteType } })
}

const quickSearchByDate = (range: string) => {
  const now = new Date()
  let fromDate: Date

  switch (range) {
    case 'today':
      fromDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      break
    case 'week':
      fromDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case 'month':
      fromDate = new Date(now.getFullYear(), now.getMonth(), 1)
      break
    default:
      return
  }

  const dateFrom = fromDate.toISOString().split('T')[0]
  searchStore.search('', { dateFrom }, { page: 1 })
  router.push({ query: { from: dateFrom } })
}

const quickSearchArchived = () => {
  searchStore.searchArchived()
  router.push({ query: { archived: 'true' } })
}

const quickSearchRecent = () => {
  searchStore.searchRecent(7)
  const fromDate = new Date()
  fromDate.setDate(fromDate.getDate() - 7)
  router.push({ query: { from: fromDate.toISOString().split('T')[0] } })
}

const toggleSidebar = () => {
  sidebarVisible.value = !sidebarVisible.value
}

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
  if (!isMobile.value) {
    sidebarVisible.value = true
  } else {
    sidebarVisible.value = false
  }
}

const initializeFromURL = () => {
  const query = route.query
  
  if (query.q) {
    searchStore.setSearchQuery(query.q as string)
  }

  const filters: SearchFilters = {}
  if (query.type) filters.noteType = query.type as any
  if (query.tags) filters.tags = (query.tags as string).split(',')
  if (query.from) filters.dateFrom = query.from as string
  if (query.to) filters.dateTo = query.to as string
  if (query.archived) filters.archived = query.archived === 'true'

  if (Object.keys(filters).length > 0 || query.q) {
    searchStore.search(query.q as string, filters)
  }
}

// Lifecycle
onMounted(async () => {
  // Initialize search store
  await searchStore.initialize()
  
  // Load notes store for tags
  await notesStore.initialize()
  
  // Check mobile
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  // Initialize from URL parameters
  initializeFromURL()
  
  // Load search stats
  await searchStore.loadSearchStats()
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
  searchStore.cancelSearch()
})
</script>

<style scoped>
.search-view {
  min-height: 100vh;
  padding: 1rem 0;
}

.search-header {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-input-section {
  margin-top: 1rem;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background-color: #f5f5f5;
}

.floating-action-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
  border-radius: 50%;
  width: 4rem;
  height: 4rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Card styling */
.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

/* Tag styling */
.tags .tag {
  cursor: pointer;
  transition: all 0.2s;
}

.tags .tag:hover {
  background-color: #3273dc;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-view {
    padding: 0.5rem 0;
  }
  
  .search-header {
    margin: 0 0.5rem;
    padding: 1rem;
  }
  
  .container.is-fluid {
    padding: 0 0.5rem;
  }
  
  .level {
    display: block;
  }
  
  .level-left,
  .level-right {
    margin-bottom: 1rem;
  }
  
  .title.is-3 {
    font-size: 1.5rem;
  }
}

/* Animation for sidebar toggle */
.column {
  transition: all 0.3s ease;
}

/* Loading states */
.is-loading {
  pointer-events: none;
}
</style>
<template>
  <div class="admin-audit-trail">
    <div class="card">
      <header class="card-header">
        <p class="card-header-title">
          <span class="icon">
            <i class="fas fa-history"></i>
          </span>
          <span>Admin Activity Log</span>
        </p>
        <div class="card-header-icon">
          <button 
            class="button is-small" 
            @click="onRefreshClick"
            :class="{ 'is-loading': isLoading }"
          >
            <span class="icon">
              <i class="fas fa-sync-alt"></i>
            </span>
          </button>
        </div>
      </header>
      
      <!-- Filters -->
      <div class="card-content" v-if="showFilters">
        <div class="columns">
          <div class="column is-3">
            <div class="field">
              <label class="label is-small">Action Type</label>
              <div class="control">
                <div class="select is-small is-fullwidth">
                  <select v-model="filters.action" @change="() => loadAuditLogs()">
                    <option value="">All Actions</option>
                    <option value="user_status_update">User Status Update</option>
                    <option value="admin_status_update">Admin Status Update</option>
                    <option value="system_config_update">System Config Update</option>
                    <option value="maintenance_mode_toggle">Maintenance Mode</option>
                    <option value="moderation_action">Moderation Action</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label is-small">Time Range</label>
              <div class="control">
                <div class="select is-small is-fullwidth">
                  <select v-model="filters.timeRange" @change="() => loadAuditLogs()">
                    <option value="1">Last Hour</option>
                    <option value="24">Last 24 Hours</option>
                    <option value="168">Last Week</option>
                    <option value="720">Last Month</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-6">
            <div class="field">
              <label class="label is-small">Search</label>
              <div class="control">
                <input 
                  v-model="filters.search" 
                  class="input is-small" 
                  type="text" 
                  placeholder="Search by user, action, or resource..."
                  @input="debouncedSearch"
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card-content">
        <!-- Loading State -->
        <div v-if="isLoading && auditLogs.length === 0" class="has-text-centered py-4">
          <div class="is-size-5 mb-3">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p>Loading audit logs...</p>
        </div>

        <!-- Empty State -->
        <div v-else-if="auditLogs.length === 0" class="has-text-centered py-4">
          <div class="is-size-5 mb-3">
            <i class="fas fa-history"></i>
          </div>
          <p class="has-text-grey">No audit logs found</p>
        </div>

        <!-- Audit Logs -->
        <div v-else class="audit-logs">
          <div 
            v-for="log in auditLogs" 
            :key="log.id"
            class="audit-log-item"
          >
            <div class="media">
              <div class="media-left">
                <span class="icon" :class="{
                  'has-text-danger': log.action.includes('ban') || log.action.includes('suspend'),
                  'has-text-warning': log.action.includes('admin') || log.action.includes('config'),
                  'has-text-info': log.action.includes('moderation'),
                  'has-text-success': log.action.includes('activate') || log.action.includes('resolve')
                }">
                  <i class="fas" :class="{
                    'fa-user-slash': log.action.includes('ban') || log.action.includes('suspend'),
                    'fa-user-shield': log.action.includes('admin'),
                    'fa-cogs': log.action.includes('config') || log.action.includes('maintenance'),
                    'fa-flag': log.action.includes('moderation'),
                    'fa-user-check': log.action.includes('activate'),
                    'fa-edit': !getActionIcon(log.action)
                  }"></i>
                </span>
              </div>
              <div class="media-content">
                <div class="content">
                  <p>
                    <strong>{{ formatAction(log.action) }}</strong>
                    <small class="has-text-grey ml-2">{{ formatDate(log.created_at) }}</small>
                    <br>
                    <span class="is-size-7">
                      {{ formatLogDescription(log) }}
                    </span>
                    <br>
                    <small class="has-text-grey">
                      IP: {{ log.ip_address }} • 
                      Response: {{ log.response_status }} • 
                      {{ log.response_time_ms }}ms
                    </small>
                  </p>
                </div>
              </div>
              <div class="media-right">
                <button 
                  class="button is-small is-text"
                  @click="toggleLogDetails(log.id)"
                >
                  <span class="icon">
                    <i class="fas" :class="expandedLogs.has(log.id) ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                  </span>
                </button>
              </div>
            </div>
            
            <!-- Expanded Details -->
            <div v-if="expandedLogs.has(log.id)" class="log-details mt-3">
              <div class="columns is-multiline">
                <div class="column is-6">
                  <div class="field">
                    <label class="label is-small">Request Details</label>
                    <div class="content is-small">
                      <p><strong>Method:</strong> {{ log.request_method }}</p>
                      <p><strong>Path:</strong> {{ log.request_path }}</p>
                      <p><strong>User Agent:</strong> {{ log.user_agent }}</p>
                    </div>
                  </div>
                </div>
                <div class="column is-6" v-if="log.metadata">
                  <div class="field">
                    <label class="label is-small">Metadata</label>
                    <div class="content is-small">
                      <pre class="metadata-json">{{ JSON.stringify(log.metadata, null, 2) }}</pre>
                    </div>
                  </div>
                </div>
                <div class="column is-12" v-if="log.request_body">
                  <div class="field">
                    <label class="label is-small">Request Body</label>
                    <div class="content is-small">
                      <pre class="request-body">{{ formatRequestBody(log.request_body) }}</pre>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Load More -->
        <div v-if="hasMore" class="has-text-centered mt-4">
          <button 
            class="button is-small"
            @click="loadMore"
            :class="{ 'is-loading': isLoadingMore }"
          >
            Load More
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface AuditLog {
  id: string
  user_id: string
  session_id: string
  action: string
  resource_type: string
  resource_id: string
  ip_address: string
  user_agent: string
  request_method: string
  request_path: string
  request_body?: string
  response_status: number
  response_time_ms: number
  metadata?: any
  created_at: string
}

// Props
const props = defineProps<{
  showFilters?: boolean
  limit?: number
}>()

// State
const auditLogs = ref<AuditLog[]>([])
const isLoading = ref(false)
const isLoadingMore = ref(false)
const hasMore = ref(true)
const expandedLogs = ref(new Set<string>())
const filters = ref({
  action: '',
  timeRange: '24',
  search: ''
})

// Methods
const loadAuditLogs = async (reset = true) => {
  if (reset) {
    isLoading.value = true
    auditLogs.value = []
  } else {
    isLoadingMore.value = true
  }

  try {
    // Mock data - replace with actual API call
    const mockLogs: AuditLog[] = [
      {
        id: '1',
        user_id: 'admin-1',
        session_id: 'session-123',
        action: 'user_status_update',
        resource_type: 'user',
        resource_id: 'user-456',
        ip_address: '***********',
        user_agent: 'Mozilla/5.0...',
        request_method: 'PUT',
        request_path: '/api/admin/users/user-456/status',
        request_body: '{"status":"suspended","reason":"Violation of terms"}',
        response_status: 200,
        response_time_ms: 150,
        metadata: {
          targetUser: '<EMAIL>',
          oldStatus: 'active',
          newStatus: 'suspended'
        },
        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString()
      },
      {
        id: '2',
        user_id: 'admin-1',
        session_id: 'session-123',
        action: 'admin_status_update',
        resource_type: 'user',
        resource_id: 'user-789',
        ip_address: '***********',
        user_agent: 'Mozilla/5.0...',
        request_method: 'PUT',
        request_path: '/api/admin/users/user-789/admin',
        request_body: '{"isAdmin":true}',
        response_status: 200,
        response_time_ms: 95,
        metadata: {
          targetUser: '<EMAIL>',
          newAdminStatus: true
        },
        created_at: new Date(Date.now() - 60 * 60 * 1000).toISOString()
      },
      {
        id: '3',
        user_id: 'admin-1',
        session_id: 'session-124',
        action: 'system_config_update',
        resource_type: 'system',
        resource_id: 'config',
        ip_address: '***********',
        user_agent: 'Mozilla/5.0...',
        request_method: 'PUT',
        request_path: '/api/admin/system/config',
        request_body: '{"features":{"maintenance":true}}',
        response_status: 200,
        response_time_ms: 200,
        metadata: {
          configKeys: ['features']
        },
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      }
    ]

    if (reset) {
      auditLogs.value = mockLogs
    } else {
      auditLogs.value.push(...mockLogs)
    }

    hasMore.value = mockLogs.length === (props.limit || 20)
  } catch (error) {
    console.error('Failed to load audit logs:', error)
  } finally {
    isLoading.value = false
    isLoadingMore.value = false
  }
}

const loadMore = () => {
  loadAuditLogs(false)
}

const toggleLogDetails = (logId: string) => {
  if (expandedLogs.value.has(logId)) {
    expandedLogs.value.delete(logId)
  } else {
    expandedLogs.value.add(logId)
  }
}

const formatAction = (action: string) => {
  return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMins / 60)

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  
  return date.toLocaleString()
}

const formatLogDescription = (log: AuditLog) => {
  switch (log.action) {
    case 'user_status_update':
      return `Updated user status to ${log.metadata?.newStatus || 'unknown'}`
    case 'admin_status_update':
      return `${log.metadata?.newAdminStatus ? 'Granted' : 'Revoked'} admin privileges`
    case 'system_config_update':
      return `Updated system configuration: ${log.metadata?.configKeys?.join(', ') || 'unknown'}`
    case 'maintenance_mode_toggle':
      return `${log.metadata?.maintenanceEnabled ? 'Enabled' : 'Disabled'} maintenance mode`
    case 'moderation_action':
      return `Performed moderation action: ${log.metadata?.actionType || 'unknown'}`
    default:
      return `Performed ${formatAction(log.action).toLowerCase()}`
  }
}

const formatRequestBody = (body: string) => {
  try {
    return JSON.stringify(JSON.parse(body), null, 2)
  } catch {
    return body
  }
}

const getActionIcon = (action: string) => {
  const iconMap: Record<string, string> = {
    'user_status_update': 'fa-user-edit',
    'admin_status_update': 'fa-user-shield',
    'system_config_update': 'fa-cogs',
    'maintenance_mode_toggle': 'fa-tools',
    'moderation_action': 'fa-flag'
  }
  return iconMap[action]
}

// Debounced search
let searchTimeout: ReturnType<typeof setTimeout>
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    loadAuditLogs()
  }, 500)
}

const onRefreshClick = () => {
  loadAuditLogs(true)
}

onMounted(() => {
  loadAuditLogs()
})
</script>

<style scoped>
.admin-audit-trail {
  width: 100%;
}

.audit-log-item {
  padding: 1rem;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s;
}

.audit-log-item:hover {
  background-color: #fafafa;
}

.audit-log-item:last-child {
  border-bottom: none;
}

.log-details {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  border-left: 3px solid #3273dc;
}

.metadata-json,
.request-body {
  background-color: #f5f5f5;
  padding: 0.5rem;
  border-radius: 3px;
  font-size: 0.75rem;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.media-content {
  overflow: visible;
}

.content p {
  margin-bottom: 0.5rem;
}

.content p:last-child {
  margin-bottom: 0;
}
</style>
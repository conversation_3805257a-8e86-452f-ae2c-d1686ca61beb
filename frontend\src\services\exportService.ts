import { httpClient } from '../utils/http';
import { http } from '../utils/http';

export interface ExportOptions {
  format: 'pdf' | 'html' | 'markdown';
  includeMetadata?: boolean;
  customStyles?: string;
}

export interface ExportJob {
  id: string;
  userId: string;
  noteIds: string[];
  format: 'pdf' | 'html' | 'markdown';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  downloadUrl?: string;
  createdAt: string;
  completedAt?: string;
  error?: string;
}

export class ExportService {
  /**
   * Export a single note
   */
  static async exportNote(noteId: string, options: ExportOptions): Promise<Blob> {
    const response = await http.post<Blob>(`/notes/${noteId}/export`, options);
    
    if (response.error) {
      throw new Error(response.error);
    }
    
    return response.data!;
  }

  /**
   * Export multiple notes
   */
  static async exportMultipleNotes(noteIds: string[], options: ExportOptions): Promise<Blob | { jobId: string; status: string; message: string }> {
    const response = await http.post<Blob | { jobId: string; status: string; message: string }>('/notes/export', {
      noteIds,
      ...options
    });
    
    if (response.error) {
      throw new Error(response.error);
    }
    
    return response.data!;
  }

  /**
   * Get export job status
   */
  static async getExportJob(jobId: string): Promise<ExportJob> {
    const response = await httpClient.get(`/exports/${jobId}`);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data as ExportJob;
  }

  /**
   * Download export job result
   */
  static async downloadExportJob(jobId: string): Promise<Blob> {
    const response = await http.get<Blob>(`/exports/${jobId}/download`);
    
    if (response.error) {
      throw new Error(response.error);
    }
    
    return response.data!;
  }

  /**
   * Get export history
   */
  static async getExportHistory(): Promise<ExportJob[]> {
    const response = await httpClient.get('/exports');
    if (response.error) {
      throw new Error(response.error);
    }
    return (response.data as any).jobs;
  }

  /**
   * Clean up old export jobs
   */
  static async cleanupExportJobs(maxAgeHours: number = 24): Promise<void> {
    const response = await httpClient.post('/exports/cleanup', { maxAgeHours });
    if (response.error) {
      throw new Error(response.error);
    }
  }

  /**
   * Download blob as file
   */
  static downloadBlob(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  /**
   * Get appropriate filename for export
   */
  static getExportFilename(title: string, format: string, isMultiple: boolean = false): string {
    const sanitizedTitle = title.replace(/[^a-zA-Z0-9]/g, '_');
    const prefix = isMultiple ? 'notes_export' : sanitizedTitle;
    const timestamp = new Date().toISOString().split('T')[0];
    
    return `${prefix}_${timestamp}.${format}`;
  }

  /**
   * Get format display name
   */
  static getFormatDisplayName(format: string): string {
    switch (format) {
      case 'pdf':
        return 'PDF Document';
      case 'html':
        return 'HTML File';
      case 'markdown':
        return 'Markdown File';
      default:
        return format.toUpperCase();
    }
  }

  /**
   * Get format icon
   */
  static getFormatIcon(format: string): string {
    switch (format) {
      case 'pdf':
        return 'fas fa-file-pdf';
      case 'html':
        return 'fas fa-file-code';
      case 'markdown':
        return 'fab fa-markdown';
      default:
        return 'fas fa-file';
    }
  }

  /**
   * Poll export job status until completion
   */
  static async pollExportJob(
    jobId: string, 
    onProgress?: (job: ExportJob) => void,
    maxAttempts: number = 60,
    intervalMs: number = 2000
  ): Promise<ExportJob> {
    let attempts = 0;
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          const job = await this.getExportJob(jobId);
          
          if (onProgress) {
            onProgress(job);
          }
          
          if (job.status === 'completed') {
            resolve(job);
            return;
          }
          
          if (job.status === 'failed') {
            reject(new Error(job.error || 'Export job failed'));
            return;
          }
          
          if (attempts >= maxAttempts) {
            reject(new Error('Export job timed out'));
            return;
          }
          
          // Continue polling
          setTimeout(poll, intervalMs);
        } catch (error) {
          reject(error);
        }
      };
      
      poll();
    });
  }
}
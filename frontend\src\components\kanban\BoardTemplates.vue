<template>
  <div class="modal is-active">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card board-templates-modal">
      <header class="modal-card-head">
        <p class="modal-card-title">Board Templates</p>
        <button class="delete" @click="$emit('close')" aria-label="close"></button>
      </header>

      <section class="modal-card-body">
        <div class="templates-container">
          <!-- Template Categories -->
          <div class="template-categories">
            <button
              v-for="category in categories"
              :key="category.id"
              @click="selectedCategory = category.id"
              class="button is-small"
              :class="{ 'is-primary': selectedCategory === category.id }"
            >
              <i :class="category.icon"></i>
              <span>{{ category.name }}</span>
            </button>
          </div>

          <!-- Templates Grid -->
          <div class="templates-grid">
            <div
              v-for="template in filteredTemplates"
              :key="template.id"
              @click="selectTemplate(template)"
              class="template-card"
              :class="{ 'selected': selectedTemplate?.id === template.id }"
            >
              <div class="template-preview">
                <div class="preview-columns">
                  <div
                    v-for="column in template.columns.slice(0, 3)"
                    :key="column.title"
                    class="preview-column"
                  >
                    <div class="preview-column-header">
                      {{ column.title }}
                    </div>
                    <div class="preview-cards">
                      <div
                        v-for="i in Math.min(3, Math.floor(Math.random() * 4) + 1)"
                        :key="i"
                        class="preview-card"
                      ></div>
                    </div>
                  </div>
                  <div v-if="template.columns.length > 3" class="preview-more">
                    +{{ template.columns.length - 3 }}
                  </div>
                </div>
              </div>
              
              <div class="template-info">
                <h4 class="template-name">{{ template.name }}</h4>
                <p class="template-description">{{ template.description }}</p>
                
                <div class="template-meta">
                  <span class="template-columns-count">
                    <i class="fas fa-columns"></i>
                    {{ template.columns.length }} columns
                  </span>
                  
                  <span v-if="template.labels.length > 0" class="template-labels-count">
                    <i class="fas fa-tags"></i>
                    {{ template.labels.length }} labels
                  </span>
                  
                  <span v-if="template.isPublic" class="template-public">
                    <i class="fas fa-globe"></i>
                    Public
                  </span>
                </div>
              </div>
            </div>

            <!-- Create Custom Template Card -->
            <div
              @click="showCustomTemplateForm = true"
              class="template-card create-template-card"
            >
              <div class="create-template-content">
                <i class="fas fa-plus create-template-icon"></i>
                <h4 class="create-template-title">Create Custom Template</h4>
                <p class="create-template-description">
                  Save your current board as a template
                </p>
              </div>
            </div>
          </div>

          <!-- Template Details -->
          <div v-if="selectedTemplate" class="template-details">
            <h3 class="details-title">{{ selectedTemplate.name }}</h3>
            <p class="details-description">{{ selectedTemplate.description }}</p>
            
            <div class="details-columns">
              <h4 class="details-section-title">Columns</h4>
              <div class="columns-list">
                <div
                  v-for="column in selectedTemplate.columns"
                  :key="column.title"
                  class="column-item"
                >
                  <span class="column-name">{{ column.title }}</span>
                  <span class="column-position">Position {{ column.position + 1 }}</span>
                </div>
              </div>
            </div>
            
            <div v-if="selectedTemplate.labels.length > 0" class="details-labels">
              <h4 class="details-section-title">Labels</h4>
              <div class="labels-list">
                <span
                  v-for="label in selectedTemplate.labels"
                  :key="label.id"
                  class="tag label-tag"
                  :style="{ backgroundColor: label.color }"
                >
                  {{ label.name }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <footer class="modal-card-foot">
        <button
          @click="applyTemplate"
          class="button is-primary"
          :disabled="!selectedTemplate"
        >
          <i class="fas fa-check"></i>
          <span>Use Template</span>
        </button>
        <button @click="$emit('close')" class="button">
          Cancel
        </button>
      </footer>
    </div>

    <!-- Custom Template Form Modal -->
    <div v-if="showCustomTemplateForm" class="modal is-active">
      <div class="modal-background" @click="showCustomTemplateForm = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Create Template</p>
          <button
            class="delete"
            @click="showCustomTemplateForm = false"
            aria-label="close"
          ></button>
        </header>
        
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Template Name</label>
            <div class="control">
              <input
                v-model="customTemplate.name"
                class="input"
                type="text"
                placeholder="Enter template name"
              />
            </div>
          </div>
          
          <div class="field">
            <label class="label">Description</label>
            <div class="control">
              <textarea
                v-model="customTemplate.description"
                class="textarea"
                placeholder="Describe what this template is for"
                rows="3"
              ></textarea>
            </div>
          </div>
          
          <div class="field">
            <label class="checkbox">
              <input
                v-model="customTemplate.isPublic"
                type="checkbox"
              />
              Make this template public (others can use it)
            </label>
          </div>
          
          <div class="template-preview-section">
            <h4 class="preview-title">Template Preview</h4>
            <div class="current-board-preview">
              <div class="preview-columns">
                <div
                  v-for="column in currentBoard.columns.slice(0, 4)"
                  :key="column.id"
                  class="preview-column"
                >
                  <div class="preview-column-header">
                    {{ column.title }}
                  </div>
                  <div class="preview-cards">
                    <div
                      v-for="i in Math.min(2, column.cards.length)"
                      :key="i"
                      class="preview-card"
                    ></div>
                  </div>
                </div>
                <div v-if="currentBoard.columns.length > 4" class="preview-more">
                  +{{ currentBoard.columns.length - 4 }}
                </div>
              </div>
            </div>
          </div>
        </section>
        
        <footer class="modal-card-foot">
          <button
            @click="saveCustomTemplate"
            class="button is-primary"
            :disabled="!customTemplate.name.trim()"
          >
            <i class="fas fa-save"></i>
            <span>Save Template</span>
          </button>
          <button @click="showCustomTemplateForm = false" class="button">
            Cancel
          </button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { BoardTemplate, KanbanBoard, KanbanLabel } from '../../types/kanban'

interface Props {
  currentBoard: KanbanBoard
}

interface Emits {
  (e: 'close'): void
  (e: 'apply-template', template: BoardTemplate): void
  (e: 'save-template', template: BoardTemplate): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const selectedCategory = ref('all')
const selectedTemplate = ref<BoardTemplate | null>(null)
const showCustomTemplateForm = ref(false)
const templates = ref<BoardTemplate[]>([])

// Custom template form
const customTemplate = ref({
  name: '',
  description: '',
  isPublic: false
})

// Template categories
const categories = [
  { id: 'all', name: 'All Templates', icon: 'fas fa-th' },
  { id: 'project', name: 'Project Management', icon: 'fas fa-project-diagram' },
  { id: 'development', name: 'Software Development', icon: 'fas fa-code' },
  { id: 'marketing', name: 'Marketing', icon: 'fas fa-bullhorn' },
  { id: 'personal', name: 'Personal', icon: 'fas fa-user' },
  { id: 'custom', name: 'My Templates', icon: 'fas fa-star' }
]

// Computed
const filteredTemplates = computed(() => {
  if (selectedCategory.value === 'all') {
    return templates.value
  }
  return templates.value.filter(template => template.category === selectedCategory.value)
})

// Methods
const selectTemplate = (template: BoardTemplate) => {
  selectedTemplate.value = template
}

const applyTemplate = () => {
  if (selectedTemplate.value) {
    emit('apply-template', selectedTemplate.value)
    emit('close')
  }
}

const saveCustomTemplate = () => {
  if (!customTemplate.value.name.trim()) return
  
  const template: BoardTemplate = {
    id: generateId(),
    name: customTemplate.value.name.trim(),
    description: customTemplate.value.description.trim(),
    columns: props.currentBoard.columns.map(col => ({
      title: col.title,
      position: col.position
    })),
    labels: props.currentBoard.labels || [],
    isPublic: customTemplate.value.isPublic,
    category: 'custom'
  }
  
  emit('save-template', template)
  templates.value.push(template)
  
  // Reset form
  customTemplate.value = {
    name: '',
    description: '',
    isPublic: false
  }
  showCustomTemplateForm.value = false
}

const generateId = () => Math.random().toString(36).substr(2, 9)

// Initialize default templates
const initializeDefaultTemplates = () => {
  templates.value = [
    // Project Management Templates
    {
      id: 'basic-kanban',
      name: 'Basic Kanban',
      description: 'Simple three-column kanban board for basic task management',
      columns: [
        { title: 'To Do', position: 0 },
        { title: 'In Progress', position: 1 },
        { title: 'Done', position: 2 }
      ],
      labels: [
        { id: 'bug', name: 'Bug', color: '#ff3860' },
        { id: 'feature', name: 'Feature', color: '#3273dc' },
        { id: 'urgent', name: 'Urgent', color: '#ff9f43' }
      ],
      isPublic: true,
      category: 'project'
    },
    {
      id: 'project-management',
      name: 'Project Management',
      description: 'Comprehensive project management board with multiple stages',
      columns: [
        { title: 'Backlog', position: 0 },
        { title: 'Planning', position: 1 },
        { title: 'In Progress', position: 2 },
        { title: 'Review', position: 3 },
        { title: 'Testing', position: 4 },
        { title: 'Done', position: 5 }
      ],
      labels: [
        { id: 'high-priority', name: 'High Priority', color: '#ff3860' },
        { id: 'medium-priority', name: 'Medium Priority', color: '#ff9f43' },
        { id: 'low-priority', name: 'Low Priority', color: '#48c774' },
        { id: 'blocked', name: 'Blocked', color: '#363636' }
      ],
      isPublic: true,
      category: 'project'
    },
    
    // Software Development Templates
    {
      id: 'scrum-board',
      name: 'Scrum Board',
      description: 'Agile scrum board for sprint management',
      columns: [
        { title: 'Product Backlog', position: 0 },
        { title: 'Sprint Backlog', position: 1 },
        { title: 'In Progress', position: 2 },
        { title: 'Code Review', position: 3 },
        { title: 'Testing', position: 4 },
        { title: 'Done', position: 5 }
      ],
      labels: [
        { id: 'story', name: 'User Story', color: '#3273dc' },
        { id: 'bug', name: 'Bug', color: '#ff3860' },
        { id: 'task', name: 'Task', color: '#48c774' },
        { id: 'epic', name: 'Epic', color: '#9c27b0' }
      ],
      isPublic: true,
      category: 'development'
    },
    {
      id: 'bug-tracking',
      name: 'Bug Tracking',
      description: 'Track and manage software bugs and issues',
      columns: [
        { title: 'Reported', position: 0 },
        { title: 'Confirmed', position: 1 },
        { title: 'In Progress', position: 2 },
        { title: 'Fixed', position: 3 },
        { title: 'Testing', position: 4 },
        { title: 'Closed', position: 5 }
      ],
      labels: [
        { id: 'critical', name: 'Critical', color: '#ff3860' },
        { id: 'major', name: 'Major', color: '#ff9f43' },
        { id: 'minor', name: 'Minor', color: '#48c774' },
        { id: 'enhancement', name: 'Enhancement', color: '#3273dc' }
      ],
      isPublic: true,
      category: 'development'
    },
    
    // Marketing Templates
    {
      id: 'content-calendar',
      name: 'Content Calendar',
      description: 'Manage content creation and publishing workflow',
      columns: [
        { title: 'Ideas', position: 0 },
        { title: 'Planning', position: 1 },
        { title: 'Writing', position: 2 },
        { title: 'Review', position: 3 },
        { title: 'Scheduled', position: 4 },
        { title: 'Published', position: 5 }
      ],
      labels: [
        { id: 'blog', name: 'Blog Post', color: '#3273dc' },
        { id: 'social', name: 'Social Media', color: '#ff9f43' },
        { id: 'video', name: 'Video', color: '#ff3860' },
        { id: 'newsletter', name: 'Newsletter', color: '#48c774' }
      ],
      isPublic: true,
      category: 'marketing'
    },
    {
      id: 'campaign-management',
      name: 'Campaign Management',
      description: 'Track marketing campaigns from concept to completion',
      columns: [
        { title: 'Concept', position: 0 },
        { title: 'Planning', position: 1 },
        { title: 'Creative', position: 2 },
        { title: 'Approval', position: 3 },
        { title: 'Launch', position: 4 },
        { title: 'Analysis', position: 5 }
      ],
      labels: [
        { id: 'email', name: 'Email Campaign', color: '#3273dc' },
        { id: 'social', name: 'Social Campaign', color: '#ff9f43' },
        { id: 'paid', name: 'Paid Ads', color: '#ff3860' },
        { id: 'organic', name: 'Organic', color: '#48c774' }
      ],
      isPublic: true,
      category: 'marketing'
    },
    
    // Personal Templates
    {
      id: 'personal-tasks',
      name: 'Personal Tasks',
      description: 'Simple personal task management',
      columns: [
        { title: 'To Do', position: 0 },
        { title: 'Doing', position: 1 },
        { title: 'Done', position: 2 }
      ],
      labels: [
        { id: 'work', name: 'Work', color: '#3273dc' },
        { id: 'personal', name: 'Personal', color: '#48c774' },
        { id: 'urgent', name: 'Urgent', color: '#ff3860' }
      ],
      isPublic: true,
      category: 'personal'
    },
    {
      id: 'habit-tracker',
      name: 'Habit Tracker',
      description: 'Track daily habits and routines',
      columns: [
        { title: 'New Habits', position: 0 },
        { title: 'Building', position: 1 },
        { title: 'Consistent', position: 2 },
        { title: 'Mastered', position: 3 }
      ],
      labels: [
        { id: 'health', name: 'Health', color: '#48c774' },
        { id: 'productivity', name: 'Productivity', color: '#3273dc' },
        { id: 'learning', name: 'Learning', color: '#ff9f43' },
        { id: 'social', name: 'Social', color: '#9c27b0' }
      ],
      isPublic: true,
      category: 'personal'
    }
  ]
}

// Initialize on mount
onMounted(() => {
  initializeDefaultTemplates()
})
</script>

<style scoped>
.board-templates-modal {
  width: 1000px;
  max-width: 95vw;
  max-height: 90vh;
}

.modal-card-body {
  padding: 1.5rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.templates-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Template Categories */
.template-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.template-categories .button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Templates Grid */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.template-card {
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.template-card:hover {
  border-color: #3273dc;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
  border-color: #3273dc;
  box-shadow: 0 0 0 1px #3273dc;
}

.create-template-card {
  border-style: dashed;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.create-template-content {
  text-align: center;
  color: #666;
}

.create-template-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #3273dc;
}

.create-template-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.create-template-description {
  font-size: 0.9rem;
  margin: 0;
}

/* Template Preview */
.template-preview {
  margin-bottom: 1rem;
}

.preview-columns {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
}

.preview-column {
  flex: 1;
  background: #f9f9f9;
  border-radius: 4px;
  padding: 0.5rem;
  min-width: 60px;
}

.preview-column-header {
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #666;
  text-align: center;
}

.preview-cards {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.preview-card {
  height: 12px;
  background: #e8e8e8;
  border-radius: 2px;
}

.preview-more {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.8rem;
  color: #666;
  min-width: 40px;
}

/* Template Info */
.template-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.template-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.template-description {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.template-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #666;
}

.template-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.template-public {
  color: #48c774;
}

/* Template Details */
.template-details {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 1rem;
}

.details-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.details-description {
  color: #666;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.details-section-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #666;
}

.columns-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.column-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
}

.column-name {
  font-weight: 500;
}

.column-position {
  font-size: 0.8rem;
  color: #666;
}

.labels-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.label-tag {
  color: white;
  font-weight: 500;
  font-size: 0.8rem;
}

/* Custom Template Form */
.template-preview-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
}

.preview-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

.current-board-preview {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .board-templates-modal {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
  }
  
  .templates-grid {
    grid-template-columns: 1fr;
  }
  
  .template-categories {
    gap: 0.25rem;
  }
  
  .template-categories .button {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
  }
  
  .preview-columns {
    gap: 0.25rem;
  }
  
  .template-meta {
    gap: 0.25rem;
  }
}
</style>
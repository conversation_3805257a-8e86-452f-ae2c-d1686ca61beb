import express from 'express';
import { Search<PERSON>ontroller } from '../controllers/SearchController';
import { authenticateToken } from '../middleware/auth';
import { generalRateLimit } from '../middleware/rateLimiting';
import { cacheSearch } from '../middleware/caching';

const router = express.Router();

// Apply authentication middleware to all search routes
router.use(authenticateToken);

// Apply rate limiting
router.use(generalRateLimit);

// Search routes
router.get('/search', cacheSearch, SearchController.searchNotes);
router.get('/search/suggestions', SearchController.getSearchSuggestions);
router.get('/search/stats', SearchController.getSearchStats);
router.post('/search/reindex', SearchController.reindexSearch);

export default router;
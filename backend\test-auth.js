// Simple test script to verify authentication endpoints
const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testAuth() {
  try {
    console.log('Testing authentication endpoints...\n');

    // Test registration
    console.log('1. Testing user registration...');
    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      display_name: 'Test User'
    });
    
    console.log('✓ Registration successful');
    console.log('User ID:', registerResponse.data.user.id);
    console.log('Email verified:', registerResponse.data.user.email_verified);
    
    const { accessToken, refreshToken } = registerResponse.data.tokens;
    console.log('✓ Tokens generated\n');

    // Test login
    console.log('2. Testing user login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    console.log('✓ Login successful');
    console.log('User ID:', loginResponse.data.user.id);
    
    const newAccessToken = loginResponse.data.tokens.accessToken;
    console.log('✓ New tokens generated\n');

    // Test protected route
    console.log('3. Testing protected route (get profile)...');
    const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
      headers: {
        'Authorization': `Bearer ${newAccessToken}`
      }
    });
    
    console.log('✓ Profile retrieved successfully');
    console.log('Display name:', profileResponse.data.user.display_name);
    console.log('Email:', profileResponse.data.user.email);
    console.log('✓ Password hash not included in response\n');

    // Test token refresh
    console.log('4. Testing token refresh...');
    const refreshResponse = await axios.post(`${BASE_URL}/auth/refresh-token`, {
      refreshToken: refreshToken
    });
    
    console.log('✓ Token refresh successful');
    console.log('✓ New access token generated\n');

    // Test password reset request
    console.log('5. Testing password reset request...');
    const resetRequestResponse = await axios.post(`${BASE_URL}/auth/forgot-password`, {
      email: '<EMAIL>'
    });
    
    console.log('✓ Password reset request successful');
    console.log('Message:', resetRequestResponse.data.message);

    console.log('\n🎉 All authentication tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data?.details) {
      console.error('Validation errors:', error.response.data.details);
    }
  }
}

// Run tests
testAuth();
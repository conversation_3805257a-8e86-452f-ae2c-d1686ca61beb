export interface AdminNotification {
  id: string
  type: 'critical' | 'warning' | 'info' | 'success'
  category: 'content_report' | 'user_action' | 'system' | 'security'
  title: string
  message: string
  read: boolean
  actionUrl?: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt?: string
}

export interface CreateNotificationData {
  type: AdminNotification['type']
  category: AdminNotification['category']
  title: string
  message: string
  actionUrl?: string
  metadata?: Record<string, any>
}

export interface NotificationFilters {
  type?: string
  category?: string
  read?: boolean
  page?: number
  limit?: number
}

export interface NotificationPagination {
  page: number
  limit: number
  total: number
  totalPages: number
}
<template>
  <div class="recent-activity-widget">
    <div class="widget-header">
      <h3 class="widget-title">Recent Activity</h3>
      <button class="button is-ghost is-small" @click="refreshActivity" title="Refresh">
        <span class="icon">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
        </span>
      </button>
    </div>

    <div class="widget-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-state">
        <div class="loader"></div>
        <p>Loading activity...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="activities.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-history"></i>
        </div>
        <p>No recent activity</p>
      </div>

      <!-- Activity List -->
      <div v-else class="activity-list">
        <div 
          v-for="activity in activities" 
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon" :class="getActivityTypeClass(activity.type)">
            <i :class="getActivityIcon(activity.type)"></i>
          </div>
          <div class="activity-content">
            <p class="activity-description">{{ activity.description }}</p>
            <div class="activity-meta">
              <span class="activity-time">{{ formatTime(activity.timestamp) }}</span>
              <span v-if="activity.target" class="activity-target">{{ activity.target }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const loading = ref(false)

// Mock activity data - replace with actual activity store
const activities = ref([
  {
    id: 1,
    type: 'note_created',
    description: 'Created a new note',
    target: 'Meeting Notes - Q4 Planning',
    timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
  },
  {
    id: 2,
    type: 'note_updated',
    description: 'Updated note',
    target: 'Project Requirements',
    timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
  },
  {
    id: 3,
    type: 'note_shared',
    description: 'Shared note with team',
    target: 'Design Mockups',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
  },
  {
    id: 4,
    type: 'group_joined',
    description: 'Joined group',
    target: 'Development Team',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
  },
  {
    id: 5,
    type: 'note_favorited',
    description: 'Added note to favorites',
    target: 'API Documentation',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
  }
])

// Methods
const getActivityIcon = (type: string): string => {
  switch (type) {
    case 'note_created': return 'fas fa-plus'
    case 'note_updated': return 'fas fa-edit'
    case 'note_shared': return 'fas fa-share-alt'
    case 'note_favorited': return 'fas fa-star'
    case 'group_joined': return 'fas fa-users'
    case 'group_created': return 'fas fa-plus-circle'
    default: return 'fas fa-circle'
  }
}

const getActivityTypeClass = (type: string): string => {
  switch (type) {
    case 'note_created': return 'created'
    case 'note_updated': return 'updated'
    case 'note_shared': return 'shared'
    case 'note_favorited': return 'favorited'
    case 'group_joined': return 'group'
    case 'group_created': return 'group'
    default: return 'default'
  }
}

const formatTime = (date: Date): string => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays}d ago`
}

const refreshActivity = async () => {
  loading.value = true
  // Simulate API call
  setTimeout(() => {
    loading.value = false
  }, 1000)
}
</script>

<style scoped>
.recent-activity-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 0;
  margin-bottom: 1rem;
}

.widget-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #363636;
  margin: 0;
}

.widget-content {
  flex: 1;
  padding: 0 1.5rem 1.5rem;
  overflow-y: auto;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #6c757d;
}

.loader {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 2rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.activity-icon.created {
  background: #d4edda;
  color: #155724;
}

.activity-icon.updated {
  background: #d1ecf1;
  color: #0c5460;
}

.activity-icon.shared {
  background: #ffeaa7;
  color: #856404;
}

.activity-icon.favorited {
  background: #fff3cd;
  color: #856404;
}

.activity-icon.group {
  background: #d1ecf1;
  color: #0c5460;
}

.activity-icon.default {
  background: #e9ecef;
  color: #6c757d;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-description {
  font-size: 0.875rem;
  color: #363636;
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.activity-time {
  font-size: 0.75rem;
  color: #6c757d;
}

.activity-target {
  font-size: 0.75rem;
  color: #007bff;
  font-weight: 500;
}
</style>
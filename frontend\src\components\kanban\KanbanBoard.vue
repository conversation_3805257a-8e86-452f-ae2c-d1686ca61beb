<template>
  <div class="kanban-board">
    <!-- Board Header -->
    <div v-if="board" class="board-header">
      <div class="board-title-section">
        <h1 class="board-title" contenteditable @blur="updateBoardTitle"
          @keydown.enter.prevent="($event.target as HTMLElement).blur()">
          {{ board.title }}
        </h1>
        <div class="board-actions">
          <button class="button is-small" @click="showFilters = !showFilters">
            <i class="fas fa-filter"></i>
            Filter
          </button>
          <button class="button is-small" @click="showBoardSettings = true">
            <i class="fas fa-cog"></i>
            Settings
          </button>
          <button class="button is-small is-primary" @click="addNewColumn">
            <i class="fas fa-plus"></i>
            New Column
          </button>
        </div>
      </div>

      <!-- Filters -->
      <BoardFilters v-if="showFilters" :filters="filters" :availableLabels="board.labels" :availableAssignees="board.members"
        :searchResults="[]" @update:filters="updateFilters" />
    </div>

    <!-- Loading State -->
    <div v-if="!board" class="loading-state">
      <div class="loading-spinner"></div>
      <p>Loading kanban board...</p>
    </div>

    <!-- Kanban Columns -->
    <div v-else class="kanban-container" ref="kanbanContainer">
      <div class="kanban-columns" :style="{ width: `${columnWidth * filteredColumns.length}px` }" @dragover.prevent
        @drop="($event) => handleColumnContainerDrop($event as any)">
        <KanbanColumnComponent v-for="column in filteredColumns" :key="column.id" :column="column" :board="board"
          :filters="filters" @update:column="updateColumn" @delete:column="deleteColumn" @add:card="addCard"
          @update:card="updateCard" @delete:card="deleteCard" @drag:card="handleCardDrag"
          @drag:column="handleColumnDrag" @select:card="selectedCard = $event" />
      </div>
    </div>

    <!-- Card Details Modal -->
    <CardDetailsModal v-if="selectedCard && board" :card="selectedCard" :columnTitle="getColumnTitle(selectedCard.columnId)" :boardMembers="board.members" @close="selectedCard = null"
      @update:card="updateCard" @delete:card="deleteCard" />

    <!-- Board Settings Modal -->
    <BoardSettingsModal v-if="showBoardSettings && board" :board="board" @close="showBoardSettings = false"
      @update:board="updateBoard" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useKanbanStore } from '@/stores/kanban'
import { useNotificationStore } from '@/stores/notifications'
import type {
  KanbanBoard,
  KanbanColumn,
  KanbanCard,
  KanbanFilters,
  DragEvent,
  ColumnDragEvent
} from '@/types/kanban'
import KanbanColumnComponent from './KanbanColumn.vue'
import BoardFilters from './BoardFilters.vue'
import CardDetailsModal from './CardDetailsModal.vue'
import BoardSettingsModal from './BoardSettingsModal.vue'

interface Props {
  boardId: string
}

const props = defineProps<Props>()

const kanbanStore = useKanbanStore()
const notificationStore = useNotificationStore()

// Reactive state
const board = ref<KanbanBoard | null>(null)
const selectedCard = ref<KanbanCard | null>(null)
const showFilters = ref(false)
const showBoardSettings = ref(false)
const kanbanContainer = ref<HTMLElement>()
const columnWidth = ref(320) // Fixed column width like Notion

// Filters
const filters = ref<KanbanFilters>({
  search: '',
  labels: [],
  assignees: [],
  priority: [],
  dueDate: undefined,
  hasAttachments: undefined,
  hasComments: undefined
})

// Computed
const filteredColumns = computed(() => {
  if (!board.value) return []

  return board.value.columns
    .sort((a, b) => a.position - b.position)
    .map(column => ({
      ...column,
      cards: filterCards(column.cards)
    }))
})

// Methods
const loadBoard = async () => {
  try {
    board.value = await kanbanStore.getBoard(props.boardId)
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to load board',
      read: false
    })
  }
}

const getColumnTitle = (columnId: string): string => {
  if (!board.value) return ''
  const column = board.value.columns.find(col => col.id === columnId)
  return column ? column.title : ''
}

const filterCards = (cards: KanbanCard[]) => {
  return cards
    .filter(card => {
      // Search filter
      if (filters.value.search) {
        const searchTerm = filters.value.search.toLowerCase()
        const matchesSearch =
          card.title.toLowerCase().includes(searchTerm) ||
          card.description?.toLowerCase().includes(searchTerm) ||
          card.comments.some(comment => comment.text.toLowerCase().includes(searchTerm))

        if (!matchesSearch) return false
      }

      // Label filter
      if (filters.value.labels?.length) {
        const hasMatchingLabel = card.labels.some(label =>
          filters.value.labels!.includes(label.id)
        )
        if (!hasMatchingLabel) return false
      }

      // Assignee filter
      if (filters.value.assignees?.length) {
        const hasMatchingAssignee = card.assignees.some(assignee =>
          filters.value.assignees!.includes(assignee.id)
        )
        if (!hasMatchingAssignee) return false
      }

      // Priority filter
      if (filters.value.priority?.length) {
        if (!filters.value.priority.includes(card.priority)) return false
      }

      // Due date filter
      if (filters.value.dueDate) {
        const now = new Date()
        const dueDate = card.dueDate ? new Date(card.dueDate) : null

        switch (filters.value.dueDate) {
          case 'overdue':
            if (!dueDate || dueDate >= now) return false
            break
          case 'today':
            if (!dueDate || dueDate.toDateString() !== now.toDateString()) return false
            break
          case 'week':
            if (!dueDate) return false
            const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
            if (dueDate > weekFromNow) return false
            break
          case 'month':
            if (!dueDate) return false
            const monthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
            if (dueDate > monthFromNow) return false
            break
          case 'none':
            if (dueDate) return false
            break
        }
      }

      // Attachments filter
      if (filters.value.hasAttachments !== undefined) {
        const hasAttachments = card.attachments.length > 0
        if (filters.value.hasAttachments !== hasAttachments) return false
      }

      // Comments filter
      if (filters.value.hasComments !== undefined) {
        const hasComments = card.comments.length > 0
        if (filters.value.hasComments !== hasComments) return false
      }

      return true
    })
    .sort((a, b) => a.position - b.position)
}

const updateFilters = (newFilters: KanbanFilters) => {
  filters.value = { ...newFilters }
}

const updateBoardTitle = async (event: Event) => {
  const target = event.target as HTMLElement
  const newTitle = target.textContent?.trim() || 'Untitled Board'

  if (board.value && newTitle !== board.value.title) {
    try {
      await kanbanStore.updateBoard(board.value.id, { title: newTitle })
      board.value.title = newTitle
    } catch (error) {
      notificationStore.addNotification({
        type: 'critical',
        category: 'system',
        title: 'Error',
        message: 'Failed to update board title',
        read: false
      })
      target.textContent = board.value.title // Revert
    }
  }
}

const addNewColumn = async () => {
  if (!board.value) return

  const newColumn: Partial<KanbanColumn> = {
    title: 'New Column',
    position: board.value.columns.length,
    boardId: board.value.id,
    cards: []
  }

  try {
    const createdColumn = await kanbanStore.createColumn(newColumn)
    board.value.columns.push(createdColumn)
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to create column',
      read: false
    })
  }
}

const updateColumn = async (columnId: string, updates: Partial<KanbanColumn>) => {
  if (!board.value) return

  try {
    await kanbanStore.updateColumn(columnId, updates)
    const columnIndex = board.value.columns.findIndex(col => col.id === columnId)
    if (columnIndex !== -1) {
      board.value.columns[columnIndex] = { ...board.value.columns[columnIndex], ...updates }
    }
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to update column',
      read: false
    })
  }
}

const deleteColumn = async (columnId: string) => {
  if (!board.value) return

  try {
    await kanbanStore.deleteColumn(columnId)
    board.value.columns = board.value.columns.filter(col => col.id !== columnId)
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to delete column',
      read: false
    })
  }
}

const addCard = async (columnId: string, cardData: Partial<KanbanCard>) => {
  if (!board.value) return

  const column = board.value.columns.find(col => col.id === columnId)
  if (!column) return

  const newCard: Partial<KanbanCard> = {
    ...cardData,
    columnId,
    position: column.cards.length,
    labels: [],
    assignees: [],
    priority: 'medium',
    checklist: [],
    attachments: [],
    comments: []
  }

  try {
    const createdCard = await kanbanStore.createCard(newCard)

    if (createdCard && createdCard.id) {
      // Ensure the cards array exists and is reactive
      if (!column.cards) {
        column.cards = []
      }
      column.cards.push(createdCard)
    }
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to create card',
      read: false
    })
  }
}

const updateCard = async (cardId: string, updates: Partial<KanbanCard>) => {
  if (!board.value) return



  try {
    await kanbanStore.updateCard(cardId, updates)

    // Update card in the appropriate column
    for (const column of board.value.columns) {
      const cardIndex = column.cards.findIndex(card => card.id === cardId)
      if (cardIndex !== -1) {
        const oldCard = column.cards[cardIndex]
        column.cards[cardIndex] = { ...column.cards[cardIndex], ...updates }
        console.log('Card updated in board:', {
          cardId,
          oldColor: oldCard.color,
          newColor: column.cards[cardIndex].color
        })
        break
      }
    }
  } catch (error) {
    console.error('Error updating card:', error)
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to update card',
      read: false
    })
  }
}

const deleteCard = async (cardId: string) => {
  if (!board.value) return

  try {
    await kanbanStore.deleteCard(cardId)

    // Remove card from all columns
    for (const column of board.value.columns) {
      column.cards = column.cards.filter(card => card.id !== cardId)
    }

    if (selectedCard.value?.id === cardId) {
      selectedCard.value = null
    }
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to delete card',
      read: false
    })
  }
}

const handleCardDrag = async (dragEvent: DragEvent) => {
  if (!board.value) return

  try {
    console.log('KanbanBoard: Handling card drag:', dragEvent)
    await kanbanStore.moveCard(dragEvent)

    // The store handles all the data updates, so we just need to refresh the board
    board.value = await kanbanStore.getBoard(board.value.id)
  } catch (error) {
    console.error('Error in handleCardDrag:', error)
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to move card',
      read: false
    })
  }
}

const handleColumnDrag = async (dragEvent: ColumnDragEvent) => {
  if (!board.value) return

  try {
    await kanbanStore.moveColumn(dragEvent)

    // Update local state
    const columnIndex = board.value.columns.findIndex(col => col.id === dragEvent.columnId)
    if (columnIndex !== -1) {
      const [column] = board.value.columns.splice(columnIndex, 1)
      column.position = dragEvent.newPosition
      board.value.columns.splice(dragEvent.newPosition, 0, column)

      // Update positions for other columns
      board.value.columns.forEach((col, index) => {
        col.position = index
      })
    }
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to move column',
      read: false
    })
  }
}

const handleColumnContainerDrop = (event: DragEvent) => {
  (event as any).preventDefault()

  try {
    const data = JSON.parse((event as any).dataTransfer!.getData('text/plain'))

    if (data.type === 'column') {
      // Calculate position based on drop location in the container
      const rect = (event as any).currentTarget.getBoundingClientRect()
      const x = (event as any).clientX - rect.left
      const columnWidth = 320 // Match the column width
      const newPosition = Math.floor(x / columnWidth)

      if (newPosition !== data.currentPosition) {
        handleColumnDrag({
          columnId: data.columnId,
          newPosition: Math.min(Math.max(0, newPosition), board.value!.columns.length - 1)
        })
      }
    }
  } catch (error) {
    console.error('Error handling column container drop:', error)
  }
}

const updateBoard = async (updates: Partial<KanbanBoard>) => {
  if (!board.value) return

  try {
    await kanbanStore.updateBoard(board.value.id, updates)
    board.value = { ...board.value, ...updates }
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to update board',
      read: false
    })
  }
}

// Lifecycle
onMounted(() => {
  loadBoard()

  // Scroll to top when board loads
  setTimeout(() => {
    if (kanbanContainer.value) {
      kanbanContainer.value.scrollTop = 0
      kanbanContainer.value.scrollLeft = 0
    }
  }, 100)
})
</script>

<style scoped>
.kanban-board {
  height: 100%;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.board-header {
  padding: 1rem 1.5rem;
  background: white;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.board-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.board-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  border: none;
  background: transparent;
  outline: none;
  cursor: text;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.board-title:hover,
.board-title:focus {
  background: #f8f9fa;
}

.board-actions {
  display: flex;
  gap: 0.5rem;
}

.kanban-container {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  padding: 1rem;
  max-height: calc(100% - 120px);
  scroll-behavior: smooth;
}

.kanban-columns {
  display: flex;
  gap: 1rem;
  min-height: 100%;
  padding-bottom: 1rem;
}

/* Scrollbar styling */
.kanban-container::-webkit-scrollbar {
  height: 8px;
}

.kanban-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.kanban-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.kanban-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading state */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
<template>
  <div class="dashboard-overview">
    <!-- Quick Actions moved to top -->
    <div class="quick-actions-top">
      <QuickActionsWidget 
        @create-note="$emit('create-note')"
        @create-group="$emit('create-group')"
        @open-search="$emit('open-search')"
        @open-settings="$emit('open-settings')"
      />
    </div>

    <div class="dashboard-grid">
      <!-- Top row - Notifications and Quick Stats -->
      <div class="grid-item notifications">
        <NotificationsWidget />
      </div>
      
      <div class="grid-item stats">
        <QuickStatsWidget />
      </div>

      <!-- Bottom row - Recent Notes and Group Activity -->
      <div class="grid-item recent-notes">
        <RecentNotesWidget @create-note="$emit('create-note')" />
      </div>
      
      <div class="grid-item group-activity">
        <RecentGroupActivityWidget />
      </div>
    </div>

    <!-- Keyboard Shortcuts at bottom -->
    <div class="keyboard-shortcuts">
      <div class="shortcuts-list">
        <div class="shortcut-item">
          <kbd>Ctrl</kbd> + <kbd>N</kbd>
          <span>New Note</span>
        </div>
        <div class="shortcut-item">
          <kbd>Ctrl</kbd> + <kbd>F</kbd>
          <span>Search</span>
        </div>
        <div class="shortcut-item">
          <kbd>Ctrl</kbd> + <kbd>S</kbd>
          <span>Save</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import NotificationsWidget from './NotificationsWidget.vue'
import QuickStatsWidget from './QuickStatsWidget.vue'
import RecentNotesWidget from './RecentNotesWidget.vue'
import QuickActionsWidget from './QuickActionsWidget.vue'
import RecentGroupActivityWidget from './RecentGroupActivityWidget.vue'

const emit = defineEmits<{
  'create-note': []
  'create-group': []
  'open-search': []
  'open-settings': []
}>()
</script>

<style scoped>
.dashboard-overview {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.quick-actions-top {
  margin-bottom: 2rem;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-areas: 
    "notifications stats"
    "recent-notes group-activity";
  gap: 1.5rem;
}

.grid-item.notifications {
  grid-area: notifications;
}

.grid-item.stats {
  grid-area: stats;
}

.grid-item.recent-notes {
  grid-area: recent-notes;
}

.grid-item.group-activity {
  grid-area: group-activity;
}



/* Responsive design */
@media (max-width: 1024px) {
  .dashboard-overview {
    padding: 1rem;
  }
  
  .dashboard-grid {
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .dashboard-title {
    font-size: 1.75rem;
  }
  
  .dashboard-subtitle {
    font-size: 1rem;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "notifications"
      "stats"
      "recent-notes"
      "group-activity";
  }
}

@media (max-width: 480px) {
  .dashboard-overview {
    padding: 0.75rem;
  }
  
  .dashboard-title {
    font-size: 1.5rem;
  }
}

/* Keyboard Shortcuts */
.keyboard-shortcuts {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e9ecef;
}

.shortcuts-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6c757d;
}

.shortcut-item kbd {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  padding: 0.125rem 0.25rem;
  font-size: 0.625rem;
  font-family: monospace;
  color: #363636;
}

/* Responsive for shortcuts */
@media (max-width: 768px) {
  .shortcuts-list {
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
  }
}
</style>
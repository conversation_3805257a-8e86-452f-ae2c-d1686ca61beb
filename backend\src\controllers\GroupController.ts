import { Request, Response } from 'express';
import { GroupRepository } from '../repositories/GroupRepository';
import { UserRepository } from '../repositories/UserRepository';
import { GroupModel, CreateGroupData, UpdateGroupData } from '../models/Group';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    jti: string;
  };
}

export class GroupController {
  // Create a new group
  static async createGroup(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { name, description, settings } = req.body;
      const userId = req.user!.id;

      // Validate group name
      const nameValidation = GroupModel.validateGroupName(name);
      if (!nameValidation.valid) {
        res.status(400).json({
          error: 'Validation failed',
          details: nameValidation.errors
        });
        return;
      }

      // Validate description if provided
      const descValidation = GroupModel.validateDescription(description);
      if (!descValidation.valid) {
        res.status(400).json({
          error: 'Validation failed',
          details: descValidation.errors
        });
        return;
      }

      // Validate settings if provided
      if (settings) {
        const settingsValidation = GroupModel.validateSettings(settings);
        if (!settingsValidation.valid) {
          res.status(400).json({
            error: 'Validation failed',
            details: settingsValidation.errors
          });
          return;
        }
      }

      const groupData: CreateGroupData = {
        ownerId: userId,
        name,
        description,
        settings
      };

      const group = await GroupRepository.create(groupData);
      const groupWithMembers = await GroupRepository.findByIdWithMembers(group.id);

      res.status(201).json({
        message: 'Group created successfully',
        group: groupWithMembers
      });
    } catch (error) {
      console.error('Error creating group:', error);
      res.status(500).json({ error: 'Failed to create group' });
    }
  }

  // Get user's groups
  static async getUserGroups(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const groups = await GroupRepository.findByUserId(userId);

      res.json({
        groups,
        count: groups.length
      });
    } catch (error) {
      console.error('Error fetching user groups:', error);
      res.status(500).json({ error: 'Failed to fetch groups' });
    }
  }

  // Get group by ID
  static async getGroup(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const group = await GroupRepository.findByIdWithMembers(id);
      if (!group) {
        res.status(404).json({ error: 'Group not found' });
        return;
      }

      // Check if user is a member of the group
      const isMember = await GroupRepository.isMember(id, userId);
      if (!isMember) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      res.json({ group });
    } catch (error) {
      console.error('Error fetching group:', error);
      res.status(500).json({ error: 'Failed to fetch group' });
    }
  }

  // Update group
  static async updateGroup(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { name, description, settings } = req.body;
      const userId = req.user!.id;

      const group = await GroupRepository.findById(id);
      if (!group) {
        res.status(404).json({ error: 'Group not found' });
        return;
      }

      // Check if user has permission to edit group settings
      const userRole = await GroupRepository.getUserRole(id, userId);
      if (!userRole || !GroupModel.canUserPerformAction(userRole, 'edit_settings')) {
        res.status(403).json({ error: 'Insufficient permissions' });
        return;
      }

      // Validate updates
      if (name !== undefined) {
        const nameValidation = GroupModel.validateGroupName(name);
        if (!nameValidation.valid) {
          res.status(400).json({
            error: 'Validation failed',
            details: nameValidation.errors
          });
          return;
        }
      }

      if (description !== undefined) {
        const descValidation = GroupModel.validateDescription(description);
        if (!descValidation.valid) {
          res.status(400).json({
            error: 'Validation failed',
            details: descValidation.errors
          });
          return;
        }
      }

      if (settings) {
        const settingsValidation = GroupModel.validateSettings(settings);
        if (!settingsValidation.valid) {
          res.status(400).json({
            error: 'Validation failed',
            details: settingsValidation.errors
          });
          return;
        }
      }

      const updateData: UpdateGroupData = { name, description, settings };
      const updatedGroup = await GroupRepository.update(id, updateData);

      if (!updatedGroup) {
        res.status(404).json({ error: 'Group not found' });
        return;
      }

      const groupWithMembers = await GroupRepository.findByIdWithMembers(id);
      res.json({
        message: 'Group updated successfully',
        group: groupWithMembers
      });
    } catch (error) {
      console.error('Error updating group:', error);
      res.status(500).json({ error: 'Failed to update group' });
    }
  }

  // Delete group
  static async deleteGroup(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const group = await GroupRepository.findById(id);
      if (!group) {
        res.status(404).json({ error: 'Group not found' });
        return;
      }

      // Check if user has permission to delete group
      const userRole = await GroupRepository.getUserRole(id, userId);
      if (!userRole || !GroupModel.canUserPerformAction(userRole, 'delete_group')) {
        res.status(403).json({ error: 'Insufficient permissions' });
        return;
      }

      const deleted = await GroupRepository.delete(id);
      if (!deleted) {
        res.status(404).json({ error: 'Group not found' });
        return;
      }

      res.json({ message: 'Group deleted successfully' });
    } catch (error) {
      console.error('Error deleting group:', error);
      res.status(500).json({ error: 'Failed to delete group' });
    }
  }

  // Invite user to group
  static async inviteUser(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { email, role } = req.body;
      const userId = req.user!.id;

      if (!email || !role) {
        res.status(400).json({ error: 'Email and role are required' });
        return;
      }

      if (!GroupModel.validateRole(role)) {
        res.status(400).json({ error: 'Invalid role' });
        return;
      }

      const group = await GroupRepository.findById(id);
      if (!group) {
        res.status(404).json({ error: 'Group not found' });
        return;
      }

      // Check if user has permission to invite
      const userRole = await GroupRepository.getUserRole(id, userId);
      if (!userRole || !GroupModel.canUserPerformAction(userRole, 'invite')) {
        res.status(403).json({ error: 'Insufficient permissions' });
        return;
      }

      // Check if group allows member invites (if user is not admin)
      if (userRole !== 'admin' && !group.settings.allowMemberInvites) {
        res.status(403).json({ error: 'Member invitations are not allowed in this group' });
        return;
      }

      // Check if user is already a member
      const existingUser = await UserRepository.findByEmail(email);
      if (existingUser) {
        const isMember = await GroupRepository.isMember(id, existingUser.id);
        if (isMember) {
          res.status(400).json({ error: 'User is already a member of this group' });
          return;
        }
      }

      // Check member limit
      const groupWithMembers = await GroupRepository.findByIdWithMembers(id);
      if (groupWithMembers && groupWithMembers.memberCount >= group.settings.maxMembers) {
        res.status(400).json({ error: 'Group has reached maximum member limit' });
        return;
      }

      // Create invitation
      const invitation = await GroupRepository.createInvitation({
        groupId: id,
        invitedBy: userId,
        invitedEmail: email,
        role
      });

      // TODO: Send invitation email
      console.log(`Invitation created for ${email} to join group ${group.name}`);

      res.status(201).json({
        message: 'Invitation sent successfully',
        invitation: {
          id: invitation.id,
          email: invitation.invitedEmail,
          role: invitation.role,
          expiresAt: invitation.expiresAt
        }
      });
    } catch (error) {
      console.error('Error inviting user:', error);
      res.status(500).json({ error: 'Failed to send invitation' });
    }
  }

  // Accept group invitation
  static async acceptInvitation(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { token } = req.params;
      const userId = req.user!.id;
      const userEmail = req.user!.email;

      const invitation = await GroupRepository.findInvitationByToken(token);
      if (!invitation) {
        res.status(404).json({ error: 'Invalid or expired invitation' });
        return;
      }

      // Check if invitation is for the current user
      if (invitation.invitedEmail !== userEmail) {
        res.status(403).json({ error: 'This invitation is not for your email address' });
        return;
      }

      const group = await GroupRepository.findById(invitation.groupId);
      if (!group) {
        res.status(404).json({ error: 'Group not found' });
        return;
      }

      // Check if user is already a member
      const isMember = await GroupRepository.isMember(invitation.groupId, userId);
      if (isMember) {
        res.status(400).json({ error: 'You are already a member of this group' });
        return;
      }

      // Add user to group
      await GroupRepository.addMember(invitation.groupId, userId, invitation.role);

      // Delete the invitation
      await GroupRepository.deleteInvitation(invitation.id);

      const groupWithMembers = await GroupRepository.findByIdWithMembers(invitation.groupId);

      res.json({
        message: 'Successfully joined group',
        group: groupWithMembers
      });
    } catch (error) {
      console.error('Error accepting invitation:', error);
      res.status(500).json({ error: 'Failed to accept invitation' });
    }
  }

  // Remove member from group
  static async removeMember(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id, memberId } = req.params;
      const userId = req.user!.id;

      const group = await GroupRepository.findById(id);
      if (!group) {
        res.status(404).json({ error: 'Group not found' });
        return;
      }

      // Check if user has permission to remove members
      const userRole = await GroupRepository.getUserRole(id, userId);
      if (!userRole || !GroupModel.canUserPerformAction(userRole, 'remove_member')) {
        res.status(403).json({ error: 'Insufficient permissions' });
        return;
      }

      // Cannot remove the group owner
      if (memberId === group.ownerId) {
        res.status(400).json({ error: 'Cannot remove group owner' });
        return;
      }

      // Check if member exists in group
      const memberRole = await GroupRepository.getUserRole(id, memberId);
      if (!memberRole) {
        res.status(404).json({ error: 'Member not found in group' });
        return;
      }

      // Non-admin users can only remove themselves
      if (userRole !== 'admin' && userId !== memberId) {
        res.status(403).json({ error: 'You can only remove yourself from the group' });
        return;
      }

      const removed = await GroupRepository.removeMember(id, memberId);
      if (!removed) {
        res.status(404).json({ error: 'Member not found in group' });
        return;
      }

      res.json({ message: 'Member removed successfully' });
    } catch (error) {
      console.error('Error removing member:', error);
      res.status(500).json({ error: 'Failed to remove member' });
    }
  }

  // Update member role
  static async updateMemberRole(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id, memberId } = req.params;
      const { role } = req.body;
      const userId = req.user!.id;

      if (!role || !GroupModel.validateRole(role)) {
        res.status(400).json({ error: 'Valid role is required' });
        return;
      }

      const group = await GroupRepository.findById(id);
      if (!group) {
        res.status(404).json({ error: 'Group not found' });
        return;
      }

      // Check if user has permission to update member roles
      const userRole = await GroupRepository.getUserRole(id, userId);
      if (!userRole || userRole !== 'admin') {
        res.status(403).json({ error: 'Only admins can update member roles' });
        return;
      }

      // Cannot change the group owner's role
      if (memberId === group.ownerId) {
        res.status(400).json({ error: 'Cannot change group owner role' });
        return;
      }

      // Check if member exists in group
      const memberRole = await GroupRepository.getUserRole(id, memberId);
      if (!memberRole) {
        res.status(404).json({ error: 'Member not found in group' });
        return;
      }

      const updated = await GroupRepository.updateMemberRole(id, memberId, role);
      if (!updated) {
        res.status(404).json({ error: 'Member not found in group' });
        return;
      }

      res.json({ message: 'Member role updated successfully' });
    } catch (error) {
      console.error('Error updating member role:', error);
      res.status(500).json({ error: 'Failed to update member role' });
    }
  }

  // Get group invitations (for admins)
  static async getGroupInvitations(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const group = await GroupRepository.findById(id);
      if (!group) {
        res.status(404).json({ error: 'Group not found' });
        return;
      }

      // Check if user has permission to view invitations
      const userRole = await GroupRepository.getUserRole(id, userId);
      if (!userRole || userRole !== 'admin') {
        res.status(403).json({ error: 'Only admins can view group invitations' });
        return;
      }

      const invitations = await GroupRepository.getGroupInvitations(id);

      res.json({
        invitations: invitations.map(inv => ({
          id: inv.id,
          email: inv.invitedEmail,
          role: inv.role,
          expiresAt: inv.expiresAt,
          createdAt: inv.createdAt
        }))
      });
    } catch (error) {
      console.error('Error fetching group invitations:', error);
      res.status(500).json({ error: 'Failed to fetch invitations' });
    }
  }
}
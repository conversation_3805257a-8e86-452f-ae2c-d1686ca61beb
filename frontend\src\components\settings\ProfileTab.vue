<template>
  <div class="profile-tab">
    <div class="notification is-info is-light" v-if="successMessage">
      <button class="delete" @click="successMessage = ''"></button>
      {{ successMessage }}
    </div>

    <div class="notification is-danger is-light" v-if="errorMessage">
      <button class="delete" @click="errorMessage = ''"></button>
      {{ errorMessage }}
    </div>

    <!-- Profile Information -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-user"></i>
        </span>
        Profile Information
      </h3>

      <div class="columns">
        <div class="column is-one-third">
          <div class="avatar-section">
            <figure class="image is-128x128 avatar-container">
              <img 
                :src="profileData.avatar_url || defaultAvatar" 
                :alt="profileData.display_name"
                class="is-rounded avatar-image"
              >
            </figure>
            <div class="file is-small has-name">
              <label class="file-label">
                <input 
                  class="file-input" 
                  type="file" 
                  accept="image/*"
                  @change="handleAvatarUpload"
                  :disabled="isLoading"
                >
                <span class="file-cta">
                  <span class="file-icon">
                    <i class="fas fa-upload"></i>
                  </span>
                  <span class="file-label">
                    Change Avatar
                  </span>
                </span>
              </label>
            </div>
          </div>
        </div>

        <div class="column">
          <div class="field">
            <label class="label">Display Name</label>
            <div class="control">
              <input 
                class="input" 
                type="text" 
                v-model="profileData.display_name"
                :disabled="isLoading"
                placeholder="Enter your display name"
                maxlength="50"
              >
            </div>
            <p class="help">This is how your name will appear to other users</p>
          </div>

          <div class="field">
            <label class="label">Email</label>
            <div class="control">
              <input 
                class="input" 
                type="email" 
                :value="user?.email"
                disabled
                readonly
              >
            </div>
            <p class="help">Email cannot be changed. Contact support if needed.</p>
          </div>

          <div class="field">
            <label class="label">Avatar URL</label>
            <div class="control">
              <input 
                class="input" 
                type="url" 
                v-model="profileData.avatar_url"
                :disabled="isLoading"
                placeholder="https://example.com/avatar.jpg"
              >
            </div>
            <p class="help">Or provide a direct URL to your avatar image</p>
          </div>

          <div class="field is-grouped">
            <div class="control">
              <button 
                class="button is-primary"
                @click="updateProfile"
                :disabled="isLoading || !hasChanges"
                :class="{ 'is-loading': isLoading }"
              >
                Save Changes
              </button>
            </div>
            <div class="control">
              <button 
                class="button"
                @click="resetChanges"
                :disabled="isLoading || !hasChanges"
              >
                Reset
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Account Status -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-info-circle"></i>
        </span>
        Account Status
      </h3>

      <div class="field">
        <label class="label">Email Verification</label>
        <div class="control">
          <span class="tag" :class="user?.emailVerified ? 'is-success' : 'is-warning'">
            <span class="icon">
              <i :class="user?.emailVerified ? 'fas fa-check' : 'fas fa-exclamation-triangle'"></i>
            </span>
            <span>{{ user?.emailVerified ? 'Verified' : 'Not Verified' }}</span>
          </span>
        </div>
        <p class="help" v-if="!user?.emailVerified">
          Please check your email and click the verification link
        </p>
      </div>

      <div class="field" v-if="user?.created_at">
        <label class="label">Member Since</label>
        <div class="control">
          <span class="tag is-light">
            {{ formatDate(user.created_at) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { userService } from '../../services/userService'

const authStore = useAuthStore()
const user = computed(() => authStore.user)

const isLoading = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const profileData = reactive({
  display_name: '',
  avatar_url: ''
})

const originalData = reactive({
  display_name: '',
  avatar_url: ''
})

const defaultAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjgiIGhlaWdodD0iMTI4IiBmaWxsPSIjZTVlNWU1Ii8+CjxjaXJjbGUgY3g9IjY0IiBjeT0iNDgiIHI9IjIwIiBmaWxsPSIjNjY2NjY2Ii8+CjxwYXRoIGQ9Ik0zMiA5NmMwLTE3LjY3MyAxNC4zMjctMzIgMzItMzJzMzIgMTQuMzI3IDMyIDMydjMySDMyVjk2eiIgZmlsbD0iIzY2NjY2NiIvPgo8L3N2Zz4K'

const hasChanges = computed(() => {
  return profileData.display_name !== originalData.display_name ||
         profileData.avatar_url !== originalData.avatar_url
})

const showSuccess = (message: string) => {
  successMessage.value = message
  errorMessage.value = ''
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
}

const showError = (message: string) => {
  errorMessage.value = message
  successMessage.value = ''
}

const loadProfileData = () => {
  if (user.value) {
    profileData.display_name = user.value.displayName || ''
    profileData.avatar_url = user.value.avatarUrl || ''
    
    // Store original data for comparison
    originalData.display_name = profileData.display_name
    originalData.avatar_url = profileData.avatar_url
  }
}

const updateProfile = async () => {
  if (!hasChanges.value) return

  isLoading.value = true
  try {
    const result = await userService.updateProfile({
      display_name: profileData.display_name.trim(),
      avatar_url: profileData.avatar_url.trim() || undefined
    })

    if (result.success && result.data) {
      // Update the auth store with new user data
      authStore.updateUser({
        displayName: result.data.user.display_name,
        avatarUrl: result.data.user.avatar_url
      })
      
      // Update original data
      originalData.display_name = profileData.display_name
      originalData.avatar_url = profileData.avatar_url
      
      showSuccess('Profile updated successfully')
    } else {
      showError(result.error || 'Failed to update profile')
    }
  } catch (error) {
    showError('Failed to update profile')
  } finally {
    isLoading.value = false
  }
}

const resetChanges = () => {
  profileData.display_name = originalData.display_name
  profileData.avatar_url = originalData.avatar_url
}

const handleAvatarUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return

  // Validate file type
  if (!file.type.startsWith('image/')) {
    showError('Please select a valid image file')
    return
  }

  // Validate file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    showError('Image file must be smaller than 5MB')
    return
  }

  try {
    // Convert to base64 for now (in a real app, you'd upload to a file service)
    const reader = new FileReader()
    reader.onload = (e) => {
      profileData.avatar_url = e.target?.result as string
    }
    reader.readAsDataURL(file)
  } catch (error) {
    showError('Failed to process image file')
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

onMounted(() => {
  loadProfileData()
})
</script>

<style scoped>
.profile-tab {
  max-width: 700px;
}

.field-group {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e5e5;
}

.field-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.subtitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #363636;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.avatar-container {
  margin: 0 auto;
}

.avatar-image {
  width: 128px;
  height: 128px;
  object-fit: cover;
  border: 3px solid #e5e5e5;
}

.file {
  width: 100%;
}

.file-label {
  width: 100%;
}

.file-cta {
  width: 100%;
  justify-content: center;
}

.field {
  margin-bottom: 1rem;
}

.label {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.help {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.notification {
  margin-bottom: 1rem;
}

/* Dark mode styles */
:global(.dark) .field-group {
  border-bottom-color: #404040;
}

:global(.dark) .subtitle {
  color: #e5e5e5;
}

:global(.dark) .help {
  color: #9ca3af;
}

:global(.dark) .input {
  background: #374151;
  border-color: #4b5563;
  color: #e5e5e5;
}

:global(.dark) .input:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 0.125em rgba(99, 102, 241, 0.25);
}

:global(.dark) .input:disabled {
  background: #1f2937;
  color: #9ca3af;
}

:global(.dark) .label {
  color: #e5e5e5;
}

:global(.dark) .avatar-image {
  border-color: #4b5563;
}

:global(.dark) .file-cta {
  background: #374151;
  border-color: #4b5563;
  color: #e5e5e5;
}

:global(.dark) .file-cta:hover {
  background: #4b5563;
}
</style>
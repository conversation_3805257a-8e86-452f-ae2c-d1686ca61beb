// Test script to verify NoteList dropdown functionality
console.log('Testing NoteList dropdown functionality...');

// Function to simulate clicking on dropdown buttons
function testDropdowns() {
  // Wait for the page to load
  setTimeout(() => {
    console.log('Looking for dropdown buttons...');
    
    // Find all dropdown trigger buttons
    const dropdownButtons = document.querySelectorAll('.note-actions .dropdown-trigger button');
    console.log(`Found ${dropdownButtons.length} dropdown buttons`);
    
    if (dropdownButtons.length > 0) {
      // Test clicking the first dropdown
      const firstButton = dropdownButtons[0];
      console.log('Clicking first dropdown button...');
      firstButton.click();
      
      // Check if dropdown menu appears
      setTimeout(() => {
        const activeDropdown = document.querySelector('.dropdown.is-active');
        if (activeDropdown) {
          console.log('✅ Dropdown opened successfully!');
          
          // Check if dropdown menu is visible
          const dropdownMenu = activeDropdown.querySelector('.dropdown-menu');
          if (dropdownMenu && window.getComputedStyle(dropdownMenu).display !== 'none') {
            console.log('✅ Dropdown menu is visible!');
            
            // Test clicking a dropdown item
            const firstItem = dropdownMenu.querySelector('.dropdown-item');
            if (firstItem) {
              console.log('✅ Dropdown items found!');
              console.log('Dropdown test completed successfully!');
            } else {
              console.log('❌ No dropdown items found');
            }
          } else {
            console.log('❌ Dropdown menu is not visible');
          }
        } else {
          console.log('❌ Dropdown did not open');
        }
      }, 100);
    } else {
      console.log('❌ No dropdown buttons found');
    }
  }, 1000);
}

// Run the test when the page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', testDropdowns);
} else {
  testDropdowns();
}
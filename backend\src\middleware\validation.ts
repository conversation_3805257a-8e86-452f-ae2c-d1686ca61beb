import { Request, Response, NextFunction } from 'express';
import { UserModel } from '../models/User';
import { NoteModel } from '../models/Note';

export interface ValidationError {
  field: string;
  message: string;
}

export const validateRegistration = (req: Request, res: Response, next: NextFunction): void => {
  const { email, password, display_name } = req.body;
  const errors: ValidationError[] = [];

  // Email validation
  if (!email) {
    errors.push({ field: 'email', message: 'Email is required' });
  } else if (!UserModel.validateEmail(email)) {
    errors.push({ field: 'email', message: 'Invalid email format' });
  }

  // Password validation
  if (!password) {
    errors.push({ field: 'password', message: 'Password is required' });
  } else {
    const passwordValidation = UserModel.validatePassword(password);
    if (!passwordValidation.valid) {
      passwordValidation.errors.forEach(error => {
        errors.push({ field: 'password', message: error });
      });
    }
  }

  // Display name validation
  if (!display_name) {
    errors.push({ field: 'display_name', message: 'Display name is required' });
  } else if (display_name.trim().length < 2) {
    errors.push({ field: 'display_name', message: 'Display name must be at least 2 characters long' });
  } else if (display_name.trim().length > 50) {
    errors.push({ field: 'display_name', message: 'Display name must be less than 50 characters' });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  req.body.email = email.trim().toLowerCase();
  req.body.display_name = display_name.trim();
  
  next();
};

export const validateLogin = (req: Request, res: Response, next: NextFunction): void => {
  const { email, password } = req.body;
  const errors: ValidationError[] = [];

  if (!email) {
    errors.push({ field: 'email', message: 'Email is required' });
  } else if (!UserModel.validateEmail(email)) {
    errors.push({ field: 'email', message: 'Invalid email format' });
  }

  if (!password) {
    errors.push({ field: 'password', message: 'Password is required' });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  req.body.email = email.trim().toLowerCase();
  
  next();
};

export const validatePasswordReset = (req: Request, res: Response, next: NextFunction): void => {
  const { email } = req.body;
  const errors: ValidationError[] = [];

  if (!email) {
    errors.push({ field: 'email', message: 'Email is required' });
  } else if (!UserModel.validateEmail(email)) {
    errors.push({ field: 'email', message: 'Invalid email format' });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  req.body.email = email.trim().toLowerCase();
  
  next();
};

export const validatePasswordResetConfirm = (req: Request, res: Response, next: NextFunction): void => {
  const { token, password } = req.body;
  const errors: ValidationError[] = [];

  if (!token) {
    errors.push({ field: 'token', message: 'Reset token is required' });
  }

  if (!password) {
    errors.push({ field: 'password', message: 'New password is required' });
  } else {
    const passwordValidation = UserModel.validatePassword(password);
    if (!passwordValidation.valid) {
      passwordValidation.errors.forEach(error => {
        errors.push({ field: 'password', message: error });
      });
    }
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  next();
};

export const validateRefreshToken = (req: Request, res: Response, next: NextFunction): void => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    res.status(400).json({
      error: 'Refresh token is required',
      code: 'VALIDATION_ERROR',
      details: [{ field: 'refreshToken', message: 'Refresh token is required' }]
    });
    return;
  }

  next();
};

export const validateGoogleAuth = (req: Request, res: Response, next: NextFunction): void => {
  const { credential } = req.body;

  if (!credential) {
    res.status(400).json({
      error: 'Google credential is required',
      code: 'VALIDATION_ERROR',
      details: [{ field: 'credential', message: 'Google credential is required' }]
    });
    return;
  }

  next();
};

// Note validation functions
export const validateCreateNote = (req: Request, res: Response, next: NextFunction): void => {
  const { title, content, noteType, tags } = req.body;
  const errors: ValidationError[] = [];

  // Title validation
  if (!title) {
    errors.push({ field: 'title', message: 'Title is required' });
  } else {
    const titleValidation = NoteModel.validateTitle(title);
    if (!titleValidation.valid) {
      titleValidation.errors.forEach(error => {
        errors.push({ field: 'title', message: error });
      });
    }
  }

  // Content validation
  if (content === undefined || content === null) {
    errors.push({ field: 'content', message: 'Content is required' });
  } else if (noteType) {
    const contentValidation = NoteModel.validateContent(content, noteType);
    if (!contentValidation.valid) {
      contentValidation.errors.forEach(error => {
        errors.push({ field: 'content', message: error });
      });
    }
  }

  // Note type validation
  if (!noteType) {
    errors.push({ field: 'noteType', message: 'Note type is required' });
  } else if (!NoteModel.validateNoteType(noteType)) {
    errors.push({ field: 'noteType', message: 'Invalid note type. Must be richtext, markdown, or kanban' });
  }

  // Tags validation
  if (tags !== undefined) {
    if (!Array.isArray(tags)) {
      errors.push({ field: 'tags', message: 'Tags must be an array' });
    } else {
      tags.forEach((tag, index) => {
        if (typeof tag !== 'string') {
          errors.push({ field: `tags[${index}]`, message: 'Tag must be a string' });
        } else if (tag.trim().length === 0) {
          errors.push({ field: `tags[${index}]`, message: 'Tag cannot be empty' });
        } else if (tag.length > 50) {
          errors.push({ field: `tags[${index}]`, message: 'Tag must be less than 50 characters' });
        }
      });
    }
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (title) req.body.title = title.trim();
  if (tags && Array.isArray(tags)) {
    req.body.tags = tags.map((tag: string) => tag.trim()).filter((tag: string) => tag.length > 0);
  }

  next();
};

export const validateUpdateNote = (req: Request, res: Response, next: NextFunction): void => {
  const { title, content, tags } = req.body;
  const errors: ValidationError[] = [];

  // Title validation (optional for updates)
  if (title !== undefined) {
    const titleValidation = NoteModel.validateTitle(title);
    if (!titleValidation.valid) {
      titleValidation.errors.forEach(error => {
        errors.push({ field: 'title', message: error });
      });
    }
  }

  // Content validation (optional for updates, but validate if provided)
  if (content !== undefined) {
    // Note: We can't validate content type here without knowing the note type
    // This will be handled in the controller
    if (typeof content !== 'string') {
      errors.push({ field: 'content', message: 'Content must be a string' });
    }
  }

  // Tags validation
  if (tags !== undefined) {
    if (!Array.isArray(tags)) {
      errors.push({ field: 'tags', message: 'Tags must be an array' });
    } else {
      tags.forEach((tag, index) => {
        if (typeof tag !== 'string') {
          errors.push({ field: `tags[${index}]`, message: 'Tag must be a string' });
        } else if (tag.trim().length === 0) {
          errors.push({ field: `tags[${index}]`, message: 'Tag cannot be empty' });
        } else if (tag.length > 50) {
          errors.push({ field: `tags[${index}]`, message: 'Tag must be less than 50 characters' });
        }
      });
    }
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (title !== undefined) req.body.title = title.trim();
  if (tags && Array.isArray(tags)) {
    req.body.tags = tags.map((tag: string) => tag.trim()).filter((tag: string) => tag.length > 0);
  }

  next();
};

export const validateCreateTag = (req: Request, res: Response, next: NextFunction): void => {
  const { name } = req.body;
  const errors: ValidationError[] = [];

  if (!name) {
    errors.push({ field: 'name', message: 'Tag name is required' });
  } else if (typeof name !== 'string') {
    errors.push({ field: 'name', message: 'Tag name must be a string' });
  } else if (name.trim().length === 0) {
    errors.push({ field: 'name', message: 'Tag name cannot be empty' });
  } else if (name.length > 50) {
    errors.push({ field: 'name', message: 'Tag name must be less than 50 characters' });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  req.body.name = name.trim();

  next();
};

// Group validation functions
export const validateCreateGroup = (req: Request, res: Response, next: NextFunction): void => {
  const { name, description, settings } = req.body;
  const errors: ValidationError[] = [];

  if (!name || typeof name !== 'string' || name.trim().length === 0) {
    errors.push({ field: 'name', message: 'Group name is required' });
  } else if (name.length > 100) {
    errors.push({ field: 'name', message: 'Group name must be less than 100 characters' });
  } else if (name.length < 3) {
    errors.push({ field: 'name', message: 'Group name must be at least 3 characters' });
  }

  if (description && (typeof description !== 'string' || description.length > 500)) {
    errors.push({ field: 'description', message: 'Group description must be less than 500 characters' });
  }

  if (settings && typeof settings !== 'object') {
    errors.push({ field: 'settings', message: 'Settings must be an object' });
  }

  if (settings?.maxMembers && (typeof settings.maxMembers !== 'number' || settings.maxMembers < 1 || settings.maxMembers > 1000)) {
    errors.push({ field: 'settings.maxMembers', message: 'Maximum members must be between 1 and 1000' });
  }

  if (settings?.defaultNotePermissions && !['view', 'edit'].includes(settings.defaultNotePermissions)) {
    errors.push({ field: 'settings.defaultNotePermissions', message: 'Default note permissions must be either "view" or "edit"' });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  req.body.name = name.trim();
  if (description) req.body.description = description.trim();

  next();
};

export const validateUpdateGroup = (req: Request, res: Response, next: NextFunction): void => {
  const { name, description, settings } = req.body;
  const errors: ValidationError[] = [];

  if (name !== undefined) {
    if (typeof name !== 'string' || name.trim().length === 0) {
      errors.push({ field: 'name', message: 'Group name cannot be empty' });
    } else if (name.length > 100) {
      errors.push({ field: 'name', message: 'Group name must be less than 100 characters' });
    } else if (name.length < 3) {
      errors.push({ field: 'name', message: 'Group name must be at least 3 characters' });
    }
  }

  if (description !== undefined && description !== null) {
    if (typeof description !== 'string' || description.length > 500) {
      errors.push({ field: 'description', message: 'Group description must be less than 500 characters' });
    }
  }

  if (settings && typeof settings !== 'object') {
    errors.push({ field: 'settings', message: 'Settings must be an object' });
  }

  if (settings?.maxMembers && (typeof settings.maxMembers !== 'number' || settings.maxMembers < 1 || settings.maxMembers > 1000)) {
    errors.push({ field: 'settings.maxMembers', message: 'Maximum members must be between 1 and 1000' });
  }

  if (settings?.defaultNotePermissions && !['view', 'edit'].includes(settings.defaultNotePermissions)) {
    errors.push({ field: 'settings.defaultNotePermissions', message: 'Default note permissions must be either "view" or "edit"' });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (name !== undefined) req.body.name = name.trim();
  if (description !== undefined && description !== null) req.body.description = description.trim();

  next();
};

export const validateInviteUser = (req: Request, res: Response, next: NextFunction): void => {
  const { email, role } = req.body;
  const errors: ValidationError[] = [];

  if (!email || typeof email !== 'string') {
    errors.push({ field: 'email', message: 'Email is required' });
  } else if (!UserModel.validateEmail(email)) {
    errors.push({ field: 'email', message: 'Invalid email format' });
  }

  if (!role || !['admin', 'editor', 'viewer'].includes(role)) {
    errors.push({ field: 'role', message: 'Valid role is required (admin, editor, or viewer)' });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  req.body.email = email.trim().toLowerCase();

  next();
};

export const validateUpdateMemberRole = (req: Request, res: Response, next: NextFunction): void => {
  const { role } = req.body;
  const errors: ValidationError[] = [];

  if (!role || !['admin', 'editor', 'viewer'].includes(role)) {
    errors.push({ field: 'role', message: 'Valid role is required (admin, editor, or viewer)' });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  next();
};

// Share validation functions
export const validateCreateShare = (req: Request, res: Response, next: NextFunction): void => {
  const { accessLevel, permissions, expiresAt, password, allowedIps, watermark } = req.body;
  const errors: ValidationError[] = [];

  // Access level validation
  if (!accessLevel) {
    errors.push({ field: 'accessLevel', message: 'Access level is required' });
  } else if (!['private', 'shared', 'unlisted', 'public'].includes(accessLevel)) {
    errors.push({ field: 'accessLevel', message: 'Invalid access level. Must be private, shared, unlisted, or public' });
  }

  // Permissions validation
  if (!permissions || !Array.isArray(permissions) || permissions.length === 0) {
    errors.push({ field: 'permissions', message: 'At least one permission is required' });
  } else {
    const validPermissions = ['view', 'comment', 'edit'];
    permissions.forEach((permission, index) => {
      if (!validPermissions.includes(permission)) {
        errors.push({ field: `permissions[${index}]`, message: 'Invalid permission. Must be view, comment, or edit' });
      }
    });
  }

  // Expiration date validation
  if (expiresAt && new Date(expiresAt) <= new Date()) {
    errors.push({ field: 'expiresAt', message: 'Expiration date must be in the future' });
  }

  // Password validation
  if (password && (typeof password !== 'string' || password.length < 6)) {
    errors.push({ field: 'password', message: 'Password must be at least 6 characters long' });
  }

  // IP addresses validation
  if (allowedIps && Array.isArray(allowedIps)) {
    allowedIps.forEach((ip, index) => {
      if (typeof ip !== 'string' || !isValidIpAddress(ip)) {
        errors.push({ field: `allowedIps[${index}]`, message: 'Invalid IP address format' });
      }
    });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (password) req.body.password = password.trim();
  if (allowedIps && Array.isArray(allowedIps)) {
    req.body.allowedIps = allowedIps.map((ip: string) => ip.trim());
  }

  next();
};

export const validateUpdateShare = (req: Request, res: Response, next: NextFunction): void => {
  const { accessLevel, permissions, expiresAt, password, allowedIps, watermark } = req.body;
  const errors: ValidationError[] = [];

  // Access level validation (optional for updates)
  if (accessLevel !== undefined && !['private', 'shared', 'unlisted', 'public'].includes(accessLevel)) {
    errors.push({ field: 'accessLevel', message: 'Invalid access level. Must be private, shared, unlisted, or public' });
  }

  // Permissions validation (optional for updates)
  if (permissions !== undefined) {
    if (!Array.isArray(permissions) || permissions.length === 0) {
      errors.push({ field: 'permissions', message: 'At least one permission is required' });
    } else {
      const validPermissions = ['view', 'comment', 'edit'];
      permissions.forEach((permission, index) => {
        if (!validPermissions.includes(permission)) {
          errors.push({ field: `permissions[${index}]`, message: 'Invalid permission. Must be view, comment, or edit' });
        }
      });
    }
  }

  // Expiration date validation
  if (expiresAt !== undefined && expiresAt && new Date(expiresAt) <= new Date()) {
    errors.push({ field: 'expiresAt', message: 'Expiration date must be in the future' });
  }

  // Password validation
  if (password !== undefined && password && (typeof password !== 'string' || password.length < 6)) {
    errors.push({ field: 'password', message: 'Password must be at least 6 characters long' });
  }

  // IP addresses validation
  if (allowedIps !== undefined && allowedIps && Array.isArray(allowedIps)) {
    allowedIps.forEach((ip, index) => {
      if (typeof ip !== 'string' || !isValidIpAddress(ip)) {
        errors.push({ field: `allowedIps[${index}]`, message: 'Invalid IP address format' });
      }
    });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (password !== undefined && password) req.body.password = password.trim();
  if (allowedIps !== undefined && allowedIps && Array.isArray(allowedIps)) {
    req.body.allowedIps = allowedIps.map((ip: string) => ip.trim());
  }

  next();
};

// Helper function to validate IP addresses
function isValidIpAddress(ip: string): boolean {
  // Basic IP validation (IPv4 and IPv6)
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  const ipv4CidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/;
  
  return ipv4Regex.test(ip) || ipv6Regex.test(ip) || ipv4CidrRegex.test(ip);
}

// User settings validation functions
export const validateUpdateSettings = (req: Request, res: Response, next: NextFunction): void => {
  const { theme, language, timezone, autoSaveInterval, notifications } = req.body;
  const errors: ValidationError[] = [];

  // Theme validation
  if (theme !== undefined && !['light', 'dark', 'auto'].includes(theme)) {
    errors.push({ field: 'theme', message: 'Theme must be one of: light, dark, auto' });
  }

  // Language validation
  if (language !== undefined) {
    if (typeof language !== 'string' || language.trim().length === 0) {
      errors.push({ field: 'language', message: 'Language must be a non-empty string' });
    } else if (language.length > 10) {
      errors.push({ field: 'language', message: 'Language code must be less than 10 characters' });
    }
  }

  // Timezone validation
  if (timezone !== undefined) {
    if (typeof timezone !== 'string' || timezone.trim().length === 0) {
      errors.push({ field: 'timezone', message: 'Timezone must be a non-empty string' });
    } else if (timezone.length > 50) {
      errors.push({ field: 'timezone', message: 'Timezone must be less than 50 characters' });
    }
  }

  // Auto-save interval validation
  if (autoSaveInterval !== undefined) {
    if (typeof autoSaveInterval !== 'number' || autoSaveInterval < 1000 || autoSaveInterval > 300000) {
      errors.push({ field: 'autoSaveInterval', message: 'Auto-save interval must be between 1000ms (1s) and 300000ms (5min)' });
    }
  }

  // Notifications validation
  if (notifications !== undefined) {
    if (typeof notifications !== 'object' || notifications === null) {
      errors.push({ field: 'notifications', message: 'Notifications must be an object' });
    } else {
      if (notifications.email !== undefined && typeof notifications.email !== 'boolean') {
        errors.push({ field: 'notifications.email', message: 'Email notifications setting must be a boolean' });
      }
      
      if (notifications.push !== undefined && typeof notifications.push !== 'boolean') {
        errors.push({ field: 'notifications.push', message: 'Push notifications setting must be a boolean' });
      }
      
      if (notifications.mentions !== undefined && typeof notifications.mentions !== 'boolean') {
        errors.push({ field: 'notifications.mentions', message: 'Mentions notifications setting must be a boolean' });
      }
    }
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (language !== undefined) req.body.language = language.trim();
  if (timezone !== undefined) req.body.timezone = timezone.trim();

  next();
};

export const validateUpdateProfile = (req: Request, res: Response, next: NextFunction): void => {
  const { display_name, avatar_url } = req.body;
  const errors: ValidationError[] = [];

  // Display name validation
  if (display_name !== undefined) {
    if (typeof display_name !== 'string') {
      errors.push({ field: 'display_name', message: 'Display name must be a string' });
    } else if (display_name.trim().length === 0) {
      errors.push({ field: 'display_name', message: 'Display name cannot be empty' });
    } else if (display_name.trim().length < 2) {
      errors.push({ field: 'display_name', message: 'Display name must be at least 2 characters long' });
    } else if (display_name.length > 50) {
      errors.push({ field: 'display_name', message: 'Display name must be less than 50 characters' });
    }
  }

  // Avatar URL validation
  if (avatar_url !== undefined) {
    if (typeof avatar_url !== 'string') {
      errors.push({ field: 'avatar_url', message: 'Avatar URL must be a string' });
    } else if (avatar_url.length > 500) {
      errors.push({ field: 'avatar_url', message: 'Avatar URL must be less than 500 characters' });
    }
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (display_name !== undefined) req.body.display_name = display_name.trim();
  if (avatar_url !== undefined) req.body.avatar_url = avatar_url.trim();

  next();
};

// Export validation functions
export const validateExportNote = (req: Request, res: Response, next: NextFunction): void => {
  const { format, includeMetadata, customStyles } = req.body;
  const errors: ValidationError[] = [];

  // Format validation
  if (!format) {
    errors.push({ field: 'format', message: 'Export format is required' });
  } else if (!['pdf', 'html', 'markdown'].includes(format)) {
    errors.push({ field: 'format', message: 'Invalid format. Must be pdf, html, or markdown' });
  }

  // Include metadata validation
  if (includeMetadata !== undefined && typeof includeMetadata !== 'boolean') {
    errors.push({ field: 'includeMetadata', message: 'includeMetadata must be a boolean' });
  }

  // Custom styles validation
  if (customStyles !== undefined) {
    if (typeof customStyles !== 'string') {
      errors.push({ field: 'customStyles', message: 'customStyles must be a string' });
    } else if (customStyles.length > 10000) {
      errors.push({ field: 'customStyles', message: 'customStyles must be less than 10000 characters' });
    }
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  next();
};

export const validateExportMultipleNotes = (req: Request, res: Response, next: NextFunction): void => {
  const { noteIds, format, includeMetadata, customStyles } = req.body;
  const errors: ValidationError[] = [];

  // Note IDs validation
  if (!noteIds) {
    errors.push({ field: 'noteIds', message: 'noteIds array is required' });
  } else if (!Array.isArray(noteIds)) {
    errors.push({ field: 'noteIds', message: 'noteIds must be an array' });
  } else if (noteIds.length === 0) {
    errors.push({ field: 'noteIds', message: 'At least one note ID is required' });
  } else if (noteIds.length > 100) {
    errors.push({ field: 'noteIds', message: 'Maximum 100 notes per export' });
  } else {
    noteIds.forEach((noteId, index) => {
      if (typeof noteId !== 'string' || noteId.trim().length === 0) {
        errors.push({ field: `noteIds[${index}]`, message: 'Note ID must be a non-empty string' });
      }
    });
  }

  // Format validation
  if (!format) {
    errors.push({ field: 'format', message: 'Export format is required' });
  } else if (!['pdf', 'html', 'markdown'].includes(format)) {
    errors.push({ field: 'format', message: 'Invalid format. Must be pdf, html, or markdown' });
  }

  // Include metadata validation
  if (includeMetadata !== undefined && typeof includeMetadata !== 'boolean') {
    errors.push({ field: 'includeMetadata', message: 'includeMetadata must be a boolean' });
  }

  // Custom styles validation
  if (customStyles !== undefined) {
    if (typeof customStyles !== 'string') {
      errors.push({ field: 'customStyles', message: 'customStyles must be a string' });
    } else if (customStyles.length > 10000) {
      errors.push({ field: 'customStyles', message: 'customStyles must be less than 10000 characters' });
    }
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (noteIds && Array.isArray(noteIds)) {
    req.body.noteIds = noteIds.map((id: string) => id.trim());
  }

  next();
};

// Template validation functions
export const validateCreateTemplate = (req: Request, res: Response, next: NextFunction): void => {
  const { name, description, noteType, content, isPublic, tags, metadata } = req.body;
  const errors: ValidationError[] = [];

  // Name validation
  if (!name) {
    errors.push({ field: 'name', message: 'Template name is required' });
  } else if (typeof name !== 'string') {
    errors.push({ field: 'name', message: 'Template name must be a string' });
  } else if (name.trim().length === 0) {
    errors.push({ field: 'name', message: 'Template name cannot be empty' });
  } else if (name.length > 100) {
    errors.push({ field: 'name', message: 'Template name must be less than 100 characters' });
  } else if (name.length < 3) {
    errors.push({ field: 'name', message: 'Template name must be at least 3 characters' });
  }

  // Description validation
  if (description !== undefined) {
    if (typeof description !== 'string') {
      errors.push({ field: 'description', message: 'Template description must be a string' });
    } else if (description.length > 500) {
      errors.push({ field: 'description', message: 'Template description must be less than 500 characters' });
    }
  }

  // Note type validation
  if (!noteType) {
    errors.push({ field: 'noteType', message: 'Note type is required' });
  } else if (!['richtext', 'markdown', 'kanban'].includes(noteType)) {
    errors.push({ field: 'noteType', message: 'Invalid note type. Must be richtext, markdown, or kanban' });
  }

  // Content validation
  if (content === undefined || content === null) {
    errors.push({ field: 'content', message: 'Template content is required' });
  } else if (typeof content !== 'string') {
    errors.push({ field: 'content', message: 'Template content must be a string' });
  } else if (content.length > 1000000) {
    errors.push({ field: 'content', message: 'Template content is too large (maximum 1MB)' });
  }

  // Public status validation
  if (isPublic !== undefined && typeof isPublic !== 'boolean') {
    errors.push({ field: 'isPublic', message: 'isPublic must be a boolean' });
  }

  // Tags validation
  if (tags !== undefined) {
    if (!Array.isArray(tags)) {
      errors.push({ field: 'tags', message: 'Tags must be an array' });
    } else {
      tags.forEach((tag, index) => {
        if (typeof tag !== 'string') {
          errors.push({ field: `tags[${index}]`, message: 'Tag must be a string' });
        } else if (tag.trim().length === 0) {
          errors.push({ field: `tags[${index}]`, message: 'Tag cannot be empty' });
        } else if (tag.length > 50) {
          errors.push({ field: `tags[${index}]`, message: 'Tag must be less than 50 characters' });
        }
      });
    }
  }

  // Metadata validation
  if (metadata !== undefined && (typeof metadata !== 'object' || metadata === null)) {
    errors.push({ field: 'metadata', message: 'Metadata must be an object' });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (name) req.body.name = name.trim();
  if (description) req.body.description = description.trim();
  if (tags && Array.isArray(tags)) {
    req.body.tags = tags.map((tag: string) => tag.trim()).filter((tag: string) => tag.length > 0);
  }

  next();
};

export const validateUpdateTemplate = (req: Request, res: Response, next: NextFunction): void => {
  const { name, description, content, isPublic, tags, metadata } = req.body;
  const errors: ValidationError[] = [];

  // Name validation (optional for updates)
  if (name !== undefined) {
    if (typeof name !== 'string') {
      errors.push({ field: 'name', message: 'Template name must be a string' });
    } else if (name.trim().length === 0) {
      errors.push({ field: 'name', message: 'Template name cannot be empty' });
    } else if (name.length > 100) {
      errors.push({ field: 'name', message: 'Template name must be less than 100 characters' });
    } else if (name.length < 3) {
      errors.push({ field: 'name', message: 'Template name must be at least 3 characters' });
    }
  }

  // Description validation
  if (description !== undefined) {
    if (typeof description !== 'string') {
      errors.push({ field: 'description', message: 'Template description must be a string' });
    } else if (description.length > 500) {
      errors.push({ field: 'description', message: 'Template description must be less than 500 characters' });
    }
  }

  // Content validation
  if (content !== undefined) {
    if (typeof content !== 'string') {
      errors.push({ field: 'content', message: 'Template content must be a string' });
    } else if (content.length > 1000000) {
      errors.push({ field: 'content', message: 'Template content is too large (maximum 1MB)' });
    }
  }

  // Public status validation
  if (isPublic !== undefined && typeof isPublic !== 'boolean') {
    errors.push({ field: 'isPublic', message: 'isPublic must be a boolean' });
  }

  // Tags validation
  if (tags !== undefined) {
    if (!Array.isArray(tags)) {
      errors.push({ field: 'tags', message: 'Tags must be an array' });
    } else {
      tags.forEach((tag, index) => {
        if (typeof tag !== 'string') {
          errors.push({ field: `tags[${index}]`, message: 'Tag must be a string' });
        } else if (tag.trim().length === 0) {
          errors.push({ field: `tags[${index}]`, message: 'Tag cannot be empty' });
        } else if (tag.length > 50) {
          errors.push({ field: `tags[${index}]`, message: 'Tag must be less than 50 characters' });
        }
      });
    }
  }

  // Metadata validation
  if (metadata !== undefined && (typeof metadata !== 'object' || metadata === null)) {
    errors.push({ field: 'metadata', message: 'Metadata must be an object' });
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (name !== undefined) req.body.name = name.trim();
  if (description !== undefined) req.body.description = description.trim();
  if (tags && Array.isArray(tags)) {
    req.body.tags = tags.map((tag: string) => tag.trim()).filter((tag: string) => tag.length > 0);
  }

  next();
};

export const validateUseTemplate = (req: Request, res: Response, next: NextFunction): void => {
  const { title, groupId } = req.body;
  const errors: ValidationError[] = [];

  // Title validation (optional)
  if (title !== undefined) {
    if (typeof title !== 'string') {
      errors.push({ field: 'title', message: 'Title must be a string' });
    } else if (title.length > 255) {
      errors.push({ field: 'title', message: 'Title must be less than 255 characters' });
    }
  }

  // Group ID validation (optional)
  if (groupId !== undefined) {
    if (typeof groupId !== 'string') {
      errors.push({ field: 'groupId', message: 'Group ID must be a string' });
    } else if (groupId.trim().length === 0) {
      errors.push({ field: 'groupId', message: 'Group ID cannot be empty' });
    }
  }

  if (errors.length > 0) {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    });
    return;
  }

  // Sanitize input
  if (title !== undefined) req.body.title = title.trim();
  if (groupId !== undefined) req.body.groupId = groupId.trim();

  next();
};

// Generic validation function that maps validation names to functions
export const validateRequest = (validationType: string) => {
  const validationMap: { [key: string]: (req: Request, res: Response, next: NextFunction) => void } = {
    'registration': validateRegistration,
    'login': validateLogin,
    'passwordReset': validatePasswordReset,
    'passwordResetConfirm': validatePasswordResetConfirm,
    'refreshToken': validateRefreshToken,
    'googleAuth': validateGoogleAuth,
    'createNote': validateCreateNote,
    'updateNote': validateUpdateNote,
    'createTag': validateCreateTag,
    'createGroup': validateCreateGroup,
    'updateGroup': validateUpdateGroup,
    'inviteUser': validateInviteUser,
    'updateMemberRole': validateUpdateMemberRole,
    'createShare': validateCreateShare,
    'updateShare': validateUpdateShare,
    'updateSettings': validateUpdateSettings,
    'updateProfile': validateUpdateProfile,
    'exportNote': validateExportNote,
    'exportMultipleNotes': validateExportMultipleNotes,
    'createTemplate': validateCreateTemplate,
    'updateTemplate': validateUpdateTemplate,
    'useTemplate': validateUseTemplate,
    'adminGetUsers': (req, res, next) => next(),
    'adminUpdateUserStatus': (req, res, next) => next(),
    'adminToggleAdminStatus': (req, res, next) => next(),
    'adminGetContentReports': (req, res, next) => next(),
    'adminGetNotifications': (req, res, next) => next(),
    'adminMarkNotificationRead': (req, res, next) => next(),
    'adminDeleteNotification': (req, res, next) => next(),
    'adminGetSystemMetrics': (req, res, next) => next(),
    'adminUpdateSystemConfig': (req, res, next) => next(),
    'adminToggleMaintenance': (req, res, next) => next(),
    'adminUpdateReportStatus': (req, res, next) => next(),
    'adminCreateModerationAction': (req, res, next) => next()
  };

  const validationFunction = validationMap[validationType];
  if (!validationFunction) {
    throw new Error(`Unknown validation type: ${validationType}`);
  }

  return validationFunction;
};
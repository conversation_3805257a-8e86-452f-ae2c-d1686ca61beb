import { Router } from 'express';
import { GroupController } from '../controllers/GroupController';
import { authenticateToken } from '../middleware/auth';
import { 
  validateCreateGroup,
  validateUpdateGroup,
  validateInviteUser,
  validateUpdateMemberRole
} from '../middleware/validation';
import { generalRateLimit } from '../middleware/rateLimiting';

const router = Router();

// All group routes require authentication
router.use(authenticateToken);

// Group management routes
router.post('/', 
  generalRateLimit,
  validateCreateGroup,
  GroupController.createGroup as any
);

router.get('/', 
  GroupController.getUserGroups as any
);

router.get('/:id', 
  GroupController.getGroup as any
);

router.put('/:id', 
  generalRateLimit,
  validateUpdateGroup,
  GroupController.updateGroup as any
);

router.delete('/:id', 
  generalRateLimit,
  GroupController.deleteGroup as any
);

// Member management routes
router.post('/:id/invite', 
  generalRateLimit,
  validateInviteUser,
  GroupController.inviteUser as any
);

router.post('/invitations/:token/accept', 
  generalRateLimit,
  GroupController.acceptInvitation as any
);

router.delete('/:id/members/:memberId', 
  generalRateLimit,
  GroupController.removeMember as any
);

router.put('/:id/members/:memberId/role', 
  generalRateLimit,
  validateUpdateMemberRole,
  GroupController.updateMemberRole as any
);

// Admin routes
router.get('/:id/invitations', 
  GroupController.getGroupInvitations as any
);

export default router;
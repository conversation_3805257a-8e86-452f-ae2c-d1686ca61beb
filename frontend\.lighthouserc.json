{"ci": {"collect": {"url": ["http://localhost:3000"], "numberOfRuns": 3, "settings": {"chromeFlags": "--no-sandbox --disable-dev-shm-usage", "preset": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}, "emulatedFormFactor": "desktop"}}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.8}], "first-contentful-paint": ["error", {"maxNumericValue": 1000}], "largest-contentful-paint": ["error", {"maxNumericValue": 1500}], "cumulative-layout-shift": ["error", {"maxNumericValue": 0.05}], "total-blocking-time": ["error", {"maxNumericValue": 200}], "speed-index": ["error", {"maxNumericValue": 1500}], "unused-javascript": ["warn", {"maxLength": 2}], "unused-css-rules": ["warn", {"maxLength": 2}], "render-blocking-resources": ["warn", {"maxLength": 1}], "unminified-css": ["error", {"maxLength": 0}], "unminified-javascript": ["error", {"maxLength": 0}], "resource-summary:script:size": ["error", {"maxNumericValue": 300000}], "resource-summary:stylesheet:size": ["error", {"maxNumericValue": 100000}], "resource-summary:total:size": ["error", {"maxNumericValue": 1500000}]}}, "upload": {"target": "temporary-public-storage"}}}
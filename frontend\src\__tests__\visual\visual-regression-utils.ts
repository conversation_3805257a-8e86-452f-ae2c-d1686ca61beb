/**
 * Visual Regression Testing Utilities
 * Provides tools for capturing and comparing visual states of components across themes
 */

export interface VisualSnapshot {
  id: string
  theme: string
  component: string
  timestamp: number
  styles: ComputedStyles
  layout: LayoutMetrics
  accessibility: AccessibilityMetrics
}

export interface ComputedStyles {
  color: string
  backgroundColor: string
  borderColor: string
  fontSize: string
  fontFamily: string
  padding: string
  margin: string
  borderRadius: string
  boxShadow: string
}

export interface LayoutMetrics {
  width: number
  height: number
  offsetTop: number
  offsetLeft: number
  scrollWidth: number
  scrollHeight: number
}

export interface AccessibilityMetrics {
  contrastRatio: number
  focusVisible: boolean
  ariaLabels: string[]
  tabIndex: number
}

export interface VisualDifference {
  property: string
  oldValue: string
  newValue: string
  severity: 'low' | 'medium' | 'high'
  category: 'color' | 'layout' | 'typography' | 'accessibility'
}

export interface ComparisonResult {
  match: boolean
  score: number
  differences: VisualDifference[]
  summary: {
    colorChanges: number
    layoutChanges: number
    typographyChanges: number
    accessibilityIssues: number
  }
}

export class VisualRegressionCapture {
  private snapshots: Map<string, VisualSnapshot> = new Map()
  private baselineSnapshots: Map<string, VisualSnapshot> = new Map()

  /**
   * Capture a visual snapshot of an element
   */
  captureElement(element: HTMLElement, theme: string, componentName: string): VisualSnapshot {
    const id = `${componentName}-${theme}-${Date.now()}`
    
    const snapshot: VisualSnapshot = {
      id,
      theme,
      component: componentName,
      timestamp: Date.now(),
      styles: this.extractComputedStyles(element),
      layout: this.extractLayoutMetrics(element),
      accessibility: this.extractAccessibilityMetrics(element)
    }
    
    this.snapshots.set(id, snapshot)
    return snapshot
  }

  /**
   * Set a snapshot as baseline for comparison
   */
  setBaseline(snapshot: VisualSnapshot): void {
    const key = `${snapshot.component}-${snapshot.theme}`
    this.baselineSnapshots.set(key, snapshot)
  }

  /**
   * Compare current snapshot with baseline
   */
  compareWithBaseline(current: VisualSnapshot): ComparisonResult {
    const key = `${current.component}-${current.theme}`
    const baseline = this.baselineSnapshots.get(key)
    
    if (!baseline) {
      throw new Error(`No baseline found for ${key}`)
    }
    
    return this.compareSnapshots(baseline, current)
  }

  /**
   * Compare two snapshots
   */
  compareSnapshots(snapshot1: VisualSnapshot, snapshot2: VisualSnapshot): ComparisonResult {
    const differences: VisualDifference[] = []
    
    // Compare styles
    differences.push(...this.compareStyles(snapshot1.styles, snapshot2.styles))
    
    // Compare layout
    differences.push(...this.compareLayout(snapshot1.layout, snapshot2.layout))
    
    // Compare accessibility
    differences.push(...this.compareAccessibility(snapshot1.accessibility, snapshot2.accessibility))
    
    // Calculate overall score
    const score = this.calculateSimilarityScore(differences)
    
    // Generate summary
    const summary = {
      colorChanges: differences.filter(d => d.category === 'color').length,
      layoutChanges: differences.filter(d => d.category === 'layout').length,
      typographyChanges: differences.filter(d => d.category === 'typography').length,
      accessibilityIssues: differences.filter(d => d.category === 'accessibility').length
    }
    
    return {
      match: score > 0.95, // 95% similarity threshold
      score,
      differences,
      summary
    }
  }

  /**
   * Extract computed styles from element
   */
  private extractComputedStyles(element: HTMLElement): ComputedStyles {
    const computed = window.getComputedStyle(element)
    
    return {
      color: computed.color,
      backgroundColor: computed.backgroundColor,
      borderColor: computed.borderColor,
      fontSize: computed.fontSize,
      fontFamily: computed.fontFamily,
      padding: computed.padding,
      margin: computed.margin,
      borderRadius: computed.borderRadius,
      boxShadow: computed.boxShadow
    }
  }

  /**
   * Extract layout metrics from element
   */
  private extractLayoutMetrics(element: HTMLElement): LayoutMetrics {
    const rect = element.getBoundingClientRect()
    
    return {
      width: rect.width,
      height: rect.height,
      offsetTop: element.offsetTop,
      offsetLeft: element.offsetLeft,
      scrollWidth: element.scrollWidth,
      scrollHeight: element.scrollHeight
    }
  }

  /**
   * Extract accessibility metrics from element
   */
  private extractAccessibilityMetrics(element: HTMLElement): AccessibilityMetrics {
    const computed = window.getComputedStyle(element)
    
    return {
      contrastRatio: this.calculateContrastRatio(computed.color, computed.backgroundColor),
      focusVisible: this.checkFocusVisibility(element),
      ariaLabels: this.extractAriaLabels(element),
      tabIndex: element.tabIndex
    }
  }

  /**
   * Compare style objects
   */
  private compareStyles(styles1: ComputedStyles, styles2: ComputedStyles): VisualDifference[] {
    const differences: VisualDifference[] = []
    
    // Color properties
    const colorProps: (keyof ComputedStyles)[] = ['color', 'backgroundColor', 'borderColor']
    colorProps.forEach(prop => {
      if (styles1[prop] !== styles2[prop]) {
        differences.push({
          property: prop,
          oldValue: styles1[prop],
          newValue: styles2[prop],
          severity: 'medium',
          category: 'color'
        })
      }
    })
    
    // Typography properties
    const typographyProps: (keyof ComputedStyles)[] = ['fontSize', 'fontFamily']
    typographyProps.forEach(prop => {
      if (styles1[prop] !== styles2[prop]) {
        differences.push({
          property: prop,
          oldValue: styles1[prop],
          newValue: styles2[prop],
          severity: 'high',
          category: 'typography'
        })
      }
    })
    
    // Layout properties
    const layoutProps: (keyof ComputedStyles)[] = ['padding', 'margin', 'borderRadius']
    layoutProps.forEach(prop => {
      if (styles1[prop] !== styles2[prop]) {
        differences.push({
          property: prop,
          oldValue: styles1[prop],
          newValue: styles2[prop],
          severity: 'low',
          category: 'layout'
        })
      }
    })
    
    return differences
  }

  /**
   * Compare layout metrics
   */
  private compareLayout(layout1: LayoutMetrics, layout2: LayoutMetrics): VisualDifference[] {
    const differences: VisualDifference[] = []
    const threshold = 2 // 2px threshold for layout differences
    
    Object.entries(layout1).forEach(([key, value]) => {
      const newValue = layout2[key as keyof LayoutMetrics]
      if (Math.abs(value - newValue) > threshold) {
        differences.push({
          property: key,
          oldValue: value.toString(),
          newValue: newValue.toString(),
          severity: 'medium',
          category: 'layout'
        })
      }
    })
    
    return differences
  }

  /**
   * Compare accessibility metrics
   */
  private compareAccessibility(a11y1: AccessibilityMetrics, a11y2: AccessibilityMetrics): VisualDifference[] {
    const differences: VisualDifference[] = []
    
    // Contrast ratio comparison
    if (Math.abs(a11y1.contrastRatio - a11y2.contrastRatio) > 0.5) {
      differences.push({
        property: 'contrastRatio',
        oldValue: a11y1.contrastRatio.toString(),
        newValue: a11y2.contrastRatio.toString(),
        severity: 'high',
        category: 'accessibility'
      })
    }
    
    // Focus visibility
    if (a11y1.focusVisible !== a11y2.focusVisible) {
      differences.push({
        property: 'focusVisible',
        oldValue: a11y1.focusVisible.toString(),
        newValue: a11y2.focusVisible.toString(),
        severity: 'high',
        category: 'accessibility'
      })
    }
    
    // Tab index
    if (a11y1.tabIndex !== a11y2.tabIndex) {
      differences.push({
        property: 'tabIndex',
        oldValue: a11y1.tabIndex.toString(),
        newValue: a11y2.tabIndex.toString(),
        severity: 'medium',
        category: 'accessibility'
      })
    }
    
    return differences
  }

  /**
   * Calculate similarity score based on differences
   */
  private calculateSimilarityScore(differences: VisualDifference[]): number {
    if (differences.length === 0) return 1.0
    
    let penalty = 0
    differences.forEach(diff => {
      switch (diff.severity) {
        case 'high':
          penalty += 0.1
          break
        case 'medium':
          penalty += 0.05
          break
        case 'low':
          penalty += 0.01
          break
      }
    })
    
    return Math.max(0, 1 - penalty)
  }

  /**
   * Calculate color contrast ratio
   */
  private calculateContrastRatio(color1: string, color2: string): number {
    // Simplified contrast calculation
    // In a real implementation, use proper WCAG contrast calculation
    const rgb1 = this.parseColor(color1)
    const rgb2 = this.parseColor(color2)
    
    const l1 = this.getLuminance(rgb1)
    const l2 = this.getLuminance(rgb2)
    
    const lighter = Math.max(l1, l2)
    const darker = Math.min(l1, l2)
    
    return (lighter + 0.05) / (darker + 0.05)
  }

  /**
   * Parse color string to RGB values
   */
  private parseColor(color: string): { r: number; g: number; b: number } {
    // Handle rgb() format
    const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
    if (rgbMatch) {
      return {
        r: parseInt(rgbMatch[1]),
        g: parseInt(rgbMatch[2]),
        b: parseInt(rgbMatch[3])
      }
    }
    
    // Handle hex format
    const hexMatch = color.match(/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i)
    if (hexMatch) {
      return {
        r: parseInt(hexMatch[1], 16),
        g: parseInt(hexMatch[2], 16),
        b: parseInt(hexMatch[3], 16)
      }
    }
    
    // Default to black
    return { r: 0, g: 0, b: 0 }
  }

  /**
   * Calculate relative luminance
   */
  private getLuminance(rgb: { r: number; g: number; b: number }): number {
    const { r, g, b } = rgb
    
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    })
    
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
  }

  /**
   * Check if element has visible focus indicator
   */
  private checkFocusVisibility(element: HTMLElement): boolean {
    // Simulate focus and check for outline or other focus indicators
    const computed = window.getComputedStyle(element, ':focus')
    return computed.outline !== 'none' || computed.boxShadow !== 'none'
  }

  /**
   * Extract ARIA labels from element
   */
  private extractAriaLabels(element: HTMLElement): string[] {
    const labels: string[] = []
    
    if (element.getAttribute('aria-label')) {
      labels.push(element.getAttribute('aria-label')!)
    }
    
    if (element.getAttribute('aria-labelledby')) {
      const labelIds = element.getAttribute('aria-labelledby')!.split(' ')
      labelIds.forEach(id => {
        const labelElement = document.getElementById(id)
        if (labelElement) {
          labels.push(labelElement.textContent || '')
        }
      })
    }
    
    return labels
  }

  /**
   * Get all captured snapshots
   */
  getSnapshots(): VisualSnapshot[] {
    return Array.from(this.snapshots.values())
  }

  /**
   * Get snapshots for a specific theme
   */
  getSnapshotsForTheme(theme: string): VisualSnapshot[] {
    return this.getSnapshots().filter(s => s.theme === theme)
  }

  /**
   * Get snapshots for a specific component
   */
  getSnapshotsForComponent(component: string): VisualSnapshot[] {
    return this.getSnapshots().filter(s => s.component === component)
  }

  /**
   * Clear all snapshots
   */
  clearSnapshots(): void {
    this.snapshots.clear()
  }

  /**
   * Clear baseline snapshots
   */
  clearBaselines(): void {
    this.baselineSnapshots.clear()
  }

  /**
   * Export snapshots to JSON
   */
  exportSnapshots(): string {
    const data = {
      snapshots: Array.from(this.snapshots.entries()),
      baselines: Array.from(this.baselineSnapshots.entries()),
      timestamp: Date.now()
    }
    
    return JSON.stringify(data, null, 2)
  }

  /**
   * Import snapshots from JSON
   */
  importSnapshots(json: string): void {
    const data = JSON.parse(json)
    
    this.snapshots = new Map(data.snapshots)
    this.baselineSnapshots = new Map(data.baselines)
  }
}

/**
 * Theme-specific visual regression testing
 */
export class ThemeVisualTester {
  private capture: VisualRegressionCapture
  private themes: string[]

  constructor(themes: string[]) {
    this.capture = new VisualRegressionCapture()
    this.themes = themes
  }

  /**
   * Test component across all themes
   */
  async testComponentAcrossThemes(
    element: HTMLElement,
    componentName: string,
    applyTheme: (theme: string) => Promise<void>
  ): Promise<Map<string, VisualSnapshot>> {
    const snapshots = new Map<string, VisualSnapshot>()
    
    for (const theme of this.themes) {
      await applyTheme(theme)
      
      // Wait for theme transition to complete
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const snapshot = this.capture.captureElement(element, theme, componentName)
      snapshots.set(theme, snapshot)
    }
    
    return snapshots
  }

  /**
   * Compare theme consistency
   */
  compareThemeConsistency(snapshots: Map<string, VisualSnapshot>): {
    consistent: boolean
    issues: Array<{
      themes: [string, string]
      differences: VisualDifference[]
    }>
  } {
    const issues: Array<{
      themes: [string, string]
      differences: VisualDifference[]
    }> = []
    
    const snapshotArray = Array.from(snapshots.entries())
    
    // Compare each theme with every other theme
    for (let i = 0; i < snapshotArray.length; i++) {
      for (let j = i + 1; j < snapshotArray.length; j++) {
        const [theme1, snapshot1] = snapshotArray[i]
        const [theme2, snapshot2] = snapshotArray[j]
        
        const comparison = this.capture.compareSnapshots(snapshot1, snapshot2)
        
        // Filter out expected color differences (themes should have different colors)
        const unexpectedDifferences = comparison.differences.filter(diff => 
          diff.category !== 'color' || diff.severity === 'high'
        )
        
        if (unexpectedDifferences.length > 0) {
          issues.push({
            themes: [theme1, theme2],
            differences: unexpectedDifferences
          })
        }
      }
    }
    
    return {
      consistent: issues.length === 0,
      issues
    }
  }

  /**
   * Generate visual regression report
   */
  generateReport(testResults: Map<string, any>): {
    summary: {
      totalTests: number
      passed: number
      failed: number
      themes: string[]
    }
    details: Array<{
      component: string
      theme: string
      status: 'pass' | 'fail'
      issues: VisualDifference[]
    }>
  } {
    const details: Array<{
      component: string
      theme: string
      status: 'pass' | 'fail'
      issues: VisualDifference[]
    }> = []
    
    let passed = 0
    let failed = 0
    
    testResults.forEach((result, key) => {
      const [component, theme] = key.split('-')
      const status = result.issues.length === 0 ? 'pass' : 'fail'
      
      if (status === 'pass') {
        passed++
      } else {
        failed++
      }
      
      details.push({
        component,
        theme,
        status,
        issues: result.issues || []
      })
    })
    
    return {
      summary: {
        totalTests: testResults.size,
        passed,
        failed,
        themes: this.themes
      },
      details
    }
  }

  /**
   * Get capture instance for advanced usage
   */
  getCapture(): VisualRegressionCapture {
    return this.capture
  }
}
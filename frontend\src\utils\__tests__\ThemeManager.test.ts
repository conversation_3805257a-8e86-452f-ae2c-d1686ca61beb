import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ThemeManager } from '../ThemeManager'
import { ThemeLoadError } from '@/types/theme'
import type { BulmaswatchTheme, ThemeIndex } from '@/types/theme'

// Mock fetch
global.fetch = vi.fn()

// Mock DOM methods
const mockDocument = {
  createElement: vi.fn(),
  head: {
    appendChild: vi.fn(),
    removeChild: vi.fn()
  },
  body: {
    className: '',
    classList: {
      add: vi.fn(),
      remove: vi.fn()
    }
  },
  documentElement: {
    setAttribute: vi.fn(),
    removeAttribute: vi.fn(),
    style: {
      setProperty: vi.fn(),
      removeProperty: vi.fn()
    }
  },
  querySelector: vi.fn(),
  querySelectorAll: vi.fn()
}

Object.defineProperty(global, 'document', {
  value: mockDocument,
  writable: true
})

// Mock window.matchMedia
const mockMatchMedia = vi.fn()
Object.defineProperty(global, 'window', {
  value: {
    matchMedia: mockMatchMedia
  },
  writable: true
})

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}
Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage
})

// Mock getComputedStyle
global.getComputedStyle = vi.fn().mockReturnValue({
  filter: () => []
})

// Mock data
const mockThemeIndex: ThemeIndex = {
  themes: [
    { name: 'default', manifestFile: 'default.json' },
    { name: 'darkly', manifestFile: 'darkly.json' }
  ],
  defaultLight: 'default',
  defaultDark: 'darkly'
}

const mockThemes: BulmaswatchTheme[] = [
  {
    name: 'default',
    displayName: 'Default Light',
    description: 'Clean light theme',
    cssFile: 'default.css',
    isDark: false,
    category: 'light',
    preview: {
      primary: '#3273dc',
      background: '#ffffff',
      surface: '#f8f9fa',
      text: '#363636',
      accent: '#209cee'
    },
    colors: {
      primary: '#3273dc',
      link: '#3273dc',
      info: '#209cee',
      success: '#23d160',
      warning: '#ffdd57',
      danger: '#ff3860',
      background: '#ffffff',
      surface: '#f8f9fa',
      text: '#363636',
      textMuted: '#6b7280',
      border: '#dbdbdb'
    }
  },
  {
    name: 'darkly',
    displayName: 'Darkly',
    description: 'Professional dark theme',
    cssFile: 'darkly.css',
    isDark: true,
    category: 'dark',
    preview: {
      primary: '#4f46e5',
      background: '#1a1a1a',
      surface: '#2d2d2d',
      text: '#e0e0e0',
      accent: '#6366f1'
    },
    colors: {
      primary: '#4f46e5',
      link: '#4f46e5',
      info: '#6366f1',
      success: '#10b981',
      warning: '#f59e0b',
      danger: '#ef4444',
      background: '#1a1a1a',
      surface: '#2d2d2d',
      text: '#e0e0e0',
      textMuted: '#9ca3af',
      border: '#404040'
    }
  }
]

describe('ThemeManager', () => {
  let themeManager: ThemeManager
  let mediaQueryList: any

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup media query mock
    mediaQueryList = {
      matches: false,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    }
    mockMatchMedia.mockReturnValue(mediaQueryList)

    // Setup fetch mock
    vi.mocked(fetch).mockImplementation((url: string) => {
      if (url.includes('index.json')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockThemeIndex)
        } as Response)
      }
      
      if (url.includes('default.json')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockThemes[0])
        } as Response)
      }
      
      if (url.includes('darkly.json')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockThemes[1])
        } as Response)
      }
      
      if (url.includes('.css')) {
        return Promise.resolve({
          ok: true,
          text: () => Promise.resolve('/* CSS content */')
        } as Response)
      }
      
      return Promise.reject(new Error('Not found'))
    })

    // Setup DOM mocks
    mockDocument.createElement.mockImplementation((tag: string) => {
      const element = {
        tagName: tag.toUpperCase(),
        rel: '',
        href: '',
        disabled: false,
        setAttribute: vi.fn(),
        getAttribute: vi.fn(),
        remove: vi.fn(),
        onload: null,
        onerror: null,
        textContent: ''
      }
      
      // Simulate load event for link elements
      if (tag === 'link') {
        setTimeout(() => {
          if (element.onload) element.onload({} as Event)
        }, 0)
      }
      
      return element
    })

    mockDocument.querySelector.mockReturnValue(null)
    mockDocument.querySelectorAll.mockReturnValue([])

    // Reset localStorage mock
    mockLocalStorage.getItem.mockReturnValue(null)

    themeManager = new ThemeManager()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await themeManager.initialize()
      
      expect(fetch).toHaveBeenCalledWith('/src/styles/themes/bulmaswatch/index.json')
      expect(fetch).toHaveBeenCalledWith('/src/styles/themes/bulmaswatch/default.json')
      expect(fetch).toHaveBeenCalledWith('/src/styles/themes/bulmaswatch/darkly.json')
    })

    it('should handle initialization failure', async () => {
      vi.mocked(fetch).mockRejectedValue(new Error('Network error'))
      
      await expect(themeManager.initialize()).rejects.toThrow(ThemeLoadError)
    })

    it('should handle theme index load failure', async () => {
      vi.mocked(fetch).mockImplementation((url: string) => {
        if (url.includes('index.json')) {
          return Promise.resolve({
            ok: false,
            statusText: 'Not Found'
          } as Response)
        }
        return Promise.reject(new Error('Not found'))
      })
      
      await expect(themeManager.initialize()).rejects.toThrow(ThemeLoadError)
    })

    it('should handle individual theme manifest failures gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      vi.mocked(fetch).mockImplementation((url: string) => {
        if (url.includes('index.json')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockThemeIndex)
          } as Response)
        }
        
        if (url.includes('default.json')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockThemes[0])
          } as Response)
        }
        
        // Fail darkly theme
        if (url.includes('darkly.json')) {
          return Promise.resolve({
            ok: false,
            statusText: 'Not Found'
          } as Response)
        }
        
        return Promise.reject(new Error('Not found'))
      })
      
      await themeManager.initialize()
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to load theme manifest for darkly:',
        expect.any(Error)
      )
      
      consoleSpy.mockRestore()
    })
  })

  describe('system preference detection', () => {
    it('should detect light system preference', () => {
      mediaQueryList.matches = false
      
      const preference = themeManager.getSystemPreference()
      
      expect(preference).toBe('light')
    })

    it('should detect dark system preference', () => {
      mediaQueryList.matches = true
      
      // Create new instance to trigger detection
      const newManager = new ThemeManager()
      const preference = newManager.getSystemPreference()
      
      expect(preference).toBe('dark')
    })

    it('should handle missing matchMedia gracefully', () => {
      delete (global.window as any).matchMedia
      
      expect(() => new ThemeManager()).not.toThrow()
      
      // Restore matchMedia
      ;(global.window as any).matchMedia = mockMatchMedia
    })
  })

  describe('theme setting', () => {
    beforeEach(async () => {
      await themeManager.initialize()
    })

    it('should set light theme mode', async () => {
      await themeManager.setTheme('light')
      
      expect(themeManager.getCurrentTheme()).toBe('default')
    })

    it('should set dark theme mode', async () => {
      await themeManager.setTheme('dark')
      
      expect(themeManager.getCurrentTheme()).toBe('darkly')
    })

    it('should set auto theme mode based on system preference', async () => {
      mediaQueryList.matches = true
      
      await themeManager.setTheme('auto')
      
      expect(themeManager.getCurrentTheme()).toBe('darkly')
    })

    it('should set specific theme by name', async () => {
      await themeManager.setTheme('default')
      
      expect(themeManager.getCurrentTheme()).toBe('default')
    })

    it('should throw error for non-existent theme', async () => {
      await expect(themeManager.setTheme('nonexistent')).rejects.toThrow(ThemeLoadError)
    })

    it('should handle theme loading failure', async () => {
      vi.mocked(fetch).mockImplementation((url: string) => {
        if (url.includes('.css')) {
          return Promise.resolve({
            ok: false,
            statusText: 'Not Found'
          } as Response)
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockThemes[0])
        } as Response)
      })
      
      await expect(themeManager.setTheme('default')).rejects.toThrow(ThemeLoadError)
    })
  })

  describe('theme loading', () => {
    beforeEach(async () => {
      await themeManager.initialize()
    })

    it('should load theme via link element in development', async () => {
      // Mock development environment
      Object.defineProperty(import.meta, 'env', {
        value: { PROD: false },
        writable: true
      })
      
      await themeManager.loadTheme('default')
      
      expect(mockDocument.createElement).toHaveBeenCalledWith('link')
      expect(mockDocument.head.appendChild).toHaveBeenCalled()
    })

    it('should load theme via fetch in production', async () => {
      // Mock production environment
      Object.defineProperty(import.meta, 'env', {
        value: { PROD: true },
        writable: true
      })
      
      await themeManager.loadTheme('default')
      
      expect(fetch).toHaveBeenCalledWith('/src/styles/themes/bulmaswatch/default.css')
    })

    it('should not reload already loaded theme', async () => {
      await themeManager.loadTheme('default')
      
      const fetchCallCount = vi.mocked(fetch).mock.calls.length
      
      await themeManager.loadTheme('default')
      
      // Should not make additional fetch calls
      expect(vi.mocked(fetch).mock.calls.length).toBe(fetchCallCount)
    })

    it('should handle concurrent loading requests', async () => {
      const promise1 = themeManager.loadTheme('default')
      const promise2 = themeManager.loadTheme('default')
      
      await Promise.all([promise1, promise2])
      
      // Should only load once
      expect(fetch).toHaveBeenCalledTimes(3) // index + 2 manifests, no duplicate CSS loads
    })

    it('should use cached CSS content', async () => {
      // Mock cached content
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'theme-cache') {
          return JSON.stringify({ default: '/* cached CSS */' })
        }
        return null
      })
      
      // Create new instance to load cache
      const cachedManager = new ThemeManager()
      await cachedManager.initialize()
      await cachedManager.loadTheme('default')
      
      expect(mockDocument.createElement).toHaveBeenCalledWith('style')
    })
  })

  describe('theme application', () => {
    beforeEach(async () => {
      await themeManager.initialize()
    })

    it('should apply theme by enabling correct stylesheet', () => {
      const mockElements = [
        { tagName: 'LINK', disabled: true, getAttribute: vi.fn().mockReturnValue('default') },
        { tagName: 'LINK', disabled: true, getAttribute: vi.fn().mockReturnValue('darkly') }
      ]
      
      mockDocument.querySelectorAll.mockImplementation((selector: string) => {
        if (selector.includes('[data-theme]')) {
          return mockElements
        }
        if (selector.includes('[data-theme="default"]')) {
          return [mockElements[0]]
        }
        return []
      })
      
      themeManager.applyTheme('default')
      
      expect(mockElements[0].disabled).toBe(false)
      expect(mockDocument.documentElement.setAttribute).toHaveBeenCalledWith('data-theme', 'default')
    })

    it('should update CSS custom properties', () => {
      themeManager.applyTheme('default')
      
      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--theme-primary',
        '#3273dc'
      )
      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--theme-mode',
        'light'
      )
    })

    it('should update body class', () => {
      mockDocument.body.className = 'existing-class theme-old'
      
      themeManager.applyTheme('default')
      
      expect(mockDocument.body.classList.add).toHaveBeenCalledWith('theme-default')
    })
  })

  describe('theme information', () => {
    beforeEach(async () => {
      await themeManager.initialize()
    })

    it('should get available themes', () => {
      const themes = themeManager.getAvailableThemes()
      
      expect(themes).toHaveLength(2)
      expect(themes[0].name).toBe('default')
      expect(themes[1].name).toBe('darkly')
    })

    it('should get specific theme by name', () => {
      const theme = themeManager.getTheme('default')
      
      expect(theme).toBeDefined()
      expect(theme?.name).toBe('default')
    })

    it('should return undefined for non-existent theme', () => {
      const theme = themeManager.getTheme('nonexistent')
      
      expect(theme).toBeUndefined()
    })

    it('should get theme preview', () => {
      const preview = themeManager.getThemePreview('default')
      
      expect(preview).toEqual(mockThemes[0].preview)
    })

    it('should return null for non-existent theme preview', () => {
      const preview = themeManager.getThemePreview('nonexistent')
      
      expect(preview).toBeNull()
    })
  })

  describe('theme callbacks', () => {
    beforeEach(async () => {
      await themeManager.initialize()
    })

    it('should register and call theme change callbacks', async () => {
      const callback = vi.fn()
      
      themeManager.onThemeChange(callback)
      await themeManager.setTheme('default')
      
      expect(callback).toHaveBeenCalledWith('default')
    })

    it('should unregister theme change callbacks', async () => {
      const callback = vi.fn()
      
      themeManager.onThemeChange(callback)
      themeManager.offThemeChange(callback)
      await themeManager.setTheme('default')
      
      expect(callback).not.toHaveBeenCalled()
    })

    it('should handle callback errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const errorCallback = vi.fn().mockImplementation(() => {
        throw new Error('Callback error')
      })
      
      themeManager.onThemeChange(errorCallback)
      await themeManager.setTheme('default')
      
      expect(consoleSpy).toHaveBeenCalledWith('Error in theme change callback:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })
  })

  describe('theme preloading', () => {
    beforeEach(async () => {
      await themeManager.initialize()
    })

    it('should preload single theme', async () => {
      await themeManager.preloadTheme('darkly')
      
      expect(fetch).toHaveBeenCalledWith('/src/styles/themes/bulmaswatch/darkly.css')
    })

    it('should preload multiple themes', async () => {
      await themeManager.preloadThemes(['default', 'darkly'])
      
      expect(fetch).toHaveBeenCalledWith('/src/styles/themes/bulmaswatch/default.css')
      expect(fetch).toHaveBeenCalledWith('/src/styles/themes/bulmaswatch/darkly.css')
    })

    it('should handle preload failures gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      vi.mocked(fetch).mockImplementation((url: string) => {
        if (url.includes('darkly.css')) {
          return Promise.reject(new Error('Network error'))
        }
        return Promise.resolve({
          ok: true,
          text: () => Promise.resolve('/* CSS */')
        } as Response)
      })
      
      await themeManager.preloadTheme('darkly')
      
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to preload theme 'darkly':",
        expect.any(Error)
      )
      
      consoleSpy.mockRestore()
    })

    it('should preload user preferences', async () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'theme-name') return 'darkly'
        return null
      })
      
      await themeManager.preloadUserPreferences()
      
      expect(fetch).toHaveBeenCalledWith('/src/styles/themes/bulmaswatch/default.css')
      expect(fetch).toHaveBeenCalledWith('/src/styles/themes/bulmaswatch/darkly.css')
    })
  })

  describe('cache management', () => {
    it('should get cache statistics', () => {
      const stats = themeManager.getCacheStats()
      
      expect(stats).toHaveProperty('size')
      expect(stats).toHaveProperty('themes')
      expect(Array.isArray(stats.themes)).toBe(true)
    })

    it('should clear cache', () => {
      themeManager.clearCache()
      
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('theme-cache')
    })

    it('should handle cache loading errors gracefully', () => {
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('Storage error')
      })
      
      expect(() => new ThemeManager()).not.toThrow()
    })

    it('should handle cache saving errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage full')
      })
      
      // Mock production environment to trigger cache saving
      Object.defineProperty(import.meta, 'env', {
        value: { PROD: true },
        writable: true
      })
      
      await themeManager.initialize()
      await themeManager.loadTheme('default')
      
      expect(consoleSpy).toHaveBeenCalledWith('Failed to save theme cache:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })
  })

  describe('error handling and fallbacks', () => {
    beforeEach(async () => {
      await themeManager.initialize()
    })

    it('should fallback to default theme', async () => {
      await themeManager.fallbackToDefaultTheme()
      
      expect(themeManager.getCurrentTheme()).toBe('default')
    })

    it('should handle fallback failure gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // Mock setTheme to fail
      vi.spyOn(themeManager, 'setTheme').mockRejectedValue(new Error('Fallback failed'))
      
      await themeManager.fallbackToDefaultTheme()
      
      expect(consoleSpy).toHaveBeenCalledWith('Failed to fallback to default theme:', expect.any(Error))
      expect(mockDocument.documentElement.removeAttribute).toHaveBeenCalledWith('data-theme')
      
      consoleSpy.mockRestore()
    })

    it('should retry theme loading with exponential backoff', async () => {
      let attempts = 0
      vi.mocked(fetch).mockImplementation(() => {
        attempts++
        if (attempts < 3) {
          return Promise.reject(new Error('Network error'))
        }
        return Promise.resolve({
          ok: true,
          text: () => Promise.resolve('/* CSS */')
        } as Response)
      })
      
      await themeManager.retryThemeLoad('default', 3)
      
      expect(attempts).toBe(3)
    })

    it('should throw error after max retries', async () => {
      vi.mocked(fetch).mockRejectedValue(new Error('Persistent error'))
      
      await expect(themeManager.retryThemeLoad('default', 2)).rejects.toThrow('Persistent error')
    })
  })

  describe('theme validation', () => {
    it('should validate complete theme', () => {
      const isValid = themeManager.validateTheme(mockThemes[0])
      
      expect(isValid).toBe(true)
    })

    it('should reject incomplete theme', () => {
      const incompleteTheme = {
        name: 'incomplete',
        displayName: 'Incomplete Theme'
        // Missing required fields
      } as BulmaswatchTheme
      
      const isValid = themeManager.validateTheme(incompleteTheme)
      
      expect(isValid).toBe(false)
    })
  })

  describe('performance optimization', () => {
    beforeEach(async () => {
      await themeManager.initialize()
    })

    it('should optimize performance', async () => {
      await themeManager.optimizePerformance()
      
      // Should preload user preferences
      expect(fetch).toHaveBeenCalledWith('/src/styles/themes/bulmaswatch/default.css')
    })

    it('should clean up unused themes', () => {
      const mockElements = [
        { getAttribute: vi.fn().mockReturnValue('unused'), remove: vi.fn() },
        { getAttribute: vi.fn().mockReturnValue('default'), remove: vi.fn() }
      ]
      
      mockDocument.querySelectorAll.mockReturnValue(mockElements)
      
      // Load default theme first
      themeManager.loadTheme('default')
      
      // This would be called internally by optimizePerformance
      // We can't test it directly as it's private, but we can test the public method
      expect(() => themeManager.optimizePerformance()).not.toThrow()
    })
  })
})
# Developer Guidelines - CSS Architecture & Theme System

## Quick Start

### Adding New Styles

1. **Identify the correct location**:
   - Component-specific styles → `components/`
   - Utility classes → `utilities/`
   - Theme-related colors → `themes/`
   - Global base styles → `base/`

2. **Use CSS custom properties** for any color or theme-related values:
   ```css
   /* ✅ Good */
   .my-component {
     background: var(--color-primary);
     color: var(--color-text);
   }
   
   /* ❌ Avoid */
   .my-component {
     background: #3273dc;
     color: #363636;
   }
   ```

3. **Follow the naming conventions** described below.

## Naming Conventions

### CSS Custom Properties
```css
/* Colors */
--color-primary          /* Main brand color */
--color-secondary        /* Secondary brand color */
--color-success          /* Success state color */
--color-danger           /* Error/danger state color */
--color-warning          /* Warning state color */
--color-info             /* Information state color */

/* Semantic colors */
--color-background       /* Main background */
--color-surface          /* Card/panel backgrounds */
--color-text             /* Primary text color */
--color-text-muted       /* Secondary text color */
--color-border           /* Border color */

/* Component-specific */
--navbar-background      /* Navbar background */
--sidebar-background     /* Sidebar background */
--modal-background       /* Modal background */
```

### CSS Classes
Follow BEM-inspired naming with Bulma compatibility:

```css
/* Component classes */
.component-name          /* Base component */
.component-name__element /* Component element */
.component-name--modifier /* Component modifier */

/* State classes (Bulma-compatible) */
.is-active              /* Active state */
.is-loading             /* Loading state */
.is-disabled            /* Disabled state */

/* Size classes (Bulma-compatible) */
.is-small               /* Small size */
.is-medium              /* Medium size */
.is-large               /* Large size */

/* Utility classes */
.has-text-primary       /* Text color utility */
.has-background-light   /* Background color utility */
.mt-3                   /* Margin top utility */
```

## Adding New Components

### 1. Create Component CSS File

Create a new file in `components/` directory:

```css
/* components/my-component.css */

/* Base component styles */
.my-component {
  display: flex;
  align-items: center;
  padding: var(--spacing-3);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  transition: var(--transition-fast);
}

/* Component elements */
.my-component__header {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-strong);
}

.my-component__content {
  flex: 1;
  color: var(--color-text);
}

/* Component modifiers */
.my-component--primary {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.my-component--large {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
}

/* State classes */
.my-component.is-loading {
  opacity: 0.7;
  pointer-events: none;
}

.my-component:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-border-hover);
}

/* Responsive behavior */
@media screen and (max-width: 768px) {
  .my-component {
    flex-direction: column;
    padding: var(--spacing-2);
  }
}
```

### 2. Add Import to main.css

Add the import in the correct section:

```css
/* In main.css, under Component Styles section */
@import './components/my-component.css';
```

### 3. Document the Component

Add documentation to the component file:

```css
/**
 * My Component
 * 
 * A flexible component for displaying content with optional header.
 * 
 * Usage:
 * <div class="my-component">
 *   <div class="my-component__header">Header</div>
 *   <div class="my-component__content">Content</div>
 * </div>
 * 
 * Modifiers:
 * - my-component--primary: Primary color variant
 * - my-component--large: Larger size variant
 * 
 * States:
 * - is-loading: Loading state with reduced opacity
 */
```

## Working with Themes

### Adding Theme Support to Components

1. **Use CSS custom properties** for all colors:
   ```css
   .my-component {
     background: var(--color-surface);
     color: var(--color-text);
     border-color: var(--color-border);
   }
   ```

2. **Define theme-specific variables** in `base/variables.css`:
   ```css
   :root {
     --my-component-background: var(--color-surface);
     --my-component-text: var(--color-text);
   }
   
   [data-theme="dark"] {
     --my-component-background: var(--color-surface-dark);
     --my-component-text: var(--color-text-dark);
   }
   ```

3. **Test across all themes** to ensure proper contrast and visibility.

### Creating New Themes

1. **Create theme file** in `themes/bulmaswatch/`:
   ```css
   /* themes/bulmaswatch/my-theme.css */
   
   [data-theme="my-theme"] {
     /* Primary colors */
     --color-primary: #your-primary-color;
     --color-secondary: #your-secondary-color;
     
     /* Background colors */
     --color-background: #your-background;
     --color-surface: #your-surface;
     
     /* Text colors */
     --color-text: #your-text-color;
     --color-text-muted: #your-muted-text;
     
     /* Border colors */
     --color-border: #your-border-color;
   }
   ```

2. **Register theme** in the theme management system:
   ```typescript
   // In theme configuration
   const newTheme: BulmaswatchTheme = {
     name: 'my-theme',
     displayName: 'My Theme',
     description: 'A custom theme with...',
     cssFile: 'my-theme.css',
     preview: {
       primary: '#your-primary-color',
       background: '#your-background',
       text: '#your-text-color'
     },
     isDark: false,
     category: 'light'
   }
   ```

3. **Test accessibility** using automated tools and manual testing.

## Utility Classes

### Creating New Utilities

1. **Add to appropriate utility file**:
   ```css
   /* utilities/spacing.css */
   .mx-auto { margin-left: auto; margin-right: auto; }
   .px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
   ```

2. **Follow consistent naming**:
   - `m` = margin, `p` = padding
   - `t` = top, `r` = right, `b` = bottom, `l` = left, `x` = horizontal, `y` = vertical
   - Numbers follow spacing scale: `1` = 0.25rem, `2` = 0.5rem, etc.

3. **Add responsive variants** when needed:
   ```css
   @media screen and (min-width: 769px) {
     .tablet\:px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
   }
   ```

## Performance Guidelines

### CSS Organization

1. **Keep files focused**: Each file should have a single responsibility
2. **Minimize nesting**: Avoid deep CSS nesting (max 3 levels)
3. **Use efficient selectors**: Prefer classes over complex selectors
4. **Group related styles**: Keep related styles together

### Optimization

1. **Use CSS custom properties** for values that change between themes
2. **Avoid `!important`**: Use specificity and cascade instead
3. **Minimize redundancy**: Use utility classes for common patterns
4. **Optimize for gzip**: Group similar rules together

### Bundle Size

Monitor CSS bundle sizes:
- Base styles: Target < 20KB
- Component styles: Target < 80KB
- Utility styles: Target < 30KB
- Theme styles: Target < 25KB each

## Testing Guidelines

### Cross-Theme Testing

Test components across all themes:
```bash
# Run visual regression tests
npm run test:visual

# Test specific theme
npm run test:theme -- --theme=darkly
```

### Accessibility Testing

1. **Color contrast**: Use automated tools to check WCAG compliance
2. **Keyboard navigation**: Test all interactive elements
3. **Screen readers**: Test with screen reader software
4. **Reduced motion**: Test with `prefers-reduced-motion`

### Performance Testing

1. **Bundle analysis**: Run `npm run build:analyze`
2. **Load testing**: Test CSS loading performance
3. **Cache testing**: Verify proper cache headers

## Common Patterns

### Responsive Design

```css
/* Mobile-first approach */
.component {
  /* Mobile styles */
  padding: var(--spacing-2);
}

@media screen and (min-width: 769px) {
  .component {
    /* Tablet and up */
    padding: var(--spacing-4);
  }
}

@media screen and (min-width: 1024px) {
  .component {
    /* Desktop and up */
    padding: var(--spacing-6);
  }
}
```

### Dark Mode Support

```css
.component {
  background: var(--color-surface);
  color: var(--color-text);
}

/* Automatic dark mode detection */
@media (prefers-color-scheme: dark) {
  .component {
    /* Fallback dark styles if theme system fails */
    background: #2d2d2d;
    color: #e0e0e0;
  }
}
```

### Animation and Transitions

```css
.component {
  transition: var(--transition-fast);
}

.component:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Respect reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .component {
    transition: none;
  }
  
  .component:hover {
    transform: none;
  }
}
```

## Troubleshooting

### Common Issues

1. **Styles not applying**: Check import order in `main.css`
2. **Theme colors not working**: Ensure CSS custom properties are used
3. **Specificity issues**: Use more specific selectors or reorganize CSS
4. **Performance issues**: Check bundle size and optimize imports

### Debug Tools

1. **Browser DevTools**: Inspect CSS custom properties
2. **Bundle analyzer**: `npm run bundle-analyze`
3. **Performance monitor**: `npm run build:analyze`

### Getting Help

1. Check this documentation first
2. Review existing component implementations
3. Test across different themes and screen sizes
4. Ask for code review before merging

## Best Practices Summary

✅ **Do:**
- Use CSS custom properties for theme-related values
- Follow the established file organization
- Test across all themes and screen sizes
- Write accessible, semantic CSS
- Document complex components
- Monitor bundle sizes

❌ **Don't:**
- Use hardcoded colors or theme-specific values
- Create overly specific selectors
- Ignore accessibility requirements
- Skip cross-browser testing
- Add styles without proper organization
- Use `!important` unless absolutely necessary
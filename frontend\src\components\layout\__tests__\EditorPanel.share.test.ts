import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import EditorPanel from '../EditorPanel.vue'

// Mock NoteEditor component
vi.mock('../../notes/NoteEditor.vue', () => ({
  default: {
    name: 'NoteEditor',
    template: '<div>Mock Note Editor</div>'
  }
}))

describe('EditorPanel - Share functionality', () => {
  const mockNote = {
    id: '1',
    title: 'Test Note',
    content: 'Test content',
    noteType: 'markdown',
    tags: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    userId: 'user1',
    isArchived: false,
    metadata: {}
  }

  it('emits share-note event when share button is clicked', async () => {
    const wrapper = mount(EditorPanel, {
      props: {
        selectedNote: mockNote,
        isFullscreen: false
      },
      global: {
        plugins: [createTesting<PERSON>inia({
          createSpy: vi.fn
        })]
      }
    })

    // Find and click the share button in the dropdown
    const shareButton = wrapper.find('a.dropdown-item[title="Share"]')
    if (!shareButton.exists()) {
      // Try alternative selector
      const shareButtons = wrapper.findAll('a.dropdown-item')
      const shareBtn = shareButtons.find(btn => btn.text().includes('Share'))
      if (shareBtn) {
        await shareBtn.trigger('click')
      }
    } else {
      await shareButton.trigger('click')
    }

    // Check if share-note event was emitted
    const emitted = wrapper.emitted('share-note')
    expect(emitted).toBeTruthy()
    if (emitted) {
      expect(emitted[0][0]).toEqual(mockNote)
    }
  })

  it('does not show share button when no note is selected', () => {
    const wrapper = mount(EditorPanel, {
      props: {
        selectedNote: null,
        isFullscreen: false
      },
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    // Should show welcome state, not editor actions
    expect(wrapper.text()).toContain('Welcome to Notes')
    expect(wrapper.find('.editor-actions').exists()).toBe(false)
  })
})
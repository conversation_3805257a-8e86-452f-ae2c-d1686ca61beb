<template>
  <div class="websocket-status">
    <div class="field is-grouped">
      <div class="control">
        <div class="tags has-addons">
          <span class="tag">WebSocket</span>
          <span 
            class="tag" 
            :class="{
              'is-success': connectionStatus === 'connected',
              'is-warning': connectionStatus === 'connecting',
              'is-danger': connectionStatus === 'error',
              'is-light': connectionStatus === 'disconnected'
            }"
          >
            {{ statusText }}
          </span>
        </div>
      </div>
      
      <div class="control" v-if="connectionStatus !== 'connected'">
        <button 
          class="button is-small is-primary" 
          :class="{ 'is-loading': isConnecting }"
          @click="connect"
          :disabled="isConnecting"
        >
          Connect
        </button>
      </div>
      
      <div class="control" v-if="connectionStatus === 'connected'">
        <button 
          class="button is-small is-light" 
          @click="disconnect"
        >
          Disconnect
        </button>
      </div>
    </div>
    
    <div v-if="connectionError" class="notification is-danger is-small mt-2">
      <button class="delete is-small" @click="clearError"></button>
      {{ connectionError }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useWebSocket } from '@/composables/useWebSocket'

const { 
  isConnected, 
  connectionError, 
  isConnecting, 
  connectionStatus,
  connect,
  disconnect
} = useWebSocket()

const statusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return 'Connected'
    case 'connecting':
      return 'Connecting...'
    case 'error':
      return 'Error'
    case 'disconnected':
    default:
      return 'Disconnected'
  }
})

const clearError = () => {
  // The error will be cleared when attempting to reconnect
}
</script>

<style scoped>
.websocket-status {
  margin-bottom: 1rem;
}

.notification.is-small {
  padding: 0.75rem;
  font-size: 0.875rem;
}
</style>

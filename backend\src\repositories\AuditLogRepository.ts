import { getDatabase } from '../config/database';
import { AuditLog, CreateAuditLogData, AuditLogFilter, AuditLogModel } from '../models/AuditLog';

export class AuditLogRepository {
  static async create(data: CreateAuditLogData): Promise<AuditLog> {
    const db = getDatabase();
    const id = AuditLogModel.generateId();
    
    const auditLog: AuditLog = {
      id,
      user_id: data.user_id,
      session_id: data.session_id,
      action: data.action,
      resource_type: data.resource_type,
      resource_id: data.resource_id,
      ip_address: data.ip_address,
      user_agent: data.user_agent,
      request_method: data.request_method,
      request_path: data.request_path,
      request_body: data.request_body,
      response_status: data.response_status,
      response_time_ms: data.response_time_ms,
      metadata: data.metadata,
      created_at: new Date()
    };

    return new Promise((resolve, reject) => {
      const stmt = db.prepare(`
        INSERT INTO audit_logs (
          id, user_id, session_id, action, resource_type, resource_id,
          ip_address, user_agent, request_method, request_path, request_body,
          response_status, response_time_ms, metadata, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run([
        auditLog.id,
        auditLog.user_id,
        auditLog.session_id,
        auditLog.action,
        auditLog.resource_type,
        auditLog.resource_id,
        auditLog.ip_address,
        auditLog.user_agent,
        auditLog.request_method,
        auditLog.request_path,
        auditLog.request_body,
        auditLog.response_status,
        auditLog.response_time_ms,
        auditLog.metadata ? JSON.stringify(auditLog.metadata) : null,
        auditLog.created_at.toISOString()
      ], function(err) {
        stmt.finalize();
        if (err) {
          console.error('Error creating audit log:', err);
          reject(err);
        } else {
          resolve(auditLog);
        }
      });
    });
  }

  static async findById(id: string): Promise<AuditLog | null> {
    const db = getDatabase();
    
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM audit_logs WHERE id = ?',
        [id],
        (err, row) => {
          if (err) {
            reject(err);
          } else if (row) {
            resolve(this.mapRowToAuditLog(row));
          } else {
            resolve(null);
          }
        }
      );
    });
  }

  static async findByFilter(filter: AuditLogFilter): Promise<{ logs: AuditLog[]; total: number }> {
    const db = getDatabase();
    const conditions: string[] = [];
    const params: any[] = [];

    // Build WHERE conditions
    if (filter.user_id) {
      conditions.push('user_id = ?');
      params.push(filter.user_id);
    }

    if (filter.action) {
      conditions.push('action = ?');
      params.push(filter.action);
    }

    if (filter.resource_type) {
      conditions.push('resource_type = ?');
      params.push(filter.resource_type);
    }

    if (filter.resource_id) {
      conditions.push('resource_id = ?');
      params.push(filter.resource_id);
    }

    if (filter.ip_address) {
      conditions.push('ip_address = ?');
      params.push(filter.ip_address);
    }

    if (filter.start_date) {
      conditions.push('created_at >= ?');
      params.push(filter.start_date.toISOString());
    }

    if (filter.end_date) {
      conditions.push('created_at <= ?');
      params.push(filter.end_date.toISOString());
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM audit_logs ${whereClause}`;
    const total = await new Promise<number>((resolve, reject) => {
      db.get(countQuery, params, (err, row: any) => {
        if (err) reject(err);
        else resolve(row.total);
      });
    });

    // Get paginated results
    const limit = filter.limit || 50;
    const offset = filter.offset || 0;
    
    const dataQuery = `
      SELECT * FROM audit_logs 
      ${whereClause} 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    
    const logs = await new Promise<AuditLog[]>((resolve, reject) => {
      db.all(dataQuery, [...params, limit, offset], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => this.mapRowToAuditLog(row)));
        }
      });
    });

    return { logs, total };
  }

  static async findByUserId(userId: string, limit: number = 50, offset: number = 0): Promise<AuditLog[]> {
    const db = getDatabase();
    
    return new Promise((resolve, reject) => {
      db.all(
        'SELECT * FROM audit_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?',
        [userId, limit, offset],
        (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows.map(row => this.mapRowToAuditLog(row)));
          }
        }
      );
    });
  }

  static async findSecurityEvents(hours: number = 24): Promise<AuditLog[]> {
    const db = getDatabase();
    const since = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    const securityActions = [
      'AUTH_LOGIN',
      'AUTH_LOGOUT',
      'AUTH_REGISTER',
      'AUTH_FORGOT_PASSWORD',
      'AUTH_RESET_PASSWORD',
      'AUTH_VERIFY_EMAIL',
      'AUTH_OAUTH_GOOGLE'
    ];

    const placeholders = securityActions.map(() => '?').join(',');
    
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM audit_logs 
         WHERE action IN (${placeholders}) 
         AND created_at >= ? 
         ORDER BY created_at DESC`,
        [...securityActions, since.toISOString()],
        (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows.map(row => this.mapRowToAuditLog(row)));
          }
        }
      );
    });
  }

  static async findFailedAttempts(hours: number = 1): Promise<AuditLog[]> {
    const db = getDatabase();
    const since = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM audit_logs 
         WHERE response_status >= 400 
         AND created_at >= ? 
         ORDER BY created_at DESC`,
        [since.toISOString()],
        (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows.map(row => this.mapRowToAuditLog(row)));
          }
        }
      );
    });
  }

  static async deleteOldLogs(retentionDays: number): Promise<number> {
    const db = getDatabase();
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
    
    return new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM audit_logs WHERE created_at < ?',
        [cutoffDate.toISOString()],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.changes);
          }
        }
      );
    });
  }

  static async getStatistics(days: number = 30): Promise<{
    totalLogs: number;
    uniqueUsers: number;
    topActions: Array<{ action: string; count: number }>;
    topIpAddresses: Array<{ ip_address: string; count: number }>;
    errorRate: number;
  }> {
    const db = getDatabase();
    const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    
    // Get total logs
    const totalLogs = await new Promise<number>((resolve, reject) => {
      db.get(
        'SELECT COUNT(*) as total FROM audit_logs WHERE created_at >= ?',
        [since.toISOString()],
        (err, row: any) => {
          if (err) reject(err);
          else resolve(row.total);
        }
      );
    });

    // Get unique users
    const uniqueUsers = await new Promise<number>((resolve, reject) => {
      db.get(
        'SELECT COUNT(DISTINCT user_id) as total FROM audit_logs WHERE created_at >= ? AND user_id IS NOT NULL',
        [since.toISOString()],
        (err, row: any) => {
          if (err) reject(err);
          else resolve(row.total);
        }
      );
    });

    // Get top actions
    const topActions = await new Promise<Array<{ action: string; count: number }>>((resolve, reject) => {
      db.all(
        'SELECT action, COUNT(*) as count FROM audit_logs WHERE created_at >= ? GROUP BY action ORDER BY count DESC LIMIT 10',
        [since.toISOString()],
        (err, rows: any[]) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Get top IP addresses
    const topIpAddresses = await new Promise<Array<{ ip_address: string; count: number }>>((resolve, reject) => {
      db.all(
        'SELECT ip_address, COUNT(*) as count FROM audit_logs WHERE created_at >= ? GROUP BY ip_address ORDER BY count DESC LIMIT 10',
        [since.toISOString()],
        (err, rows: any[]) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Get error rate
    const errorCount = await new Promise<number>((resolve, reject) => {
      db.get(
        'SELECT COUNT(*) as total FROM audit_logs WHERE created_at >= ? AND response_status >= 400',
        [since.toISOString()],
        (err, row: any) => {
          if (err) reject(err);
          else resolve(row.total);
        }
      );
    });

    const errorRate = totalLogs > 0 ? (errorCount / totalLogs) * 100 : 0;

    return {
      totalLogs,
      uniqueUsers,
      topActions,
      topIpAddresses,
      errorRate
    };
  }

  private static mapRowToAuditLog(row: any): AuditLog {
    return {
      id: row.id,
      user_id: row.user_id,
      session_id: row.session_id,
      action: row.action,
      resource_type: row.resource_type,
      resource_id: row.resource_id,
      ip_address: row.ip_address,
      user_agent: row.user_agent,
      request_method: row.request_method,
      request_path: row.request_path,
      request_body: row.request_body,
      response_status: row.response_status,
      response_time_ms: row.response_time_ms,
      metadata: row.metadata ? JSON.parse(row.metadata) : null,
      created_at: new Date(row.created_at)
    };
  }
}
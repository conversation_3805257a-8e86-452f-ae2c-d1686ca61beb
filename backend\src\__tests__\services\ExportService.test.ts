import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ExportService } from '../../services/ExportService';
import { Note } from '../../models/Note';
import { User } from '../../models/User';
import fs from 'fs/promises';
import path from 'path';

// Mock dependencies
vi.mock('../../models/Note');
vi.mock('../../models/User');
vi.mock('fs/promises');
vi.mock('html-pdf-node');
vi.mock('marked');

describe('ExportService', () => {
  let exportService: ExportService;

  beforeEach(() => {
    vi.clearAllMocks();
    exportService = new ExportService();
  });

  describe('exportUserData', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      displayName: 'Test User',
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-06-01')
    };

    const mockNotes = [
      {
        id: 'note-1',
        title: 'First Note',
        content: 'This is the first note content',
        noteType: 'richtext',
        userId: 'user-123',
        tags: ['work', 'important'],
        createdAt: new Date('2023-02-01'),
        updatedAt: new Date('2023-02-15')
      },
      {
        id: 'note-2',
        title: 'Second Note',
        content: '# Markdown Note\n\nThis is markdown content',
        noteType: 'markdown',
        userId: 'user-123',
        tags: ['personal'],
        createdAt: new Date('2023-03-01'),
        updatedAt: new Date('2023-03-10')
      }
    ];

    const mockSettings = {
      theme: 'dark',
      language: 'en',
      autoSaveInterval: 30000,
      notifications: {
        email: true,
        push: false
      }
    };

    beforeEach(() => {
      vi.mocked(User.findById).mockResolvedValue(mockUser);
      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: mockNotes,
        total: 2,
        page: 1,
        limit: 1000
      });
      vi.mocked(User.getSettings).mockResolvedValue(mockSettings);
    });

    it('should export user data in JSON format', async () => {
      const exportData = await exportService.exportUserData('user-123', {
        format: 'json',
        includeNotes: true,
        includeSettings: true,
        includeProfile: true
      });

      expect(exportData).toBeDefined();
      expect(exportData.user).toEqual(mockUser);
      expect(exportData.notes).toHaveLength(2);
      expect(exportData.settings).toEqual(mockSettings);
      expect(exportData.exportedAt).toBeDefined();
    });

    it('should export only selected data types', async () => {
      const exportData = await exportService.exportUserData('user-123', {
        format: 'json',
        includeNotes: true,
        includeSettings: false,
        includeProfile: false
      });

      expect(exportData.notes).toHaveLength(2);
      expect(exportData.user).toBeUndefined();
      expect(exportData.settings).toBeUndefined();
    });

    it('should filter notes by date range', async () => {
      const exportData = await exportService.exportUserData('user-123', {
        format: 'json',
        includeNotes: true,
        dateFrom: new Date('2023-02-15'),
        dateTo: new Date('2023-03-15')
      });

      expect(Note.findByUserId).toHaveBeenCalledWith('user-123', {
        page: 1,
        limit: 10000,
        dateFrom: new Date('2023-02-15'),
        dateTo: new Date('2023-03-15'),
        includeArchived: true
      });
    });

    it('should filter notes by type', async () => {
      const exportData = await exportService.exportUserData('user-123', {
        format: 'json',
        includeNotes: true,
        noteTypes: ['markdown']
      });

      expect(Note.findByUserId).toHaveBeenCalledWith('user-123', {
        page: 1,
        limit: 10000,
        type: 'markdown',
        includeArchived: true
      });
    });

    it('should handle user not found', async () => {
      vi.mocked(User.findById).mockResolvedValue(null);

      await expect(
        exportService.exportUserData('nonexistent-user', {
          format: 'json',
          includeNotes: true
        })
      ).rejects.toThrow('User not found');
    });
  });

  describe('exportNotesToPDF', () => {
    const mockNotes = [
      {
        id: 'note-1',
        title: 'PDF Test Note',
        content: 'This is content for PDF export',
        noteType: 'richtext',
        userId: 'user-123',
        tags: ['test'],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    beforeEach(() => {
      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: mockNotes,
        total: 1,
        page: 1,
        limit: 1000
      });
    });

    it('should generate PDF from notes', async () => {
      const mockPDFBuffer = Buffer.from('PDF content');
      
      // Mock html-pdf-node
      const htmlPdf = await import('html-pdf-node');
      vi.mocked(htmlPdf.generatePdf).mockResolvedValue(mockPDFBuffer);

      const pdfBuffer = await exportService.exportNotesToPDF('user-123', {
        noteIds: ['note-1']
      });

      expect(pdfBuffer).toEqual(mockPDFBuffer);
      expect(htmlPdf.generatePdf).toHaveBeenCalled();
    });

    it('should handle PDF generation options', async () => {
      const mockPDFBuffer = Buffer.from('PDF content');
      
      const htmlPdf = await import('html-pdf-node');
      vi.mocked(htmlPdf.generatePdf).mockResolvedValue(mockPDFBuffer);

      await exportService.exportNotesToPDF('user-123', {
        noteIds: ['note-1'],
        options: {
          format: 'A4',
          margin: { top: '20px', bottom: '20px' },
          printBackground: true
        }
      });

      expect(htmlPdf.generatePdf).toHaveBeenCalledWith(
        expect.objectContaining({
          content: expect.stringContaining('PDF Test Note')
        }),
        expect.objectContaining({
          format: 'A4',
          margin: { top: '20px', bottom: '20px' },
          printBackground: true
        })
      );
    });

    it('should handle markdown notes in PDF export', async () => {
      const markdownNote = {
        id: 'note-2',
        title: 'Markdown Note',
        content: '# Header\n\nThis is **bold** text',
        noteType: 'markdown',
        userId: 'user-123',
        tags: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: [markdownNote],
        total: 1,
        page: 1,
        limit: 1000
      });

      const marked = await import('marked');
      vi.mocked(marked.parse).mockReturnValue('<h1>Header</h1><p>This is <strong>bold</strong> text</p>');

      const mockPDFBuffer = Buffer.from('PDF content');
      const htmlPdf = await import('html-pdf-node');
      vi.mocked(htmlPdf.generatePdf).mockResolvedValue(mockPDFBuffer);

      await exportService.exportNotesToPDF('user-123', {
        noteIds: ['note-2']
      });

      expect(marked.parse).toHaveBeenCalledWith('# Header\n\nThis is **bold** text');
    });
  });

  describe('exportNotesToMarkdown', () => {
    const mockNotes = [
      {
        id: 'note-1',
        title: 'Rich Text Note',
        content: '<p>This is <strong>rich text</strong> content</p>',
        noteType: 'richtext',
        userId: 'user-123',
        tags: ['export'],
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02')
      },
      {
        id: 'note-2',
        title: 'Markdown Note',
        content: '# Already Markdown\n\nThis is already markdown',
        noteType: 'markdown',
        userId: 'user-123',
        tags: ['markdown'],
        createdAt: new Date('2023-01-03'),
        updatedAt: new Date('2023-01-04')
      }
    ];

    beforeEach(() => {
      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: mockNotes,
        total: 2,
        page: 1,
        limit: 1000
      });
    });

    it('should export notes to markdown format', async () => {
      const markdownContent = await exportService.exportNotesToMarkdown('user-123', {
        noteIds: ['note-1', 'note-2']
      });

      expect(markdownContent).toContain('# Rich Text Note');
      expect(markdownContent).toContain('# Markdown Note');
      expect(markdownContent).toContain('This is **rich text** content');
      expect(markdownContent).toContain('This is already markdown');
    });

    it('should include metadata in markdown export', async () => {
      const markdownContent = await exportService.exportNotesToMarkdown('user-123', {
        noteIds: ['note-1'],
        includeMetadata: true
      });

      expect(markdownContent).toContain('**Created:**');
      expect(markdownContent).toContain('**Updated:**');
      expect(markdownContent).toContain('**Tags:**');
      expect(markdownContent).toContain('**Type:**');
    });

    it('should handle HTML to markdown conversion', async () => {
      const htmlContent = '<h1>Title</h1><p>Paragraph with <em>emphasis</em></p>';
      const mockNote = {
        id: 'note-html',
        title: 'HTML Note',
        content: htmlContent,
        noteType: 'richtext',
        userId: 'user-123',
        tags: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: [mockNote],
        total: 1,
        page: 1,
        limit: 1000
      });

      const markdownContent = await exportService.exportNotesToMarkdown('user-123', {
        noteIds: ['note-html']
      });

      expect(markdownContent).toContain('# Title');
      expect(markdownContent).toContain('Paragraph with *emphasis*');
    });
  });

  describe('createExportJob', () => {
    it('should create export job and return job ID', async () => {
      const jobId = await exportService.createExportJob('user-123', {
        format: 'json',
        includeNotes: true,
        includeSettings: true
      });

      expect(jobId).toBeDefined();
      expect(typeof jobId).toBe('string');
    });

    it('should handle large export jobs asynchronously', async () => {
      const largeMockNotes = Array.from({ length: 10000 }, (_, i) => ({
        id: `note-${i}`,
        title: `Note ${i}`,
        content: `Content for note ${i}`,
        noteType: 'richtext' as const,
        userId: 'user-123',
        tags: [`tag-${i}`],
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: largeMockNotes,
        total: 10000,
        page: 1,
        limit: 10000
      });

      const jobId = await exportService.createExportJob('user-123', {
        format: 'pdf',
        includeNotes: true
      });

      expect(jobId).toBeDefined();
      // Job should be created immediately, processing happens asynchronously
    });
  });

  describe('getExportJobStatus', () => {
    it('should return job status', async () => {
      const jobId = await exportService.createExportJob('user-123', {
        format: 'json',
        includeNotes: true
      });

      const status = await exportService.getExportJobStatus(jobId);

      expect(status).toBeDefined();
      expect(status.id).toBe(jobId);
      expect(status.status).toMatch(/pending|processing|completed|failed/);
    });

    it('should return null for non-existent job', async () => {
      const status = await exportService.getExportJobStatus('non-existent-job');
      expect(status).toBeNull();
    });
  });

  describe('downloadExportFile', () => {
    it('should return export file buffer', async () => {
      const jobId = await exportService.createExportJob('user-123', {
        format: 'json',
        includeNotes: true
      });

      // Mock file system
      const mockFileBuffer = Buffer.from('{"notes": []}');
      vi.mocked(fs.readFile).mockResolvedValue(mockFileBuffer);

      const fileBuffer = await exportService.downloadExportFile(jobId);

      expect(fileBuffer).toEqual(mockFileBuffer);
    });

    it('should throw error for non-existent export file', async () => {
      vi.mocked(fs.readFile).mockRejectedValue(new Error('File not found'));

      await expect(
        exportService.downloadExportFile('non-existent-job')
      ).rejects.toThrow('Export file not found');
    });
  });

  describe('cleanupExpiredExports', () => {
    it('should remove expired export files', async () => {
      const mockFiles = ['export-old.json', 'export-recent.json'];
      const mockStats = {
        'export-old.json': { mtime: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000) }, // 8 days old
        'export-recent.json': { mtime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) } // 1 day old
      };

      vi.mocked(fs.readdir).mockResolvedValue(mockFiles as any);
      vi.mocked(fs.stat).mockImplementation((filePath) => {
        const fileName = path.basename(filePath as string);
        return Promise.resolve(mockStats[fileName] as any);
      });
      vi.mocked(fs.unlink).mockResolvedValue(undefined);

      await exportService.cleanupExpiredExports();

      expect(fs.unlink).toHaveBeenCalledTimes(1);
      expect(fs.unlink).toHaveBeenCalledWith(
        expect.stringContaining('export-old.json')
      );
    });
  });

  describe('error handling', () => {
    it('should handle export errors gracefully', async () => {
      vi.mocked(Note.findByUserId).mockRejectedValue(new Error('Database error'));

      await expect(
        exportService.exportUserData('user-123', {
          format: 'json',
          includeNotes: true
        })
      ).rejects.toThrow('Database error');
    });

    it('should handle PDF generation errors', async () => {
      vi.mocked(Note.findByUserId).mockResolvedValue({
        notes: [{
          id: 'note-1',
          title: 'Test',
          content: 'Content',
          noteType: 'richtext',
          userId: 'user-123',
          tags: [],
          createdAt: new Date(),
          updatedAt: new Date()
        }],
        total: 1,
        page: 1,
        limit: 1000
      });

      const htmlPdf = await import('html-pdf-node');
      vi.mocked(htmlPdf.generatePdf).mockRejectedValue(new Error('PDF generation failed'));

      await expect(
        exportService.exportNotesToPDF('user-123', { noteIds: ['note-1'] })
      ).rejects.toThrow('PDF generation failed');
    });

    it('should validate export options', async () => {
      await expect(
        exportService.exportUserData('', {
          format: 'json',
          includeNotes: false,
          includeSettings: false,
          includeProfile: false
        })
      ).rejects.toThrow('User ID is required');

      await expect(
        exportService.exportUserData('user-123', {
          format: 'invalid' as any,
          includeNotes: true
        })
      ).rejects.toThrow('Invalid export format');
    });
  });
});
<template>
  <div :class="isModal ? 'modal is-active' : 'board-settings-inline'">
    <div v-if="isModal" class="modal-background" @click="$emit('close')"></div>
    <div :class="isModal ? 'modal-card' : 'settings-card'">
      <header class="modal-card-head">
        <p class="modal-card-title">Board Settings</p>
        <button class="delete" @click="$emit('close')"></button>
      </header>

      <section class="modal-card-body">
        <div class="tabs">
          <ul>
            <li :class="{ 'is-active': activeTab === 'general' }">
              <a @click="activeTab = 'general'">General</a>
            </li>
            <li :class="{ 'is-active': activeTab === 'appearance' }">
              <a @click="activeTab = 'appearance'">Appearance</a>
            </li>
            <li :class="{ 'is-active': activeTab === 'permissions' }">
              <a @click="activeTab = 'permissions'">Permissions</a>
            </li>
          </ul>
        </div>

        <!-- General Tab -->
        <div v-if="activeTab === 'general'" class="tab-content">
          <div class="field">
            <label class="label">Board Title</label>
            <div class="control">
              <input v-model="localSettings.title" class="input" type="text" placeholder="Board title" />
            </div>
          </div>

          <div class="field">
            <label class="label">Description</label>
            <div class="control">
              <textarea v-model="localSettings.description" class="textarea" placeholder="Board description"
                rows="3"></textarea>
            </div>
          </div>

          <div class="field">
            <div class="control">
              <label class="checkbox">
                <input v-model="localSettings.settings.allowComments" type="checkbox" />
                Allow comments on cards
              </label>
            </div>
          </div>

          <div class="field">
            <div class="control">
              <label class="checkbox">
                <input v-model="localSettings.settings.allowAttachments" type="checkbox" />
                Allow file attachments
              </label>
            </div>
          </div>

          <div class="field">
            <div class="control">
              <label class="checkbox">
                <input v-model="localSettings.settings.cardCoverImages" type="checkbox" />
                Show cover images on cards
              </label>
            </div>
          </div>

          <div class="field">
            <div class="control">
              <label class="checkbox">
                <input v-model="localSettings.settings.votingEnabled" type="checkbox" />
                Enable voting on cards
              </label>
            </div>
          </div>

          <div class="field">
            <div class="control">
              <label class="checkbox">
                <input v-model="localSettings.settings.dueDateReminders" type="checkbox" />
                Send due date reminders
              </label>
            </div>
          </div>
        </div>

        <!-- Appearance Tab -->
        <div v-if="activeTab === 'appearance'" class="tab-content">
          <div class="field">
            <label class="label">Background Color</label>
            <div class="color-picker-section">
              <div class="color-options">
                <button v-for="color in backgroundColors" :key="color.value" class="color-option"
                  :class="{ 'is-selected': localSettings.settings.backgroundColor === color.value }"
                  :style="{ backgroundColor: color.value }"
                  @click="localSettings.settings.backgroundColor = color.value" :title="color.name" />
              </div>
            </div>
          </div>

          <div class="field">
            <label class="label">Background Image</label>
            <div class="control">
              <input v-model="localSettings.settings.backgroundImage" class="input" type="url"
                placeholder="Background image URL" />
            </div>
            <p class="help">Enter a URL for a background image (optional)</p>
          </div>

          <div v-if="localSettings.settings.backgroundImage" class="field">
            <label class="label">Preview</label>
            <div class="background-preview">
              <img :src="localSettings.settings.backgroundImage" alt="Background preview" @error="handleImageError" />
            </div>
          </div>
        </div>

        <!-- Permissions Tab -->
        <div v-if="activeTab === 'permissions'" class="tab-content">
          <div class="field">
            <div class="control">
              <label class="checkbox">
                <input v-model="localSettings.shareSettings.isPublic" type="checkbox" />
                Make board public
              </label>
            </div>
            <p class="help">Public boards can be viewed by anyone with the link</p>
          </div>

          <div v-if="localSettings.shareSettings.isPublic" class="field">
            <label class="label">Share Link</label>
            <div class="field has-addons">
              <div class="control is-expanded">
                <input :value="shareLink" class="input" type="text" readonly />
              </div>
              <div class="control">
                <button class="button" @click="copyShareLink">
                  <i class="fas fa-copy"></i>
                </button>
              </div>
            </div>
          </div>

          <div class="field">
            <label class="label">Board Members</label>
            <div class="members-list">
              <div v-for="member in board.members" :key="member.id" class="member-item">
                <div class="member-info">
                  <div class="member-avatar">
                    <img v-if="member.avatar" :src="member.avatar" :alt="member.name" />
                    <span v-else>{{ member.name.charAt(0).toUpperCase() }}</span>
                  </div>
                  <div class="member-details">
                    <div class="member-name">{{ member.name }}</div>
                    <div class="member-email">{{ member.email }}</div>
                  </div>
                </div>
                <div class="member-permission">
                  <div class="select is-small">
                    <select :value="localSettings.shareSettings.permissions[member.id] || 'view'"
                      @change="updateMemberPermission(member.id, ($event.target as HTMLSelectElement).value)">
                      <option value="view">View</option>
                      <option value="edit">Edit</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="field">
            <label class="label">Add Member</label>
            <div class="field has-addons">
              <div class="control is-expanded">
                <input v-model="newMemberEmail" class="input" type="email" placeholder="Enter email address" />
              </div>
              <div class="control">
                <button class="button is-primary" @click="addMember" :disabled="!newMemberEmail">
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <footer class="modal-card-foot">
        <button class="button is-primary" @click="saveSettings">Save Changes</button>
        <button class="button" @click="$emit('close')">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useNotificationStore } from '@/stores/notifications'
import type { KanbanBoard } from '@/types/kanban'

interface Props {
  board: KanbanBoard
  isModal?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'update:board', updates: Partial<KanbanBoard>): void
}

const props = withDefaults(defineProps<Props>(), {
  isModal: true
})
const emit = defineEmits<Emits>()

const notificationStore = useNotificationStore()

// Reactive state
const activeTab = ref('general')
const localSettings = ref<KanbanBoard>({ ...props.board })
const newMemberEmail = ref('')

// Background color options
const backgroundColors = [
  { name: 'Default', value: '#f8f9fa' },
  { name: 'Blue', value: '#e3f2fd' },
  { name: 'Green', value: '#e8f5e8' },
  { name: 'Purple', value: '#f3e5f5' },
  { name: 'Orange', value: '#fff3e0' },
  { name: 'Red', value: '#ffebee' },
  { name: 'Teal', value: '#e0f2f1' },
  { name: 'Yellow', value: '#fffde7' },
  { name: 'Pink', value: '#fce4ec' },
  { name: 'Indigo', value: '#e8eaf6' }
]

// Computed
const shareLink = computed(() => {
  if (localSettings.value.shareSettings.shareLink) {
    return `${window.location.origin}/shared/board/${localSettings.value.shareSettings.shareLink}`
  }
  return `${window.location.origin}/shared/board/${props.board.id}`
})

// Methods
const saveSettings = () => {
  emit('update:board', localSettings.value)
  emit('close')

  notificationStore.addNotification({
    type: 'success',
    category: 'system',
    title: 'Success',
    message: 'Board settings updated successfully',
    read: false
  })
}

const updateMemberPermission = (memberId: string, permission: string) => {
  localSettings.value.shareSettings.permissions[memberId] = permission as 'view' | 'edit' | 'admin'
}

const addMember = () => {
  if (!newMemberEmail.value) return

  // In a real app, this would make an API call to invite the user
  notificationStore.addNotification({
    type: 'info',
    category: 'system',
    title: 'Info',
    message: `Invitation sent to ${newMemberEmail.value}`,
    read: false
  })

  newMemberEmail.value = ''
}

const copyShareLink = async () => {
  try {
    await navigator.clipboard.writeText(shareLink.value)
    notificationStore.addNotification({
      type: 'success',
      category: 'system',
      title: 'Success',
      message: 'Share link copied to clipboard',
      read: false
    })
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to copy share link',
      read: false
    })
  }
}

const handleImageError = () => {
  localSettings.value.settings.backgroundImage = ''
  notificationStore.addNotification({
    type: 'critical',
    category: 'system',
    title: 'Error',
    message: 'Invalid image URL',
    read: false
  })
}

// Lifecycle
onMounted(() => {
  // Deep clone the board to avoid mutating the original
  localSettings.value = JSON.parse(JSON.stringify(props.board))
})
</script>

<style scoped>
.modal-card {
  width: 600px;
  max-width: 90vw;
}

.board-settings-inline {
  width: 100%;
}

.settings-card {
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-card .modal-card-head,
.settings-card .modal-card-body,
.settings-card .modal-card-foot {
  border-radius: 0;
}

.settings-card .modal-card-head {
  border-radius: 8px 8px 0 0;
}

.settings-card .modal-card-foot {
  border-radius: 0 0 8px 8px;
}

.tab-content {
  padding-top: 1rem;
}

.color-picker-section {
  margin-top: 0.5rem;
}

.color-options {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.5rem;
  max-width: 200px;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.is-selected {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.background-preview {
  width: 100%;
  height: 120px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #dee2e6;
}

.background-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.members-list {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  max-height: 300px;
  overflow-y: auto;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #f1f3f4;
}

.member-item:last-child {
  border-bottom: none;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  overflow: hidden;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-details {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-weight: 600;
  color: #2c3e50;
}

.member-email {
  font-size: 0.875rem;
  color: #6c757d;
}

.member-permission {
  flex-shrink: 0;
}
</style>
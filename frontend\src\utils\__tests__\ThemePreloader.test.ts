import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ThemePreloader } from '../ThemePreloader'
import type { PreloadStrategy, PreloadProgress } from '../ThemePreloader'

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}
Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage
})

// Mock window.matchMedia
const mockMatchMedia = vi.fn()
Object.defineProperty(global, 'window', {
  value: {
    matchMedia: mockMatchMedia
  },
  writable: true
})

// Mock navigator.serviceWorker
const mockServiceWorker = {
  register: vi.fn(),
  active: {
    postMessage: vi.fn()
  }
}
Object.defineProperty(global, 'navigator', {
  value: {
    serviceWorker: mockServiceWorker
  },
  writable: true
})

// Mock console methods
const consoleSpy = {
  log: vi.spyOn(console, 'log').mockImplementation(() => {}),
  warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
  error: vi.spyOn(console, 'error').mockImplementation(() => {})
}

describe('ThemePreloader', () => {
  let preloader: ThemePreloader
  let mockThemeManager: any
  let mediaQueryList: any

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup media query mock
    mediaQueryList = {
      matches: false
    }
    mockMatchMedia.mockReturnValue(mediaQueryList)

    // Setup service worker mock
    mockServiceWorker.register.mockResolvedValue({
      active: mockServiceWorker.active
    })

    // Setup theme manager mock
    mockThemeManager = {
      preloadTheme: vi.fn().mockResolvedValue(undefined)
    }

    // Reset localStorage mock
    mockLocalStorage.getItem.mockReturnValue(null)

    preloader = new ThemePreloader()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('initialization', () => {
    it('should initialize service worker successfully', async () => {
      // Wait for constructor to complete
      await new Promise(resolve => setTimeout(resolve, 0))
      
      expect(mockServiceWorker.register).toHaveBeenCalledWith('/sw.js')
      expect(consoleSpy.log).toHaveBeenCalledWith('Theme preloader: Service worker registered')
    })

    it('should handle service worker registration failure', async () => {
      mockServiceWorker.register.mockRejectedValue(new Error('SW registration failed'))
      
      new ThemePreloader()
      
      // Wait for async initialization
      await new Promise(resolve => setTimeout(resolve, 0))
      
      expect(consoleSpy.warn).toHaveBeenCalledWith(
        'Theme preloader: Service worker registration failed:',
        expect.any(Error)
      )
    })

    it('should handle missing service worker support', async () => {
      delete (global.navigator as any).serviceWorker
      
      expect(() => new ThemePreloader()).not.toThrow()
      
      // Restore service worker
      ;(global.navigator as any).serviceWorker = mockServiceWorker
    })
  })

  describe('preload strategy generation', () => {
    it('should generate strategy for auto mode with light preference', () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'theme-mode') return 'auto'
        return null
      })
      mediaQueryList.matches = false // light preference
      
      const strategy = preloader.getPreloadStrategy()
      
      expect(strategy.immediate).toContain('default')
      expect(strategy.priority).toContain('darkly')
    })

    it('should generate strategy for auto mode with dark preference', () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'theme-mode') return 'auto'
        return null
      })
      mediaQueryList.matches = true // dark preference
      
      const strategy = preloader.getPreloadStrategy()
      
      expect(strategy.immediate).toContain('darkly')
      expect(strategy.priority).toContain('default')
    })

    it('should generate strategy for specific saved theme', () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'theme-mode') return 'dark'
        if (key === 'theme-name') return 'cerulean'
        return null
      })
      
      const strategy = preloader.getPreloadStrategy()
      
      expect(strategy.immediate).toContain('cerulean')
      expect(strategy.priority).toContain('default')
      expect(strategy.priority).toContain('darkly')
    })

    it('should avoid duplicates in strategy', () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'theme-mode') return 'light'
        if (key === 'theme-name') return 'default'
        return null
      })
      
      const strategy = preloader.getPreloadStrategy()
      
      // default should only appear once
      const allThemes = [...strategy.immediate, ...strategy.priority, ...strategy.background]
      const defaultCount = allThemes.filter(t => t === 'default').length
      expect(defaultCount).toBe(1)
    })

    it('should include background themes', () => {
      const strategy = preloader.getPreloadStrategy()
      
      expect(strategy.background).toContain('flatly')
      expect(strategy.background).toContain('cerulean')
    })
  })

  describe('strategy execution', () => {
    it('should execute complete preload strategy', async () => {
      const strategy: PreloadStrategy = {
        immediate: ['default'],
        priority: ['darkly'],
        background: ['flatly']
      }
      
      const progressCallback = vi.fn()
      preloader.onProgress(progressCallback)
      
      await preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledWith('default')
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledWith('darkly')
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledWith('flatly')
      
      // Should notify progress
      expect(progressCallback).toHaveBeenCalled()
      
      // Final progress should have current: null
      const finalCall = progressCallback.mock.calls[progressCallback.mock.calls.length - 1]
      expect(finalCall[0].current).toBeNull()
    })

    it('should handle theme loading errors gracefully', async () => {
      const strategy: PreloadStrategy = {
        immediate: ['default'],
        priority: ['darkly'],
        background: []
      }
      
      mockThemeManager.preloadTheme.mockImplementation((theme: string) => {
        if (theme === 'darkly') {
          return Promise.reject(new Error('Load failed'))
        }
        return Promise.resolve()
      })
      
      const progressCallback = vi.fn()
      preloader.onProgress(progressCallback)
      
      await preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      // Should still complete execution
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledWith('default')
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledWith('darkly')
      
      // Should track errors in progress
      const progressCalls = progressCallback.mock.calls
      const errorProgress = progressCalls.find(call => call[0].errors.length > 0)
      expect(errorProgress).toBeDefined()
      expect(errorProgress[0].errors[0]).toContain('darkly')
    })

    it('should add delays for background themes', async () => {
      const strategy: PreloadStrategy = {
        immediate: [],
        priority: [],
        background: ['flatly', 'cerulean']
      }
      
      const startTime = Date.now()
      await preloader.executePreloadStrategy(strategy, mockThemeManager)
      const endTime = Date.now()
      
      // Should take at least 200ms due to delay
      expect(endTime - startTime).toBeGreaterThanOrEqual(200)
    })

    it('should track progress correctly', async () => {
      const strategy: PreloadStrategy = {
        immediate: ['default'],
        priority: ['darkly'],
        background: ['flatly']
      }
      
      const progressCallback = vi.fn()
      preloader.onProgress(progressCallback)
      
      await preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      // Should have initial progress call
      expect(progressCallback.mock.calls[0][0]).toEqual({
        total: 3,
        loaded: 0,
        current: null,
        errors: []
      })
      
      // Should have final progress call
      const finalCall = progressCallback.mock.calls[progressCallback.mock.calls.length - 1]
      expect(finalCall[0].loaded).toBe(3)
      expect(finalCall[0].current).toBeNull()
    })
  })

  describe('progress tracking', () => {
    it('should register progress callbacks', () => {
      const callback = vi.fn()
      
      preloader.onProgress(callback)
      
      // Trigger progress notification
      const strategy: PreloadStrategy = { immediate: ['default'], priority: [], background: [] }
      preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      expect(callback).toHaveBeenCalled()
    })

    it('should unregister progress callbacks', async () => {
      const callback = vi.fn()
      
      preloader.onProgress(callback)
      preloader.offProgress(callback)
      
      const strategy: PreloadStrategy = { immediate: ['default'], priority: [], background: [] }
      await preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      expect(callback).not.toHaveBeenCalled()
    })

    it('should handle callback errors gracefully', async () => {
      const errorCallback = vi.fn().mockImplementation(() => {
        throw new Error('Callback error')
      })
      
      preloader.onProgress(errorCallback)
      
      const strategy: PreloadStrategy = { immediate: ['default'], priority: [], background: [] }
      await preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      expect(consoleSpy.error).toHaveBeenCalledWith(
        'Error in preload progress callback:',
        expect.any(Error)
      )
    })
  })

  describe('service worker integration', () => {
    it('should preload via service worker when available', async () => {
      const strategy: PreloadStrategy = {
        immediate: ['default'],
        priority: [],
        background: []
      }
      
      await preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      expect(mockServiceWorker.active.postMessage).toHaveBeenCalledWith({
        type: 'PRELOAD_THEMES',
        themes: [
          '/src/styles/themes/bulmaswatch/default.json',
          '/src/styles/themes/bulmaswatch/default.css'
        ]
      })
    })

    it('should handle missing service worker gracefully', async () => {
      // Remove service worker active
      delete (mockServiceWorker as any).active
      
      const strategy: PreloadStrategy = {
        immediate: ['default'],
        priority: [],
        background: []
      }
      
      await expect(preloader.executePreloadStrategy(strategy, mockThemeManager)).resolves.not.toThrow()
      
      // Restore service worker
      ;(mockServiceWorker as any).active = { postMessage: vi.fn() }
    })
  })

  describe('statistics and management', () => {
    it('should provide preload statistics', () => {
      const stats = preloader.getStats()
      
      expect(stats).toHaveProperty('loaded')
      expect(stats).toHaveProperty('loading')
      expect(stats).toHaveProperty('total')
      expect(typeof stats.loaded).toBe('number')
      expect(typeof stats.loading).toBe('number')
      expect(typeof stats.total).toBe('number')
    })

    it('should clear preload cache', () => {
      preloader.clear()
      
      const stats = preloader.getStats()
      expect(stats.loaded).toBe(0)
      expect(stats.loading).toBe(0)
      expect(stats.total).toBe(0)
    })

    it('should avoid duplicate preloading', async () => {
      const strategy: PreloadStrategy = {
        immediate: ['default', 'default'], // Duplicate theme
        priority: [],
        background: []
      }
      
      await preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      // Should only call preloadTheme once for default
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledTimes(1)
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledWith('default')
    })
  })

  describe('usage tracking', () => {
    it('should record theme usage', () => {
      preloader.recordThemeUsage('default')
      preloader.recordThemeUsage('default')
      preloader.recordThemeUsage('darkly')
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'theme-usage-stats',
        JSON.stringify({ default: 2, darkly: 1 })
      )
    })

    it('should handle usage recording errors gracefully', () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage full')
      })
      
      expect(() => preloader.recordThemeUsage('default')).not.toThrow()
      expect(consoleSpy.warn).toHaveBeenCalledWith(
        'Failed to record theme usage:',
        expect.any(Error)
      )
    })

    it('should preload based on usage patterns', async () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'theme-usage-stats') {
          return JSON.stringify({
            'cerulean': 10,
            'default': 8,
            'darkly': 5,
            'flatly': 3,
            'cosmo': 1
          })
        }
        return null
      })
      
      await preloader.preloadBasedOnUsage()
      
      expect(consoleSpy.log).toHaveBeenCalledWith(
        'Usage-based preload strategy:',
        expect.objectContaining({
          immediate: ['cerulean'],
          priority: ['default', 'darkly'],
          background: ['flatly', 'cosmo']
        })
      )
    })

    it('should handle missing usage stats gracefully', async () => {
      mockLocalStorage.getItem.mockReturnValue(null)
      
      await expect(preloader.preloadBasedOnUsage()).resolves.not.toThrow()
    })

    it('should handle corrupted usage stats gracefully', async () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'theme-usage-stats') {
          return 'invalid json'
        }
        return null
      })
      
      await expect(preloader.preloadBasedOnUsage()).resolves.not.toThrow()
    })
  })

  describe('concurrent loading', () => {
    it('should handle concurrent preload requests', async () => {
      const strategy: PreloadStrategy = {
        immediate: ['default'],
        priority: [],
        background: []
      }
      
      // Start multiple preload operations
      const promise1 = preloader.executePreloadStrategy(strategy, mockThemeManager)
      const promise2 = preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      await Promise.all([promise1, promise2])
      
      // Should only preload once
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledTimes(1)
    })

    it('should track loading state correctly', async () => {
      let resolvePreload: () => void
      const preloadPromise = new Promise<void>((resolve) => {
        resolvePreload = resolve
      })
      
      mockThemeManager.preloadTheme.mockReturnValue(preloadPromise)
      
      const strategy: PreloadStrategy = {
        immediate: ['default'],
        priority: [],
        background: []
      }
      
      const executePromise = preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      // Should show loading state
      const stats = preloader.getStats()
      expect(stats.loading).toBe(1)
      
      resolvePreload!()
      await executePromise
      
      // Should show loaded state
      const finalStats = preloader.getStats()
      expect(finalStats.loaded).toBe(1)
      expect(finalStats.loading).toBe(0)
    })
  })

  describe('error handling', () => {
    it('should handle theme manager errors', async () => {
      mockThemeManager.preloadTheme.mockRejectedValue(new Error('Theme manager error'))
      
      const strategy: PreloadStrategy = {
        immediate: ['default'],
        priority: [],
        background: []
      }
      
      await expect(preloader.executePreloadStrategy(strategy, mockThemeManager)).resolves.not.toThrow()
      
      expect(consoleSpy.warn).toHaveBeenCalledWith(
        'Failed to preload theme default:',
        expect.any(Error)
      )
    })

    it('should continue execution after errors', async () => {
      mockThemeManager.preloadTheme.mockImplementation((theme: string) => {
        if (theme === 'default') {
          return Promise.reject(new Error('Load failed'))
        }
        return Promise.resolve()
      })
      
      const strategy: PreloadStrategy = {
        immediate: ['default'],
        priority: ['darkly'],
        background: []
      }
      
      await preloader.executePreloadStrategy(strategy, mockThemeManager)
      
      // Should still try to load darkly after default fails
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledWith('default')
      expect(mockThemeManager.preloadTheme).toHaveBeenCalledWith('darkly')
    })
  })
})
import { v4 as uuidv4 } from 'uuid';

export interface AuditLog {
  id: string;
  user_id?: string;
  session_id?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  ip_address: string;
  user_agent?: string;
  request_method: string;
  request_path: string;
  request_body?: string;
  response_status: number;
  response_time_ms: number;
  metadata?: Record<string, any>;
  created_at: Date;
}

export interface CreateAuditLogData {
  user_id?: string;
  session_id?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  ip_address: string;
  user_agent?: string;
  request_method: string;
  request_path: string;
  request_body?: string;
  response_status: number;
  response_time_ms: number;
  metadata?: Record<string, any>;
}

export interface AuditLogFilter {
  user_id?: string;
  action?: string;
  resource_type?: string;
  resource_id?: string;
  ip_address?: string;
  start_date?: Date;
  end_date?: Date;
  limit?: number;
  offset?: number;
}

export class AuditLogModel {
  static generateId(): string {
    return uuidv4();
  }

  static sanitizeRequestBody(body: any, sensitiveFields: string[] = []): string | undefined {
    if (!body) return undefined;
    
    try {
      const sanitized = { ...body };
      
      // Default sensitive fields to remove
      const defaultSensitiveFields = [
        'password',
        'password_hash',
        'token',
        'refresh_token',
        'access_token',
        'two_fa_secret',
        'oauth_token',
        'api_key',
        'secret'
      ];
      
      const fieldsToRemove = [...defaultSensitiveFields, ...sensitiveFields];
      
      // Recursively remove sensitive fields
      const removeSensitiveFields = (obj: any): any => {
        if (typeof obj !== 'object' || obj === null) return obj;
        
        if (Array.isArray(obj)) {
          return obj.map(removeSensitiveFields);
        }
        
        const cleaned: any = {};
        for (const [key, value] of Object.entries(obj)) {
          if (fieldsToRemove.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
            cleaned[key] = '[REDACTED]';
          } else {
            cleaned[key] = removeSensitiveFields(value);
          }
        }
        return cleaned;
      };
      
      const cleanedBody = removeSensitiveFields(sanitized);
      return JSON.stringify(cleanedBody);
    } catch (error) {
      return '[INVALID_JSON]';
    }
  }

  static getActionFromRequest(method: string, path: string): string {
    const normalizedPath = path.toLowerCase();
    
    // Authentication actions
    if (normalizedPath.includes('/auth/login')) return 'AUTH_LOGIN';
    if (normalizedPath.includes('/auth/logout')) return 'AUTH_LOGOUT';
    if (normalizedPath.includes('/auth/register')) return 'AUTH_REGISTER';
    if (normalizedPath.includes('/auth/refresh')) return 'AUTH_REFRESH';
    if (normalizedPath.includes('/auth/forgot-password')) return 'AUTH_FORGOT_PASSWORD';
    if (normalizedPath.includes('/auth/reset-password')) return 'AUTH_RESET_PASSWORD';
    if (normalizedPath.includes('/auth/verify-email')) return 'AUTH_VERIFY_EMAIL';
    if (normalizedPath.includes('/auth/google')) return 'AUTH_OAUTH_GOOGLE';
    
    // Note actions
    if (normalizedPath.includes('/notes')) {
      switch (method.toUpperCase()) {
        case 'GET': return normalizedPath.includes('/notes/') ? 'NOTE_VIEW' : 'NOTE_LIST';
        case 'POST': return 'NOTE_CREATE';
        case 'PUT': return 'NOTE_UPDATE';
        case 'DELETE': return 'NOTE_DELETE';
      }
    }
    
    // Share actions
    if (normalizedPath.includes('/share')) {
      switch (method.toUpperCase()) {
        case 'GET': return 'SHARE_ACCESS';
        case 'POST': return 'SHARE_CREATE';
        case 'PUT': return 'SHARE_UPDATE';
        case 'DELETE': return 'SHARE_DELETE';
      }
    }
    
    // Group actions
    if (normalizedPath.includes('/groups')) {
      switch (method.toUpperCase()) {
        case 'GET': return normalizedPath.includes('/groups/') ? 'GROUP_VIEW' : 'GROUP_LIST';
        case 'POST': return 'GROUP_CREATE';
        case 'PUT': return 'GROUP_UPDATE';
        case 'DELETE': return 'GROUP_DELETE';
      }
    }
    
    // User actions
    if (normalizedPath.includes('/user')) {
      switch (method.toUpperCase()) {
        case 'GET': return 'USER_VIEW';
        case 'PUT': return 'USER_UPDATE';
        case 'DELETE': return 'USER_DELETE';
      }
    }
    
    // Search actions
    if (normalizedPath.includes('/search')) return 'SEARCH_QUERY';
    
    // Template actions
    if (normalizedPath.includes('/templates')) {
      switch (method.toUpperCase()) {
        case 'GET': return 'TEMPLATE_VIEW';
        case 'POST': return 'TEMPLATE_CREATE';
        case 'PUT': return 'TEMPLATE_UPDATE';
        case 'DELETE': return 'TEMPLATE_DELETE';
      }
    }
    
    // Default action based on HTTP method
    return `${method.toUpperCase()}_REQUEST`;
  }

  static getResourceTypeFromPath(path: string): string {
    const normalizedPath = path.toLowerCase();
    
    if (normalizedPath.includes('/auth')) return 'authentication';
    if (normalizedPath.includes('/notes')) return 'note';
    if (normalizedPath.includes('/share')) return 'share';
    if (normalizedPath.includes('/groups')) return 'group';
    if (normalizedPath.includes('/user')) return 'user';
    if (normalizedPath.includes('/search')) return 'search';
    if (normalizedPath.includes('/templates')) return 'template';
    
    return 'unknown';
  }

  static extractResourceId(path: string): string | undefined {
    // Extract UUID patterns from path
    const uuidRegex = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i;
    const match = path.match(uuidRegex);
    return match ? match[0] : undefined;
  }

  static shouldLogRequest(path: string, method: string): boolean {
    const normalizedPath = path.toLowerCase();
    
    // Don't log health checks and static assets
    if (normalizedPath === '/health') return false;
    if (normalizedPath.startsWith('/static/')) return false;
    if (normalizedPath.startsWith('/assets/')) return false;
    
    // Don't log OPTIONS requests (CORS preflight)
    if (method.toUpperCase() === 'OPTIONS') return false;
    
    return true;
  }
}
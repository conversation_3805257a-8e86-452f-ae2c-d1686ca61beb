// Validation script for authentication fixes
// This script checks that the necessary code changes have been applied

const fs = require('fs');
const path = require('path');

function checkFileContains(filePath, searchText, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const found = content.includes(searchText);
    console.log(`${found ? '✅' : '❌'} ${description}`);
    return found;
  } catch (error) {
    console.log(`❌ ${description} - File not found: ${filePath}`);
    return false;
  }
}

function validateAuthFixes() {
  console.log('🔍 Validating authentication fixes...\n');
  
  let allChecksPass = true;
  
  // Check Sidebar component
  console.log('📁 Checking Sidebar component:');
  allChecksPass &= checkFileContains(
    'frontend/src/components/layout/Sidebar.vue',
    'if (authStore?.isAuthenticated && groupsStore',
    'Authentication check before loading groups'
  );
  allChecksPass &= checkFileContains(
    'frontend/src/components/layout/Sidebar.vue',
    'handleAuthExpired',
    'Auth expired event handler'
  );
  
  // Check QuickStatsWidget component
  console.log('\n📁 Checking QuickStatsWidget component:');
  allChecksPass &= checkFileContains(
    'frontend/src/components/dashboard/QuickStatsWidget.vue',
    'import { useAuthStore }',
    'Auth store import'
  );
  allChecksPass &= checkFileContains(
    'frontend/src/components/dashboard/QuickStatsWidget.vue',
    'if (authStore.isAuthenticated)',
    'Authentication check before loading data'
  );
  
  // Check groups store
  console.log('\n📁 Checking groups store:');
  allChecksPass &= checkFileContains(
    'frontend/src/stores/groups.ts',
    'if (!authStore.isAuthenticated || !authStore.token)',
    'Authentication check in loadGroups'
  );
  allChecksPass &= checkFileContains(
    'frontend/src/stores/groups.ts',
    'groups.value = []',
    'Clear groups on auth failure'
  );
  
  // Check HTTP client
  console.log('\n📁 Checking HTTP client:');
  allChecksPass &= checkFileContains(
    'frontend/src/utils/http.ts',
    'auth-expired',
    'Auth expired event dispatch'
  );
  allChecksPass &= checkFileContains(
    'frontend/src/utils/http.ts',
    'HTTP 401',
    'Improved 401 error handling'
  );
  
  // Check main app
  console.log('\n📁 Checking main app:');
  allChecksPass &= checkFileContains(
    'frontend/src/main-full.ts',
    'auth-expired',
    'Auth expired event listener'
  );
  
  console.log('\n' + '='.repeat(50));
  
  if (allChecksPass) {
    console.log('🎉 All authentication fixes have been successfully applied!');
    console.log('\n📋 Summary of fixes:');
    console.log('   ✅ Components now check authentication before loading data');
    console.log('   ✅ Groups store validates auth before API calls');
    console.log('   ✅ HTTP client handles 401 errors gracefully');
    console.log('   ✅ Auth expired events are properly handled');
    console.log('   ✅ Event listeners clean up properly');
    
    console.log('\n🔍 The fixes should resolve:');
    console.log('   • HTTP 401 errors when loading groups');
    console.log('   • Components trying to load data without authentication');
    console.log('   • Graceful handling of expired sessions');
    
  } else {
    console.log('⚠️  Some authentication fixes may not have been applied correctly.');
    console.log('Please check the failed items above.');
  }
  
  console.log('\n🚀 To test the fixes:');
  console.log('   1. Open the app in your browser');
  console.log('   2. Open browser dev tools (F12)');
  console.log('   3. Check the Console tab for authentication messages');
  console.log('   4. Check the Network tab for HTTP 401 errors');
  console.log('   5. You should see "User not authenticated" messages instead of 401 errors');
}

validateAuthFixes();
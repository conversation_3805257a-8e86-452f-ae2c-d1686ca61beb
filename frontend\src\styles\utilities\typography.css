/* Typography Utility Classes */

/* Font Family Utilities */
.has-font-sans {
  font-family: var(--font-family-sans) !important;
}
.has-font-mono {
  font-family: var(--font-family-mono) !important;
}

/* Font Size Utilities */
.is-size-1 {
  font-size: var(--font-size-6xl) !important;
}
.is-size-2 {
  font-size: var(--font-size-5xl) !important;
}
.is-size-3 {
  font-size: var(--font-size-4xl) !important;
}
.is-size-4 {
  font-size: var(--font-size-3xl) !important;
}
.is-size-5 {
  font-size: var(--font-size-2xl) !important;
}
.is-size-6 {
  font-size: var(--font-size-xl) !important;
}
.is-size-7 {
  font-size: var(--font-size-lg) !important;
}

.has-text-xs {
  font-size: var(--font-size-xs) !important;
}
.has-text-sm {
  font-size: var(--font-size-sm) !important;
}
.has-text-base {
  font-size: var(--font-size-base) !important;
}
.has-text-lg {
  font-size: var(--font-size-lg) !important;
}
.has-text-xl {
  font-size: var(--font-size-xl) !important;
}
.has-text-2xl {
  font-size: var(--font-size-2xl) !important;
}
.has-text-3xl {
  font-size: var(--font-size-3xl) !important;
}
.has-text-4xl {
  font-size: var(--font-size-4xl) !important;
}
.has-text-5xl {
  font-size: var(--font-size-5xl) !important;
}
.has-text-6xl {
  font-size: var(--font-size-6xl) !important;
}

/* Font Weight Utilities */
.has-text-weight-light {
  font-weight: var(--font-weight-light) !important;
}
.has-text-weight-normal {
  font-weight: var(--font-weight-normal) !important;
}
.has-text-weight-medium {
  font-weight: var(--font-weight-medium) !important;
}
.has-text-weight-semibold {
  font-weight: var(--font-weight-semibold) !important;
}
.has-text-weight-bold {
  font-weight: var(--font-weight-bold) !important;
}

/* Line Height Utilities */
.has-line-height-none {
  line-height: var(--line-height-none) !important;
}
.has-line-height-tight {
  line-height: var(--line-height-tight) !important;
}
.has-line-height-snug {
  line-height: var(--line-height-snug) !important;
}
.has-line-height-normal {
  line-height: var(--line-height-normal) !important;
}
.has-line-height-relaxed {
  line-height: var(--line-height-relaxed) !important;
}
.has-line-height-loose {
  line-height: var(--line-height-loose) !important;
}

/* Letter Spacing Utilities */
.has-letter-spacing-tighter {
  letter-spacing: var(--letter-spacing-tighter) !important;
}
.has-letter-spacing-tight {
  letter-spacing: var(--letter-spacing-tight) !important;
}
.has-letter-spacing-normal {
  letter-spacing: var(--letter-spacing-normal) !important;
}
.has-letter-spacing-wide {
  letter-spacing: var(--letter-spacing-wide) !important;
}
.has-letter-spacing-wider {
  letter-spacing: var(--letter-spacing-wider) !important;
}
.has-letter-spacing-widest {
  letter-spacing: var(--letter-spacing-widest) !important;
}

/* Text Alignment */
.has-text-left {
  text-align: left !important;
}
.has-text-centered {
  text-align: center !important;
}
.has-text-right {
  text-align: right !important;
}
.has-text-justified {
  text-align: justify !important;
}

/* Text Transform */
.has-text-uppercase {
  text-transform: uppercase !important;
}
.has-text-lowercase {
  text-transform: lowercase !important;
}
.has-text-capitalize {
  text-transform: capitalize !important;
}
.has-text-normal-case {
  text-transform: none !important;
}

/* Text Decoration */
.has-text-underlined {
  text-decoration: underline !important;
}
.has-text-line-through {
  text-decoration: line-through !important;
}
.has-text-no-decoration {
  text-decoration: none !important;
}

/* Text Style */
.has-text-italic {
  font-style: italic !important;
}
.has-text-not-italic {
  font-style: normal !important;
}

/* Vertical Alignment */
.has-text-baseline {
  vertical-align: baseline !important;
}
.has-text-top {
  vertical-align: top !important;
}
.has-text-middle {
  vertical-align: middle !important;
}
.has-text-bottom {
  vertical-align: bottom !important;
}
.has-text-text-top {
  vertical-align: text-top !important;
}
.has-text-text-bottom {
  vertical-align: text-bottom !important;
}

/* White Space */
.has-text-nowrap {
  white-space: nowrap !important;
}
.has-text-pre {
  white-space: pre !important;
}
.has-text-pre-line {
  white-space: pre-line !important;
}
.has-text-pre-wrap {
  white-space: pre-wrap !important;
}
.has-text-break-spaces {
  white-space: break-spaces !important;
}

/* Word Break */
.has-text-break-normal {
  word-break: normal !important;
  overflow-wrap: normal !important;
}
.has-text-break-words {
  overflow-wrap: break-word !important;
}
.has-text-break-all {
  word-break: break-all !important;
}

/* Text Overflow */
.has-text-truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.has-text-ellipsis {
  text-overflow: ellipsis !important;
}
.has-text-clip {
  text-overflow: clip !important;
}

/* Text Selection */
.has-text-select-none {
  user-select: none !important;
}
.has-text-select-text {
  user-select: text !important;
}
.has-text-select-all {
  user-select: all !important;
}
.has-text-select-auto {
  user-select: auto !important;
}

/* List Style */
.has-list-none {
  list-style-type: none !important;
}
.has-list-disc {
  list-style-type: disc !important;
}
.has-list-decimal {
  list-style-type: decimal !important;
}
.has-list-square {
  list-style-type: square !important;
}
.has-list-circle {
  list-style-type: circle !important;
}

.has-list-inside {
  list-style-position: inside !important;
}
.has-list-outside {
  list-style-position: outside !important;
}

/* Text Indent */
.has-text-indent-0 {
  text-indent: 0 !important;
}
.has-text-indent-1 {
  text-indent: var(--spacing-1) !important;
}
.has-text-indent-2 {
  text-indent: var(--spacing-2) !important;
}
.has-text-indent-3 {
  text-indent: var(--spacing-3) !important;
}
.has-text-indent-4 {
  text-indent: var(--spacing-4) !important;
}

/* Font Variant */
.has-text-small-caps {
  font-variant: small-caps !important;
}
.has-text-normal-caps {
  font-variant: normal !important;
}

/* Text Shadow */
.has-text-shadow-sm {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}
.has-text-shadow {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}
.has-text-shadow-md {
  text-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}
.has-text-shadow-lg {
  text-shadow: 0 10px 15px rgba(0, 0, 0, 0.1) !important;
}
.has-text-shadow-none {
  text-shadow: none !important;
}

/* Responsive Typography */
@media screen and (max-width: 768px) {
  .is-size-1-mobile {
    font-size: var(--font-size-4xl) !important;
  }
  .is-size-2-mobile {
    font-size: var(--font-size-3xl) !important;
  }
  .is-size-3-mobile {
    font-size: var(--font-size-2xl) !important;
  }
  .is-size-4-mobile {
    font-size: var(--font-size-xl) !important;
  }
  .is-size-5-mobile {
    font-size: var(--font-size-lg) !important;
  }
  .is-size-6-mobile {
    font-size: var(--font-size-base) !important;
  }
  .is-size-7-mobile {
    font-size: var(--font-size-sm) !important;
  }

  .has-text-left-mobile {
    text-align: left !important;
  }
  .has-text-centered-mobile {
    text-align: center !important;
  }
  .has-text-right-mobile {
    text-align: right !important;
  }

  .has-text-nowrap-mobile {
    white-space: nowrap !important;
  }
  .has-text-wrap-mobile {
    white-space: normal !important;
  }
}

@media screen and (min-width: 769px) and (max-width: 1023px) {
  .is-size-1-tablet {
    font-size: var(--font-size-5xl) !important;
  }
  .is-size-2-tablet {
    font-size: var(--font-size-4xl) !important;
  }
  .is-size-3-tablet {
    font-size: var(--font-size-3xl) !important;
  }
  .is-size-4-tablet {
    font-size: var(--font-size-2xl) !important;
  }
  .is-size-5-tablet {
    font-size: var(--font-size-xl) !important;
  }
  .is-size-6-tablet {
    font-size: var(--font-size-lg) !important;
  }
  .is-size-7-tablet {
    font-size: var(--font-size-base) !important;
  }

  .has-text-left-tablet {
    text-align: left !important;
  }
  .has-text-centered-tablet {
    text-align: center !important;
  }
  .has-text-right-tablet {
    text-align: right !important;
  }
}

@media screen and (min-width: 1024px) {
  .is-size-1-desktop {
    font-size: var(--font-size-6xl) !important;
  }
  .is-size-2-desktop {
    font-size: var(--font-size-5xl) !important;
  }
  .is-size-3-desktop {
    font-size: var(--font-size-4xl) !important;
  }
  .is-size-4-desktop {
    font-size: var(--font-size-3xl) !important;
  }
  .is-size-5-desktop {
    font-size: var(--font-size-2xl) !important;
  }
  .is-size-6-desktop {
    font-size: var(--font-size-xl) !important;
  }
  .is-size-7-desktop {
    font-size: var(--font-size-lg) !important;
  }

  .has-text-left-desktop {
    text-align: left !important;
  }
  .has-text-centered-desktop {
    text-align: center !important;
  }
  .has-text-right-desktop {
    text-align: right !important;
  }
}

/* Accessibility Typography */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.not-sr-only {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

/* Focus Visible Typography */
.focus-visible-text:focus-visible {
  outline: 2px solid var(--color-primary) !important;
  outline-offset: 2px !important;
}

/* Print Typography */
@media print {
  .has-text-black-print {
    color: #000000 !important;
  }
  .has-text-sm-print {
    font-size: var(--font-size-sm) !important;
  }
  .has-text-base-print {
    font-size: var(--font-size-base) !important;
  }
  .has-text-lg-print {
    font-size: var(--font-size-lg) !important;
  }

  .has-text-left-print {
    text-align: left !important;
  }
  .has-text-centered-print {
    text-align: center !important;
  }
  .has-text-right-print {
    text-align: right !important;
  }

  .has-text-no-decoration-print {
    text-decoration: none !important;
  }
  .has-text-underlined-print {
    text-decoration: underline !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .has-text-high-contrast {
    color: CanvasText !important;
    background-color: Canvas !important;
  }

  .has-text-high-contrast-link {
    color: LinkText !important;
  }

  .has-text-high-contrast-visited {
    color: VisitedText !important;
  }
}

/* Reduced Motion Typography */
@media (prefers-reduced-motion: reduce) {
  .has-text-no-animation {
    animation: none !important;
    transition: none !important;
  }
}

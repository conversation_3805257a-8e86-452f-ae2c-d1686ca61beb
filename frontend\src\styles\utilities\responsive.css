/* Responsive Utility Classes */

/* Display Utilities */
.is-block {
  display: block !important;
}
.is-flex {
  display: flex !important;
}
.is-inline {
  display: inline !important;
}
.is-inline-block {
  display: inline-block !important;
}
.is-inline-flex {
  display: inline-flex !important;
}
.is-grid {
  display: grid !important;
}
.is-inline-grid {
  display: inline-grid !important;
}
.is-table {
  display: table !important;
}
.is-table-cell {
  display: table-cell !important;
}
.is-table-row {
  display: table-row !important;
}
.is-hidden {
  display: none !important;
}

/* Responsive Display Utilities */
@media screen and (max-width: 768px) {
  .is-block-mobile {
    display: block !important;
  }
  .is-flex-mobile {
    display: flex !important;
  }
  .is-inline-mobile {
    display: inline !important;
  }
  .is-inline-block-mobile {
    display: inline-block !important;
  }
  .is-inline-flex-mobile {
    display: inline-flex !important;
  }
  .is-grid-mobile {
    display: grid !important;
  }
  .is-hidden-mobile {
    display: none !important;
  }
}

@media screen and (min-width: 769px) and (max-width: 1023px) {
  .is-block-tablet {
    display: block !important;
  }
  .is-flex-tablet {
    display: flex !important;
  }
  .is-inline-tablet {
    display: inline !important;
  }
  .is-inline-block-tablet {
    display: inline-block !important;
  }
  .is-inline-flex-tablet {
    display: inline-flex !important;
  }
  .is-grid-tablet {
    display: grid !important;
  }
  .is-hidden-tablet {
    display: none !important;
  }
}

@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .is-block-desktop {
    display: block !important;
  }
  .is-flex-desktop {
    display: flex !important;
  }
  .is-inline-desktop {
    display: inline !important;
  }
  .is-inline-block-desktop {
    display: inline-block !important;
  }
  .is-inline-flex-desktop {
    display: inline-flex !important;
  }
  .is-grid-desktop {
    display: grid !important;
  }
  .is-hidden-desktop {
    display: none !important;
  }
}

@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .is-block-widescreen {
    display: block !important;
  }
  .is-flex-widescreen {
    display: flex !important;
  }
  .is-inline-widescreen {
    display: inline !important;
  }
  .is-inline-block-widescreen {
    display: inline-block !important;
  }
  .is-inline-flex-widescreen {
    display: inline-flex !important;
  }
  .is-grid-widescreen {
    display: grid !important;
  }
  .is-hidden-widescreen {
    display: none !important;
  }
}

@media screen and (min-width: 1408px) {
  .is-block-fullhd {
    display: block !important;
  }
  .is-flex-fullhd {
    display: flex !important;
  }
  .is-inline-fullhd {
    display: inline !important;
  }
  .is-inline-block-fullhd {
    display: inline-block !important;
  }
  .is-inline-flex-fullhd {
    display: inline-flex !important;
  }
  .is-grid-fullhd {
    display: grid !important;
  }
  .is-hidden-fullhd {
    display: none !important;
  }
}

/* Visibility Utilities */
.is-visible {
  visibility: visible !important;
}
.is-invisible {
  visibility: hidden !important;
}

/* Responsive Visibility */
@media screen and (max-width: 768px) {
  .is-visible-mobile {
    visibility: visible !important;
  }
  .is-invisible-mobile {
    visibility: hidden !important;
  }
}

@media screen and (min-width: 769px) and (max-width: 1023px) {
  .is-visible-tablet {
    visibility: visible !important;
  }
  .is-invisible-tablet {
    visibility: hidden !important;
  }
}

@media screen and (min-width: 1024px) {
  .is-visible-desktop {
    visibility: visible !important;
  }
  .is-invisible-desktop {
    visibility: hidden !important;
  }
}

/* Flexbox Utilities */
.is-flex-direction-row {
  flex-direction: row !important;
}
.is-flex-direction-row-reverse {
  flex-direction: row-reverse !important;
}
.is-flex-direction-column {
  flex-direction: column !important;
}
.is-flex-direction-column-reverse {
  flex-direction: column-reverse !important;
}

.is-flex-wrap-nowrap {
  flex-wrap: nowrap !important;
}
.is-flex-wrap-wrap {
  flex-wrap: wrap !important;
}
.is-flex-wrap-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.is-justify-content-flex-start {
  justify-content: flex-start !important;
}
.is-justify-content-flex-end {
  justify-content: flex-end !important;
}
.is-justify-content-center {
  justify-content: center !important;
}
.is-justify-content-space-between {
  justify-content: space-between !important;
}
.is-justify-content-space-around {
  justify-content: space-around !important;
}
.is-justify-content-space-evenly {
  justify-content: space-evenly !important;
}

.is-align-content-flex-start {
  align-content: flex-start !important;
}
.is-align-content-flex-end {
  align-content: flex-end !important;
}
.is-align-content-center {
  align-content: center !important;
}
.is-align-content-space-between {
  align-content: space-between !important;
}
.is-align-content-space-around {
  align-content: space-around !important;
}
.is-align-content-stretch {
  align-content: stretch !important;
}

.is-align-items-stretch {
  align-items: stretch !important;
}
.is-align-items-flex-start {
  align-items: flex-start !important;
}
.is-align-items-flex-end {
  align-items: flex-end !important;
}
.is-align-items-center {
  align-items: center !important;
}
.is-align-items-baseline {
  align-items: baseline !important;
}

.is-align-self-auto {
  align-self: auto !important;
}
.is-align-self-flex-start {
  align-self: flex-start !important;
}
.is-align-self-flex-end {
  align-self: flex-end !important;
}
.is-align-self-center {
  align-self: center !important;
}
.is-align-self-baseline {
  align-self: baseline !important;
}
.is-align-self-stretch {
  align-self: stretch !important;
}

.is-flex-grow-0 {
  flex-grow: 0 !important;
}
.is-flex-grow-1 {
  flex-grow: 1 !important;
}

.is-flex-shrink-0 {
  flex-shrink: 0 !important;
}
.is-flex-shrink-1 {
  flex-shrink: 1 !important;
}

/* Responsive Flexbox */
@media screen and (max-width: 768px) {
  .is-flex-direction-column-mobile {
    flex-direction: column !important;
  }
  .is-flex-direction-row-mobile {
    flex-direction: row !important;
  }
  .is-justify-content-center-mobile {
    justify-content: center !important;
  }
  .is-align-items-center-mobile {
    align-items: center !important;
  }
}

@media screen and (min-width: 769px) and (max-width: 1023px) {
  .is-flex-direction-column-tablet {
    flex-direction: column !important;
  }
  .is-flex-direction-row-tablet {
    flex-direction: row !important;
  }
  .is-justify-content-center-tablet {
    justify-content: center !important;
  }
  .is-align-items-center-tablet {
    align-items: center !important;
  }
}

@media screen and (min-width: 1024px) {
  .is-flex-direction-column-desktop {
    flex-direction: column !important;
  }
  .is-flex-direction-row-desktop {
    flex-direction: row !important;
  }
  .is-justify-content-center-desktop {
    justify-content: center !important;
  }
  .is-align-items-center-desktop {
    align-items: center !important;
  }
}

/* Grid Utilities */
.is-grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
}
.is-grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
}
.is-grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
}
.is-grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
}
.is-grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr)) !important;
}
.is-grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr)) !important;
}
.is-grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr)) !important;
}

.is-grid-rows-1 {
  grid-template-rows: repeat(1, minmax(0, 1fr)) !important;
}
.is-grid-rows-2 {
  grid-template-rows: repeat(2, minmax(0, 1fr)) !important;
}
.is-grid-rows-3 {
  grid-template-rows: repeat(3, minmax(0, 1fr)) !important;
}
.is-grid-rows-4 {
  grid-template-rows: repeat(4, minmax(0, 1fr)) !important;
}
.is-grid-rows-5 {
  grid-template-rows: repeat(5, minmax(0, 1fr)) !important;
}
.is-grid-rows-6 {
  grid-template-rows: repeat(6, minmax(0, 1fr)) !important;
}

.is-col-span-1 {
  grid-column: span 1 / span 1 !important;
}
.is-col-span-2 {
  grid-column: span 2 / span 2 !important;
}
.is-col-span-3 {
  grid-column: span 3 / span 3 !important;
}
.is-col-span-4 {
  grid-column: span 4 / span 4 !important;
}
.is-col-span-5 {
  grid-column: span 5 / span 5 !important;
}
.is-col-span-6 {
  grid-column: span 6 / span 6 !important;
}
.is-col-span-full {
  grid-column: 1 / -1 !important;
}

.is-row-span-1 {
  grid-row: span 1 / span 1 !important;
}
.is-row-span-2 {
  grid-row: span 2 / span 2 !important;
}
.is-row-span-3 {
  grid-row: span 3 / span 3 !important;
}
.is-row-span-4 {
  grid-row: span 4 / span 4 !important;
}
.is-row-span-5 {
  grid-row: span 5 / span 5 !important;
}
.is-row-span-6 {
  grid-row: span 6 / span 6 !important;
}
.is-row-span-full {
  grid-row: 1 / -1 !important;
}

/* Responsive Grid */
@media screen and (max-width: 768px) {
  .is-grid-cols-1-mobile {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
  .is-grid-cols-2-mobile {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  .is-grid-cols-3-mobile {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }
}

@media screen and (min-width: 769px) and (max-width: 1023px) {
  .is-grid-cols-1-tablet {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
  .is-grid-cols-2-tablet {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  .is-grid-cols-3-tablet {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }
  .is-grid-cols-4-tablet {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
  }
}

@media screen and (min-width: 1024px) {
  .is-grid-cols-1-desktop {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
  .is-grid-cols-2-desktop {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  .is-grid-cols-3-desktop {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }
  .is-grid-cols-4-desktop {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
  }
  .is-grid-cols-5-desktop {
    grid-template-columns: repeat(5, minmax(0, 1fr)) !important;
  }
  .is-grid-cols-6-desktop {
    grid-template-columns: repeat(6, minmax(0, 1fr)) !important;
  }
}

/* Position Utilities */
.is-relative {
  position: relative !important;
}
.is-absolute {
  position: absolute !important;
}
.is-fixed {
  position: fixed !important;
}
.is-sticky {
  position: sticky !important;
}
.is-static {
  position: static !important;
}

/* Overflow Utilities */
.is-overflow-auto {
  overflow: auto !important;
}
.is-overflow-hidden {
  overflow: hidden !important;
}
.is-overflow-visible {
  overflow: visible !important;
}
.is-overflow-scroll {
  overflow: scroll !important;
}

.is-overflow-x-auto {
  overflow-x: auto !important;
}
.is-overflow-x-hidden {
  overflow-x: hidden !important;
}
.is-overflow-x-visible {
  overflow-x: visible !important;
}
.is-overflow-x-scroll {
  overflow-x: scroll !important;
}

.is-overflow-y-auto {
  overflow-y: auto !important;
}
.is-overflow-y-hidden {
  overflow-y: hidden !important;
}
.is-overflow-y-visible {
  overflow-y: visible !important;
}
.is-overflow-y-scroll {
  overflow-y: scroll !important;
}

/* Width and Height Utilities */
.is-fullwidth {
  width: 100% !important;
}

.is-fullheight {
  height: 100vh !important;
}

.is-unselectable {
  user-select: none !important;
}

/* Clickable utility */
.is-clickable {
  cursor: pointer !important;
}
.is-width-auto {
  width: auto !important;
}
.is-width-full {
  width: 100% !important;
}
.is-width-screen {
  width: 100vw !important;
}
.is-width-min {
  width: min-content !important;
}
.is-width-max {
  width: max-content !important;
}

.is-height-auto {
  height: auto !important;
}
.is-height-full {
  height: 100% !important;
}
.is-height-screen {
  height: 100vh !important;
}
.is-height-min {
  height: min-content !important;
}
.is-height-max {
  height: max-content !important;
}

.is-min-width-0 {
  min-width: 0 !important;
}
.is-min-width-full {
  min-width: 100% !important;
}
.is-min-width-min {
  min-width: min-content !important;
}
.is-min-width-max {
  min-width: max-content !important;
}

.is-min-height-0 {
  min-height: 0 !important;
}
.is-min-height-full {
  min-height: 100% !important;
}
.is-min-height-screen {
  min-height: 100vh !important;
}

.is-max-width-none {
  max-width: none !important;
}
.is-max-width-full {
  max-width: 100% !important;
}
.is-max-width-screen {
  max-width: 100vw !important;
}

.is-max-height-none {
  max-height: none !important;
}
.is-max-height-full {
  max-height: 100% !important;
}
.is-max-height-screen {
  max-height: 100vh !important;
}

/* Z-Index Utilities */
.is-z-auto {
  z-index: auto !important;
}
.is-z-0 {
  z-index: 0 !important;
}
.is-z-10 {
  z-index: var(--z-dropdown) !important;
}
.is-z-20 {
  z-index: var(--z-sticky) !important;
}
.is-z-30 {
  z-index: var(--z-fixed) !important;
}
.is-z-40 {
  z-index: var(--z-modal-backdrop) !important;
}
.is-z-50 {
  z-index: var(--z-modal) !important;
}

/* Cursor Utilities */
.is-cursor-auto {
  cursor: auto !important;
}
.is-cursor-default {
  cursor: default !important;
}
.is-cursor-pointer {
  cursor: pointer !important;
}
.is-cursor-wait {
  cursor: wait !important;
}
.is-cursor-text {
  cursor: text !important;
}
.is-cursor-move {
  cursor: move !important;
}
.is-cursor-help {
  cursor: help !important;
}
.is-cursor-not-allowed {
  cursor: not-allowed !important;
}

/* Pointer Events */
.is-pointer-events-none {
  pointer-events: none !important;
}
.is-pointer-events-auto {
  pointer-events: auto !important;
}

/* User Select */
.is-select-none {
  user-select: none !important;
}
.is-select-text {
  user-select: text !important;
}
.is-select-all {
  user-select: all !important;
}
.is-select-auto {
  user-select: auto !important;
}

/* Print Utilities */
@media print {
  .is-hidden-print {
    display: none !important;
  }
  .is-visible-print {
    display: block !important;
  }
  .is-flex-print {
    display: flex !important;
  }
  .is-grid-print {
    display: grid !important;
  }
}

/* Screen Reader Only */
.is-sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.is-not-sr-only {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

/* Aspect Ratio Utilities */
.is-aspect-square {
  aspect-ratio: 1 / 1 !important;
}
.is-aspect-video {
  aspect-ratio: 16 / 9 !important;
}
.is-aspect-photo {
  aspect-ratio: 4 / 3 !important;
}
.is-aspect-portrait {
  aspect-ratio: 3 / 4 !important;
}
.is-aspect-auto {
  aspect-ratio: auto !important;
}

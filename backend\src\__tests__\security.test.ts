import request from 'supertest';
import express from 'express';
import { JWTUtils } from '../utils/jwt';
import { SanitizationUtils } from '../utils/sanitization';
import { securityHeaders, sanitizeInput, bruteForceProtection, requestSizeLimit } from '../middleware/security';
import { createRateLimit } from '../middleware/rateLimiting';
import { securityValidation } from '../middleware/enhancedValidation';

describe('Security Middleware Tests', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
  });

  describe('Security Headers', () => {
    it('should set security headers', async () => {
      app.use(securityHeaders());
      app.get('/test', (req, res) => res.json({ success: true }));

      const response = await request(app).get('/test');

      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['referrer-policy']).toBe('strict-origin-when-cross-origin');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['content-security-policy']).toContain("default-src 'self'");
    });
  });

  describe('Input Sanitization', () => {
    it('should sanitize malicious input', async () => {
      app.use(sanitizeInput());
      app.post('/test', (req, res) => res.json(req.body));

      const maliciousInput = {
        name: '<script>alert("xss")</script>John',
        email: '<EMAIL><script>',
        content: 'Hello javascript:alert("xss") world'
      };

      const response = await request(app)
        .post('/test')
        .send(maliciousInput);

      expect(response.body.name).not.toContain('<script>');
      expect(response.body.email).not.toContain('<script>');
      expect(response.body.content).not.toContain('javascript:');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      const rateLimit = createRateLimit({
        windowMs: 60000, // 1 minute
        max: 2 // 2 requests per minute
      });

      app.use(rateLimit);
      app.get('/test', (req, res) => res.json({ success: true }));

      // First two requests should succeed
      await request(app).get('/test').expect(200);
      await request(app).get('/test').expect(200);

      // Third request should be rate limited
      const response = await request(app).get('/test').expect(429);
      expect(response.body.code).toBe('RATE_LIMIT_EXCEEDED');
    });

    it('should set rate limit headers', async () => {
      const rateLimit = createRateLimit({
        windowMs: 60000,
        max: 5
      });

      app.use(rateLimit);
      app.get('/test', (req, res) => res.json({ success: true }));

      const response = await request(app).get('/test');

      expect(response.headers['x-ratelimit-limit']).toBe('5');
      expect(response.headers['x-ratelimit-remaining']).toBe('4');
      expect(response.headers['x-ratelimit-reset']).toBeDefined();
    });
  });

  describe('Request Size Limiting', () => {
    it('should reject requests that are too large', async () => {
      app.use(requestSizeLimit(100)); // 100 bytes limit
      app.post('/test', (req, res) => res.json({ success: true }));

      const largePayload = 'x'.repeat(200); // 200 bytes

      const response = await request(app)
        .post('/test')
        .send({ data: largePayload })
        .expect(413);

      expect(response.body.code).toBe('REQUEST_TOO_LARGE');
    });
  });

  describe('Security Validation', () => {
    it('should detect suspicious input patterns', async () => {
      app.use(securityValidation);
      app.post('/test', (req, res) => res.json({ success: true }));

      const suspiciousInput = {
        query: "'; DROP TABLE users; --",
        script: '<script>alert("xss")</script>',
        path: '../../../etc/passwd'
      };

      const response = await request(app)
        .post('/test')
        .send(suspiciousInput)
        .expect(400);

      expect(response.body.code).toBe('SUSPICIOUS_INPUT');
    });
  });

  describe('Brute Force Protection', () => {
    it('should block after max attempts', async () => {
      const bruteForce = bruteForceProtection({
        maxAttempts: 2,
        windowMs: 60000,
        blockDurationMs: 60000
      });

      app.use(bruteForce);
      app.post('/login', (req, res) => {
        // Simulate failed login
        res.status(401).json({ error: 'Invalid credentials' });
      });

      // First two attempts should go through
      await request(app).post('/login').send({ email: '<EMAIL>' }).expect(401);
      await request(app).post('/login').send({ email: '<EMAIL>' }).expect(401);

      // Third attempt should be blocked
      const response = await request(app)
        .post('/login')
        .send({ email: '<EMAIL>' })
        .expect(429);

      expect(response.body.code).toBe('ACCOUNT_BLOCKED');
    });
  });
});

describe('JWT Security Tests', () => {
  const testUserId = 'test-user-id';
  const testEmail = '<EMAIL>';

  describe('Token Generation', () => {
    it('should generate secure token pair', () => {
      const tokenPair = JWTUtils.generateTokenPair(testUserId, testEmail, {
        userAgent: 'test-agent',
        ipAddress: '127.0.0.1'
      });

      expect(tokenPair.accessToken).toBeDefined();
      expect(tokenPair.refreshToken).toBeDefined();
      expect(tokenPair.expiresIn).toBeGreaterThan(0);
      expect(tokenPair.tokenType).toBe('Bearer');
    });

    it('should include security metadata in token', () => {
      const tokenPair = JWTUtils.generateTokenPair(testUserId, testEmail, {
        userAgent: 'test-agent',
        ipAddress: '127.0.0.1'
      });

      const payload = JWTUtils.verifyAccessToken(tokenPair.accessToken);

      expect(payload.userId).toBe(testUserId);
      expect(payload.email).toBe(testEmail);
      expect(payload.jti).toBeDefined();
      expect(payload.sessionId).toBeDefined();
      expect(payload.deviceId).toBeDefined();
      expect(payload.ipAddress).toBe('127.0.0.1');
    });
  });

  describe('Token Validation', () => {
    it('should validate tokens with IP checking', () => {
      const tokenPair = JWTUtils.generateTokenPair(testUserId, testEmail, {
        ipAddress: '127.0.0.1'
      });

      // Should succeed with same IP
      expect(() => {
        JWTUtils.verifyAccessToken(tokenPair.accessToken, { ipAddress: '127.0.0.1' });
      }).not.toThrow();

      // Should still work with different IP (but log warning)
      expect(() => {
        JWTUtils.verifyAccessToken(tokenPair.accessToken, { ipAddress: '***********' });
      }).not.toThrow();
    });

    it('should reject blacklisted tokens', () => {
      const tokenPair = JWTUtils.generateTokenPair(testUserId, testEmail);
      const payload = JWTUtils.verifyAccessToken(tokenPair.accessToken);

      // Blacklist the token
      JWTUtils.blacklistToken(payload.jti);

      // Should reject blacklisted token
      expect(() => {
        JWTUtils.verifyAccessToken(tokenPair.accessToken);
      }).toThrow('Token has been revoked');
    });
  });

  describe('Token Revocation', () => {
    it('should revoke tokens', () => {
      const tokenPair = JWTUtils.generateTokenPair(testUserId, testEmail);
      
      // Token should be valid initially
      expect(() => {
        JWTUtils.verifyAccessToken(tokenPair.accessToken);
      }).not.toThrow();

      // Revoke the token
      JWTUtils.revokeToken(tokenPair.accessToken);

      // Token should be invalid after revocation
      expect(() => {
        JWTUtils.verifyAccessToken(tokenPair.accessToken);
      }).toThrow();
    });
  });
});

describe('Sanitization Utils Tests', () => {
  describe('HTML Sanitization', () => {
    it('should remove dangerous HTML tags', () => {
      const maliciousHtml = '<script>alert("xss")</script><p>Safe content</p><iframe src="evil.com"></iframe>';
      const sanitized = SanitizationUtils.sanitizeHtml(maliciousHtml);

      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('<iframe>');
      expect(sanitized).toContain('Safe content');
    });

    it('should remove javascript: URLs', () => {
      const maliciousContent = 'Click <a href="javascript:alert(\'xss\')">here</a>';
      const sanitized = SanitizationUtils.sanitizeHtml(maliciousContent);

      expect(sanitized).not.toContain('javascript:');
    });

    it('should remove event handlers', () => {
      const maliciousContent = '<div onclick="alert(\'xss\')">Click me</div>';
      const sanitized = SanitizationUtils.sanitizeHtml(maliciousContent);

      expect(sanitized).not.toContain('onclick');
    });
  });

  describe('Text Sanitization', () => {
    it('should remove HTML tags from text', () => {
      const textWithHtml = 'Hello <b>world</b> <script>alert("xss")</script>';
      const sanitized = SanitizationUtils.sanitizeText(textWithHtml);

      expect(sanitized).toBe('Hello world');
      expect(sanitized).not.toContain('<');
      expect(sanitized).not.toContain('>');
    });

    it('should remove control characters', () => {
      const textWithControlChars = 'Hello\x00\x08\x0B\x0C\x0E\x1F\x7F world';
      const sanitized = SanitizationUtils.sanitizeText(textWithControlChars);

      expect(sanitized).toBe('Hello world');
    });
  });

  describe('SQL Sanitization', () => {
    it('should remove SQL injection patterns', () => {
      const maliciousSql = "'; DROP TABLE users; --";
      const sanitized = SanitizationUtils.sanitizeSql(maliciousSql);

      expect(sanitized).not.toContain('DROP');
      expect(sanitized).not.toContain('--');
      expect(sanitized).not.toContain(';');
    });

    it('should remove UNION SELECT patterns', () => {
      const maliciousSql = "1' UNION SELECT * FROM users --";
      const sanitized = SanitizationUtils.sanitizeSql(maliciousSql);

      expect(sanitized).not.toContain('UNION');
      expect(sanitized).not.toContain('SELECT');
    });
  });

  describe('URL Sanitization', () => {
    it('should allow valid HTTP/HTTPS URLs', () => {
      const validUrl = 'https://example.com/path?param=value';
      const sanitized = SanitizationUtils.sanitizeUrl(validUrl);

      expect(sanitized).toBe(validUrl);
    });

    it('should reject invalid protocols', () => {
      const invalidUrl = 'javascript:alert("xss")';
      const sanitized = SanitizationUtils.sanitizeUrl(invalidUrl);

      expect(sanitized).toBe('');
    });

    it('should reject malformed URLs', () => {
      const malformedUrl = 'not-a-url';
      const sanitized = SanitizationUtils.sanitizeUrl(malformedUrl);

      expect(sanitized).toBe('');
    });
  });

  describe('Email Sanitization', () => {
    it('should normalize valid emails', () => {
      const email = '  <EMAIL>  ';
      const sanitized = SanitizationUtils.sanitizeEmail(email);

      expect(sanitized).toBe('<EMAIL>');
    });

    it('should reject invalid emails', () => {
      const invalidEmail = 'not-an-email';
      const sanitized = SanitizationUtils.sanitizeEmail(invalidEmail);

      expect(sanitized).toBe('');
    });
  });

  describe('File Name Sanitization', () => {
    it('should remove dangerous characters', () => {
      const dangerousName = '../../../etc/passwd<>:"/\\|?*';
      const sanitized = SanitizationUtils.sanitizeFileName(dangerousName);

      expect(sanitized).not.toContain('../');
      expect(sanitized).not.toContain('<');
      expect(sanitized).not.toContain('>');
      expect(sanitized).not.toContain(':');
    });

    it('should provide default name for empty input', () => {
      const emptyName = '';
      const sanitized = SanitizationUtils.sanitizeFileName(emptyName);

      expect(sanitized).toBe('untitled');
    });
  });

  describe('Search Query Sanitization', () => {
    it('should escape regex special characters', () => {
      const regexQuery = '.*+?^${}()|[]\\';
      const sanitized = SanitizationUtils.sanitizeSearchQuery(regexQuery);

      expect(sanitized).toContain('\\.');
      expect(sanitized).toContain('\\*');
      expect(sanitized).toContain('\\+');
    });

    it('should limit query length', () => {
      const longQuery = 'x'.repeat(300);
      const sanitized = SanitizationUtils.sanitizeSearchQuery(longQuery);

      expect(sanitized.length).toBeLessThanOrEqual(200);
    });
  });

  describe('Tag Sanitization', () => {
    it('should normalize tags', () => {
      const tag = '  My Tag Name  ';
      const sanitized = SanitizationUtils.sanitizeTag(tag);

      expect(sanitized).toBe('my-tag-name');
    });

    it('should remove special characters', () => {
      const tag = 'tag<>with"special&chars';
      const sanitized = SanitizationUtils.sanitizeTag(tag);

      expect(sanitized).not.toContain('<');
      expect(sanitized).not.toContain('>');
      expect(sanitized).not.toContain('"');
      expect(sanitized).not.toContain('&');
    });
  });
});

describe('Security Configuration Tests', () => {
  it('should validate security configuration', () => {
    // This would test the security configuration validation
    // For now, we'll just ensure the module can be imported
    expect(() => {
      require('../config/security');
    }).not.toThrow();
  });
});
import { v4 as uuidv4 } from 'uuid';

export interface Tag {
  id: string;
  userId: string;
  name: string;
  icon: string;
  color: string;
  isPredefined: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTagData {
  userId: string;
  name: string;
  icon?: string;
  color?: string;
  isPredefined?: boolean;
}

export interface UpdateTagData {
  name?: string;
  icon?: string;
  color?: string;
}

export interface TagFilters {
  userId: string;
  isPredefined?: boolean;
  search?: string;
}

// Predefined tags that come with the app
export const PREDEFINED_TAGS: Omit<Tag, 'id' | 'userId' | 'createdAt' | 'updatedAt'>[] = [
  { name: 'todo', icon: 'fas fa-check-square', color: '#007bff', isPredefined: true },
  { name: 'personal', icon: 'fas fa-user', color: '#28a745', isPredefined: true },
  { name: 'family', icon: 'fas fa-heart', color: '#dc3545', isPredefined: true },
  { name: 'health', icon: 'fas fa-heartbeat', color: '#fd7e14', isPredefined: true },
  { name: 'blog', icon: 'fas fa-blog', color: '#6f42c1', isPredefined: true },
  { name: 'code', icon: 'fas fa-code', color: '#20c997', isPredefined: true },
  { name: 'recipe', icon: 'fas fa-utensils', color: '#ffc107', isPredefined: true },
  { name: 'science', icon: 'fas fa-flask', color: '#17a2b8', isPredefined: true },
  { name: 'study', icon: 'fas fa-graduation-cap', color: '#6610f2', isPredefined: true },
  { name: 'travel', icon: 'fas fa-plane', color: '#e83e8c', isPredefined: true }
];

export class TagModel {
  static generateId(): string {
    return uuidv4();
  }

  static validateTagName(name: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!name || name.trim().length === 0) {
      errors.push('Tag name is required');
    }
    
    if (name.length > 50) {
      errors.push('Tag name must be less than 50 characters');
    }

    // Check for valid characters (alphanumeric, spaces, hyphens, underscores)
    if (!/^[a-zA-Z0-9\s\-_]+$/.test(name)) {
      errors.push('Tag name can only contain letters, numbers, spaces, hyphens, and underscores');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static validateIcon(icon: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!icon || icon.trim().length === 0) {
      errors.push('Icon is required');
    }
    
    // Basic validation for FontAwesome class format
    if (icon && !icon.startsWith('fas fa-') && !icon.startsWith('far fa-') && !icon.startsWith('fab fa-')) {
      errors.push('Icon must be a valid FontAwesome class (e.g., "fas fa-tag")');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static validateColor(color: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!color || color.trim().length === 0) {
      errors.push('Color is required');
    }
    
    // Basic hex color validation
    if (color && !/^#[0-9A-Fa-f]{6}$/.test(color)) {
      errors.push('Color must be a valid hex color (e.g., "#007bff")');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static validateCreateData(data: CreateTagData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    const nameValidation = this.validateTagName(data.name);
    if (!nameValidation.valid) {
      errors.push(...nameValidation.errors);
    }

    if (data.icon) {
      const iconValidation = this.validateIcon(data.icon);
      if (!iconValidation.valid) {
        errors.push(...iconValidation.errors);
      }
    }

    if (data.color) {
      const colorValidation = this.validateColor(data.color);
      if (!colorValidation.valid) {
        errors.push(...colorValidation.errors);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static validateUpdateData(data: UpdateTagData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (data.name !== undefined) {
      const nameValidation = this.validateTagName(data.name);
      if (!nameValidation.valid) {
        errors.push(...nameValidation.errors);
      }
    }

    if (data.icon !== undefined) {
      const iconValidation = this.validateIcon(data.icon);
      if (!iconValidation.valid) {
        errors.push(...iconValidation.errors);
      }
    }

    if (data.color !== undefined) {
      const colorValidation = this.validateColor(data.color);
      if (!colorValidation.valid) {
        errors.push(...colorValidation.errors);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static normalizeTagName(name: string): string {
    return name.toLowerCase().trim().replace(/\s+/g, ' ');
  }

  static getDefaultIcon(): string {
    return 'fas fa-tag';
  }

  static getDefaultColor(): string {
    return '#6c757d';
  }

  static createPredefinedTags(userId: string): Tag[] {
    const now = new Date();
    return PREDEFINED_TAGS.map(predefinedTag => ({
      ...predefinedTag,
      id: this.generateId(),
      userId,
      createdAt: now,
      updatedAt: now
    }));
  }
}
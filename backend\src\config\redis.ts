import Redis from 'ioredis';
import dotenv from 'dotenv';

dotenv.config();

export interface CacheConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  keyPrefix: string;
  lazyConnect: boolean;
}

const redisConfig: CacheConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  keyPrefix: process.env.REDIS_KEY_PREFIX || 'notes_app:',
  lazyConnect: true
};

let redisClient: Redis | null = null;

export async function initializeRedis(): Promise<Redis | null> {
  if (redisClient) {
    return redisClient;
  }

  // Skip Redis initialization if explicitly disabled
  if (process.env.REDIS_URL === '' || process.env.DISABLE_REDIS === 'true') {
    console.log('Redis disabled, using fallback cache');
    return null;
  }

  try {
    redisClient = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      db: redisConfig.db,
      keyPrefix: redisConfig.keyPrefix,
      lazyConnect: redisConfig.lazyConnect,
      connectTimeout: 5000,
      commandTimeout: 5000,
      maxRetriesPerRequest: 0,
      enableOfflineQueue: false,
    });

    let connectionAttempted = false;

    redisClient.on('connect', () => {
      console.log('Redis connected successfully');
    });

    redisClient.on('error', (err) => {
      if (!connectionAttempted) {
        console.warn('Redis unavailable, falling back to in-memory cache');
        connectionAttempted = true;
      }
      // Disconnect to prevent further retry attempts
      if (redisClient) {
        redisClient.disconnect();
        redisClient = null;
      }
    });

    redisClient.on('ready', () => {
      console.log('Redis is ready to accept commands');
    });

    // Test the connection with timeout
    await Promise.race([
      redisClient.ping(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout')), 3000)
      )
    ]);
    
    console.log('Redis ping successful');
    return redisClient;
  } catch (error) {
    console.warn('Redis unavailable, using fallback cache');
    if (redisClient) {
      redisClient.disconnect();
      redisClient = null;
    }
    return null;
  }
}

export function getRedisClient(): Redis | null {
  return redisClient;
}

export async function closeRedis(): Promise<void> {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
  }
}

// Cache key generators
export const CacheKeys = {
  user: (userId: string) => `user:${userId}`,
  userSettings: (userId: string) => `user:${userId}:settings`,
  note: (noteId: string) => `note:${noteId}`,
  noteList: (userId: string, page: number, limit: number, filters?: string) => 
    `notes:${userId}:page:${page}:limit:${limit}${filters ? `:filters:${filters}` : ''}`,
  searchResults: (query: string, userId: string, filters?: string) => 
    `search:${userId}:${Buffer.from(query).toString('base64')}${filters ? `:${filters}` : ''}`,
  tags: (userId: string) => `tags:${userId}`,
  groups: (userId: string) => `groups:${userId}`,
  groupMembers: (groupId: string) => `group:${groupId}:members`,
  session: (sessionId: string) => `session:${sessionId}`,
  rateLimit: (identifier: string) => `ratelimit:${identifier}`,
  templates: (userId: string) => `templates:${userId}`,
  sharedNote: (token: string) => `shared:${token}`
};

// Cache TTL constants (in seconds)
export const CacheTTL = {
  USER: 3600, // 1 hour
  USER_SETTINGS: 1800, // 30 minutes
  NOTE: 1800, // 30 minutes
  NOTE_LIST: 300, // 5 minutes
  SEARCH_RESULTS: 600, // 10 minutes
  TAGS: 1800, // 30 minutes
  GROUPS: 1800, // 30 minutes
  GROUP_MEMBERS: 900, // 15 minutes
  SESSION: 86400, // 24 hours
  TEMPLATES: 3600, // 1 hour
  SHARED_NOTE: 1800 // 30 minutes
};
import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import { userService, type UserPreferences } from '../services/userService'
import { useAuthStore } from './auth'
import { useTheme } from '../composables/useTheme'
import type { BulmaswatchTheme, ThemeSettings } from '../types/theme'

export const useSettingsStore = defineStore('settings', () => {
  const preferences = ref<UserPreferences>({
    theme: 'auto',
    language: 'en',
    timezone: 'UTC',
    autoSaveInterval: 30000,
    notifications: {
      email: true,
      push: true,
      mentions: true
    }
  })
  
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const isInitialized = ref(false)
  
  // Enhanced theme management state
  const availableThemes = ref<BulmaswatchTheme[]>([])
  const currentThemeMode = ref<'light' | 'dark' | 'auto'>('auto')
  const currentThemeName = ref<string>('default')
  const themeError = ref<string | null>(null)
  const isThemeLoading = ref(false)

  const isDarkMode = computed(() => {
    if (preferences.value.theme === 'dark') return true
    if (preferences.value.theme === 'light') return false
    // Auto mode - check system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  })

  const loadSettings = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await userService.getSettings()
      
      if (response.success && response.data) {
        preferences.value = response.data.preferences
        
        // Update theme name if available from backend
        if (response.data.preferences.themeName) {
          currentThemeName.value = response.data.preferences.themeName
        }
        
        // Defer theme application to next tick to avoid blocking initialization
        nextTick(() => {
          applyTheme()
        })
      } else {
        // If failed to load settings, try to load theme from authStore
        const authStore = useAuthStore()
        const savedTheme = authStore.loadThemePreference()
        if (savedTheme.mode) {
          preferences.value.theme = savedTheme.mode
          nextTick(() => {
            applyTheme()
          })
        }
        error.value = response.error || 'Failed to load settings'
      }
    } catch (err) {
      // If failed to load settings, try to load theme from authStore
      const authStore = useAuthStore()
      const savedTheme = authStore.loadThemePreference()
      if (savedTheme.mode) {
        preferences.value.theme = savedTheme.mode
        nextTick(() => {
          applyTheme()
        })
      }
      error.value = err instanceof Error ? err.message : 'Failed to load settings'
    } finally {
      isLoading.value = false
    }
  }

  const updateSettings = async (newPreferences: Partial<UserPreferences>) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await userService.updateSettings(newPreferences)
      
      if (response.success && response.data) {
        preferences.value = response.data.preferences
        
        // Save theme preference to authStore for persistence across sessions
        if (newPreferences.theme) {
          const authStore = useAuthStore()
          // Also save theme name if available
          if (newPreferences.themeName) {
            authStore.saveThemePreference(newPreferences.theme, newPreferences.themeName)
          } else {
            authStore.saveThemePreference(newPreferences.theme)
          }
        }
        
        // Update local theme name if available
        if (response.data.preferences.themeName) {
          currentThemeName.value = response.data.preferences.themeName
        }
        
        // Defer theme application to next tick to avoid blocking UI updates
        nextTick(() => {
          applyTheme()
        })
        return { success: true, message: response.message }
      } else {
        error.value = response.error || 'Failed to update settings'
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update settings'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const updateTheme = async (theme: 'light' | 'dark' | 'auto') => {
    return await updateSettings({ theme })
  }
  
  // Enhanced theme management methods
  const loadAvailableThemes = async () => {
    isThemeLoading.value = true
    themeError.value = null
    
    try {
      const themeComposable = useTheme()
      await themeComposable.initialize()
      // Access the raw value from the composable
      const themes = themeComposable.availableThemes
      if (Array.isArray(themes)) {
        availableThemes.value = themes
      }
      return { success: true, themes: availableThemes.value }
    } catch (err) {
      themeError.value = err instanceof Error ? err.message : 'Failed to load available themes'
      console.error('Failed to load available themes:', err)
      return { success: false, error: themeError.value }
    } finally {
      isThemeLoading.value = false
    }
  }
  
  const updateThemeMode = async (mode: 'light' | 'dark' | 'auto') => {
    isThemeLoading.value = true
    themeError.value = null
    
    try {
      // Apply the theme mode directly
      const { setThemeMode } = useTheme()
      await setThemeMode(mode)
      
      // Update local state to match what was applied
      currentThemeMode.value = mode
      
      // Determine theme name based on mode (same logic as setThemeMode uses)
      const authStore = useAuthStore()
      currentThemeName.value = authStore.getFallbackTheme(mode)
      
      // Update preferences and sync with backend
      const result = await updateSettings({ 
        theme: mode,
        themeName: currentThemeName.value
      })
      
      // Save to auth store for persistence
      authStore.saveThemePreference(mode, currentThemeName.value)
      
      return result
    } catch (err) {
      themeError.value = err instanceof Error ? err.message : 'Failed to update theme mode'
      return { success: false, error: themeError.value }
    } finally {
      isThemeLoading.value = false
    }
  }
  
  const updateSpecificTheme = async (themeName: string) => {
    isThemeLoading.value = true
    themeError.value = null
    
    try {
      const authStore = useAuthStore()
      
      // Ensure themes are loaded before validation
      if (availableThemes.value.length === 0) {
        await loadAvailableThemes()
      }
      
      // Validate theme name
      const availableThemeNames = availableThemes.value.map(t => t.name)
      if (availableThemeNames.length === 0) {
        // Fallback validation if themes haven't loaded yet
        const fallbackThemes = ['default', 'darkly', 'flatly', 'cerulean']
        if (!fallbackThemes.includes(themeName)) {
          throw new Error(`Invalid theme name: ${themeName}`)
        }
      } else if (!authStore.validateThemeName(themeName, availableThemeNames)) {
        throw new Error(`Invalid theme name: ${themeName}`)
      }
      
      const { setTheme } = useTheme()
      await setTheme(themeName)
      
      currentThemeName.value = themeName
      
      // Determine mode based on theme
      const theme = availableThemes.value.find(t => t.name === themeName)
      let mode: 'light' | 'dark'
      
      if (theme) {
        // Use the theme's isDark property if available
        mode = theme.isDark ? 'dark' : 'light'
      } else {
        // Fallback mapping when themes haven't loaded yet
        const darkThemes = ['darkly', 'cyborg', 'slate', 'superhero', 'vapor']
        mode = darkThemes.includes(themeName) ? 'dark' : 'light'
      }
      
      currentThemeMode.value = mode
      
      // Update preferences and sync with backend
      // Backend now supports both theme mode and specific theme name
      console.log(`Updating backend settings with theme mode: ${mode} and theme name: ${themeName}`)
      const result = await updateSettings({ 
        theme: mode,
        themeName: themeName
      })
      
      // Save to auth store for persistence (includes both mode and specific theme name)
      authStore.saveThemePreference(mode, themeName)
      
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update theme'
      themeError.value = errorMessage
      console.error('Failed to update theme:', err)
      
      // Backend should now support custom theme names, so any error is unexpected
      console.error('Unexpected theme update error:', errorMessage)
      
      return { success: false, error: themeError.value }
    } finally {
      isThemeLoading.value = false
    }
  }
  
  const getThemePreview = (themeName: string) => {
    const { getThemePreview } = useTheme()
    return getThemePreview(themeName)
  }
  
  const syncThemeWithBackend = async () => {
    isThemeLoading.value = true
    themeError.value = null
    
    try {
      const themeSettings = {
        mode: currentThemeMode.value,
        themeName: currentThemeName.value
      }
      
      // Sync theme settings with the backend
      const result = await updateSettings({ 
        theme: currentThemeMode.value,
        themeName: currentThemeName.value
      })
      
      return result
    } catch (err) {
      themeError.value = err instanceof Error ? err.message : 'Failed to sync theme with backend'
      console.error('Failed to sync theme with backend:', err)
      return { success: false, error: themeError.value }
    } finally {
      isThemeLoading.value = false
    }
  }
  
  const resetThemeToDefault = async () => {
    try {
      const { resetToDefault } = useTheme()
      await resetToDefault()
      
      currentThemeMode.value = 'light'
      currentThemeName.value = 'default'
      
      const authStore = useAuthStore()
      authStore.saveThemePreference('light', 'default')
      
      return await updateSettings({ 
        theme: 'light',
        themeName: 'default'
      })
    } catch (err) {
      themeError.value = err instanceof Error ? err.message : 'Failed to reset theme'
      return { success: false, error: themeError.value }
    }
  }
  
  const handleThemeError = (error: string, showUserFeedback: boolean = true) => {
    themeError.value = error
    console.error('Theme error:', error)
    
    if (showUserFeedback) {
      // This could dispatch a notification event or update a global error state
      window.dispatchEvent(new CustomEvent('theme-error', {
        detail: { message: error }
      }))
    }
  }
  
  const clearThemeError = () => {
    themeError.value = null
    isThemeLoading.value = false // Also clear loading state
  }
  
  // Update local theme state without syncing to backend (for initialization)
  const setLocalThemeState = (mode: 'light' | 'dark' | 'auto', themeName?: string) => {
    currentThemeMode.value = mode
    if (themeName) {
      currentThemeName.value = themeName
    }
    // Also update preferences to keep them in sync
    preferences.value.theme = mode
    if (themeName) {
      preferences.value.themeName = themeName
    }
  }

  const updateLanguage = async (language: string) => {
    return await updateSettings({ language })
  }

  const updateAutoSaveInterval = async (interval: number) => {
    return await updateSettings({ autoSaveInterval: interval })
  }

  const updateNotifications = async (notifications: Partial<UserPreferences['notifications']>) => {
    const updatedNotifications = { ...preferences.value.notifications, ...notifications }
    return await updateSettings({ notifications: updatedNotifications })
  }

  const applyTheme = () => {
    // Defer DOM operations to avoid blocking during initialization
    const applyThemeToDOM = () => {
      const html = document.documentElement
      
      // Determine the theme name to apply
      let themeNameToApply = currentThemeName.value || 'default'
      
      if (preferences.value.theme === 'dark') {
        html.classList.add('dark')
        html.classList.remove('light')
        // Use darkly theme if no specific theme name is set
        if (!currentThemeName.value || currentThemeName.value === 'default') {
          themeNameToApply = 'darkly'
        }
      } else if (preferences.value.theme === 'light') {
        html.classList.add('light')
        html.classList.remove('dark')
        // Use default theme for light mode
        if (!currentThemeName.value || currentThemeName.value === 'darkly') {
          themeNameToApply = 'default'
        }
      } else {
        // Auto mode
        html.classList.remove('dark', 'light')
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          html.classList.add('dark')
          themeNameToApply = currentThemeName.value || 'darkly'
        } else {
          html.classList.add('light')
          themeNameToApply = currentThemeName.value || 'default'
        }
      }
      
      // Set the data-theme attribute for CSS custom properties
      html.setAttribute('data-theme', themeNameToApply)
      
      console.log(`Theme applied: ${themeNameToApply} (mode: ${preferences.value.theme})`)
    }
    
    // Use nextTick to defer DOM manipulation
    nextTick(applyThemeToDOM)
  }

  const initializeSettings = async () => {
    if (isInitialized.value) {
      console.log('Settings already initialized, skipping...')
      return
    }
    
    try {
      // Load available themes first
      await loadAvailableThemes()
      
      // Load user settings (this will also load theme name from backend)
      await loadSettings()
      
      // Initialize theme from auth store preferences as fallback
      const authStore = useAuthStore()
      const themePrefs = authStore.loadThemePreference()
      
      // Use backend settings if available, otherwise fall back to auth store
      if (preferences.value.theme) {
        currentThemeMode.value = preferences.value.theme
      } else if (themePrefs.mode) {
        currentThemeMode.value = themePrefs.mode
      }
      
      if (preferences.value.themeName) {
        currentThemeName.value = preferences.value.themeName
      } else if (themePrefs.themeName) {
        currentThemeName.value = themePrefs.themeName
      } else if (currentThemeMode.value) {
        // Use fallback theme based on mode
        currentThemeName.value = authStore.getFallbackTheme(currentThemeMode.value)
      }
      
      // If we have a theme name but no mode, determine mode from theme
      if (currentThemeName.value && !currentThemeMode.value) {
        const theme = availableThemes.value.find(t => t.name === currentThemeName.value)
        if (theme) {
          currentThemeMode.value = theme.isDark ? 'dark' : 'light'
        }
      }
      
      // Listen for system theme changes when in auto mode with deferred execution
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', () => {
        if (preferences.value.theme === 'auto' || currentThemeMode.value === 'auto') {
          // Defer theme change to avoid blocking the main thread
          nextTick(() => {
            applyTheme()
          })
        }
      })
      
      isInitialized.value = true
    } catch (err) {
      console.error('Failed to initialize settings:', err)
      handleThemeError('Failed to initialize theme system', false)
      isInitialized.value = true // Mark as initialized even on error to prevent retries
    }
  }

  const resetInitialization = () => {
    isInitialized.value = false
  }

  return {
    preferences,
    isLoading,
    error,
    isDarkMode,
    isInitialized,
    
    // Enhanced theme state
    availableThemes,
    currentThemeMode,
    currentThemeName,
    themeError,
    isThemeLoading,
    
    // Original methods
    loadSettings,
    updateSettings,
    updateTheme,
    updateLanguage,
    updateAutoSaveInterval,
    updateNotifications,
    applyTheme,
    initializeSettings,
    resetInitialization,
    
    // Enhanced theme methods
    loadAvailableThemes,
    updateThemeMode,
    updateSpecificTheme,
    getThemePreview,
    syncThemeWithBackend,
    resetThemeToDefault,
    handleThemeError,
    clearThemeError,
    setLocalThemeState
  }
})
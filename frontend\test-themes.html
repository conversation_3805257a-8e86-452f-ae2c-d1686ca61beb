<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: var(--color-background, #ffffff);
            color: var(--color-text, #333333);
            transition: all 0.3s ease;
        }
        
        .theme-selector {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid var(--color-border, #ddd);
            border-radius: 8px;
            background: var(--color-surface, #f5f5f5);
        }
        
        .theme-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .theme-card {
            padding: 20px;
            border: 1px solid var(--color-border, #ddd);
            border-radius: 8px;
            background: var(--color-card, #ffffff);
            box-shadow: var(--shadow, 0 2px 4px rgba(0,0,0,0.1));
        }
        
        .color-palette {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .color-swatch {
            width: 30px;
            height: 30px;
            border-radius: 4px;
            border: 1px solid var(--color-border, #ddd);
        }
        
        button {
            background: var(--color-primary, #007bff);
            color: var(--color-text-inverse, #ffffff);
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.2s ease;
        }
        
        button:hover {
            opacity: 0.8;
        }
        
        .test-elements {
            margin-top: 20px;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--color-border, #ddd);
            border-radius: 4px;
            background: var(--color-background, #ffffff);
            color: var(--color-text, #333333);
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="theme-selector">
        <h1>Theme Test Page</h1>
        <p>Select a theme to test:</p>
        <h4>Light Themes:</h4>
        <button onclick="setTheme('default')">Default</button>
        <button onclick="setTheme('flatly')">Flatly</button>
        <button onclick="setTheme('cerulean')">Cerulean</button>
        <button onclick="setTheme('mini-me')">Mini Me</button>
        <button onclick="setTheme('the-brave')">The Brave</button>
        <button onclick="setTheme('medium-light')">Medium Light</button>
        
        <h4>Dark Themes:</h4>
        <button onclick="setTheme('darkly')">Darkly</button>
        <button onclick="setTheme('solarized')">Solarized</button>
        <button onclick="setTheme('flatly-dark')">Flatly Dark</button>
        <button onclick="setTheme('gunmetal-dark')">Gunmetal Dark</button>
        <button onclick="setTheme('jet-black-electric-blue')">Jet Black Electric Blue</button>
    </div>

    <div class="theme-preview">
        <div class="theme-card">
            <h3>Theme Preview</h3>
            <p>This is a sample text to show how the theme looks.</p>
            <div class="color-palette">
                <div class="color-swatch" style="background: var(--color-primary, #007bff);" title="Primary"></div>
                <div class="color-swatch" style="background: var(--color-link, #007bff);" title="Link"></div>
                <div class="color-swatch" style="background: var(--color-success, #28a745);" title="Success"></div>
                <div class="color-swatch" style="background: var(--color-warning, #ffc107);" title="Warning"></div>
                <div class="color-swatch" style="background: var(--color-danger, #dc3545);" title="Danger"></div>
            </div>
        </div>

        <div class="theme-card">
            <h3>Test Elements</h3>
            <div class="test-elements">
                <button>Primary Button</button>
                <button style="background: var(--color-success, #28a745);">Success Button</button>
                <button style="background: var(--color-danger, #dc3545);">Danger Button</button>
                <input type="text" class="test-input" placeholder="Test input field" />
                <textarea class="test-input" placeholder="Test textarea"></textarea>
            </div>
        </div>
    </div>

    <script>
        // Theme definitions with CSS custom properties
        const themes = {
            'default': {
                '--color-primary': '#00d1b2',
                '--color-link': '#3273dc',
                '--color-success': '#48c774',
                '--color-warning': '#ffdd57',
                '--color-danger': '#f14668',
                '--color-background': '#ffffff',
                '--color-surface': '#f5f5f5',
                '--color-card': '#ffffff',
                '--color-text': '#4a4a4a',
                '--color-text-inverse': '#ffffff',
                '--color-border': '#dbdbdb',
                '--shadow': '0 2px 4px rgba(10, 10, 10, 0.1)'
            },
            'darkly': {
                '--color-primary': '#375a7f',
                '--color-link': '#375a7f',
                '--color-success': '#00bc8c',
                '--color-warning': '#f39c12',
                '--color-danger': '#e74c3c',
                '--color-background': '#1f2424',
                '--color-surface': '#303030',
                '--color-card': '#2d2d2d',
                '--color-text': '#dee2e6',
                '--color-text-inverse': '#1f2424',
                '--color-border': '#495057',
                '--shadow': '0 2px 4px rgba(0, 0, 0, 0.3)'
            },
            'solarized': {
                '--color-primary': '#2aa198',
                '--color-link': '#b58900',
                '--color-success': '#859900',
                '--color-warning': '#cb4b16',
                '--color-danger': '#d33682',
                '--color-background': '#002b36',
                '--color-surface': '#073642',
                '--color-card': '#073642',
                '--color-text': '#839496',
                '--color-text-inverse': '#002b36',
                '--color-border': '#586e75',
                '--shadow': '0 2px 4px rgba(0, 0, 0, 0.3)'
            },
            'flatly-dark': {
                '--color-primary': '#375a7f',
                '--color-link': '#1abc9c',
                '--color-success': '#2ecc71',
                '--color-warning': '#f1b70e',
                '--color-danger': '#e74c3c',
                '--color-background': '#1f2424',
                '--color-surface': '#282f2f',
                '--color-card': '#282f2f',
                '--color-text': '#ffffff',
                '--color-text-inverse': '#1f2424',
                '--color-border': '#4c5759',
                '--shadow': '0 2px 4px rgba(0, 0, 0, 0.3)'
            },
            'mini-me': {
                '--color-primary': '#d9230f',
                '--color-link': '#029acf',
                '--color-success': '#469408',
                '--color-warning': '#9b479f',
                '--color-danger': '#d9831f',
                '--color-background': '#ffffff',
                '--color-surface': '#f5f5f5',
                '--color-card': '#ffffff',
                '--color-text': '#444444',
                '--color-text-inverse': '#ffffff',
                '--color-border': '#dddddd',
                '--shadow': '0 2px 4px rgba(0, 0, 0, 0.1)'
            },
            'the-brave': {
                '--color-primary': '#ff6b35',
                '--color-link': '#3498db',
                '--color-success': '#28a745',
                '--color-warning': '#ffc107',
                '--color-danger': '#dc3545',
                '--color-background': '#ffffff',
                '--color-surface': '#f8f9fa',
                '--color-card': '#ffffff',
                '--color-text': '#2c3e50',
                '--color-text-inverse': '#ffffff',
                '--color-border': '#dee2e6',
                '--shadow': '0 2px 4px rgba(0, 0, 0, 0.1)'
            },
            'gunmetal-dark': {
                '--color-primary': '#6c757d',
                '--color-link': '#17a2b8',
                '--color-success': '#28a745',
                '--color-warning': '#ffc107',
                '--color-danger': '#dc3545',
                '--color-background': '#212529',
                '--color-surface': '#343a40',
                '--color-card': '#343a40',
                '--color-text': '#f8f9fa',
                '--color-text-inverse': '#212529',
                '--color-border': '#495057',
                '--shadow': '0 2px 4px rgba(0, 0, 0, 0.3)'
            },
            'medium-light': {
                '--color-primary': '#5a6c7d',
                '--color-link': '#3498db',
                '--color-success': '#27ae60',
                '--color-warning': '#f39c12',
                '--color-danger': '#e74c3c',
                '--color-background': '#f5f7fa',
                '--color-surface': '#ffffff',
                '--color-card': '#ffffff',
                '--color-text': '#2c3e50',
                '--color-text-inverse': '#ffffff',
                '--color-border': '#bdc3c7',
                '--shadow': '0 2px 4px rgba(0, 0, 0, 0.1)'
            },
            'jet-black-electric-blue': {
                '--color-primary': '#00d4ff',
                '--color-link': '#0099cc',
                '--color-success': '#00ff88',
                '--color-warning': '#ffaa00',
                '--color-danger': '#ff3366',
                '--color-background': '#000000',
                '--color-surface': '#1a1a1a',
                '--color-card': '#1a1a1a',
                '--color-text': '#ffffff',
                '--color-text-inverse': '#000000',
                '--color-border': '#333333',
                '--shadow': '0 2px 4px rgba(0, 0, 0, 0.5)'
            }
        };

        function setTheme(themeName) {
            const theme = themes[themeName];
            if (!theme) {
                console.error('Theme not found:', themeName);
                return;
            }

            // Apply CSS custom properties to document root
            const root = document.documentElement;
            Object.entries(theme).forEach(([property, value]) => {
                root.style.setProperty(property, value);
            });

            // Set data-theme attribute for CSS selectors
            root.setAttribute('data-theme', themeName);

            console.log('Applied theme:', themeName);
        }

        // Set default theme on load
        setTheme('default');
    </script>
</body>
</html>
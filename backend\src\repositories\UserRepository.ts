import { getDatabase } from '../config/database';
import { User, CreateUserData, UserPreferences, UserModel } from '../models/User';

export class UserRepository {
  private static getDb() {
    return getDatabase();
  }

  static async create(userData: CreateUserData): Promise<User> {
    const id = UserModel.generateId();
    const hashedPassword = userData.password ? await UserModel.hashPassword(userData.password) : '';
    const defaultPreferences = UserModel.getDefaultPreferences();
    const now = new Date().toISOString();

    const query = `
      INSERT INTO users (
        id, email, password_hash, display_name, avatar_url, 
        preferences, email_verified, oauth_provider, oauth_id, admin, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      userData.email.toLowerCase(),
      hashedPassword,
      userData.display_name,
      userData.avatar_url || null,
      JSON.stringify(defaultPreferences),
      userData.email_verified || false,
      userData.oauth_provider || null,
      userData.oauth_id || null,
      userData.admin || false, // admin from userData or default to false
      now,
      now
    ];

    return new Promise((resolve, reject) => {
      UserRepository.getDb().run(query, params, function(err) {
        if (err) {
          if (err.message.includes('UNIQUE constraint failed: users.email')) {
            reject(new Error('Email already exists'));
          } else {
            reject(err);
          }
          return;
        }

        // Fetch the created user
        UserRepository.findById(id)
          .then(user => {
            if (!user) {
              reject(new Error('Failed to create user'));
              return;
            }
            resolve(user);
          })
          .catch(reject);
      });
    });
  }

  static async findById(id: string): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE id = ?';
    
    return new Promise((resolve, reject) => {
      UserRepository.getDb().get(query, [id], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        
        if (!row) {
          resolve(null);
          return;
        }

        resolve(this.mapRowToUser(row));
      });
    });
  }

  static async findByEmail(email: string): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE email = ?';
    
    return new Promise((resolve, reject) => {
      UserRepository.getDb().get(query, [email.toLowerCase()], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        
        if (!row) {
          resolve(null);
          return;
        }

        resolve(this.mapRowToUser(row));
      });
    });
  }

  static async updatePassword(userId: string, newPassword: string): Promise<void> {
    const hashedPassword = await UserModel.hashPassword(newPassword);
    const query = 'UPDATE users SET password_hash = ?, updated_at = ? WHERE id = ?';
    const now = new Date().toISOString();

    return new Promise((resolve, reject) => {
      UserRepository.getDb().run(query, [hashedPassword, now, userId], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('User not found'));
          return;
        }

        resolve();
      });
    });
  }

  static async verifyEmail(userId: string): Promise<void> {
    const query = 'UPDATE users SET email_verified = ?, updated_at = ? WHERE id = ?';
    const now = new Date().toISOString();

    return new Promise((resolve, reject) => {
      UserRepository.getDb().run(query, [true, now, userId], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('User not found'));
          return;
        }

        resolve();
      });
    });
  }

  static async updatePreferences(userId: string, preferences: Partial<UserPreferences>): Promise<void> {
    // First get current preferences
    const user = await this.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const updatedPreferences = { ...user.preferences, ...preferences };
    const query = 'UPDATE users SET preferences = ?, updated_at = ? WHERE id = ?';
    const now = new Date().toISOString();

    return new Promise((resolve, reject) => {
      UserRepository.getDb().run(query, [JSON.stringify(updatedPreferences), now, userId], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('User not found'));
          return;
        }

        resolve();
      });
    });
  }

  static async updateProfile(userId: string, updates: { display_name?: string; avatar_url?: string }): Promise<void> {
    const fields: string[] = [];
    const params: any[] = [];

    if (updates.display_name !== undefined) {
      fields.push('display_name = ?');
      params.push(updates.display_name);
    }

    if (updates.avatar_url !== undefined) {
      fields.push('avatar_url = ?');
      params.push(updates.avatar_url);
    }

    if (fields.length === 0) {
      return; // Nothing to update
    }

    fields.push('updated_at = ?');
    params.push(new Date().toISOString());
    params.push(userId);

    const query = `UPDATE users SET ${fields.join(', ')} WHERE id = ?`;

    return new Promise((resolve, reject) => {
      UserRepository.getDb().run(query, params, function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('User not found'));
          return;
        }

        resolve();
      });
    });
  }

  static async updateAdminStatus(userId: string, isAdmin: boolean): Promise<void> {
    const query = 'UPDATE users SET admin = ?, updated_at = ? WHERE id = ?';
    const now = new Date().toISOString();

    return new Promise((resolve, reject) => {
      UserRepository.getDb().run(query, [isAdmin, now, userId], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('User not found'));
          return;
        }

        resolve();
      });
    });
  }

  // Two-Factor Authentication methods
  static async updateTwoFactorSecret(userId: string, secret: string | null, backupCodes: string[] = []): Promise<void> {
    const query = 'UPDATE users SET two_fa_secret = ?, backup_codes = ?, updated_at = ? WHERE id = ?';
    const now = new Date().toISOString();

    return new Promise((resolve, reject) => {
      UserRepository.getDb().run(query, [
        secret,
        JSON.stringify(backupCodes),
        now,
        userId
      ], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('User not found'));
          return;
        }

        resolve();
      });
    });
  }

  static async updateBackupCodes(userId: string, backupCodes: string[]): Promise<void> {
    const query = 'UPDATE users SET backup_codes = ?, updated_at = ? WHERE id = ?';
    const now = new Date().toISOString();

    return new Promise((resolve, reject) => {
      UserRepository.getDb().run(query, [
        JSON.stringify(backupCodes),
        now,
        userId
      ], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('User not found'));
          return;
        }

        resolve();
      });
    });
  }

  static async useBackupCode(userId: string, hashedCode: string): Promise<boolean> {
    const user = await this.findById(userId);
    if (!user) {
      return false;
    }

    try {
      const backupCodes = JSON.parse(user.backup_codes || '[]');
      const codeIndex = backupCodes.indexOf(hashedCode);
      
      if (codeIndex === -1) {
        return false; // Code not found
      }

      // Remove the used backup code
      backupCodes.splice(codeIndex, 1);
      await this.updateBackupCodes(userId, backupCodes);
      
      return true;
    } catch (error) {
      console.error('Error using backup code:', error);
      return false;
    }
  }

  private static mapRowToUser(row: any): User {
    return {
      id: row.id,
      email: row.email,
      password_hash: row.password_hash,
      display_name: row.display_name,
      avatar_url: row.avatar_url,
      preferences: JSON.parse(row.preferences || '{}'),
      email_verified: Boolean(row.email_verified),
      two_fa_secret: row.two_fa_secret,
      backup_codes: row.backup_codes,
      oauth_provider: row.oauth_provider,
      oauth_id: row.oauth_id,
      admin: Boolean(row.admin),
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at)
    };
  }
}
import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';
import { UserRepository } from '../repositories/UserRepository';

export interface AdminRequest extends AuthenticatedRequest {
  user: {
    id: string;
    email: string;
    jti: string;
    isAdmin: boolean;
  };
}

/**
 * Middleware to check if the authenticated user has admin privileges
 */
export async function requireAdmin(req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    const authReq = req as AuthenticatedRequest;
    
    if (!authReq.user) {
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    // Check if user has admin role
    const user = await UserRepository.findById(authReq.user.id);
    if (!user) {
      res.status(401).json({ error: 'User not found' });
      return;
    }

    // Check admin status from database admin column, user preferences, or admin email
    const preferences = typeof user.preferences === 'string' 
      ? JSON.parse(user.preferences) 
      : user.preferences;
    
    const isAdmin = user.admin === true || 
                   preferences?.isAdmin === true || 
                   user.email === process.env.ADMIN_EMAIL ||
                   user.email === '<EMAIL>';
    
    // Debug logging
    console.log('Admin check for user:', {
      email: user.email,
      adminField: user.admin,
      preferencesAdmin: preferences?.isAdmin,
      envAdminEmail: process.env.ADMIN_EMAIL,
      isAdmin
    });
    
    if (!isAdmin) {
      res.status(403).json({ error: 'Admin privileges required' });
      return;
    }

    // Add admin flag to request
    (req as AdminRequest).user = {
      ...authReq.user,
      isAdmin: true
    };

    next();
  } catch (error) {
    console.error('Admin auth middleware error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Middleware to check if user is admin or super admin
 */
export async function requireSuperAdmin(req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    const authReq = req as AuthenticatedRequest;
    
    if (!authReq.user) {
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    // Super admin is determined by environment variable
    const isSuperAdmin = authReq.user.email === process.env.SUPER_ADMIN_EMAIL;
    
    if (!isSuperAdmin) {
      res.status(403).json({ error: 'Super admin privileges required' });
      return;
    }

    next();
  } catch (error) {
    console.error('Super admin auth middleware error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
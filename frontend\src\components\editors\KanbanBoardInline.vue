<template>
    <div class="kanban-board-inline">
        <KanbanBoard v-if="boardId" :board-id="boardId" />
        <div v-else class="loading-board">
            <p>Initializing kanban board...</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import KanbanBoard from '../kanban/KanbanBoard.vue'
import { useKanbanStore } from '@/stores/kanban'

interface Props {
    modelValue: string
    placeholder?: string
    disabled?: boolean
}

interface Emits {
    (e: 'update:modelValue', value: string): void
    (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: 'Start building your kanban board...',
    disabled: false
})

const emit = defineEmits<Emits>()

const kanbanStore = useKanbanStore()
const boardId = ref<string>('')
const lastEmittedContent = ref<string>('')
let saveTimeout: number | null = null

// Initialize or parse board data
const initializeBoard = async () => {


    if (props.modelValue && props.modelValue.trim() !== '') {
        try {
            // Try to parse as JSON first
            const parsed = JSON.parse(props.modelValue)
            if (parsed && (parsed.boardId || parsed.id)) {

                boardId.value = parsed.boardId || parsed.id

                // Load the board data into the kanban store
                await loadBoardDataIntoStore(parsed)
            } else {
                // Create new board ID

                boardId.value = `board-${Date.now()}`
                await emitChange()
            }
        } catch (error) {
            // If it's not JSON, treat it as a simple boardId string (backward compatibility)
            if (props.modelValue.startsWith('board-')) {

                boardId.value = props.modelValue
                await emitChange() // Convert to proper format
            } else {
                // Create new board

                boardId.value = `board-${Date.now()}`
                await emitChange()
            }
        }
    } else {
        // Only create new board if we're sure there's no content coming
        // Wait a bit to see if content loads
        console.log('No modelValue yet, waiting for content to load...')

        // Wait up to 2 seconds for content to load
        let attempts = 0
        const maxAttempts = 20 // 20 * 100ms = 2 seconds

        while (attempts < maxAttempts && (!props.modelValue || props.modelValue.trim() === '')) {
            await new Promise(resolve => setTimeout(resolve, 100))
            attempts++
        }

        if (props.modelValue && props.modelValue.trim() !== '') {
            console.log('Content loaded after waiting, reinitializing...')
            return initializeBoard() // Recursive call with loaded content
        } else {
            console.log('No content loaded after waiting, creating new board')
            boardId.value = `board-${Date.now()}`
            await emitChange()
        }
    }

    // Start watching for changes after initialization
    startWatchingBoard()
}

const emitChange = async () => {
    if (!boardId.value) {
        console.warn('No boardId available for emitChange')
        return
    }

    try {
        console.log('Emitting change for board:', boardId.value)

        // Get the current board data from the store
        const currentBoard = await kanbanStore.getBoard(boardId.value)

        if (currentBoard) {
            // Create the full kanban structure for the backend
            const kanbanContent = {
                id: currentBoard.id,
                boardId: currentBoard.id, // Include both for compatibility
                title: currentBoard.title,
                columns: currentBoard.columns.map(col => ({
                    id: col.id,
                    title: col.title,
                    position: col.position,
                    boardId: col.boardId,
                    color: col.color,
                    cards: col.cards.map(card => ({
                        id: card.id,
                        columnId: card.columnId,
                        title: card.title,
                        description: card.description,
                        position: card.position,
                        labels: card.labels,
                        assignees: card.assignees,
                        priority: card.priority,
                        dueDate: card.dueDate,
                        color: card.color,
                        createdAt: card.createdAt,
                        updatedAt: card.updatedAt
                    })),
                    createdAt: col.createdAt,
                    updatedAt: col.updatedAt
                })),
                labels: currentBoard.labels,
                members: currentBoard.members,
                settings: currentBoard.settings,
                shareSettings: currentBoard.shareSettings,
                createdAt: currentBoard.createdAt,
                updatedAt: currentBoard.updatedAt
            }

            // Emit as JSON string to match backend expectations
            const jsonValue = JSON.stringify(kanbanContent, null, 2)

            // Only emit if content has actually changed
            if (jsonValue !== lastEmittedContent.value) {
                console.log('Emitting kanban board changes:', kanbanContent.columns.length, 'columns')
                lastEmittedContent.value = jsonValue
                emit('update:modelValue', jsonValue)
                emit('change', jsonValue)
            } else {
                console.log('No changes detected, skipping emit')
            }
        } else {
            console.warn('No board found for ID:', boardId.value)
            // Fallback to minimal structure
            const kanbanContent = {
                boardId: boardId.value,
                columns: []
            }
            const jsonValue = JSON.stringify(kanbanContent)
            lastEmittedContent.value = jsonValue
            emit('update:modelValue', jsonValue)
            emit('change', jsonValue)
        }
    } catch (error) {
        console.error('Error emitting kanban changes:', error)
        // Fallback to minimal structure
        const kanbanContent = {
            boardId: boardId.value,
            columns: []
        }
        const jsonValue = JSON.stringify(kanbanContent)
        lastEmittedContent.value = jsonValue
        emit('update:modelValue', jsonValue)
        emit('change', jsonValue)
    }
}

// Watch for external changes
watch(() => props.modelValue, (newValue, oldValue) => {

    // If we went from no content to having content, reinitialize
    if ((!oldValue || oldValue.trim() === '') && newValue && newValue.trim() !== '') {
        console.log('Content loaded, reinitializing board...')
        initializeBoard()
        return
    }

    // Check if the current value matches what we expect
    try {
        const parsed = JSON.parse(newValue || '{}')
        const newBoardId = parsed.boardId || parsed.id
        if (newBoardId && newBoardId !== boardId.value) {
            console.log('Different board ID detected, reinitializing:', { newBoardId, currentBoardId: boardId.value })
            initializeBoard()
        }
    } catch (error) {
        // If it's not JSON or doesn't match, reinitialize
        if (newValue !== boardId.value && newValue !== lastEmittedContent.value) {
            console.log('Content format changed, reinitializing')
            initializeBoard()
        }
    }
})

// Debounced emit change to prevent too many saves
// Load board data from note content into the kanban store
const loadBoardDataIntoStore = async (boardData: any) => {
    try {


        // Create a proper KanbanBoard object with complete card structure
        const board = {
            id: boardData.id || boardData.boardId,
            title: boardData.title || 'Kanban Board',
            description: boardData.description || '',
            columns: (boardData.columns || []).map((col: { cards: any }) => ({
                ...col,
                cards: (col.cards || []).map((card: { attachments: any; comments: any; checklist: any; labels: any; assignees: any; priority: any; dueDate: any; color: any }) => ({
                    ...card,
                    // Ensure all required card properties exist
                    attachments: card.attachments || [],
                    comments: card.comments || [],
                    checklist: card.checklist || [],
                    labels: card.labels || [],
                    assignees: card.assignees || [],
                    priority: card.priority || 'medium',
                    dueDate: card.dueDate || null,
                    color: card.color || null
                }))
            })),
            labels: boardData.labels || [],
            members: boardData.members || [],
            settings: boardData.settings || {
                allowComments: true,
                allowAttachments: true,
                cardCoverImages: true,
                votingEnabled: false,
                dueDateReminders: true,
                backgroundColor: '#f8f9fa'
            },
            shareSettings: boardData.shareSettings || {
                isPublic: false,
                allowedUsers: [],
                permissions: {}
            },
            createdAt: boardData.createdAt || new Date().toISOString(),
            updatedAt: boardData.updatedAt || new Date().toISOString()
        }

        // Add the board to the store's boards array if it doesn't exist
        const existingBoardIndex = kanbanStore.boards.findIndex(b => b.id === board.id)
        if (existingBoardIndex !== -1) {
            kanbanStore.boards[existingBoardIndex] = board
        } else {
            kanbanStore.boards.push(board)
        }

        // Set as current board
        kanbanStore.currentBoard = board

        console.log('Board data loaded into store successfully')
    } catch (error) {
        console.error('Error loading board data into store:', error)
    }
}

const debouncedEmitChange = () => {
    if (saveTimeout) {
        clearTimeout(saveTimeout)
    }
    saveTimeout = setTimeout(() => {
        emitChange()
    }, 500)
}

// Watch for changes in the kanban store
let boardWatcher: any = null

const startWatchingBoard = () => {
    if (boardWatcher) {
        boardWatcher() // Stop previous watcher
    }

    console.log('Starting to watch board:', boardId.value)

    // Watch for changes in the current board
    boardWatcher = watch(
        () => kanbanStore.currentBoard,
        (newBoard) => {
            if (newBoard && newBoard.id === boardId.value) {
                debouncedEmitChange()
            }
        },
        { deep: true }
    )
}

onMounted(() => {
    initializeBoard()

    // Ensure we start at the top
    setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 200)
})

onUnmounted(() => {
    if (boardWatcher) {
        boardWatcher()
    }
    if (saveTimeout) {
        clearTimeout(saveTimeout)
    }
})
</script>

<style scoped>
.kanban-board-inline {
    width: 100%;
    min-height: 600px;
    max-height: 80vh;
    overflow: hidden;
    position: relative;
}

.loading-board {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
}
</style>
import { httpClient } from '../utils/http'

export interface Tag {
  id: string
  userId: string
  name: string
  icon: string
  color: string
  isPredefined: boolean
  count: number
  createdAt: Date
  updatedAt: Date
}

export interface CreateTagData {
  name: string
  icon?: string
  color?: string
}

export interface UpdateTagData {
  name?: string
  icon?: string
  color?: string
}

export interface TagFilters {
  isPredefined?: boolean
  search?: string
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  details?: string[]
}

class TagService {
  private baseUrl = '/tags'

  async getTags(filters?: TagFilters): Promise<ApiResponse<Tag[]>> {
    try {
      const params = new URLSearchParams()
      
      if (filters?.isPredefined !== undefined) {
        params.append('isPredefined', filters.isPredefined.toString())
      }
      
      if (filters?.search) {
        params.append('search', filters.search)
      }

      const url = params.toString() ? `${this.baseUrl}?${params}` : this.baseUrl
      const response = await httpClient.get(url)
      
      return {
        success: true,
        data: response.data.data.map((tag: any) => ({
          ...tag,
          createdAt: new Date(tag.createdAt),
          updatedAt: new Date(tag.updatedAt)
        }))
      }
    } catch (error: any) {
      console.error('Error fetching tags:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to fetch tags'
      }
    }
  }

  async getTag(id: string): Promise<ApiResponse<Tag>> {
    try {
      const response = await httpClient.get(`${this.baseUrl}/${id}`)
      
      return {
        success: true,
        data: {
          ...response.data.data,
          createdAt: new Date(response.data.data.createdAt),
          updatedAt: new Date(response.data.data.updatedAt)
        }
      }
    } catch (error: any) {
      console.error('Error fetching tag:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to fetch tag'
      }
    }
  }

  async createTag(data: CreateTagData): Promise<ApiResponse<Tag>> {
    try {
      const response = await httpClient.post(this.baseUrl, data)
      
      return {
        success: true,
        data: {
          ...response.data.data,
          createdAt: new Date(response.data.data.createdAt),
          updatedAt: new Date(response.data.data.updatedAt)
        }
      }
    } catch (error: any) {
      console.error('Error creating tag:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to create tag',
        details: error.response?.data?.details
      }
    }
  }

  async updateTag(id: string, data: UpdateTagData): Promise<ApiResponse<Tag>> {
    try {
      const response = await httpClient.put(`${this.baseUrl}/${id}`, data)
      
      return {
        success: true,
        data: {
          ...response.data.data,
          createdAt: new Date(response.data.data.createdAt),
          updatedAt: new Date(response.data.data.updatedAt)
        }
      }
    } catch (error: any) {
      console.error('Error updating tag:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to update tag',
        details: error.response?.data?.details
      }
    }
  }

  async deleteTag(id: string): Promise<ApiResponse<void>> {
    try {
      await httpClient.delete(`${this.baseUrl}/${id}`)
      
      return {
        success: true
      }
    } catch (error: any) {
      console.error('Error deleting tag:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to delete tag'
      }
    }
  }

  async initializePredefinedTags(): Promise<ApiResponse<void>> {
    try {
      await httpClient.post(`${this.baseUrl}/initialize`)
      
      return {
        success: true
      }
    } catch (error: any) {
      console.error('Error initializing predefined tags:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to initialize predefined tags'
      }
    }
  }
}

export const tagService = new TagService()
export default tagService
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import MarkdownEditor from '../MarkdownEditor.vue'

// Mock highlight.js
vi.mock('highlight.js', () => ({
  default: {
    highlight: vi.fn().mockReturnValue({ value: 'highlighted code' }),
    highlightAuto: vi.fn().mockReturnValue({ value: 'auto highlighted code' }),
    getLanguage: vi.fn().mockReturnValue(true)
  }
}))

describe('MarkdownEditor', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(MarkdownEditor, {
      props: {
        modelValue: '',
        placeholder: 'Test placeholder'
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.find('.markdown-editor').exists()).toBe(true)
    expect(wrapper.find('.toolbar').exists()).toBe(true)
    expect(wrapper.find('.editor-content').exists()).toBe(true)
    expect(wrapper.find('.markdown-textarea').exists()).toBe(true)
  })

  it('displays toolbar buttons', () => {
    const toolbar = wrapper.find('.toolbar')
    
    // Check for formatting buttons
    expect(toolbar.find('[title="Bold (Ctrl+B)"]').exists()).toBe(true)
    expect(toolbar.find('[title="Italic (Ctrl+I)"]').exists()).toBe(true)
    expect(toolbar.find('[title="Strikethrough"]').exists()).toBe(true)
    
    // Check for header buttons
    expect(toolbar.find('[title="Heading 1"]').exists()).toBe(true)
    expect(toolbar.find('[title="Heading 2"]').exists()).toBe(true)
    expect(toolbar.find('[title="Heading 3"]').exists()).toBe(true)
    
    // Check for list buttons
    expect(toolbar.find('[title="Bullet List"]').exists()).toBe(true)
    expect(toolbar.find('[title="Numbered List"]').exists()).toBe(true)
    expect(toolbar.find('[title="Task List"]').exists()).toBe(true)
  })

  it('shows preview pane by default', () => {
    expect(wrapper.find('.preview-pane').exists()).toBe(true)
    expect(wrapper.find('.editor-content').classes()).toContain('split-view')
  })

  it('can toggle preview pane', async () => {
    const toggleButton = wrapper.find('[title="Toggle Preview"]')
    
    // Initially preview should be visible
    expect(wrapper.find('.preview-pane').exists()).toBe(true)
    
    // Click toggle button
    await toggleButton.trigger('click')
    
    // Preview should be hidden
    expect(wrapper.find('.preview-pane').exists()).toBe(false)
    expect(wrapper.find('.editor-content').classes()).not.toContain('split-view')
  })

  it('emits update:modelValue when content changes', async () => {
    const textarea = wrapper.find('.markdown-textarea')
    
    await textarea.setValue('# Test Heading')
    
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')[0]).toEqual(['# Test Heading'])
  })

  it('emits change event when content changes', async () => {
    const textarea = wrapper.find('.markdown-textarea')
    
    await textarea.setValue('Test content')
    
    expect(wrapper.emitted('change')).toBeTruthy()
    expect(wrapper.emitted('change')[0]).toEqual(['Test content'])
  })

  it('renders markdown in preview pane', async () => {
    await wrapper.setProps({ modelValue: '# Test Heading\n\nThis is **bold** text.' })
    
    const previewContent = wrapper.find('.preview-content')
    expect(previewContent.html()).toContain('<h1>')
    expect(previewContent.html()).toContain('Test Heading')
  })

  it('shows placeholder when content is empty', () => {
    const previewContent = wrapper.find('.preview-content')
    expect(previewContent.html()).toContain('Preview will appear here...')
  })

  it('handles disabled state', async () => {
    await wrapper.setProps({ disabled: true })
    
    const textarea = wrapper.find('.markdown-textarea')
    expect(textarea.attributes('disabled')).toBeDefined()
  })

  it('inserts bold markdown when bold button is clicked', async () => {
    const boldButton = wrapper.find('[title="Bold (Ctrl+B)"]')
    const textarea = wrapper.find('.markdown-textarea')
    
    // Set some text and select it
    await textarea.setValue('selected text')
    textarea.element.setSelectionRange(0, 13)
    
    await boldButton.trigger('click')
    
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    const lastEmit = wrapper.emitted('update:modelValue').slice(-1)[0]
    expect(lastEmit[0]).toBe('**selected text**')
  })

  it('inserts italic markdown when italic button is clicked', async () => {
    const italicButton = wrapper.find('[title="Italic (Ctrl+I)"]')
    const textarea = wrapper.find('.markdown-textarea')
    
    await textarea.setValue('text')
    textarea.element.setSelectionRange(0, 4)
    
    await italicButton.trigger('click')
    
    const lastEmit = wrapper.emitted('update:modelValue').slice(-1)[0]
    expect(lastEmit[0]).toBe('*text*')
  })

  it('inserts heading markdown when heading button is clicked', async () => {
    const h1Button = wrapper.find('[title="Heading 1"]')
    const textarea = wrapper.find('.markdown-textarea')
    
    await textarea.setValue('heading text')
    textarea.element.setSelectionRange(0, 0)
    
    await h1Button.trigger('click')
    
    const lastEmit = wrapper.emitted('update:modelValue').slice(-1)[0]
    expect(lastEmit[0]).toBe('# heading text')
  })

  it('inserts bullet list markdown when list button is clicked', async () => {
    const listButton = wrapper.find('[title="Bullet List"]')
    const textarea = wrapper.find('.markdown-textarea')
    
    await textarea.setValue('list item')
    textarea.element.setSelectionRange(0, 0)
    
    await listButton.trigger('click')
    
    const lastEmit = wrapper.emitted('update:modelValue').slice(-1)[0]
    expect(lastEmit[0]).toBe('- list item')
  })

  it('handles keyboard shortcuts', async () => {
    const textarea = wrapper.find('.markdown-textarea')
    
    // Test Ctrl+B for bold
    await textarea.setValue('text')
    textarea.element.setSelectionRange(0, 4)
    
    await textarea.trigger('keydown', {
      key: 'b',
      ctrlKey: true
    })
    
    const lastEmit = wrapper.emitted('update:modelValue').slice(-1)[0]
    expect(lastEmit[0]).toBe('**text**')
  })

  it('handles tab key for indentation', async () => {
    const textarea = wrapper.find('.markdown-textarea')
    
    await textarea.setValue('text')
    textarea.element.setSelectionRange(0, 0)
    
    await textarea.trigger('keydown', {
      key: 'Tab'
    })
    
    const lastEmit = wrapper.emitted('update:modelValue').slice(-1)[0]
    expect(lastEmit[0]).toBe('  text')
  })

  it('can toggle sync scroll', async () => {
    const syncButton = wrapper.find('[title="Sync Scroll"]')
    
    // Initially sync scroll should be active
    expect(syncButton.classes()).toContain('is-active')
    
    await syncButton.trigger('click')
    
    // Sync scroll should be inactive
    expect(syncButton.classes()).not.toContain('is-active')
  })

  it('exposes focus method', () => {
    expect(wrapper.vm.focus).toBeDefined()
    expect(typeof wrapper.vm.focus).toBe('function')
  })

  it('exposes markdown insertion methods', () => {
    expect(wrapper.vm.insertMarkdown).toBeDefined()
    expect(wrapper.vm.insertLineMarkdown).toBeDefined()
    expect(wrapper.vm.insertLink).toBeDefined()
    expect(wrapper.vm.insertImage).toBeDefined()
    expect(wrapper.vm.insertCodeBlock).toBeDefined()
    expect(wrapper.vm.insertTable).toBeDefined()
  })

  it('exposes enhanced functionality methods', () => {
    expect(wrapper.vm.insertTableRow).toBeDefined()
    expect(wrapper.vm.insertTableColumn).toBeDefined()
    expect(wrapper.vm.exportMarkdown).toBeDefined()
    expect(wrapper.vm.toggleSearchReplace).toBeDefined()
    expect(wrapper.vm.performSearch).toBeDefined()
    expect(wrapper.vm.findNext).toBeDefined()
    expect(wrapper.vm.findPrevious).toBeDefined()
    expect(wrapper.vm.replaceNext).toBeDefined()
    expect(wrapper.vm.replaceAll).toBeDefined()
  })

  it('can toggle search and replace panel', async () => {
    const searchButton = wrapper.find('[title="Search & Replace (Ctrl+F)"]')
    
    // Initially search panel should be hidden
    expect(wrapper.find('.search-replace-panel').exists()).toBe(false)
    
    await searchButton.trigger('click')
    
    // Search panel should be visible
    expect(wrapper.find('.search-replace-panel').exists()).toBe(true)
    expect(searchButton.classes()).toContain('is-active')
  })

  it('can perform search functionality', async () => {
    await wrapper.setProps({ modelValue: 'This is a test. This is only a test.' })
    
    // Open search panel
    const searchButton = wrapper.find('[title="Search & Replace (Ctrl+F)"]')
    await searchButton.trigger('click')
    
    // Enter search query
    const searchInput = wrapper.find('.search-replace-panel input[placeholder="Search..."]')
    await searchInput.setValue('test')
    
    // Should find matches
    expect(wrapper.find('.search-info .tag').text()).toContain('of')
  })

  it('handles export functionality', () => {
    // Mock URL.createObjectURL and related methods
    global.URL.createObjectURL = vi.fn(() => 'mock-url')
    global.URL.revokeObjectURL = vi.fn()
    
    const mockLink = {
      href: '',
      download: '',
      click: vi.fn()
    }
    
    vi.spyOn(document, 'createElement').mockReturnValue(mockLink as any)
    vi.spyOn(document.body, 'appendChild').mockImplementation(() => {})
    vi.spyOn(document.body, 'removeChild').mockImplementation(() => {})
    
    const exportButton = wrapper.find('[title="Export Markdown"]')
    exportButton.trigger('click')
    
    expect(document.createElement).toHaveBeenCalledWith('a')
    expect(mockLink.click).toHaveBeenCalled()
  })


})
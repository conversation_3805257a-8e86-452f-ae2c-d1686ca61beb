<template>
  <div class="auth-error-container">
    <div class="container">
      <div class="columns is-centered">
        <div class="column is-narrow">
          <div class="card">
            <div class="card-content has-text-centered">
              <span class="icon is-large has-text-danger">
                <i class="fas fa-exclamation-triangle fa-3x"></i>
              </span>
              
              <h1 class="title is-3 mt-4">Authentication Error</h1>
              
              <p class="subtitle is-5 has-text-grey">
                {{ errorMessage }}
              </p>
              
              <div class="content">
                <p>
                  We encountered an issue while trying to authenticate your account.
                  This could be due to:
                </p>
                <ul class="has-text-left">
                  <li>The authentication session expired</li>
                  <li>The request was cancelled</li>
                  <li>A temporary server issue</li>
                </ul>
              </div>
              
              <div class="buttons is-centered">
                <router-link to="/login" class="button is-primary">
                  <span class="icon">
                    <i class="fas fa-sign-in-alt"></i>
                  </span>
                  <span>Try Again</span>
                </router-link>
                
                <router-link to="/register" class="button is-light">
                  <span class="icon">
                    <i class="fas fa-user-plus"></i>
                  </span>
                  <span>Create Account</span>
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const errorMessage = computed(() => {
  const message = route.query.message as string
  return message || 'An unexpected error occurred during authentication.'
})
</script>

<style scoped>
.auth-error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
  max-width: 500px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-content {
  padding: 3rem 2rem;
}

.content ul {
  margin: 1rem 0;
}

.buttons {
  margin-top: 2rem;
}
</style>
import { Request, Response, NextFunction } from 'express';
import { JWTUtils, JWTPayload } from '../utils/jwt';
import { UserRepository } from '../repositories/UserRepository';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        jti: string;
      };
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    jti: string;
  };
}

export const authenticateToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({ 
        error: 'Access token required',
        code: 'MISSING_TOKEN'
      });
      return;
    }

    // Enhanced token verification with IP validation
    const payload: JWTPayload = JWTUtils.verifyAccessToken(token, {
      ipAddress: req.ip
    });
    
    // Verify user still exists and is active
    const user = await UserRepository.findById(payload.userId);
    if (!user) {
      res.status(401).json({ 
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
      return;
    }

    // Check if user account is active (if such fields exist in the future)
    // For now, we'll skip this check as the User model doesn't have these fields yet

    // Add user info to request
    req.user = {
      id: payload.userId,
      email: payload.email,
      jti: payload.jti
    };

    // Update last activity timestamp (optional, for session management)
    // This could be implemented in the future when the UserRepository supports it

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    
    // Provide specific error messages for different token issues
    if (error instanceof Error) {
      if (error.message.includes('expired')) {
        res.status(401).json({ 
          error: 'Token expired',
          code: 'TOKEN_EXPIRED'
        });
      } else if (error.message.includes('revoked')) {
        res.status(401).json({ 
          error: 'Token has been revoked',
          code: 'TOKEN_REVOKED'
        });
      } else {
        res.status(401).json({ 
          error: 'Invalid token',
          code: 'INVALID_TOKEN'
        });
      }
    } else {
      res.status(401).json({ 
        error: 'Authentication failed',
        code: 'AUTH_FAILED'
      });
    }
  }
};

export const optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const payload: JWTPayload = JWTUtils.verifyAccessToken(token, {
        ipAddress: req.ip
      });
      
      // Verify user still exists and is active
      const user = await UserRepository.findById(payload.userId);
      if (user) {
        req.user = {
          id: payload.userId,
          email: payload.email,
          jti: payload.jti
        };
      }
    }

    next();
  } catch (error) {
    // For optional auth, we don't return an error, just continue without user
    next();
  }
};

export const requireEmailVerification = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
      return;
    }

    const user = await UserRepository.findById(req.user.id);
    if (!user) {
      res.status(401).json({ 
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
      return;
    }

    if (!user.email_verified) {
      res.status(403).json({ 
        error: 'Email verification required',
        code: 'EMAIL_NOT_VERIFIED'
      });
      return;
    }

    next();
  } catch (error) {
    console.error('Email verification check error:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
};
// Test script to verify tag functionality across all editors
// This should be run in the browser console after logging in

async function testTagsInAllEditors() {
    console.log("🧪 Testing tag functionality across all editors...");

    const API_BASE = "/api";
    const authToken = localStorage.getItem("authToken");

    if (!authToken) {
        console.error("❌ No auth token found. Please log in first.");
        return;
    }

    const headers = {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
    };

    try {
        // Test 1: Get available tags
        console.log("📋 Step 1: Getting available tags...");
        const tagsResponse = await fetch(`${API_BASE}/tags`, { headers });
        const tagsData = await tagsResponse.json();
        console.log(
            `✅ Found ${tagsData.tags.length} tags:`,
            tagsData.tags.map((t) => t.name)
        );

        // Test 2: Create test notes with tags for each editor type
        const testNotes = [
            {
                title: "Markdown Test with Tags",
                content:
                    "# Test Markdown Note\n\nThis is a test note with tags.",
                noteType: "markdown",
                tags: ["test", "markdown", "work"],
            },
            {
                title: "Rich Text Test with Tags",
                content:
                    "<h1>Test Rich Text Note</h1><p>This is a test note with tags.</p>",
                noteType: "richtext",
                tags: ["test", "richtext", "personal"],
            },
            {
                title: "Kanban Test with Tags",
                content: JSON.stringify({
                    columns: [
                        {
                            id: "todo",
                            title: "To Do",
                            cards: [
                                {
                                    id: "card1",
                                    title: "Test card",
                                    description: "Test description",
                                },
                            ],
                        },
                    ],
                }),
                noteType: "kanban",
                tags: ["test", "kanban", "project"],
            },
        ];

        console.log("📝 Step 2: Creating test notes with tags...");
        const createdNotes = [];

        for (const noteData of testNotes) {
            const response = await fetch(`${API_BASE}/notes`, {
                method: "POST",
                headers,
                body: JSON.stringify(noteData),
            });

            if (response.ok) {
                const note = await response.json();
                createdNotes.push(note);
                console.log(
                    `✅ Created ${note.noteType} note: "${
                        note.title
                    }" with tags: [${note.tags.map((t) => t.name).join(", ")}]`
                );
            } else {
                const error = await response.json();
                console.error(
                    `❌ Failed to create ${noteData.noteType} note:`,
                    error
                );
            }
        }

        // Test 3: Update notes with additional tags
        console.log("🔄 Step 3: Testing tag updates...");

        for (const note of createdNotes) {
            const newTags = [
                ...note.tags.map((t) => t.name),
                "updated",
                "test-complete",
            ];

            const response = await fetch(`${API_BASE}/notes/${note.id}`, {
                method: "PUT",
                headers,
                body: JSON.stringify({ tags: newTags }),
            });

            if (response.ok) {
                const updatedNote = await response.json();
                console.log(
                    `✅ Updated ${
                        updatedNote.noteType
                    } note tags: [${updatedNote.tags
                        .map((t) => t.name)
                        .join(", ")}]`
                );
            } else {
                const error = await response.json();
                console.error(
                    `❌ Failed to update ${note.noteType} note tags:`,
                    error
                );
            }
        }

        // Test 4: Search by tags
        console.log("🔍 Step 4: Testing tag-based search...");

        const searchResponse = await fetch(`${API_BASE}/notes?tags=test`, {
            headers,
        });
        if (searchResponse.ok) {
            const searchData = await searchResponse.json();
            const testNotes = searchData.notes.filter((note) =>
                note.tags.some((tag) => tag.name === "test")
            );
            console.log(`✅ Found ${testNotes.length} notes with 'test' tag`);
        } else {
            console.error("❌ Failed to search by tags");
        }

        // Test 5: Filter by note type and tags
        console.log("🎯 Step 5: Testing combined filters...");

        const filterResponse = await fetch(
            `${API_BASE}/notes?noteType=markdown&tags=test`,
            { headers }
        );
        if (filterResponse.ok) {
            const filterData = await filterResponse.json();
            console.log(
                `✅ Found ${filterData.notes.length} markdown notes with 'test' tag`
            );
        } else {
            console.error("❌ Failed to filter by type and tags");
        }

        console.log("🎉 Tag functionality test completed!");

        // Cleanup: Delete test notes
        console.log("🧹 Cleaning up test notes...");
        for (const note of createdNotes) {
            try {
                await fetch(`${API_BASE}/notes/${note.id}`, {
                    method: "DELETE",
                    headers,
                });
                console.log(`🗑️ Deleted test note: ${note.title}`);
            } catch (error) {
                console.error(`❌ Failed to delete note ${note.title}:`, error);
            }
        }
    } catch (error) {
        console.error("❌ Test failed:", error);
    }
}

// Export for use in browser console
window.testTagsInAllEditors = testTagsInAllEditors;

console.log(
    "🚀 Tag test script loaded. Run testTagsInAllEditors() to start testing."
);

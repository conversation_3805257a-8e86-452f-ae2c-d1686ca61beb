import { getDatabase } from '../config/database';
import { CacheService } from './CacheService';

export interface QueryMetrics {
  query: string;
  params: any[];
  executionTime: number;
  rowsAffected: number;
  timestamp: number;
  cached: boolean;
  planAnalysis?: any;
}

export interface PerformanceReport {
  totalQueries: number;
  averageExecutionTime: number;
  slowQueries: QueryMetrics[];
  mostFrequentQueries: { query: string; count: number; avgTime: number }[];
  cacheHitRate: number;
  recommendations: string[];
}

export class QueryPerformanceAnalyzer {
  private static instance: QueryPerformanceAnalyzer;
  private metrics: QueryMetrics[] = [];
  private queryFrequency: Map<string, { count: number; totalTime: number }> = new Map();
  private isEnabled = process.env.NODE_ENV === 'development' || process.env.ENABLE_QUERY_ANALYSIS === 'true';
  private maxMetricsSize = 1000;

  private constructor() {}

  static getInstance(): QueryPerformanceAnalyzer {
    if (!QueryPerformanceAnalyzer.instance) {
      QueryPerformanceAnalyzer.instance = new QueryPerformanceAnalyzer();
    }
    return QueryPerformanceAnalyzer.instance;
  }

  // Wrap database queries with performance monitoring
  async executeQuery<T>(
    query: string,
    params: any[] = [],
    options: {
      cache?: boolean;
      cacheKey?: string;
      cacheTTL?: number;
      tags?: string[];
    } = {}
  ): Promise<T[]> {
    const startTime = performance.now();
    const db = getDatabase();
    
    // Check cache first if enabled
    let cached = false;
    if (options.cache && options.cacheKey) {
      const cachedResult = await CacheService.get<T[]>(options.cacheKey);
      if (cachedResult) {
        this.recordMetrics(query, params, performance.now() - startTime, cachedResult.length, true);
        return cachedResult;
      }
    }

    // Execute query
    const result = await new Promise<T[]>((resolve, reject) => {
      db.all(query, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows as T[]);
      });
    });

    const executionTime = performance.now() - startTime;

    // Cache result if enabled
    if (options.cache && options.cacheKey && result) {
      await CacheService.set(options.cacheKey, result, {
        ttl: options.cacheTTL || 300,
        tags: options.tags
      });
    }

    // Record metrics
    this.recordMetrics(query, params, executionTime, result.length, cached);

    // Analyze slow queries
    if (executionTime > 100) { // Queries taking more than 100ms
      await this.analyzeSlowQuery(query, params, executionTime);
    }

    return result;
  }

  // Execute single row query with monitoring
  async executeQuerySingle<T>(
    query: string,
    params: any[] = [],
    options: {
      cache?: boolean;
      cacheKey?: string;
      cacheTTL?: number;
      tags?: string[];
    } = {}
  ): Promise<T | null> {
    const startTime = performance.now();
    const db = getDatabase();
    
    // Check cache first if enabled
    let cached = false;
    if (options.cache && options.cacheKey) {
      const cachedResult = await CacheService.get<T>(options.cacheKey);
      if (cachedResult) {
        this.recordMetrics(query, params, performance.now() - startTime, 1, true);
        return cachedResult;
      }
    }

    // Execute query
    const result = await new Promise<T | null>((resolve, reject) => {
      db.get(query, params, (err, row) => {
        if (err) reject(err);
        else resolve(row as T || null);
      });
    });

    const executionTime = performance.now() - startTime;

    // Cache result if enabled
    if (options.cache && options.cacheKey && result) {
      await CacheService.set(options.cacheKey, result, {
        ttl: options.cacheTTL || 300,
        tags: options.tags
      });
    }

    // Record metrics
    this.recordMetrics(query, params, executionTime, result ? 1 : 0, cached);

    return result;
  }

  // Record query metrics
  private recordMetrics(
    query: string,
    params: any[],
    executionTime: number,
    rowsAffected: number,
    cached: boolean
  ): void {
    if (!this.isEnabled) return;

    const metric: QueryMetrics = {
      query: this.normalizeQuery(query),
      params,
      executionTime,
      rowsAffected,
      timestamp: Date.now(),
      cached
    };

    this.metrics.push(metric);

    // Keep metrics size under control
    if (this.metrics.length > this.maxMetricsSize) {
      this.metrics = this.metrics.slice(-this.maxMetricsSize);
    }

    // Update frequency tracking
    const normalizedQuery = this.normalizeQuery(query);
    const existing = this.queryFrequency.get(normalizedQuery);
    if (existing) {
      existing.count++;
      existing.totalTime += executionTime;
    } else {
      this.queryFrequency.set(normalizedQuery, { count: 1, totalTime: executionTime });
    }
  }

  // Normalize query for analysis (remove specific values)
  private normalizeQuery(query: string): string {
    return query
      .replace(/\s+/g, ' ')
      .replace(/\?/g, '?')
      .replace(/\d+/g, 'N')
      .replace(/'[^']*'/g, "'?'")
      .trim();
  }

  // Analyze slow queries
  private async analyzeSlowQuery(query: string, params: any[], executionTime: number): Promise<void> {
    try {
      const db = getDatabase();
      const explainQuery = `EXPLAIN QUERY PLAN ${query}`;
      
      const planAnalysis = await new Promise<any[]>((resolve, reject) => {
        db.all(explainQuery, params, (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      console.warn(`Slow query detected (${executionTime.toFixed(2)}ms):`, {
        query: this.normalizeQuery(query),
        params,
        planAnalysis
      });

      // Store plan analysis with the last metric
      if (this.metrics.length > 0) {
        this.metrics[this.metrics.length - 1].planAnalysis = planAnalysis;
      }
    } catch (error) {
      console.error('Failed to analyze slow query:', error);
    }
  }

  // Generate performance report
  generateReport(): PerformanceReport {
    const totalQueries = this.metrics.length;
    const cachedQueries = this.metrics.filter(m => m.cached).length;
    const cacheHitRate = totalQueries > 0 ? (cachedQueries / totalQueries) * 100 : 0;

    const executionTimes = this.metrics.map(m => m.executionTime);
    const averageExecutionTime = executionTimes.length > 0 
      ? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length 
      : 0;

    // Find slow queries (top 10% by execution time)
    const sortedMetrics = [...this.metrics].sort((a, b) => b.executionTime - a.executionTime);
    const slowQueryCount = Math.max(1, Math.floor(totalQueries * 0.1));
    const slowQueries = sortedMetrics.slice(0, slowQueryCount);

    // Most frequent queries
    const mostFrequentQueries = Array.from(this.queryFrequency.entries())
      .map(([query, stats]) => ({
        query,
        count: stats.count,
        avgTime: stats.totalTime / stats.count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Generate recommendations
    const recommendations = this.generateRecommendations(slowQueries, mostFrequentQueries, cacheHitRate);

    return {
      totalQueries,
      averageExecutionTime,
      slowQueries,
      mostFrequentQueries,
      cacheHitRate,
      recommendations
    };
  }

  // Generate performance recommendations
  private generateRecommendations(
    slowQueries: QueryMetrics[],
    frequentQueries: { query: string; count: number; avgTime: number }[],
    cacheHitRate: number
  ): string[] {
    const recommendations: string[] = [];

    // Cache hit rate recommendations
    if (cacheHitRate < 50) {
      recommendations.push('Consider implementing more aggressive caching strategies. Current cache hit rate is low.');
    }

    // Slow query recommendations
    if (slowQueries.length > 0) {
      const avgSlowTime = slowQueries.reduce((sum, q) => sum + q.executionTime, 0) / slowQueries.length;
      if (avgSlowTime > 500) {
        recommendations.push('Several queries are taking over 500ms. Consider adding indexes or optimizing query structure.');
      }

      // Check for missing indexes
      const selectQueries = slowQueries.filter(q => q.query.toLowerCase().includes('select'));
      if (selectQueries.length > 0) {
        recommendations.push('Consider adding indexes on frequently queried columns, especially in WHERE and JOIN clauses.');
      }
    }

    // Frequent query recommendations
    const highFrequencyQueries = frequentQueries.filter(q => q.count > 100);
    if (highFrequencyQueries.length > 0) {
      recommendations.push('Some queries are executed very frequently. Consider caching their results or using materialized views.');
    }

    // N+1 query detection
    const potentialNPlusOne = frequentQueries.filter(q => 
      q.count > 50 && q.query.toLowerCase().includes('where') && q.query.toLowerCase().includes('=')
    );
    if (potentialNPlusOne.length > 0) {
      recommendations.push('Potential N+1 query pattern detected. Consider using JOIN queries or batch loading.');
    }

    // Full table scan detection
    const fullScans = slowQueries.filter(q => 
      q.planAnalysis && q.planAnalysis.some((step: any) => 
        step.detail && step.detail.toLowerCase().includes('scan')
      )
    );
    if (fullScans.length > 0) {
      recommendations.push('Some queries are performing full table scans. Add appropriate indexes to improve performance.');
    }

    return recommendations;
  }

  // Get query statistics
  getQueryStats(): any {
    const totalQueries = this.metrics.length;
    const cachedQueries = this.metrics.filter(m => m.cached).length;
    const slowQueries = this.metrics.filter(m => m.executionTime > 100).length;

    const executionTimes = this.metrics.map(m => m.executionTime);
    const avgTime = executionTimes.length > 0 
      ? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length 
      : 0;

    const maxTime = executionTimes.length > 0 ? Math.max(...executionTimes) : 0;
    const minTime = executionTimes.length > 0 ? Math.min(...executionTimes) : 0;

    return {
      totalQueries,
      cachedQueries,
      slowQueries,
      cacheHitRate: totalQueries > 0 ? (cachedQueries / totalQueries) * 100 : 0,
      averageExecutionTime: avgTime,
      maxExecutionTime: maxTime,
      minExecutionTime: minTime,
      queryFrequency: Object.fromEntries(this.queryFrequency)
    };
  }

  // Clear metrics
  clearMetrics(): void {
    this.metrics = [];
    this.queryFrequency.clear();
  }

  // Enable/disable monitoring
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  // Get recent slow queries
  getRecentSlowQueries(limit: number = 10): QueryMetrics[] {
    return this.metrics
      .filter(m => m.executionTime > 100)
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  // Export metrics for analysis
  exportMetrics(): any {
    return {
      timestamp: Date.now(),
      metrics: this.metrics,
      queryFrequency: Object.fromEntries(this.queryFrequency),
      report: this.generateReport()
    };
  }
}

// Export singleton instance
export const queryPerformanceAnalyzer = QueryPerformanceAnalyzer.getInstance();
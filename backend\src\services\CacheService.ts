import { getRedisClient, <PERSON>acheKeys, CacheTTL } from '../config/redis';
import Redis from 'ioredis';

export interface CacheOptions {
  ttl?: number;
  compress?: boolean;
  tags?: string[];
}

export class CacheService {
  private static redis: Redis | null = null;
  private static fallbackCache: Map<string, { data: any; expires: number; tags?: string[] }> = new Map();
  private static isRedisAvailable = false;

  static initialize(redisClient: Redis | null) {
    this.redis = redisClient;
    this.isRedisAvailable = !!redisClient;
    
    if (!this.isRedisAvailable) {
      console.warn('CacheService: Running in fallback mode without Redis');
      // Clean up fallback cache every 5 minutes
      setInterval(() => this.cleanupFallbackCache(), 5 * 60 * 1000);
    }
  }

  // Generic cache operations
  static async get<T>(key: string): Promise<T | null> {
    try {
      if (this.isRedisAvailable && this.redis) {
        const data = await this.redis.get(key);
        return data ? JSON.parse(data) : null;
      } else {
        return this.getFallback<T>(key);
      }
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  static async set(key: string, value: any, options: CacheOptions = {}): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      const ttl = options.ttl || 3600; // Default 1 hour

      if (this.isRedisAvailable && this.redis) {
        if (options.tags && options.tags.length > 0) {
          // Store tags for cache invalidation
          const tagKey = `tags:${key}`;
          await this.redis.setex(tagKey, ttl, JSON.stringify(options.tags));
          
          // Add key to each tag's set
          for (const tag of options.tags) {
            await this.redis.sadd(`tag:${tag}`, key);
            await this.redis.expire(`tag:${tag}`, ttl);
          }
        }
        
        await this.redis.setex(key, ttl, serialized);
        return true;
      } else {
        return this.setFallback(key, value, ttl, options.tags);
      }
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  static async del(key: string): Promise<boolean> {
    try {
      if (this.isRedisAvailable && this.redis) {
        // Remove from tag sets
        const tagKey = `tags:${key}`;
        const tags = await this.redis.get(tagKey);
        if (tags) {
          const tagList = JSON.parse(tags);
          for (const tag of tagList) {
            await this.redis.srem(`tag:${tag}`, key);
          }
          await this.redis.del(tagKey);
        }
        
        const result = await this.redis.del(key);
        return result > 0;
      } else {
        return this.delFallback(key);
      }
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  static async invalidateByTags(tags: string[]): Promise<number> {
    let invalidatedCount = 0;
    
    try {
      if (this.isRedisAvailable && this.redis) {
        for (const tag of tags) {
          const keys = await this.redis.smembers(`tag:${tag}`);
          if (keys.length > 0) {
            await this.redis.del(...keys);
            invalidatedCount += keys.length;
          }
          await this.redis.del(`tag:${tag}`);
        }
      } else {
        invalidatedCount = this.invalidateByTagsFallback(tags);
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
    
    return invalidatedCount;
  }

  static async clear(): Promise<boolean> {
    try {
      if (this.isRedisAvailable && this.redis) {
        await this.redis.flushdb();
        return true;
      } else {
        this.fallbackCache.clear();
        return true;
      }
    } catch (error) {
      console.error('Cache clear error:', error);
      return false;
    }
  }

  // Specific cache methods for common operations
  static async cacheUser(userId: string, userData: any): Promise<boolean> {
    return this.set(CacheKeys.user(userId), userData, {
      ttl: CacheTTL.USER,
      tags: ['user', `user:${userId}`]
    });
  }

  static async getCachedUser(userId: string): Promise<any> {
    return this.get(CacheKeys.user(userId));
  }

  static async cacheNote(noteId: string, noteData: any): Promise<boolean> {
    return this.set(CacheKeys.note(noteId), noteData, {
      ttl: CacheTTL.NOTE,
      tags: ['note', `note:${noteId}`, `user:${noteData.user_id}`]
    });
  }

  static async getCachedNote(noteId: string): Promise<any> {
    return this.get(CacheKeys.note(noteId));
  }

  static async cacheNoteList(userId: string, page: number, limit: number, notes: any[], filters?: string): Promise<boolean> {
    return this.set(CacheKeys.noteList(userId, page, limit, filters), notes, {
      ttl: CacheTTL.NOTE_LIST,
      tags: ['notes', `user:${userId}`]
    });
  }

  static async getCachedNoteList(userId: string, page: number, limit: number, filters?: string): Promise<any[]> {
    const result = await this.get<any[]>(CacheKeys.noteList(userId, page, limit, filters));
    return result || [];
  }

  static async cacheSearchResults(query: string, userId: string, results: any[], filters?: string): Promise<boolean> {
    return this.set(CacheKeys.searchResults(query, userId, filters), results, {
      ttl: CacheTTL.SEARCH_RESULTS,
      tags: ['search', `user:${userId}`]
    });
  }

  static async getCachedSearchResults(query: string, userId: string, filters?: string): Promise<any[]> {
    const result = await this.get<any[]>(CacheKeys.searchResults(query, userId, filters));
    return result || [];
  }

  static async cacheTags(userId: string, tags: any[]): Promise<boolean> {
    return this.set(CacheKeys.tags(userId), tags, {
      ttl: CacheTTL.TAGS,
      tags: ['tags', `user:${userId}`]
    });
  }

  static async getCachedTags(userId: string): Promise<any[]> {
    const result = await this.get<any[]>(CacheKeys.tags(userId));
    return result || [];
  }

  static async cacheUserSettings(userId: string, settings: any): Promise<boolean> {
    return this.set(CacheKeys.userSettings(userId), settings, {
      ttl: CacheTTL.USER_SETTINGS,
      tags: ['settings', `user:${userId}`]
    });
  }

  static async getCachedUserSettings(userId: string): Promise<any> {
    return this.get(CacheKeys.userSettings(userId));
  }

  // Cache invalidation methods
  static async invalidateUserCache(userId: string): Promise<void> {
    await this.invalidateByTags([`user:${userId}`]);
  }

  static async invalidateNoteCache(noteId: string, userId?: string): Promise<void> {
    const tags = [`note:${noteId}`];
    if (userId) {
      tags.push(`user:${userId}`);
    }
    await this.invalidateByTags(tags);
  }

  static async invalidateNotesCache(userId: string): Promise<void> {
    await this.invalidateByTags(['notes', `user:${userId}`]);
  }

  static async invalidateSearchCache(userId: string): Promise<void> {
    await this.invalidateByTags(['search', `user:${userId}`]);
  }

  // Fallback cache methods (in-memory)
  private static getFallback<T>(key: string): T | null {
    const cached = this.fallbackCache.get(key);
    if (!cached) return null;
    
    if (Date.now() > cached.expires) {
      this.fallbackCache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  private static setFallback(key: string, value: any, ttl: number, tags?: string[]): boolean {
    const expires = Date.now() + (ttl * 1000);
    this.fallbackCache.set(key, { data: value, expires, tags });
    return true;
  }

  private static delFallback(key: string): boolean {
    return this.fallbackCache.delete(key);
  }

  private static invalidateByTagsFallback(tags: string[]): number {
    let count = 0;
    for (const [key, cached] of this.fallbackCache.entries()) {
      if (cached.tags && cached.tags.some(tag => tags.includes(tag))) {
        this.fallbackCache.delete(key);
        count++;
      }
    }
    return count;
  }

  private static cleanupFallbackCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.fallbackCache.entries()) {
      if (now > cached.expires) {
        this.fallbackCache.delete(key);
      }
    }
  }

  // Cache statistics
  static async getStats(): Promise<any> {
    if (this.isRedisAvailable && this.redis) {
      try {
        const info = await this.redis.info('memory');
        const keyspace = await this.redis.info('keyspace');
        return {
          type: 'redis',
          memory: info,
          keyspace: keyspace,
          connected: true
        };
      } catch (error) {
        return { type: 'redis', connected: false, error: error instanceof Error ? error.message : String(error) };
      }
    } else {
      return {
        type: 'fallback',
        size: this.fallbackCache.size,
        connected: true
      };
    }
  }
}
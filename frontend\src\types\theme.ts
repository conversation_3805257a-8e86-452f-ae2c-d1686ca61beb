export interface ThemeColors {
  primary: string
  link: string
  info: string
  success: string
  warning: string
  danger: string
  background: string
  surface: string
  text: string
  textMuted: string
  border: string
}

export interface ThemePreview {
  primary: string
  background: string
  surface: string
  text: string
  accent: string
}

export interface BulmaswatchTheme {
  name: string
  displayName: string
  description: string
  cssFile: string
  isDark: boolean
  category: 'light' | 'dark' | 'colorful'
  preview: ThemePreview
  colors: ThemeColors
}

export interface ThemeConfig {
  name: string
  displayName: string
  cssFile: string
  preview: ThemePreview
}

export interface ThemeSettings {
  mode: 'light' | 'dark' | 'auto'
  selectedTheme: string
  customThemes: BulmaswatchTheme[]
  transitionDuration: number
  preloadThemes: boolean
}

export interface ThemeIndex {
  themes: Array<{
    name: string
    manifestFile: string
  }>
  defaultLight: string
  defaultDark: string
}

export class ThemeLoadError extends Error {
  constructor(
    public themeName: string,
    public reason: 'network' | 'parse' | 'missing',
    message: string
  ) {
    super(message)
    this.name = 'ThemeLoadError'
  }
}
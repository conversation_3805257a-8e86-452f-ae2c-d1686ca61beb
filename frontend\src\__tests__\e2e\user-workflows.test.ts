import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import { createRouter, createWebHistory } from 'vue-router'

// Mock API calls
const mockApi = {
  login: vi.fn(),
  createNote: vi.fn(),
  updateNote: vi.fn(),
  deleteNote: vi.fn(),
  searchNotes: vi.fn(),
  shareNote: vi.fn()
}

vi.mock('../../services/authService', () => ({
  login: mockApi.login,
  logout: vi.fn(),
  register: vi.fn()
}))

vi.mock('../../services/noteService', () => ({
  createNote: mockApi.createNote,
  updateNote: mockApi.updateNote,
  deleteNote: mockApi.deleteNote,
  getNotes: vi.fn(() => Promise.resolve({ notes: [], total: 0 })),
  searchNotes: mockApi.searchNotes
}))

describe('End-to-End User Workflows', () => {
  let router: any
  let pinia: any

  beforeEach(() => {
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/login', component: { template: '<div>Login</div>' } },
        { path: '/dashboard', component: { template: '<div>Dashboard</div>' } },
        { path: '/notes/:id', component: { template: '<div>Note Detail</div>' } }
      ]
    })
    
    pinia = createTestingPinia({
      createSpy: vi.fn,
      initialState: {
        auth: {
          user: null,
          isAuthenticated: false
        },
        notes: {
          notes: [],
          currentNote: null
        }
      }
    })

    vi.clearAllMocks()
  })

  describe('User Authentication Flow', () => {
    it('should complete login workflow', async () => {
      const LoginWorkflow = {
        template: `
          <div>
            <form @submit.prevent="handleLogin" v-if="!isLoggedIn">
              <input v-model="email" type="email" data-testid="email-input" />
              <input v-model="password" type="password" data-testid="password-input" />
              <button type="submit" data-testid="login-button">Login</button>
            </form>
            <div v-else data-testid="welcome-message">
              Welcome, {{ user.name }}!
            </div>
          </div>
        `,
        data() {
          return {
            email: '',
            password: '',
            isLoggedIn: false,
            user: null
          }
        },
        methods: {
          async handleLogin() {
            try {
              const response = await mockApi.login({
                email: this.email,
                password: this.password
              })
              this.user = response.user
              this.isLoggedIn = true
            } catch (error) {
              console.error('Login failed:', error)
            }
          }
        }
      }

      mockApi.login.mockResolvedValue({
        user: { id: '1', name: 'Test User', email: '<EMAIL>' },
        token: 'mock-token'
      })

      const wrapper = mount(LoginWorkflow, {
        global: {
          plugins: [router, pinia]
        }
      })

      // Fill in login form
      const emailInput = wrapper.find('[data-testid="email-input"]')
      const passwordInput = wrapper.find('[data-testid="password-input"]')
      const loginButton = wrapper.find('[data-testid="login-button"]')

      await emailInput.setValue('<EMAIL>')
      await passwordInput.setValue('password123')
      await loginButton.trigger('submit')

      await wrapper.vm.$nextTick()

      expect(mockApi.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      })

      expect(wrapper.find('[data-testid="welcome-message"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="welcome-message"]').text()).toContain('Welcome, Test User!')
    })
  })

  describe('Note Creation and Editing Workflow', () => {
    it('should complete note creation workflow', async () => {
      const NoteCreationWorkflow = {
        template: `
          <div>
            <button @click="showCreateForm = true" data-testid="create-note-button">
              Create Note
            </button>
            
            <form v-if="showCreateForm" @submit.prevent="createNote">
              <input 
                v-model="newNote.title" 
                placeholder="Note title"
                data-testid="note-title-input"
              />
              <select v-model="newNote.type" data-testid="note-type-select">
                <option value="richtext">Rich Text</option>
                <option value="markdown">Markdown</option>
                <option value="kanban">Kanban</option>
              </select>
              <textarea 
                v-model="newNote.content"
                placeholder="Note content"
                data-testid="note-content-input"
              ></textarea>
              <button type="submit" data-testid="save-note-button">Save Note</button>
              <button type="button" @click="showCreateForm = false">Cancel</button>
            </form>

            <div v-if="createdNote" data-testid="success-message">
              Note "{{ createdNote.title }}" created successfully!
            </div>
          </div>
        `,
        data() {
          return {
            showCreateForm: false,
            newNote: {
              title: '',
              type: 'richtext',
              content: ''
            },
            createdNote: null
          }
        },
        methods: {
          async createNote() {
            try {
              const response = await mockApi.createNote(this.newNote)
              this.createdNote = response
              this.showCreateForm = false
              this.newNote = { title: '', type: 'richtext', content: '' }
            } catch (error) {
              console.error('Failed to create note:', error)
            }
          }
        }
      }

      mockApi.createNote.mockResolvedValue({
        id: 'note-1',
        title: 'My Test Note',
        type: 'richtext',
        content: 'This is test content',
        createdAt: new Date().toISOString()
      })

      const wrapper = mount(NoteCreationWorkflow, {
        global: {
          plugins: [router, pinia]
        }
      })

      // Start note creation
      const createButton = wrapper.find('[data-testid="create-note-button"]')
      await createButton.trigger('click')

      // Fill in note form
      const titleInput = wrapper.find('[data-testid="note-title-input"]')
      const typeSelect = wrapper.find('[data-testid="note-type-select"]')
      const contentInput = wrapper.find('[data-testid="note-content-input"]')
      const saveButton = wrapper.find('[data-testid="save-note-button"]')

      await titleInput.setValue('My Test Note')
      await typeSelect.setValue('richtext')
      await contentInput.setValue('This is test content')
      await saveButton.trigger('submit')

      await wrapper.vm.$nextTick()

      expect(mockApi.createNote).toHaveBeenCalledWith({
        title: 'My Test Note',
        type: 'richtext',
        content: 'This is test content'
      })

      expect(wrapper.find('[data-testid="success-message"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="success-message"]').text()).toContain('My Test Note')
    })

    it('should complete note editing workflow', async () => {
      const NoteEditingWorkflow = {
        template: `
          <div>
            <div v-if="!isEditing" data-testid="note-display">
              <h2>{{ note.title }}</h2>
              <p>{{ note.content }}</p>
              <button @click="startEditing" data-testid="edit-button">Edit</button>
            </div>

            <form v-else @submit.prevent="saveChanges">
              <input 
                v-model="editedNote.title"
                data-testid="edit-title-input"
              />
              <textarea 
                v-model="editedNote.content"
                data-testid="edit-content-input"
              ></textarea>
              <button type="submit" data-testid="save-changes-button">Save Changes</button>
              <button type="button" @click="cancelEditing">Cancel</button>
            </form>

            <div v-if="updateSuccess" data-testid="update-success">
              Note updated successfully!
            </div>
          </div>
        `,
        data() {
          return {
            note: {
              id: 'note-1',
              title: 'Original Title',
              content: 'Original content'
            },
            editedNote: {},
            isEditing: false,
            updateSuccess: false
          }
        },
        methods: {
          startEditing() {
            this.editedNote = { ...this.note }
            this.isEditing = true
          },
          async saveChanges() {
            try {
              const response = await mockApi.updateNote(this.note.id, this.editedNote)
              this.note = response
              this.isEditing = false
              this.updateSuccess = true
              setTimeout(() => { this.updateSuccess = false }, 3000)
            } catch (error) {
              console.error('Failed to update note:', error)
            }
          },
          cancelEditing() {
            this.isEditing = false
            this.editedNote = {}
          }
        }
      }

      mockApi.updateNote.mockResolvedValue({
        id: 'note-1',
        title: 'Updated Title',
        content: 'Updated content'
      })

      const wrapper = mount(NoteEditingWorkflow, {
        global: {
          plugins: [router, pinia]
        }
      })

      // Start editing
      const editButton = wrapper.find('[data-testid="edit-button"]')
      await editButton.trigger('click')

      // Make changes
      const titleInput = wrapper.find('[data-testid="edit-title-input"]')
      const contentInput = wrapper.find('[data-testid="edit-content-input"]')
      const saveButton = wrapper.find('[data-testid="save-changes-button"]')

      await titleInput.setValue('Updated Title')
      await contentInput.setValue('Updated content')
      await saveButton.trigger('submit')

      await wrapper.vm.$nextTick()

      expect(mockApi.updateNote).toHaveBeenCalledWith('note-1', {
        id: 'note-1',
        title: 'Updated Title',
        content: 'Updated content'
      })

      expect(wrapper.find('[data-testid="update-success"]').exists()).toBe(true)
    })
  })

  describe('Search and Filter Workflow', () => {
    it('should complete search workflow', async () => {
      const SearchWorkflow = {
        template: `
          <div>
            <form @submit.prevent="performSearch">
              <input 
                v-model="searchQuery"
                placeholder="Search notes..."
                data-testid="search-input"
              />
              <select v-model="searchFilters.type" data-testid="type-filter">
                <option value="">All Types</option>
                <option value="richtext">Rich Text</option>
                <option value="markdown">Markdown</option>
                <option value="kanban">Kanban</option>
              </select>
              <button type="submit" data-testid="search-button">Search</button>
            </form>

            <div v-if="searchResults.length > 0" data-testid="search-results">
              <div 
                v-for="result in searchResults" 
                :key="result.id"
                data-testid="search-result-item"
              >
                <h3>{{ result.title }}</h3>
                <p>{{ result.excerpt }}</p>
              </div>
            </div>

            <div v-else-if="hasSearched" data-testid="no-results">
              No results found for "{{ searchQuery }}"
            </div>
          </div>
        `,
        data() {
          return {
            searchQuery: '',
            searchFilters: {
              type: ''
            },
            searchResults: [],
            hasSearched: false
          }
        },
        methods: {
          async performSearch() {
            try {
              const response = await mockApi.searchNotes({
                query: this.searchQuery,
                filters: this.searchFilters
              })
              this.searchResults = response.results
              this.hasSearched = true
            } catch (error) {
              console.error('Search failed:', error)
            }
          }
        }
      }

      mockApi.searchNotes.mockResolvedValue({
        results: [
          {
            id: 'note-1',
            title: 'JavaScript Tutorial',
            excerpt: 'Learn JavaScript fundamentals...'
          },
          {
            id: 'note-2',
            title: 'JavaScript Best Practices',
            excerpt: 'Advanced JavaScript techniques...'
          }
        ],
        total: 2
      })

      const wrapper = mount(SearchWorkflow, {
        global: {
          plugins: [router, pinia]
        }
      })

      // Perform search
      const searchInput = wrapper.find('[data-testid="search-input"]')
      const typeFilter = wrapper.find('[data-testid="type-filter"]')
      const searchButton = wrapper.find('[data-testid="search-button"]')

      await searchInput.setValue('JavaScript')
      await typeFilter.setValue('richtext')
      await searchButton.trigger('submit')

      await wrapper.vm.$nextTick()

      expect(mockApi.searchNotes).toHaveBeenCalledWith({
        query: 'JavaScript',
        filters: { type: 'richtext' }
      })

      const searchResults = wrapper.find('[data-testid="search-results"]')
      const resultItems = wrapper.findAll('[data-testid="search-result-item"]')

      expect(searchResults.exists()).toBe(true)
      expect(resultItems).toHaveLength(2)
      expect(resultItems[0].text()).toContain('JavaScript Tutorial')
      expect(resultItems[1].text()).toContain('JavaScript Best Practices')
    })
  })

  describe('Collaboration Workflow', () => {
    it('should complete note sharing workflow', async () => {
      const SharingWorkflow = {
        template: `
          <div>
            <div data-testid="note-info">
              <h2>{{ note.title }}</h2>
              <button @click="showShareModal = true" data-testid="share-button">
                Share Note
              </button>
            </div>

            <div v-if="showShareModal" class="modal" data-testid="share-modal">
              <form @submit.prevent="shareNote">
                <h3>Share Note</h3>
                <label>
                  <input 
                    type="radio" 
                    v-model="shareSettings.visibility" 
                    value="private"
                    data-testid="private-radio"
                  />
                  Private
                </label>
                <label>
                  <input 
                    type="radio" 
                    v-model="shareSettings.visibility" 
                    value="shared"
                    data-testid="shared-radio"
                  />
                  Shared with specific users
                </label>
                <label>
                  <input 
                    type="radio" 
                    v-model="shareSettings.visibility" 
                    value="public"
                    data-testid="public-radio"
                  />
                  Public
                </label>

                <input 
                  v-if="shareSettings.visibility === 'shared'"
                  v-model="shareSettings.emails"
                  placeholder="Enter email addresses"
                  data-testid="share-emails-input"
                />

                <button type="submit" data-testid="confirm-share-button">
                  Share
                </button>
                <button type="button" @click="showShareModal = false">
                  Cancel
                </button>
              </form>
            </div>

            <div v-if="shareSuccess" data-testid="share-success">
              Note shared successfully!
              <div v-if="shareUrl" data-testid="share-url">
                Share URL: {{ shareUrl }}
              </div>
            </div>
          </div>
        `,
        data() {
          return {
            note: {
              id: 'note-1',
              title: 'Shared Note'
            },
            showShareModal: false,
            shareSettings: {
              visibility: 'private',
              emails: ''
            },
            shareSuccess: false,
            shareUrl: ''
          }
        },
        methods: {
          async shareNote() {
            try {
              const response = await mockApi.shareNote(this.note.id, this.shareSettings)
              this.shareUrl = response.shareUrl
              this.shareSuccess = true
              this.showShareModal = false
            } catch (error) {
              console.error('Sharing failed:', error)
            }
          }
        }
      }

      mockApi.shareNote.mockResolvedValue({
        shareUrl: 'https://app.example.com/shared/note-1-abc123',
        shareId: 'share-123'
      })

      const wrapper = mount(SharingWorkflow, {
        global: {
          plugins: [router, pinia]
        }
      })

      // Open share modal
      const shareButton = wrapper.find('[data-testid="share-button"]')
      await shareButton.trigger('click')

      // Configure sharing
      const sharedRadio = wrapper.find('[data-testid="shared-radio"]')
      const emailsInput = wrapper.find('[data-testid="share-emails-input"]')
      const confirmButton = wrapper.find('[data-testid="confirm-share-button"]')

      await sharedRadio.trigger('change')
      await emailsInput.setValue('<EMAIL>')
      await confirmButton.trigger('submit')

      await wrapper.vm.$nextTick()

      expect(mockApi.shareNote).toHaveBeenCalledWith('note-1', {
        visibility: 'shared',
        emails: '<EMAIL>'
      })

      expect(wrapper.find('[data-testid="share-success"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="share-url"]').text()).toContain('https://app.example.com/shared/note-1-abc123')
    })
  })

  describe('Error Handling Workflows', () => {
    it('should handle network errors gracefully', async () => {
      const ErrorHandlingWorkflow = {
        template: `
          <div>
            <button @click="performAction" data-testid="action-button">
              Perform Action
            </button>
            
            <div v-if="loading" data-testid="loading-indicator">
              Loading...
            </div>
            
            <div v-if="error" data-testid="error-message" class="error">
              {{ error }}
              <button @click="retry" data-testid="retry-button">Retry</button>
            </div>
            
            <div v-if="success" data-testid="success-message">
              Action completed successfully!
            </div>
          </div>
        `,
        data() {
          return {
            loading: false,
            error: null,
            success: false
          }
        },
        methods: {
          async performAction() {
            this.loading = true
            this.error = null
            this.success = false
            
            try {
              await mockApi.createNote({ title: 'Test', content: 'Test' })
              this.success = true
            } catch (error) {
              this.error = 'Failed to perform action. Please try again.'
            } finally {
              this.loading = false
            }
          },
          retry() {
            this.performAction()
          }
        }
      }

      // First call fails
      mockApi.createNote.mockRejectedValueOnce(new Error('Network error'))
      // Second call succeeds
      mockApi.createNote.mockResolvedValueOnce({ id: 'note-1', title: 'Test' })

      const wrapper = mount(ErrorHandlingWorkflow, {
        global: {
          plugins: [router, pinia]
        }
      })

      // Trigger action that will fail
      const actionButton = wrapper.find('[data-testid="action-button"]')
      await actionButton.trigger('click')

      await wrapper.vm.$nextTick()

      // Should show error
      expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="error-message"]').text()).toContain('Failed to perform action')

      // Retry should succeed
      const retryButton = wrapper.find('[data-testid="retry-button"]')
      await retryButton.trigger('click')

      await wrapper.vm.$nextTick()

      expect(wrapper.find('[data-testid="success-message"]').exists()).toBe(true)
    })
  })
});
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import NoteList from '../NoteList.vue'

// Mock the stores
const mockNotesStore = {
  notes: [
    {
      id: '1',
      title: 'Test Note',
      content: 'Test content',
      noteType: 'markdown',
      tags: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId: 'user1',
      isArchived: false,
      metadata: {}
    }
  ],
  isLoading: false,
  setSearchQuery: vi.fn(),
  updateNote: vi.fn(),
  createNote: vi.fn(),
  deleteNote: vi.fn()
}

const mockShareStore = {
  hasActiveShares: vi.fn(() => false)
}

vi.mock('../../../stores/notes', () => ({
  useNotesStore: () => mockNotesStore
}))

vi.mock('../../../stores/noteShares', () => ({
  useNoteSharesStore: () => mockShareStore
}))

describe('NoteList - Share functionality', () => {
  it('emits share-note event when share button is clicked', async () => {
    const wrapper = mount(NoteList, {
      props: {
        section: 'all-notes',
        activeTags: []
      },
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    // Wait for component to render
    await wrapper.vm.$nextTick()

    // Find the note card and its dropdown
    const noteCard = wrapper.find('.note-card')
    expect(noteCard.exists()).toBe(true)

    // Find and click the share button in the dropdown
    const shareButtons = wrapper.findAll('a.dropdown-item')
    const shareBtn = shareButtons.find(btn => btn.text().includes('Share'))
    
    if (shareBtn) {
      await shareBtn.trigger('click')
      
      // Check if share-note event was emitted
      const emitted = wrapper.emitted('share-note')
      expect(emitted).toBeTruthy()
      if (emitted) {
        expect(emitted[0][0]).toEqual(mockNotesStore.notes[0])
      }
    }
  })

  it('shows empty state when no notes exist', () => {
    // Mock empty notes
    mockNotesStore.notes = []
    
    const wrapper = mount(NoteList, {
      props: {
        section: 'all-notes',
        activeTags: []
      },
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })

    expect(wrapper.text()).toContain('No notes found')
    expect(wrapper.find('.empty-state').exists()).toBe(true)
  })
})
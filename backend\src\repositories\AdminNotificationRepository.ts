import { Database } from 'sqlite3'
import { v4 as uuidv4 } from 'uuid'
import { AdminNotification, CreateNotificationData, NotificationFilters } from '../models/AdminNotification'

export class AdminNotificationRepository {
  constructor(private db: Database) {}

  async create(data: CreateNotificationData): Promise<AdminNotification> {
    const id = uuidv4()
    const now = new Date().toISOString()
    
    const notification: AdminNotification = {
      id,
      type: data.type,
      category: data.category,
      title: data.title,
      message: data.message,
      read: false,
      actionUrl: data.actionUrl,
      metadata: data.metadata,
      createdAt: now
    }

    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        INSERT INTO admin_notifications (
          id, type, category, title, message, read, action_url, metadata, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      stmt.run([
        notification.id,
        notification.type,
        notification.category,
        notification.title,
        notification.message,
        notification.read ? 1 : 0,
        notification.actionUrl || null,
        notification.metadata ? JSON.stringify(notification.metadata) : null,
        notification.createdAt
      ], function(err) {
        if (err) {
          reject(err)
        } else {
          resolve(notification)
        }
      })

      stmt.finalize()
    })
  }

  async findAll(filters: NotificationFilters = {}): Promise<{ notifications: AdminNotification[], total: number }> {
    const { type, category, read, page = 1, limit = 20 } = filters
    const offset = (page - 1) * limit

    let whereClause = ''
    const params: any[] = []

    const conditions: string[] = []
    
    if (type) {
      conditions.push('type = ?')
      params.push(type)
    }
    
    if (category) {
      conditions.push('category = ?')
      params.push(category)
    }
    
    if (read !== undefined) {
      conditions.push('read = ?')
      params.push(read ? 1 : 0)
    }

    if (conditions.length > 0) {
      whereClause = 'WHERE ' + conditions.join(' AND ')
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM admin_notifications ${whereClause}`
    const total = await new Promise<number>((resolve, reject) => {
      this.db.get(countQuery, params, (err, row: any) => {
        if (err) {
          reject(err)
        } else {
          resolve(row.total)
        }
      })
    })

    // Get notifications
    const query = `
      SELECT * FROM admin_notifications 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `
    
    const notifications = await new Promise<AdminNotification[]>((resolve, reject) => {
      this.db.all(query, [...params, limit, offset], (err, rows: any[]) => {
        if (err) {
          reject(err)
        } else {
          const notifications = rows.map(row => ({
            id: row.id,
            type: row.type,
            category: row.category,
            title: row.title,
            message: row.message,
            read: row.read === 1,
            actionUrl: row.action_url,
            metadata: row.metadata ? JSON.parse(row.metadata) : undefined,
            createdAt: row.created_at,
            updatedAt: row.updated_at
          }))
          resolve(notifications)
        }
      })
    })

    return { notifications, total }
  }

  async findById(id: string): Promise<AdminNotification | null> {
    return new Promise((resolve, reject) => {
      this.db.get(
        'SELECT * FROM admin_notifications WHERE id = ?',
        [id],
        (err, row: any) => {
          if (err) {
            reject(err)
          } else if (!row) {
            resolve(null)
          } else {
            resolve({
              id: row.id,
              type: row.type,
              category: row.category,
              title: row.title,
              message: row.message,
              read: row.read === 1,
              actionUrl: row.action_url,
              metadata: row.metadata ? JSON.parse(row.metadata) : undefined,
              createdAt: row.created_at,
              updatedAt: row.updated_at
            })
          }
        }
      )
    })
  }

  async markAsRead(id: string): Promise<boolean> {
    const now = new Date().toISOString()
    
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        UPDATE admin_notifications 
        SET read = 1, updated_at = ? 
        WHERE id = ?
      `)

      stmt.run([now, id], function(err) {
        if (err) {
          reject(err)
        } else {
          resolve(this.changes > 0)
        }
      })

      stmt.finalize()
    })
  }

  async markAllAsRead(): Promise<number> {
    const now = new Date().toISOString()
    
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        UPDATE admin_notifications 
        SET read = 1, updated_at = ? 
        WHERE read = 0
      `)

      stmt.run([now], function(err) {
        if (err) {
          reject(err)
        } else {
          resolve(this.changes)
        }
      })

      stmt.finalize()
    })
  }

  async delete(id: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare('DELETE FROM admin_notifications WHERE id = ?')

      stmt.run([id], function(err) {
        if (err) {
          reject(err)
        } else {
          resolve(this.changes > 0)
        }
      })

      stmt.finalize()
    })
  }

  async getUnreadCount(): Promise<number> {
    return new Promise((resolve, reject) => {
      this.db.get(
        'SELECT COUNT(*) as count FROM admin_notifications WHERE read = 0',
        [],
        (err, row: any) => {
          if (err) {
            reject(err)
          } else {
            resolve(row.count)
          }
        }
      )
    })
  }
}
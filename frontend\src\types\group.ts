export interface GroupSettings {
  allowMemberInvites: boolean;
  defaultNotePermissions: 'view' | 'edit';
  requireApprovalForJoin: boolean;
  maxMembers: number;
}

export interface Group {
  id: string;
  ownerId: string;
  name: string;
  description?: string;
  settings: GroupSettings;
  createdAt: string;
}

export interface GroupMember {
  groupId: string;
  userId: string;
  role: 'admin' | 'editor' | 'viewer';
  joinedAt: string;
  displayName: string;
  email: string;
  avatarUrl?: string;
}

export interface GroupWithMembers extends Group {
  members: GroupMember[];
  memberCount: number;
}

export interface CreateGroupData {
  name: string;
  description?: string;
  settings?: Partial<GroupSettings>;
}

export interface UpdateGroupData {
  name?: string;
  description?: string;
  settings?: Partial<GroupSettings>;
}

export interface GroupInvitation {
  id: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  expiresAt: string;
  createdAt: string;
}

export interface InviteUserData {
  email: string;
  role: 'admin' | 'editor' | 'viewer';
}

export interface UpdateMemberRoleData {
  role: 'admin' | 'editor' | 'viewer';
}

export type UserRole = 'admin' | 'editor' | 'viewer';

export interface GroupPermissions {
  canInvite: boolean;
  canRemoveMembers: boolean;
  canEditSettings: boolean;
  canDeleteGroup: boolean;
  canEditNotes: boolean;
  canViewNotes: boolean;
}

export const getPermissions = (role: UserRole): GroupPermissions => {
  switch (role) {
    case 'admin':
      return {
        canInvite: true,
        canRemoveMembers: true,
        canEditSettings: true,
        canDeleteGroup: true,
        canEditNotes: true,
        canViewNotes: true
      };
    case 'editor':
      return {
        canInvite: false,
        canRemoveMembers: false,
        canEditSettings: false,
        canDeleteGroup: false,
        canEditNotes: true,
        canViewNotes: true
      };
    case 'viewer':
      return {
        canInvite: false,
        canRemoveMembers: false,
        canEditSettings: false,
        canDeleteGroup: false,
        canEditNotes: false,
        canViewNotes: true
      };
    default:
      return {
        canInvite: false,
        canRemoveMembers: false,
        canEditSettings: false,
        canDeleteGroup: false,
        canEditNotes: false,
        canViewNotes: false
      };
  }
};

export const getRoleDisplayName = (role: UserRole): string => {
  switch (role) {
    case 'admin':
      return 'Administrator';
    case 'editor':
      return 'Editor';
    case 'viewer':
      return 'Viewer';
    default:
      return 'Unknown';
  }
};

export const getRoleColor = (role: UserRole): string => {
  switch (role) {
    case 'admin':
      return 'is-danger';
    case 'editor':
      return 'is-warning';
    case 'viewer':
      return 'is-info';
    default:
      return 'is-light';
  }
};
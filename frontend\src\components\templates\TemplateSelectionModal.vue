<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="closeModal"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">
          <i class="fas fa-layer-group mr-2"></i>
          Choose Template
        </p>
        <button class="delete" @click="closeModal" aria-label="close"></button>
      </header>

      <section class="modal-card-body">
        <!-- Search and Filters -->
        <div class="field is-grouped">
          <div class="control is-expanded">
            <input
              v-model="searchQuery"
              @input="onSearchChange"
              class="input"
              type="text"
              placeholder="Search templates..."
            />
          </div>
          <div class="control">
            <div class="select">
              <select v-model="selectedNoteType" @change="applyFilters">
                <option value="">All Types</option>
                <option value="richtext">Rich Text</option>
                <option value="markdown">Markdown</option>
                <option value="kanban">Kanban</option>
              </select>
            </div>
          </div>
          <div class="control">
            <div class="select">
              <select v-model="selectedCategory" @change="applyFilters">
                <option value="">All Categories</option>
                <option v-for="category in categories" :key="category" :value="category">
                  {{ category }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="has-text-centered py-6">
          <i class="fas fa-spinner fa-spin fa-2x"></i>
          <p class="mt-3">Loading templates...</p>
        </div>

        <!-- Empty State -->
        <div v-else-if="templates.length === 0" class="has-text-centered py-6">
          <i class="fas fa-layer-group fa-3x has-text-grey-light"></i>
          <p class="mt-3 has-text-grey">No templates found</p>
          <p class="has-text-grey-light">Try adjusting your search or filters</p>
        </div>

        <!-- Templates Grid -->
        <div v-else class="templates-grid">
          <div
            v-for="template in templates"
            :key="template.id"
            class="template-card"
            @click="selectTemplate(template)"
          >
            <div class="card">
              <div class="card-content">
                <div class="media">
                  <div class="media-left">
                    <i 
                      :class="getNoteTypeIcon(template.noteType)" 
                      class="fa-2x has-text-primary"
                    ></i>
                  </div>
                  <div class="media-content">
                    <p class="title is-6">{{ template.name }}</p>
                    <p class="subtitle is-7 has-text-grey">
                      {{ getNoteTypeDisplayName(template.noteType) }}
                    </p>
                  </div>
                  <div class="media-right">
                    <span v-if="template.isPublic" class="tag is-info is-small">
                      Public
                    </span>
                  </div>
                </div>

                <div class="content">
                  <p v-if="template.description" class="is-size-7">
                    {{ template.description }}
                  </p>
                  
                  <!-- Metadata -->
                  <div class="template-meta mt-3">
                    <span 
                      v-if="template.metadata.difficulty" 
                      class="tag is-small"
                      :class="getDifficultyColor(template.metadata.difficulty)"
                    >
                      {{ template.metadata.difficulty }}
                    </span>
                    <span 
                      v-if="template.metadata.estimatedTime" 
                      class="tag is-small is-light"
                    >
                      <i class="fas fa-clock mr-1"></i>
                      {{ formatEstimatedTime(template.metadata.estimatedTime) }}
                    </span>
                    <span 
                      v-if="template.metadata.usageCount" 
                      class="tag is-small is-light"
                    >
                      <i class="fas fa-users mr-1"></i>
                      {{ template.metadata.usageCount }}
                    </span>
                  </div>

                  <!-- Tags -->
                  <div v-if="template.tags.length > 0" class="template-tags mt-2">
                    <span
                      v-for="tag in template.tags.slice(0, 3)"
                      :key="tag"
                      class="tag is-small is-light"
                    >
                      {{ tag }}
                    </span>
                    <span v-if="template.tags.length > 3" class="tag is-small is-light">
                      +{{ template.tags.length - 3 }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Load More -->
        <div v-if="hasMore" class="has-text-centered mt-4">
          <button 
            class="button is-outlined"
            @click="loadMore"
            :class="{ 'is-loading': isLoadingMore }"
          >
            Load More
          </button>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="notification is-danger is-light">
          <p><strong>Error:</strong> {{ error }}</p>
        </div>
      </section>

      <footer class="modal-card-foot">
        <button class="button" @click="closeModal">Cancel</button>
        <button 
          class="button is-primary" 
          @click="createBlankNote"
        >
          <i class="fas fa-file mr-2"></i>
          Create Blank Note
        </button>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { TemplateService, type Template } from '../../services/templateService';

interface Props {
  isOpen: boolean;
}

interface Emits {
  (e: 'close'): void;
  (e: 'template-selected', template: Template): void;
  (e: 'create-blank'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Reactive state
const templates = ref<Template[]>([]);
const categories = ref<string[]>([]);
const isLoading = ref(false);
const isLoadingMore = ref(false);
const error = ref('');
const searchQuery = ref('');
const selectedNoteType = ref('');
const selectedCategory = ref('');
const currentPage = ref(1);
const totalPages = ref(1);
const searchTimeout = ref<number | null>(null);

// Computed properties
const hasMore = computed(() => currentPage.value < totalPages.value);

// Methods
const closeModal = () => {
  emit('close');
};

const loadTemplates = async (append = false) => {
  try {
    if (!append) {
      isLoading.value = true;
      currentPage.value = 1;
    } else {
      isLoadingMore.value = true;
      currentPage.value++;
    }

    error.value = '';

    const filters = {
      noteType: selectedNoteType.value as any,
      category: selectedCategory.value,
      search: searchQuery.value,
      isPublic: true // For now, only show public templates in selection
    };

    const pagination = {
      page: currentPage.value,
      limit: 12,
      sortBy: 'usage_count' as const,
      sortOrder: 'desc' as const
    };

    const response = await TemplateService.getTemplates(filters, pagination);
    
    if (append) {
      templates.value.push(...response.templates);
    } else {
      templates.value = response.templates;
    }
    
    totalPages.value = response.pagination.totalPages;
  } catch (err) {
    console.error('Failed to load templates:', err);
    error.value = err instanceof Error ? err.message : 'Failed to load templates';
  } finally {
    isLoading.value = false;
    isLoadingMore.value = false;
  }
};

const loadCategories = async () => {
  try {
    categories.value = await TemplateService.getCategories();
  } catch (err) {
    console.error('Failed to load categories:', err);
  }
};

const onSearchChange = () => {
  // Debounce search
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
  
  searchTimeout.value = setTimeout(() => {
    loadTemplates();
  }, 300);
};

const applyFilters = () => {
  loadTemplates();
};

const loadMore = () => {
  loadTemplates(true);
};

const selectTemplate = (template: Template) => {
  emit('template-selected', template);
};

const createBlankNote = () => {
  emit('create-blank');
};

const getNoteTypeDisplayName = (noteType: string) => {
  return TemplateService.getNoteTypeDisplayName(noteType);
};

const getNoteTypeIcon = (noteType: string) => {
  return TemplateService.getNoteTypeIcon(noteType);
};

const getDifficultyColor = (difficulty?: string) => {
  return TemplateService.getDifficultyColor(difficulty);
};

const formatEstimatedTime = (minutes?: number) => {
  return TemplateService.formatEstimatedTime(minutes);
};

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    loadTemplates();
    loadCategories();
  } else {
    // Reset state when modal closes
    templates.value = [];
    searchQuery.value = '';
    selectedNoteType.value = '';
    selectedCategory.value = '';
    error.value = '';
  }
});

// Lifecycle
onMounted(() => {
  if (props.isOpen) {
    loadTemplates();
    loadCategories();
  }
});
</script>

<style scoped>
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.template-card {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.template-card:hover {
  transform: translateY(-2px);
}

.template-card .card {
  height: 100%;
  border: 2px solid transparent;
  transition: border-color 0.2s ease;
}

.template-card:hover .card {
  border-color: #3273dc;
}

.template-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.media-left i {
  width: 40px;
  text-align: center;
}

@media (max-width: 768px) {
  .templates-grid {
    grid-template-columns: 1fr;
  }
  
  .field.is-grouped {
    flex-direction: column;
  }
  
  .field.is-grouped .control {
    margin-bottom: 0.5rem;
  }
}
</style>
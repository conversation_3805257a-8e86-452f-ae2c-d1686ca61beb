// Performance API Service
// Handles communication with backend performance monitoring endpoints

import { httpClient } from '../utils/http'

export interface PerformanceOverview {
  database: {
    totalQueries: number
    cachedQueries: number
    slowQueries: number
    cacheHitRate: number
    averageExecutionTime: number
    maxExecutionTime: number
    minExecutionTime: number
  }
  cache: {
    type: string
    connected: boolean
    memory?: string
    keyspace?: string
    size?: number
  }
  timestamp: number
}

export interface PerformanceReport {
  totalQueries: number
  averageExecutionTime: number
  slowQueries: SlowQuery[]
  mostFrequentQueries: FrequentQuery[]
  cacheHitRate: number
  recommendations: string[]
}

export interface SlowQuery {
  query: string
  params: any[]
  executionTime: number
  rowsAffected: number
  timestamp: number
  cached: boolean
  planAnalysis?: any
}

export interface FrequentQuery {
  query: string
  count: number
  avgTime: number
}

export interface SystemStats {
  memory: {
    rss: number
    heapTotal: number
    heapUsed: number
    external: number
    arrayBuffers: number
  }
  cpu: {
    user: number
    system: number
  }
  uptime: number
  nodeVersion: string
  platform: string
  arch: string
  timestamp: number
}

export interface DatabaseStats {
  totalNotes: any[]
  totalUsers: any[]
  totalTags: any[]
  totalGroups: any[]
  indexes: any[]
  notesTableInfo: any[]
  notesIndexes: any[]
}

export interface QueryAnalysis {
  query: string
  params: any[]
  analysis: any[]
  timestamp: number
}

export interface PerformanceConfig {
  queryAnalysisEnabled: boolean
  cacheEnabled: boolean
  slowQueryThreshold: number
  metricsRetention: number
  environment: string
}

class PerformanceApiService {
  private baseUrl = '/performance'

  // Get performance overview
  async getOverview(): Promise<PerformanceOverview> {
    const response = await httpClient.get<PerformanceOverview>(`${this.baseUrl}/overview`)
    if (response.error) throw new Error(response.error)
    return response.data!
  }

  // Additional endpoints used by admin monitor UI
  async getMetrics(): Promise<any> {
    const response = await httpClient.get(`${this.baseUrl}/metrics`)
    if ((response as any).error) throw new Error((response as any).error)
    return (response as any).data
  }

  async getHistoricalData(range: string): Promise<any> {
    const response = await httpClient.get(`${this.baseUrl}/historical?range=${encodeURIComponent(range)}`)
    if ((response as any).error) throw new Error((response as any).error)
    return (response as any).data
  }

  async getBundleStats(): Promise<any> {
    const response = await httpClient.get(`${this.baseUrl}/bundle-stats`)
    if ((response as any).error) throw new Error((response as any).error)
    return (response as any).data
  }

  async getRecommendations(): Promise<any[]> {
    // Backed by generateRecommendations if API not available
    const response = await httpClient.get<{ recommendations?: string[] }>(`${this.baseUrl}/recommendations`)
    if (response.error) {
      // Fallback: synthesize from report if endpoint missing
      const report = await this.getReport()
      return this.generateRecommendations(report).map((text, idx) => ({ id: String(idx), title: text, description: text, priority: 'medium', impact: 'medium', effort: 'medium' }))
    }
    const recs = response.data?.recommendations || []
    return recs.map((text, idx) => ({ id: String(idx), title: text, description: text, priority: 'medium', impact: 'medium', effort: 'medium' }))
  }

  // Get detailed performance report
  async getReport(): Promise<PerformanceReport> {
    const response = await httpClient.get<PerformanceReport>(`${this.baseUrl}/report`)
    if (response.error) throw new Error(response.error)
    return response.data!
  }

  // Get recent slow queries
  async getSlowQueries(limit: number = 10): Promise<SlowQuery[]> {
    const response = await httpClient.get<SlowQuery[]>(`${this.baseUrl}/slow-queries?limit=${limit}`)
    if (response.error) throw new Error(response.error)
    return response.data!
  }

  // Get cache statistics
  async getCacheStats(): Promise<any> {
    const response = await httpClient.get(`${this.baseUrl}/cache`)
    if (response.error) throw new Error(response.error)
    return response.data
  }

  // Clear cache
  async clearCache(): Promise<{ message: string }> {
    const response = await httpClient.delete<{ message: string }>(`${this.baseUrl}/cache`)
    if (response.error) throw new Error(response.error)
    return response.data!
  }

  // Clear performance metrics
  async clearMetrics(): Promise<{ message: string }> {
    const response = await httpClient.delete<{ message: string }>(`${this.baseUrl}/metrics`)
    if (response.error) throw new Error(response.error)
    return response.data!
  }

  // Export performance data
  async exportData(): Promise<Blob> {
    // For blob responses, we need to use fetch directly
    const authStore = (await import('../stores/auth')).useAuthStore()
    const response = await fetch(`${this.baseUrl}/export`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    if (!response.ok) throw new Error('Failed to export data')
    return response.blob()
  }

  // Get system resource usage
  async getSystemStats(): Promise<SystemStats> {
    const response = await httpClient.get<SystemStats>(`${this.baseUrl}/system`)
    if (response.error) throw new Error(response.error)
    return response.data!
  }

  // Get database statistics
  async getDatabaseStats(): Promise<DatabaseStats> {
    const response = await httpClient.get<DatabaseStats>(`${this.baseUrl}/database`)
    if (response.error) throw new Error(response.error)
    return response.data!
  }

  // Analyze specific query
  async analyzeQuery(query: string, params: any[] = []): Promise<QueryAnalysis> {
    const response = await httpClient.post<QueryAnalysis>(`${this.baseUrl}/analyze-query`, {
      query,
      params
    })
    if (response.error) throw new Error(response.error)
    return response.data!
  }

  // Get performance configuration
  async getConfig(): Promise<PerformanceConfig> {
    const response = await httpClient.get<PerformanceConfig>(`${this.baseUrl}/config`)
    if (response.error) throw new Error(response.error)
    return response.data!
  }

  // Update performance configuration
  async updateConfig(config: Partial<PerformanceConfig>): Promise<{ message: string }> {
    const response = await httpClient.put<{ message: string }>(`${this.baseUrl}/config`, config)
    if (response.error) throw new Error(response.error)
    return response.data!
  }

  // Helper methods for data formatting
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }

  formatDuration(ms: number): string {
    if (ms < 1000) return `${ms.toFixed(2)}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`
    return `${(ms / 60000).toFixed(2)}m`
  }

  formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  getPerformanceStatus(value: number, thresholds: { good: number; warning: number }): 'good' | 'warning' | 'danger' {
    if (value <= thresholds.good) return 'good'
    if (value <= thresholds.warning) return 'warning'
    return 'danger'
  }

  getCacheHitRateStatus(rate: number): 'good' | 'warning' | 'danger' {
    if (rate >= 80) return 'good'
    if (rate >= 60) return 'warning'
    return 'danger'
  }

  getMemoryUsageStatus(used: number, total: number): 'good' | 'warning' | 'danger' {
    const percentage = (used / total) * 100
    if (percentage < 70) return 'good'
    if (percentage < 85) return 'warning'
    return 'danger'
  }

  // Real-time monitoring helpers
  async startMonitoring(interval: number = 30000): Promise<() => void> {
    let isMonitoring = true
    
    const monitor = async () => {
      if (!isMonitoring) return
      
      try {
        const overview = await this.getOverview()
        
        // Emit custom event with performance data
        window.dispatchEvent(new CustomEvent('performance-update', {
          detail: overview
        }))
      } catch (error) {
        console.error('Performance monitoring error:', error)
      }
      
      if (isMonitoring) {
        setTimeout(monitor, interval)
      }
    }
    
    // Start monitoring
    monitor()
    
    // Return stop function
    return () => {
      isMonitoring = false
    }
  }

  // Performance alerts
  checkPerformanceAlerts(overview: PerformanceOverview): string[] {
    const alerts: string[] = []
    
    // Check cache hit rate
    if (overview.database.cacheHitRate < 50) {
      alerts.push(`Low cache hit rate: ${overview.database.cacheHitRate.toFixed(1)}%`)
    }
    
    // Check slow queries
    if (overview.database.slowQueries > overview.database.totalQueries * 0.1) {
      alerts.push(`High number of slow queries: ${overview.database.slowQueries}`)
    }
    
    // Check average execution time
    if (overview.database.averageExecutionTime > 100) {
      alerts.push(`High average query time: ${overview.database.averageExecutionTime.toFixed(2)}ms`)
    }
    
    return alerts
  }

  // Generate performance recommendations
  generateRecommendations(report: PerformanceReport): string[] {
    const recommendations: string[] = [...report.recommendations]
    
    // Add dynamic recommendations based on current data
    if (report.cacheHitRate < 70) {
      recommendations.push('Consider implementing more aggressive caching for frequently accessed data')
    }
    
    if (report.slowQueries.length > 10) {
      recommendations.push('Review and optimize the slowest database queries')
    }
    
    const avgSlowQueryTime = report.slowQueries.reduce((sum, q) => sum + q.executionTime, 0) / report.slowQueries.length
    if (avgSlowQueryTime > 500) {
      recommendations.push('Consider adding database indexes for queries taking over 500ms')
    }
    
    return [...new Set(recommendations)] // Remove duplicates
  }
}

export const performanceApiService = new PerformanceApiService()
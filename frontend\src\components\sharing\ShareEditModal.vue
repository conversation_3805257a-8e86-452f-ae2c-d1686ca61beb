<template>
  <div class="modal is-active">
    <div class="modal-background" @click="closeModal"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">
          <span class="icon">
            <i class="fas fa-edit"></i>
          </span>
          Edit Share Settings
        </p>
        <button class="delete" @click="closeModal" aria-label="close"></button>
      </header>

      <section class="modal-card-body">
        <div v-if="error" class="notification is-danger">
          <button class="delete" @click="clearError"></button>
          {{ error }}
        </div>

        <form @submit.prevent="handleUpdateShare">
          <!-- Access Level -->
          <div class="field">
            <label class="label">Access Level</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="editData.accessLevel" required>
                  <option value="private">Private - Only you</option>
                  <option value="shared">Shared - Specific people</option>
                  <option value="unlisted">Unlisted - Anyone with link</option>
                  <option value="public">Public - Anyone can find</option>
                </select>
              </div>
            </div>
            <p class="help">{{ editData.accessLevel ? getAccessLevelDescription(editData.accessLevel) : '' }}</p>
          </div>

          <!-- Permissions -->
          <div class="field">
            <label class="label">Permissions</label>
            <div class="control">
              <label class="checkbox">
                <input 
                  type="checkbox" 
                  value="view" 
                  v-model="editData.permissions"
                  :disabled="true"
                >
                View (always included)
              </label>
            </div>
            <div class="control">
              <label class="checkbox">
                <input 
                  type="checkbox" 
                  value="comment" 
                  v-model="editData.permissions"
                >
                Comment
              </label>
            </div>
            <div class="control">
              <label class="checkbox">
                <input 
                  type="checkbox" 
                  value="edit" 
                  v-model="editData.permissions"
                >
                Edit
              </label>
            </div>
          </div>

          <!-- Expiration Date -->
          <div class="field">
            <label class="label">
              <input type="checkbox" v-model="hasExpiration" class="mr-2">
              Set Expiration Date
            </label>
            <div v-if="hasExpiration" class="control">
              <input 
                type="datetime-local" 
                class="input" 
                v-model="expirationDate"
                :min="minDate"
              >
            </div>
            <div v-else class="control">
              <p class="help">This share will never expire</p>
            </div>
          </div>

          <!-- Password Protection -->
          <div class="field">
            <label class="label">
              <input type="checkbox" v-model="hasPassword" class="mr-2">
              Password Protection
            </label>
            <div v-if="hasPassword" class="control">
              <input 
                type="password" 
                class="input" 
                v-model="sharePassword"
                placeholder="Enter new password (leave empty to keep current)"
                minlength="6"
              >
              <p class="help">Leave empty to keep the current password</p>
            </div>
            <div v-else class="control">
              <p class="help">No password protection</p>
            </div>
          </div>

          <!-- IP Restrictions -->
          <div class="field">
            <label class="label">
              <input type="checkbox" v-model="hasIpRestrictions" class="mr-2">
              IP Address Restrictions
            </label>
            <div v-if="hasIpRestrictions">
              <div v-for="(ip, index) in allowedIps" :key="index" class="field has-addons mb-2">
                <div class="control is-expanded">
                  <input 
                    type="text" 
                    class="input" 
                    v-model="allowedIps[index]"
                    placeholder="*********** or ***********/24"
                  >
                </div>
                <div class="control">
                  <button 
                    type="button" 
                    class="button is-danger" 
                    @click="removeIpAddress(index)"
                  >
                    <span class="icon">
                      <i class="fas fa-times"></i>
                    </span>
                  </button>
                </div>
              </div>
              <button 
                type="button" 
                class="button is-small is-info" 
                @click="addIpAddress"
              >
                <span class="icon">
                  <i class="fas fa-plus"></i>
                </span>
                <span>Add IP Address</span>
              </button>
            </div>
            <div v-else class="control">
              <p class="help">No IP restrictions</p>
            </div>
          </div>

          <!-- Watermark -->
          <div class="field">
            <div class="control">
              <label class="checkbox">
                <input type="checkbox" v-model="editData.watermark">
                Add watermark to shared content
              </label>
            </div>
            <p class="help">Adds a watermark indicating the content is shared</p>
          </div>

          <!-- Current Share Info -->
          <div class="box">
            <h6 class="title is-6">Current Share Statistics</h6>
            <div class="columns">
              <div class="column">
                <p><strong>Created:</strong> {{ formatDate(share.createdAt) }}</p>
                <p><strong>Access Count:</strong> {{ share.accessCount }}</p>
              </div>
              <div class="column">
                <p><strong>Last Updated:</strong> {{ formatDate(share.updatedAt) }}</p>
                <p v-if="share.lastAccessedAt">
                  <strong>Last Accessed:</strong> {{ formatDate(share.lastAccessedAt) }}
                </p>
                <p v-else>
                  <strong>Last Accessed:</strong> Never
                </p>
              </div>
            </div>
          </div>
        </form>
      </section>

      <footer class="modal-card-foot">
        <button 
          class="button is-primary" 
          @click="handleUpdateShare"
          :class="{ 'is-loading': isLoading }"
          :disabled="isLoading || !isFormValid"
        >
          <span class="icon">
            <i class="fas fa-save"></i>
          </span>
          <span>Save Changes</span>
        </button>
        <button class="button" @click="closeModal">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useNoteSharesStore } from '../../stores/noteShares';
import { 
  getAccessLevelDescription, 
  type NoteShare, 
  type UpdateShareData,
  type AccessLevel,
  type Permission
} from '../../types/noteShare';

interface Props {
  share: NoteShare;
}

interface Emits {
  (e: 'close'): void;
  (e: 'updated', share: NoteShare): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const shareStore = useNoteSharesStore();

// Reactive state
const editData = ref<UpdateShareData>({
  accessLevel: props.share.accessLevel,
  permissions: [...props.share.permissions],
  watermark: props.share.watermark
});

const hasExpiration = ref(!!props.share.expiresAt);
const hasPassword = ref(!!props.share.passwordHash);
const hasIpRestrictions = ref(!!(props.share.allowedIps && props.share.allowedIps.length > 0));
const expirationDate = ref('');
const sharePassword = ref('');
const allowedIps = ref<string[]>([]);

// Computed properties
const isLoading = computed(() => shareStore.isLoading);
const error = computed(() => shareStore.error);

const minDate = computed(() => {
  const now = new Date();
  now.setMinutes(now.getMinutes() + 5); // Minimum 5 minutes from now
  return now.toISOString().slice(0, 16);
});

const isFormValid = computed(() => {
  if (!editData.value.accessLevel || !editData.value.permissions || editData.value.permissions.length === 0) {
    return false;
  }
  
  if (hasPassword.value && sharePassword.value && sharePassword.value.length < 6) {
    return false;
  }
  
  if (hasExpiration.value && !expirationDate.value) {
    return false;
  }
  
  if (hasIpRestrictions.value && allowedIps.value.some(ip => ip.trim() && !isValidIpAddress(ip.trim()))) {
    return false;
  }
  
  return true;
});

// Methods
const closeModal = () => {
  emit('close');
};

const clearError = () => {
  shareStore.clearError();
};

const handleUpdateShare = async () => {
  if (!isFormValid.value) return;

  try {
    const updateData: UpdateShareData = {
      ...editData.value,
      expiresAt: hasExpiration.value ? expirationDate.value : undefined,
      password: hasPassword.value && sharePassword.value ? sharePassword.value : undefined,
      allowedIps: hasIpRestrictions.value ? allowedIps.value.filter(ip => ip.trim()) : undefined
    };

    // Remove undefined values to avoid overwriting with undefined
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof UpdateShareData] === undefined) {
        delete updateData[key as keyof UpdateShareData];
      }
    });

    const updatedShare = await shareStore.updateShare(props.share.id, updateData);
    emit('updated', updatedShare);
  } catch (error) {
    // Error is handled by the store
  }
};

const addIpAddress = () => {
  allowedIps.value.push('');
};

const removeIpAddress = (index: number) => {
  allowedIps.value.splice(index, 1);
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

const isValidIpAddress = (ip: string): boolean => {
  // Basic IP validation (IPv4 and IPv6)
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  const ipv4CidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/;
  
  return ipv4Regex.test(ip) || ipv6Regex.test(ip) || ipv4CidrRegex.test(ip);
};

// Initialize form data
onMounted(() => {
  // Set expiration date if exists
  if (props.share.expiresAt) {
    const date = new Date(props.share.expiresAt);
    expirationDate.value = date.toISOString().slice(0, 16);
  }

  // Set allowed IPs if exists
  if (props.share.allowedIps && props.share.allowedIps.length > 0) {
    allowedIps.value = [...props.share.allowedIps];
  } else {
    allowedIps.value = [];
  }

  // Ensure view permission is always included
  if (!editData.value.permissions?.includes('view')) {
    editData.value.permissions = ['view', ...(editData.value.permissions || [])];
  }
});

// Watch for permission changes to ensure view is always included
watch(() => editData.value.permissions, (permissions) => {
  if (permissions && !permissions.includes('view')) {
    editData.value.permissions = ['view', ...permissions];
  }
}, { deep: true });

// Watch for expiration toggle
watch(hasExpiration, (value) => {
  if (!value) {
    expirationDate.value = '';
  }
});

// Watch for password toggle
watch(hasPassword, (value) => {
  if (!value) {
    sharePassword.value = '';
  }
});

// Watch for IP restrictions toggle
watch(hasIpRestrictions, (value) => {
  if (!value) {
    allowedIps.value = [];
  } else if (allowedIps.value.length === 0) {
    allowedIps.value = [''];
  }
});
</script>

<style scoped>
.modal-card {
  width: 90vw;
  max-width: 600px;
  max-height: 90vh;
}

.modal-card-body {
  max-height: 70vh;
  overflow-y: auto;
}

.checkbox {
  display: block;
  margin-bottom: 0.5rem;
}

.field.has-addons .control.is-expanded {
  flex: 1;
}

.box {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}
</style>
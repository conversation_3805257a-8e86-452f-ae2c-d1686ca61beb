<template>
  <div class="admin-system">
    <div class="container is-fluid">
      <!-- Header -->
      <div class="level mb-6">
        <div class="level-left">
          <div class="level-item">
            <div>
              <h1 class="title is-3">System Settings</h1>
              <p class="subtitle is-6">Configure system features and limits</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="field is-grouped">
              <div class="control">
                <button 
                  class="button is-primary" 
                  @click="loadSystemConfig"
                  :class="{ 'is-loading': isLoading }"
                >
                  <span class="icon">
                    <i class="fas fa-sync-alt"></i>
                  </span>
                  <span>Refresh</span>
                </button>
              </div>
              <div class="control">
                <button 
                  class="button is-success" 
                  @click="saveConfig"
                  :class="{ 'is-loading': isSaving }"
                  :disabled="!hasChanges"
                >
                  <span class="icon">
                    <i class="fas fa-save"></i>
                  </span>
                  <span>Save Changes</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="notification is-danger mb-5">
        <button class="delete" @click="error = null"></button>
        {{ error }}
      </div>

      <!-- Success Message -->
      <div v-if="successMessage" class="notification is-success mb-5">
        <button class="delete" @click="successMessage = null"></button>
        {{ successMessage }}
      </div>

      <!-- Loading State -->
      <div v-if="isLoading && !systemConfig" class="has-text-centered py-6">
        <div class="is-size-4 mb-3">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>Loading system configuration...</p>
      </div>

      <!-- System Configuration -->
      <div v-else-if="systemConfig" class="columns is-multiline">
        <!-- Features -->
        <div class="column is-6">
          <div class="card mb-5">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon">
                  <i class="fas fa-toggle-on"></i>
                </span>
                <span>Features</span>
              </p>
            </header>
            <div class="card-content">
              <div class="field">
                <input 
                  id="registration" 
                  v-model="localConfig.features.registration" 
                  type="checkbox" 
                  class="switch is-rounded"
                  @change="markChanged"
                >
                <label for="registration">User Registration</label>
                <p class="help">Allow new users to register accounts</p>
              </div>

              <div class="field">
                <input 
                  id="googleAuth" 
                  v-model="localConfig.features.googleAuth" 
                  type="checkbox" 
                  class="switch is-rounded"
                  @change="markChanged"
                >
                <label for="googleAuth">Google Authentication</label>
                <p class="help">Enable Google OAuth login</p>
              </div>

              <div class="field">
                <input 
                  id="emailVerification" 
                  v-model="localConfig.features.emailVerification" 
                  type="checkbox" 
                  class="switch is-rounded"
                  @change="markChanged"
                >
                <label for="emailVerification">Email Verification</label>
                <p class="help">Require email verification for new accounts</p>
              </div>

              <div class="field">
                <input 
                  id="twoFactor" 
                  v-model="localConfig.features.twoFactor" 
                  type="checkbox" 
                  class="switch is-rounded"
                  @change="markChanged"
                >
                <label for="twoFactor">Two-Factor Authentication</label>
                <p class="help">Enable 2FA for enhanced security</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Limits -->
        <div class="column is-6">
          <div class="card mb-5">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon">
                  <i class="fas fa-sliders-h"></i>
                </span>
                <span>System Limits</span>
              </p>
            </header>
            <div class="card-content">
              <div class="field">
                <label class="label">Max Notes Per User</label>
                <div class="control">
                  <input 
                    v-model.number="localConfig.limits.maxNotesPerUser" 
                    class="input" 
                    type="number" 
                    min="1"
                    @input="markChanged"
                  >
                </div>
                <p class="help">Maximum number of notes a user can create</p>
              </div>

              <div class="field">
                <label class="label">Max File Size (MB)</label>
                <div class="control">
                  <input 
                    v-model.number="maxFileSizeMB" 
                    class="input" 
                    type="number" 
                    min="1"
                    step="0.1"
                    @input="updateMaxFileSize"
                  >
                </div>
                <p class="help">Maximum file upload size in megabytes</p>
              </div>

              <div class="field">
                <label class="label">General Rate Limit (per minute)</label>
                <div class="control">
                  <input 
                    v-model.number="localConfig.limits.rateLimit.general" 
                    class="input" 
                    type="number" 
                    min="1"
                    @input="markChanged"
                  >
                </div>
                <p class="help">General API requests per minute per user</p>
              </div>

              <div class="field">
                <label class="label">Auth Rate Limit (per minute)</label>
                <div class="control">
                  <input 
                    v-model.number="localConfig.limits.rateLimit.auth" 
                    class="input" 
                    type="number" 
                    min="1"
                    @input="markChanged"
                  >
                </div>
                <p class="help">Authentication requests per minute per IP</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Maintenance Mode -->
        <div class="column is-6">
          <div class="card mb-5">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon">
                  <i class="fas fa-tools"></i>
                </span>
                <span>Maintenance Mode</span>
              </p>
            </header>
            <div class="card-content">
              <div class="field">
                <input 
                  id="maintenanceMode" 
                  v-model="localConfig.maintenance.mode" 
                  type="checkbox" 
                  class="switch is-rounded"
                  :class="{ 'is-danger': localConfig.maintenance.mode }"
                  @change="markChanged"
                >
                <label for="maintenanceMode">
                  <span :class="{ 'has-text-danger': localConfig.maintenance.mode }">
                    Maintenance Mode {{ localConfig.maintenance.mode ? 'Enabled' : 'Disabled' }}
                  </span>
                </label>
                <p class="help">Temporarily disable the application for maintenance</p>
              </div>

              <div class="field">
                <label class="label">Maintenance Message</label>
                <div class="control">
                  <textarea 
                    v-model="localConfig.maintenance.message" 
                    class="textarea" 
                    rows="3"
                    placeholder="Enter maintenance message for users..."
                    @input="markChanged"
                  ></textarea>
                </div>
                <p class="help">Message displayed to users during maintenance</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Notifications -->
        <div class="column is-6">
          <div class="card mb-5">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon">
                  <i class="fas fa-bell"></i>
                </span>
                <span>Notifications</span>
              </p>
            </header>
            <div class="card-content">
              <div class="field">
                <input 
                  id="emailEnabled" 
                  v-model="localConfig.notifications.emailEnabled" 
                  type="checkbox" 
                  class="switch is-rounded"
                  @change="markChanged"
                >
                <label for="emailEnabled">Email Notifications</label>
                <p class="help">Enable email notifications system-wide</p>
              </div>

              <div class="field">
                <input 
                  id="adminAlerts" 
                  v-model="localConfig.notifications.adminAlerts" 
                  type="checkbox" 
                  class="switch is-rounded"
                  @change="markChanged"
                >
                <label for="adminAlerts">Admin Alerts</label>
                <p class="help">Send alerts to administrators for critical events</p>
              </div>

              <div class="field">
                <input 
                  id="securityAlerts" 
                  v-model="localConfig.notifications.securityAlerts" 
                  type="checkbox" 
                  class="switch is-rounded"
                  @change="markChanged"
                >
                <label for="securityAlerts">Security Alerts</label>
                <p class="help">Send alerts for security-related events</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAdminStore } from '../stores/admin'
import { storeToRefs } from 'pinia'

const adminStore = useAdminStore()
const { systemConfig, isLoading, error } = storeToRefs(adminStore)

// Local state
const localConfig = ref<any>({})
const hasChanges = ref(false)
const isSaving = ref(false)
const successMessage = ref<string | null>(null)

// Computed for file size in MB
const maxFileSizeMB = computed({
  get: () => localConfig.value.limits?.maxFileSize ? localConfig.value.limits.maxFileSize / (1024 * 1024) : 10,
  set: (value) => {
    if (localConfig.value.limits) {
      localConfig.value.limits.maxFileSize = value * 1024 * 1024
      markChanged()
    }
  }
})

const updateMaxFileSize = () => {
  markChanged()
}

const markChanged = () => {
  hasChanges.value = true
}

const loadSystemConfig = async () => {
  await adminStore.loadSystemConfig()
  if (systemConfig.value) {
    localConfig.value = JSON.parse(JSON.stringify(systemConfig.value))
    hasChanges.value = false
  }
}

const saveConfig = async () => {
  if (!hasChanges.value) return
  
  isSaving.value = true
  try {
    const result = await adminStore.updateSystemConfig(localConfig.value)
    if (result.success) {
      hasChanges.value = false
      successMessage.value = 'System configuration updated successfully'
      setTimeout(() => {
        successMessage.value = null
      }, 5000)
    }
  } catch (err) {
    console.error('Failed to save config:', err)
  } finally {
    isSaving.value = false
  }
}

// Watch for changes in systemConfig from store
watch(systemConfig, (newConfig) => {
  if (newConfig && !hasChanges.value) {
    localConfig.value = JSON.parse(JSON.stringify(newConfig))
  }
}, { immediate: true })

onMounted(() => {
  loadSystemConfig()
})
</script>

<style scoped>
.admin-system {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem 0;
}

.card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.switch {
  appearance: none;
  width: 3rem;
  height: 1.5rem;
  border-radius: 1rem;
  background-color: #dbdbdb;
  border: none;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s;
  margin-right: 0.5rem;
}

.switch:checked {
  background-color: #3273dc;
}

.switch:checked.is-danger {
  background-color: #ff3860;
}

.switch::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  background-color: white;
  transition: transform 0.2s;
}

.switch:checked::before {
  transform: translateX(1.5rem);
}

.field {
  margin-bottom: 1.5rem;
}

.field:last-child {
  margin-bottom: 0;
}

.help {
  font-size: 0.75rem;
  color: #7a7a7a;
  margin-top: 0.25rem;
}

label {
  font-weight: 600;
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}
</style>
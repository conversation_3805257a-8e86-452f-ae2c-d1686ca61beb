<template>
    <div class="board-settings-view">
        <div class="container">
            <div class="columns is-centered">
                <div class="column is-8-tablet is-6-desktop">
                    <!-- Header -->
                    <div class="page-header">
                        <nav class="breadcrumb" aria-label="breadcrumbs">
                            <ul>
                                <li>
                                    <router-link to="/boards">
                                        <span class="icon is-small">
                                            <i class="fas fa-columns"></i>
                                        </span>
                                        <span>Boards</span>
                                    </router-link>
                                </li>
                                <li>
                                    <router-link :to="`/boards/${boardId}`">
                                        {{ board?.title || 'Board' }}
                                    </router-link>
                                </li>
                                <li class="is-active">
                                    <a href="#" aria-current="page">Settings</a>
                                </li>
                            </ul>
                        </nav>

                        <h1 class="title is-3">
                            <span class="icon">
                                <i class="fas fa-cog"></i>
                            </span>
                            <span>Board Settings</span>
                        </h1>
                    </div>

                    <!-- Loading State -->
                    <div v-if="isLoading" class="loading-state">
                        <div class="has-text-centered">
                            <span class="icon is-large">
                                <i class="fas fa-spinner fa-pulse"></i>
                            </span>
                            <p>Loading board settings...</p>
                        </div>
                    </div>

                    <!-- Settings Content -->
                    <div v-else-if="board" class="settings-content">
                        <BoardSettingsModal :board="board" :is-modal="false" @update:board="updateBoard"
                            @close="goBack" />
                    </div>

                    <!-- Error State -->
                    <div v-else class="error-state">
                        <div class="has-text-centered">
                            <span class="icon is-large has-text-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                            </span>
                            <h3 class="title is-4 has-text-danger">Board Not Found</h3>
                            <p class="subtitle">The board you're trying to configure doesn't exist or you don't have
                                access to it.</p>
                            <router-link to="/boards" class="button is-primary">
                                <span class="icon">
                                    <i class="fas fa-arrow-left"></i>
                                </span>
                                <span>Back to Boards</span>
                            </router-link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useKanbanStore } from '@/stores/kanban'
import { useNotificationStore } from '@/stores/notifications'
import BoardSettingsModal from '@/components/kanban/BoardSettingsModal.vue'
import type { KanbanBoard } from '@/types/kanban'

const route = useRoute()
const router = useRouter()
const kanbanStore = useKanbanStore()
const notificationStore = useNotificationStore()

// Reactive state
const board = ref<KanbanBoard | null>(null)
const isLoading = ref(true)

// Computed
const boardId = computed(() => route.params.id as string)

// Methods
const loadBoard = async () => {
    isLoading.value = true
    try {
        board.value = await kanbanStore.getBoard(boardId.value)
    } catch (error) {
        notificationStore.addNotification({
            type: 'critical',
            category: 'system',
            title: 'Error',
            message: 'Failed to load board',
            read: false
        })
    } finally {
        isLoading.value = false
    }
}

const updateBoard = async (updates: Partial<KanbanBoard>) => {
    if (!board.value) return

    try {
        await kanbanStore.updateBoard(board.value.id, updates)
        board.value = { ...board.value, ...updates }

        notificationStore.addNotification({
            type: 'success',
            category: 'system',
            title: 'Success',
            message: 'Board settings updated successfully',
            read: false
        })
    } catch (error) {
        notificationStore.addNotification({
            type: 'critical',
            category: 'system',
            title: 'Error',
            message: 'Failed to update board settings',
            read: false
        })
    }
}

const goBack = () => {
    router.push(`/boards/${boardId.value}`)
}

// Lifecycle
onMounted(() => {
    loadBoard()
})
</script>

<style scoped>
.board-settings-view {
    min-height: 100vh;
    background: #f8f9fa;
    padding: 2rem 0;
}

.page-header {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.breadcrumb {
    margin-bottom: 1rem;
}

.breadcrumb a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #007bff;
}

.breadcrumb .is-active a {
    color: #2c3e50;
}

.settings-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.loading-state,
.error-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Override modal styles for inline use */
.settings-content :deep(.modal-card) {
    width: 100%;
    max-width: none;
    margin: 0;
    box-shadow: none;
}

.settings-content :deep(.modal-card-head) {
    border-radius: 8px 8px 0 0;
}

.settings-content :deep(.modal-card-foot) {
    border-radius: 0 0 8px 8px;
}

/* Responsive */
@media (max-width: 768px) {
    .board-settings-view {
        padding: 1rem;
    }

    .page-header {
        margin: 0 0 1rem 0;
        padding: 1.5rem;
    }

    .title.is-3 {
        font-size: 1.75rem;
    }
}
</style>
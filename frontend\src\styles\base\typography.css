/* Typography Definitions */

/* Font families */
:root {
  --font-family-sans:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-family-mono:
    'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* Font weights */
:root {
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}

/* Font sizes */
:root {
  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-base: 1rem; /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem; /* 20px */
  --font-size-2xl: 1.5rem; /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem; /* 36px */
  --font-size-5xl: 3rem; /* 48px */
  --font-size-6xl: 3.75rem; /* 60px */
}

/* Line heights */
:root {
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
}

/* Letter spacing */
:root {
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
}

/* Base typography styles */
body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
}

/* Headings */
h1,
.title.is-1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
}

h2,
.title.is-2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
}

h3,
.title.is-3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

h4,
.title.is-4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

h5,
.title.is-5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

h6,
.title.is-6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

/* Subtitle */
.subtitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-muted);
}

/* Text elements */
p {
  margin-bottom: 1rem;
  line-height: var(--line-height-relaxed);
}

p:last-child {
  margin-bottom: 0;
}

/* Links */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Code and preformatted text */
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--color-surface);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  color: var(--color-text);
}

pre {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  background-color: var(--color-surface);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  line-height: var(--line-height-relaxed);
}

pre code {
  background: none;
  padding: 0;
  font-size: inherit;
}

/* Lists */
ul,
ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

li {
  margin-bottom: 0.25rem;
}

li:last-child {
  margin-bottom: 0;
}

/* Blockquotes */
blockquote {
  margin: 1rem 0;
  padding: 1rem;
  border-left: 4px solid var(--color-primary);
  background-color: var(--color-surface);
  font-style: italic;
}

blockquote p:last-child {
  margin-bottom: 0;
}

/* Emphasis */
strong,
b {
  font-weight: var(--font-weight-bold);
}

em,
i {
  font-style: italic;
}

mark {
  background-color: var(--color-warning);
  color: var(--color-text);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Small text */
small {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
}

/* Utility classes for font sizes */
.is-size-1 {
  font-size: var(--font-size-6xl) !important;
}
.is-size-2 {
  font-size: var(--font-size-5xl) !important;
}
.is-size-3 {
  font-size: var(--font-size-4xl) !important;
}
.is-size-4 {
  font-size: var(--font-size-3xl) !important;
}
.is-size-5 {
  font-size: var(--font-size-2xl) !important;
}
.is-size-6 {
  font-size: var(--font-size-xl) !important;
}
.is-size-7 {
  font-size: var(--font-size-lg) !important;
}

/* Utility classes for font weights */
.has-text-weight-light {
  font-weight: var(--font-weight-light) !important;
}
.has-text-weight-normal {
  font-weight: var(--font-weight-normal) !important;
}
.has-text-weight-medium {
  font-weight: var(--font-weight-medium) !important;
}
.has-text-weight-semibold {
  font-weight: var(--font-weight-semibold) !important;
}
.has-text-weight-bold {
  font-weight: var(--font-weight-bold) !important;
}

/* Responsive typography */
@media screen and (max-width: 768px) {
  h1,
  .title.is-1 {
    font-size: var(--font-size-3xl);
  }

  h2,
  .title.is-2 {
    font-size: var(--font-size-2xl);
  }

  h3,
  .title.is-3 {
    font-size: var(--font-size-xl);
  }
}

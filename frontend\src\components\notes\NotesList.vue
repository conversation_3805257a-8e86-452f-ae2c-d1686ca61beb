<template>
  <div class="notes-list">
    <!-- Search and filters -->
    <div class="search-filters">
      <div class="field has-addons">
        <div class="control is-expanded">
          <input v-model="searchQuery" @input="onSearchChange" class="input" type="text"
            placeholder="Search notes..." />
        </div>
        <div class="control">
          <button class="button" @click="clearSearch">
            <span class="icon">
              <i class="fas fa-times"></i>
            </span>
          </button>
        </div>
      </div>

      <div class="field is-grouped is-grouped-multiline">
        <div class="control">
          <div class="select is-small">
            <select v-model="selectedType" @change="applyFilters">
              <option value="">All Types</option>
              <option value="markdown">Markdown</option>
              <option value="richtext">Rich Text</option>
              <option value="kanban">Kanban</option>
            </select>
          </div>
        </div>

        <div class="control">
          <label class="checkbox">
            <input type="checkbox" v-model="showArchived" @change="applyFilters" />
            Show archived
          </label>
        </div>

        <div class="control">
          <button @click="clearFilters" class="button is-small" :disabled="!hasActiveFilters">
            Clear filters
          </button>
        </div>

        <div class="control">
          <button @click="toggleBulkSelect" class="button is-small" :class="{ 'is-primary': isBulkSelectMode }">
            <i class="fas fa-check-square mr-1"></i>
            {{ isBulkSelectMode ? 'Cancel' : 'Select' }}
          </button>
        </div>
      </div>

      <!-- Bulk Actions -->
      <div v-if="isBulkSelectMode && selectedNotes.length > 0" class="bulk-actions">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <span class="tag is-info">
                {{ selectedNotes.length }} selected
              </span>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <div class="buttons">
                <button @click="exportSelectedNotes" class="button is-primary is-small">
                  <i class="fas fa-download mr-1"></i>
                  Export
                </button>
                <button @click="selectAll" class="button is-small">
                  Select All
                </button>
                <button @click="clearSelection" class="button is-small">
                  Clear
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Notes grid -->
    <div class="notes-grid" v-if="displayedNotes.length > 0">
      <div v-for="note in displayedNotes" :key="note.id" class="note-card" :class="{
        'is-archived': note.isArchived,
        'is-selected': selectedNotes.includes(note.id),
        'is-selectable': isBulkSelectMode
      }" @click="handleNoteClick(note)">
        <div class="note-header">
          <div class="note-header-left">
            <label v-if="isBulkSelectMode" class="checkbox" @click.stop>
              <input type="checkbox" :checked="selectedNotes.includes(note.id)"
                @change="toggleNoteSelection(note.id)" />
            </label>
            <h3 class="note-title">{{ note.title || 'Untitled' }}</h3>
          </div>
          <span class="note-type-badge" :class="`is-${note.noteType}`">
            {{ note.noteType }}
          </span>
        </div>

        <div class="note-content">
          {{ getPreviewText(note.content, note.noteType) }}
        </div>

        <div class="note-meta">
          <div class="note-tags" v-if="note.tags.length > 0">
            <span v-for="tag in note.tags.slice(0, 3)" :key="tag.id" class="tag is-small">
              {{ tag.name }}
            </span>
            <span v-if="note.tags.length > 3" class="tag is-small">
              +{{ note.tags.length - 3 }}
            </span>
          </div>

          <div class="note-stats">
            <span class="stat-item">
              <i class="fas fa-clock"></i>
              {{ formatDate(note.updatedAt) }}
            </span>
            <span class="stat-item" v-if="note.metadata.wordCount">
              <i class="fas fa-file-word"></i>
              {{ note.metadata.wordCount }} words
            </span>
          </div>
        </div>

        <div class="note-actions">
          <button @click.stop="toggleArchive(note)" class="button is-small"
            :title="note.isArchived ? 'Unarchive' : 'Archive'">
            <span class="icon">
              <i :class="note.isArchived ? 'fas fa-box-open' : 'fas fa-archive'"></i>
            </span>
          </button>

          <button @click.stop="deleteNote(note)" class="button is-small is-danger" title="Delete">
            <span class="icon">
              <i class="fas fa-trash"></i>
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else-if="!notesStore.isLoading" class="empty-state">
      <div class="has-text-centered">
        <i class="fas fa-sticky-note fa-3x has-text-grey-light"></i>
        <h3 class="title is-4 has-text-grey">
          {{ hasActiveFilters ? 'No notes match your filters' : 'No notes yet' }}
        </h3>
        <p class="subtitle has-text-grey">
          {{ hasActiveFilters
            ? 'Try adjusting your search or filters'
            : 'Create your first note to get started'
          }}
        </p>
        <button v-if="!hasActiveFilters" @click="$emit('create-note')" class="button is-primary">
          <span class="icon">
            <i class="fas fa-plus"></i>
          </span>
          <span>Create Note</span>
        </button>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="notesStore.isLoading" class="loading-state">
      <div class="has-text-centered">
        <i class="fas fa-spinner fa-spin fa-2x has-text-primary"></i>
        <p class="has-text-grey">Loading notes...</p>
      </div>
    </div>

    <!-- Load more button -->
    <div v-if="canLoadMore && !notesStore.isLoading" class="load-more">
      <button @click="loadMore" class="button is-fullwidth">
        Load More Notes
      </button>
    </div>

    <!-- Error message -->
    <div v-if="notesStore.error" class="notification is-danger">
      <button @click="notesStore.clearError" class="delete"></button>
      {{ notesStore.error }}
    </div>

    <!-- Export Modal -->
    <ExportModal :is-open="showExportModal" :note-ids="selectedNotes" @close="showExportModal = false"
      @exported="onExported" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useNotesStore } from '../../stores/notes'
import type { Note } from '../../services/noteService'
import ExportModal from '../export/ExportModal.vue'

interface Emits {
  (e: 'select-note', note: Note): void
  (e: 'create-note'): void
}

const emit = defineEmits<Emits>()

const notesStore = useNotesStore()

// Local state
const searchQuery = ref('')
const selectedType = ref<string>('')
const showArchived = ref(false)
const searchTimeout = ref<number | null>(null)

// Bulk selection state
const isBulkSelectMode = ref(false)
const selectedNotes = ref<string[]>([])
const showExportModal = ref(false)

// Computed
const displayedNotes = computed(() => {
  return notesStore.filteredNotes
})

const hasActiveFilters = computed(() => {
  return searchQuery.value !== '' ||
    selectedType.value !== '' ||
    showArchived.value ||
    notesStore.selectedTags.length > 0
})

const canLoadMore = computed(() => {
  return notesStore.currentPage < notesStore.totalPages
})

// Methods
const onSearchChange = () => {
  // Debounce search
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }

  searchTimeout.value = window.setTimeout(() => {
    notesStore.setSearchQuery(searchQuery.value)
    applyFilters()
  }, 300)
}

const clearSearch = () => {
  searchQuery.value = ''
  notesStore.setSearchQuery('')
  applyFilters()
}

const applyFilters = async () => {
  notesStore.setSelectedNoteType(selectedType.value as any)
  notesStore.setShowArchived(showArchived.value)
  await notesStore.applyFilters()
}

const clearFilters = async () => {
  searchQuery.value = ''
  selectedType.value = ''
  showArchived.value = false
  notesStore.clearFilters()
  await notesStore.applyFilters()
}

const loadMore = async () => {
  await notesStore.loadMoreNotes()
}

const toggleArchive = async (note: Note) => {
  try {
    await notesStore.updateNote(note.id, {
      isArchived: !note.isArchived
    })
  } catch (error) {
    console.error('Failed to toggle archive:', error)
  }
}

const deleteNote = async (note: Note) => {
  if (confirm(`Are you sure you want to delete "${note.title || 'Untitled'}"?`)) {
    try {
      await notesStore.deleteNote(note.id)
    } catch (error) {
      console.error('Failed to delete note:', error)
    }
  }
}

// Bulk selection methods
const toggleBulkSelect = () => {
  isBulkSelectMode.value = !isBulkSelectMode.value
  if (!isBulkSelectMode.value) {
    selectedNotes.value = []
  }
}

const handleNoteClick = (note: Note) => {
  if (isBulkSelectMode.value) {
    toggleNoteSelection(note.id)
  } else {
    emit('select-note', note)
  }
}

const toggleNoteSelection = (noteId: string) => {
  const index = selectedNotes.value.indexOf(noteId)
  if (index > -1) {
    selectedNotes.value.splice(index, 1)
  } else {
    selectedNotes.value.push(noteId)
  }
}

const selectAll = () => {
  selectedNotes.value = displayedNotes.value.map(note => note.id)
}

const clearSelection = () => {
  selectedNotes.value = []
}

const exportSelectedNotes = () => {
  if (selectedNotes.value.length > 0) {
    showExportModal.value = true
  }
}

const onExported = (result: { success: boolean; filename?: string; error?: string }) => {
  if (result.success) {
    console.log('Notes exported successfully:', result.filename)
    // Optionally clear selection after successful export
    clearSelection()
    toggleBulkSelect()
  } else {
    console.error('Export failed:', result.error)
  }
}

const getPreviewText = (content: string, noteType: string): string => {
  if (!content) return 'No content'

  let text = content

  if (noteType === 'kanban') {
    try {
      const kanban = JSON.parse(content)
      // Extract text from kanban cards
      const cardTexts: string[] = []
      if (kanban.columns && Array.isArray(kanban.columns)) {
        kanban.columns.forEach((column: any) => {
          if (column.cards && Array.isArray(column.cards)) {
            column.cards.forEach((card: any) => {
              if (card.title) cardTexts.push(card.title)
              if (card.description) cardTexts.push(card.description)
            })
          }
        })
      }
      text = cardTexts.join(' ')
    } catch {
      text = 'Kanban board'
    }
  } else {
    // Remove HTML tags and markdown formatting
    text = content
      .replace(/<[^>]*>/g, '') // Remove HTML
      .replace(/[#*_`~\[\]()]/g, '') // Remove markdown formatting
      .trim()
  }

  return text.length > 150 ? text.substring(0, 150) + '...' : text
}

const formatDate = (dateInput: string | Date): string => {
  // Handle both string and Date inputs
  let date: Date

  if (typeof dateInput === 'string') {
    date = new Date(dateInput)
  } else if (dateInput instanceof Date) {
    date = dateInput
  } else {
    return 'Invalid date'
  }

  // Check if date is valid
  if (isNaN(date.getTime())) {
    return 'Invalid date'
  }

  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return 'Today'
  } else if (diffDays === 1) {
    return 'Yesterday'
  } else if (diffDays < 7) {
    return `${diffDays} days ago`
  } else {
    return date.toLocaleDateString()
  }
}

// Initialize
onMounted(async () => {
  if (notesStore.notes.length === 0) {
    await notesStore.loadNotes()
  }
})
</script>

<style scoped>
.notes-list {
  padding: 1rem;
}

.search-filters {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #dbdbdb;
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.note-card {
  border: 1px solid var(--card-border);
  border-radius: 6px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--card-background);
  position: relative;
}

.note-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.note-card.is-archived {
  opacity: 0.6;
  background: var(--color-surface);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.note-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  margin-right: 0.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.note-type-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  text-transform: uppercase;
  font-weight: 600;
  color: white;
}

.note-type-badge.is-markdown {
  background-color: #3273dc;
}

.note-type-badge.is-richtext {
  background-color: #23d160;
}

.note-type-badge.is-kanban {
  background-color: #ff3860;
}

.note-content {
  color: #4a4a4a;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  min-height: 3rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.note-meta {
  margin-bottom: 0.5rem;
}

.note-tags {
  margin-bottom: 0.5rem;
}

.note-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #7a7a7a;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.note-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.note-card:hover .note-actions {
  opacity: 1;
}

.empty-state,
.loading-state {
  padding: 3rem 1rem;
}

.load-more {
  margin-top: 2rem;
}

/* Bulk selection styles */
.bulk-actions {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.note-card.is-selectable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.note-card.is-selectable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.note-card.is-selected {
  border-color: #3273dc;
  box-shadow: 0 0 0 2px rgba(50, 115, 220, 0.2);
}

.note-header-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.note-header-left .checkbox {
  margin: 0;
}

.note-header-left .note-title {
  margin: 0;
  flex: 1;
}

@media (max-width: 768px) {
  .notes-grid {
    grid-template-columns: 1fr;
  }

  .note-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .note-type-badge {
    margin-top: 0.5rem;
  }
}
</style>
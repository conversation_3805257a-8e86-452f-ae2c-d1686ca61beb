import { Request, Response } from 'express';
import { TemplateRepository } from '../repositories/TemplateRepository';
import { TemplateModel, CreateTemplateData, UpdateTemplateData, TemplateFilters, PaginationOptions } from '../models/Template';
import { NoteRepository } from '../repositories/NoteRepository';

export class TemplateController {
  /**
   * GET /api/templates - List templates with pagination and filtering
   */
  static async getTemplates(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Parse query parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
      const sortBy = (req.query.sortBy as string) || 'updated_at';
      const sortOrder = (req.query.sortOrder as string) || 'desc';
      const noteType = req.query.noteType as string;
      const category = req.query.category as string;
      const isPublic = req.query.isPublic === 'true' ? true : req.query.isPublic === 'false' ? false : undefined;
      const search = req.query.search as string;
      const tags = req.query.tags ? (req.query.tags as string).split(',') : undefined;
      const groupId = req.query.groupId as string;

      const filters: TemplateFilters = {
        userId,
        groupId,
        noteType: noteType as any,
        category,
        isPublic,
        tags,
        search
      };

      const pagination: PaginationOptions = {
        page,
        limit,
        sortBy: sortBy as any,
        sortOrder: sortOrder as any
      };

      const result = await TemplateRepository.findByFilters(filters, pagination);

      res.json({
        templates: result.templates,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: Math.ceil(result.total / limit)
        }
      });
    } catch (error) {
      console.error('Error fetching templates:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * GET /api/templates/:id - Get specific template
   */
  static async getTemplateById(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const templateId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const template = await TemplateRepository.findById(templateId);

      if (!template) {
        res.status(404).json({ error: 'Template not found' });
        return;
      }

      // Check if user has access to the template
      if (template.userId !== userId && !template.isPublic) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      res.json(template);
    } catch (error) {
      console.error('Error fetching template:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * POST /api/templates - Create new template
   */
  static async createTemplate(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { name, description, noteType, content, isPublic, tags, metadata, groupId } = req.body;

      // Validate required fields
      if (!name || !content || !noteType) {
        res.status(400).json({ error: 'Name, content, and noteType are required' });
        return;
      }

      // Validate note type
      if (!TemplateModel.validateNoteType(noteType)) {
        res.status(400).json({ error: 'Invalid note type. Must be richtext, markdown, or kanban' });
        return;
      }

      // Validate name
      const nameValidation = TemplateModel.validateName(name);
      if (!nameValidation.valid) {
        res.status(400).json({ error: 'Invalid name', details: nameValidation.errors });
        return;
      }

      // Validate content
      const contentValidation = TemplateModel.validateContent(content, noteType);
      if (!contentValidation.valid) {
        res.status(400).json({ error: 'Invalid content', details: contentValidation.errors });
        return;
      }

      // Validate description
      if (description) {
        const descriptionValidation = TemplateModel.validateDescription(description);
        if (!descriptionValidation.valid) {
          res.status(400).json({ error: 'Invalid description', details: descriptionValidation.errors });
          return;
        }
      }

      const templateData: CreateTemplateData = {
        userId,
        groupId,
        name: name.trim(),
        description: description?.trim(),
        noteType,
        content,
        isPublic: Boolean(isPublic),
        tags: tags && Array.isArray(tags) ? tags.map((t: string) => t.trim()) : [],
        metadata
      };

      const template = await TemplateRepository.create(templateData);

      res.status(201).json(template);
    } catch (error) {
      console.error('Error creating template:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * PUT /api/templates/:id - Update template
   */
  static async updateTemplate(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const templateId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if template exists and user has access
      const existingTemplate = await TemplateRepository.findById(templateId);
      if (!existingTemplate) {
        res.status(404).json({ error: 'Template not found' });
        return;
      }

      if (existingTemplate.userId !== userId) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const { name, description, content, isPublic, tags, metadata } = req.body;
      const updateData: UpdateTemplateData = {};

      // Validate and set name if provided
      if (name !== undefined) {
        const nameValidation = TemplateModel.validateName(name);
        if (!nameValidation.valid) {
          res.status(400).json({ error: 'Invalid name', details: nameValidation.errors });
          return;
        }
        updateData.name = name.trim();
      }

      // Validate and set description if provided
      if (description !== undefined) {
        if (description) {
          const descriptionValidation = TemplateModel.validateDescription(description);
          if (!descriptionValidation.valid) {
            res.status(400).json({ error: 'Invalid description', details: descriptionValidation.errors });
            return;
          }
        }
        updateData.description = description?.trim();
      }

      // Validate and set content if provided
      if (content !== undefined) {
        const contentValidation = TemplateModel.validateContent(content, existingTemplate.noteType);
        if (!contentValidation.valid) {
          res.status(400).json({ error: 'Invalid content', details: contentValidation.errors });
          return;
        }
        updateData.content = content;
      }

      // Set public status if provided
      if (isPublic !== undefined) {
        updateData.isPublic = Boolean(isPublic);
      }

      // Set tags if provided
      if (tags !== undefined && Array.isArray(tags)) {
        updateData.tags = tags.map((t: string) => t.trim());
      }

      // Set metadata if provided
      if (metadata !== undefined) {
        updateData.metadata = metadata;
      }

      const updatedTemplate = await TemplateRepository.update(templateId, updateData, userId);

      res.json(updatedTemplate);
    } catch (error) {
      console.error('Error updating template:', error);
      if (error instanceof Error) {
        res.status(500).json({ error: 'Internal server error', details: error.message });
      } else {
        res.status(500).json({ error: 'Internal server error' });
      }
    }
  }

  /**
   * DELETE /api/templates/:id - Delete template
   */
  static async deleteTemplate(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const templateId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if template exists and user has access
      const existingTemplate = await TemplateRepository.findById(templateId);
      if (!existingTemplate) {
        res.status(404).json({ error: 'Template not found' });
        return;
      }

      if (existingTemplate.userId !== userId) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      await TemplateRepository.delete(templateId);

      res.json({ message: 'Template deleted successfully' });
    } catch (error) {
      console.error('Error deleting template:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * POST /api/templates/:id/use - Use template to create a new note
   */
  static async useTemplate(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const templateId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const template = await TemplateRepository.findById(templateId);

      if (!template) {
        res.status(404).json({ error: 'Template not found' });
        return;
      }

      // Check if user has access to the template
      if (template.userId !== userId && !template.isPublic) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const { title, groupId } = req.body;

      // Create note from template
      const noteData = {
        userId,
        groupId,
        title: title || template.name,
        content: template.content,
        noteType: template.noteType
      };

      const note = await NoteRepository.create(noteData);

      // Add template tags to the note
      if (template.tags && template.tags.length > 0) {
        for (const tagName of template.tags) {
          try {
            // Create tag if it doesn't exist
            let tag;
            try {
              tag = await NoteRepository.createTag(tagName, userId);
            } catch (error) {
              // Tag might already exist
              const existingTags = await NoteRepository.getTagsByUserId(userId);
              tag = existingTags.find(t => t.name === tagName);
            }

            if (tag) {
              await NoteRepository.addTagToNote(note.id, tag.id);
            }
          } catch (error) {
            console.error('Error adding tag to note:', error);
            // Continue with other tags
          }
        }
      }

      // Increment template usage count
      try {
        await TemplateRepository.incrementUsageCount(templateId);
      } catch (error) {
        console.error('Error incrementing template usage count:', error);
        // Don't fail the operation if this fails
      }

      // Fetch the created note with tags
      const noteWithTags = await NoteRepository.findById(note.id);

      res.status(201).json(noteWithTags);
    } catch (error) {
      console.error('Error using template:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * GET /api/templates/categories - Get available template categories
   */
  static async getCategories(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const categories = await TemplateRepository.getCategories();

      res.json({ categories });
    } catch (error) {
      console.error('Error fetching template categories:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * POST /api/templates/seed - Seed built-in templates (admin only)
   */
  static async seedBuiltInTemplates(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // For now, allow any authenticated user to seed templates
      // In production, you might want to restrict this to admin users
      
      const builtInTemplates = TemplateModel.getBuiltInTemplates();
      const createdTemplates = [];

      for (const templateData of builtInTemplates) {
        try {
          // Check if template already exists
          const existing = await TemplateRepository.findByFilters(
            { userId: 'system', search: templateData.name },
            { page: 1, limit: 1 }
          );

          if (existing.templates.length === 0) {
            const template = await TemplateRepository.create({
              ...templateData,
              userId: 'system' // Use system user for built-in templates
            });
            createdTemplates.push(template);
          }
        } catch (error) {
          console.error(`Error creating built-in template "${templateData.name}":`, error);
          // Continue with other templates
        }
      }

      res.json({
        message: `Seeded ${createdTemplates.length} built-in templates`,
        templates: createdTemplates
      });
    } catch (error) {
      console.error('Error seeding built-in templates:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
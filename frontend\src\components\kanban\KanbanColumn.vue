<template>
  <div class="kanban-column" :class="{ 
    'is-dragging': isDragging, 
    'drag-over': isDragOver,
    [`column-color-${getColumnColorClass(column.color)}`]: column.color
  }" draggable="true" @dragstart="handleColumnDragStart" @dragend="handleColumnDragEnd" @dragover="handleColumnDragOver"
    @dragleave="handleDragLeave" @drop="handleColumnDrop">
    <!-- Column Header -->
    <div class="column-header">
      <div class="column-title-section">
        <input v-model="localTitle" class="column-title-input" @blur="updateColumnTitle"
          @keydown.enter.prevent="($event.target as HTMLInputElement).blur()" placeholder="Column title" />
        <span class="card-count">{{ filteredCards.length }}</span>
      </div>

      <div class="column-actions">
        <div class="dropdown is-right" :class="{ 'is-active': showDropdown }">
          <div class="dropdown-trigger">
            <button class="button is-small is-ghost" @click="showDropdown = !showDropdown">
              <i class="fas fa-ellipsis-h"></i>
            </button>
          </div>
          <div class="dropdown-menu">
            <div class="dropdown-content">
              <a class="dropdown-item" @click="showColorPicker = true">
                <i class="fas fa-palette"></i>
                Change Color
              </a>
              <a class="dropdown-item" @click="addCard">
                <i class="fas fa-plus"></i>
                Add Card
              </a>
              <hr class="dropdown-divider">
              <a class="dropdown-item has-text-danger" @click="deleteColumn">
                <i class="fas fa-trash"></i>
                Delete Column
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Color Picker -->
    <div v-if="showColorPicker" class="color-picker-overlay" @click="showColorPicker = false">
      <div class="color-picker" @click.stop>
        <h6 class="title is-6">Choose Column Color</h6>
        <div class="color-options">
          <button v-for="color in colorOptions" :key="color.value" class="color-option"
            :class="{ 'is-selected': column.color === color.value, [getColorOptionClass(color.value)]: true }"
            @click="updateColumnColor(color.value)" :title="color.name" />
          <button class="color-option is-clear" :class="{ 'is-selected': !column.color }"
            @click="updateColumnColor(null)" title="Default">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Cards Container -->
    <div class="cards-container" ref="cardsContainer" @dragover.prevent="handleCardDragOver" @drop="handleCardDrop"
      @dragleave="handleDragLeave">
      <template v-for="(cardItem, index) in filteredCards" :key="cardItem.id">
        <div v-if="(globalDraggedCard || draggedCard) && isDragOver && dropPosition === index"
          class="drop-indicator active"></div>
        <KanbanCardComponent :card="cardItem" :column="column" :board="board"
          @update:card="(cardId, updates) => $emit('update:card', cardId, updates)"
          @delete:card="$emit('delete:card', cardItem.id)" @click="$emit('select:card', cardItem)"
          @drag:start="handleCardDragStart" @drag:end="handleCardDragEnd" />
      </template>

      <!-- Drop indicator at the end -->
      <div v-if="(globalDraggedCard || draggedCard) && isDragOver && dropPosition >= filteredCards.length"
        class="drop-indicator active"></div>

      <!-- Add Card Button -->
      <button v-if="!showAddCard" class="add-card-button" @click="showAddCard = true">
        <i class="fas fa-plus"></i>
        Add a card
      </button>

      <!-- Add Card Form -->
      <div v-if="showAddCard" class="add-card-form">
        <textarea v-model="newCardTitle" class="textarea is-small" placeholder="Enter a title for this card..." rows="3"
          @keydown.enter.prevent="createCard" @keydown.escape="cancelAddCard" ref="newCardInput" />
        <div class="add-card-actions">
          <button class="button is-small is-primary" @click="createCard">
            Add Card
          </button>
          <button class="button is-small" @click="cancelAddCard">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import type { KanbanColumn, KanbanCard, KanbanBoard } from '@/types/kanban'
import KanbanCardComponent from './KanbanCard.vue'
import { useDragAndDrop } from '@/composables/useDragAndDrop'

interface Props {
  column: KanbanColumn
  board: KanbanBoard
  filters?: any
}

interface Emits {
  (e: 'update:column', columnId: string, updates: Partial<KanbanColumn>): void
  (e: 'delete:column', columnId: string): void
  (e: 'add:card', columnId: string, cardData: Partial<KanbanCard>): void
  (e: 'update:card', cardId: string, updates: Partial<KanbanCard>): void
  (e: 'delete:card', cardId: string): void
  (e: 'drag:card', dragEvent: any): void
  (e: 'drag:column', dragEvent: any): void
  (e: 'select:card', cardData: KanbanCard): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Drag and drop composable
const { draggedCard: globalDraggedCard, getDraggedCardData } = useDragAndDrop()

// Reactive state
const localTitle = ref(props.column.title)
const showDropdown = ref(false)
const showColorPicker = ref(false)
const showAddCard = ref(false)
const newCardTitle = ref('')
const isDragging = ref(false)
const isDragOver = ref(false)
const draggedCard = ref<KanbanCard | null>(null)
const dropPosition = ref(-1)
const cardsContainer = ref<HTMLElement>()
const newCardInput = ref<HTMLTextAreaElement>()

// Color options for columns
const colorOptions = [
  { name: 'Red', value: '#ff6b6b' },
  { name: 'Orange', value: '#ffa726' },
  { name: 'Yellow', value: '#ffeb3b' },
  { name: 'Green', value: '#66bb6a' },
  { name: 'Blue', value: '#42a5f5' },
  { name: 'Purple', value: '#ab47bc' },
  { name: 'Pink', value: '#ec407a' },
  { name: 'Teal', value: '#26a69a' },
  { name: 'Indigo', value: '#5c6bc0' },
  { name: 'Brown', value: '#8d6e63' },
  { name: 'Grey', value: '#78909c' }
]

// Helper methods for color classes
const getColumnColorClass = (color: string | null): string => {
  if (!color) return ''
  
  const colorMap: { [key: string]: string } = {
    '#ff6b6b': 'red',
    '#ffa726': 'orange',
    '#ffeb3b': 'yellow',
    '#66bb6a': 'green',
    '#42a5f5': 'blue',
    '#ab47bc': 'purple',
    '#ec407a': 'pink',
    '#26a69a': 'teal',
    '#5c6bc0': 'indigo',
    '#8d6e63': 'brown',
    '#78909c': 'grey'
  }
  
  return colorMap[color] || ''
}

const getColorOptionClass = (color: string): string => {
  return `color-option-${getColumnColorClass(color)}`
}

// Computed
const filteredCards = computed(() => {
  if (!props.column?.cards || !Array.isArray(props.column.cards)) {
    return []
  }

  const validCards = props.column.cards
    .filter(cardItem => cardItem && cardItem.id) // Filter out undefined/null cards
    .sort((a, b) => a.position - b.position)

  return validCards
})



// Watchers
watch(() => props.column.title, (newTitle) => {
  localTitle.value = newTitle
})

watch(showAddCard, async (show) => {
  if (show) {
    await nextTick()
    newCardInput.value?.focus()
  }
})

// Methods
const updateColumnTitle = () => {
  if (localTitle.value.trim() !== props.column.title) {
    emit('update:column', props.column.id, { title: localTitle.value.trim() || 'Untitled' })
  }
}

const updateColumnColor = (color: string | null) => {
  emit('update:column', props.column.id, { color: color || undefined })
  showColorPicker.value = false
  showDropdown.value = false
}

const deleteColumn = () => {
  if (confirm('Are you sure you want to delete this column? All cards will be lost.')) {
    emit('delete:column', props.column.id)
  }
  showDropdown.value = false
}

const addCard = () => {
  showAddCard.value = true
  showDropdown.value = false
}

const createCard = () => {
  if (newCardTitle.value.trim()) {
    emit('add:card', props.column.id, {
      title: newCardTitle.value.trim(),
      description: ''
    })
    newCardTitle.value = ''
    showAddCard.value = false
  }
}

const cancelAddCard = () => {
  newCardTitle.value = ''
  showAddCard.value = false
}

// Drag and Drop handlers
const handleColumnDragStart = (event: DragEvent) => {
  isDragging.value = true
  event.dataTransfer!.effectAllowed = 'move'
  event.dataTransfer!.setData('text/plain', JSON.stringify({
    type: 'column',
    columnId: props.column.id,
    currentPosition: props.column.position
  }))
}

const handleColumnDragEnd = () => {
  isDragging.value = false
  isDragOver.value = false
}

const handleDragLeave = (event: DragEvent) => {
  // Only remove drag over state if we're actually leaving the column
  const currentTarget = event.currentTarget as Element
  const relatedTarget = event.relatedTarget as Node
  if (!currentTarget.contains(relatedTarget)) {
    isDragOver.value = false
    dropPosition.value = -1
  }
}

const handleCardDragStart = (cardData: KanbanCard) => {
  console.log('KanbanColumn: Card drag started:', cardData.title)
  draggedCard.value = cardData
}

const handleCardDragEnd = () => {
  draggedCard.value = null
  isDragOver.value = false
  dropPosition.value = -1
}

const handleCardDrop = (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = false
  dropPosition.value = -1

  console.log('KanbanColumn: Card drop event triggered')

  try {
    // Try to get drag data from global state first
    let data = getDraggedCardData()

    // Fallback to dataTransfer if global state is not available
    if (!data) {
      const dragData = event.dataTransfer!.getData('text/plain')
      console.log('KanbanColumn: Drag data from dataTransfer:', dragData)
      data = JSON.parse(dragData)
    }

    console.log('KanbanColumn: Using drag data:', data)

    if (data && data.type === 'card') {
      // Calculate drop position based on mouse position
      const rect = cardsContainer.value!.getBoundingClientRect()
      const y = event.clientY - rect.top
      const cardElements = Array.from(cardsContainer.value!.querySelectorAll('.kanban-card:not(.is-dragging)'))

      let newPosition = filteredCards.value.length

      // Find the position to insert the card
      for (let i = 0; i < cardElements.length; i++) {
        const cardRect = cardElements[i].getBoundingClientRect()
        const cardMiddle = cardRect.top - rect.top + cardRect.height / 2

        if (y < cardMiddle) {
          newPosition = i
          break
        }
      }

      // If dropping in the same column, adjust position for cards that come after the dragged card
      if (data.sourceColumnId === props.column.id && newPosition > data.currentPosition) {
        newPosition = Math.max(0, newPosition - 1)
      }

      // Only emit if it's actually a different position or column
      if (data.sourceColumnId !== props.column.id || newPosition !== data.currentPosition) {
        console.log('Dropping card:', {
          cardId: data.cardId,
          from: data.sourceColumnId,
          to: props.column.id,
          oldPosition: data.currentPosition,
          newPosition
        })

        emit('drag:card', {
          cardId: data.cardId,
          sourceColumnId: data.sourceColumnId,
          targetColumnId: props.column.id,
          newPosition
        })
      }
    }
  } catch (error) {
    console.error('Error handling card drop:', error)
  }
}

const handleColumnDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'move'

  try {
    const data = JSON.parse(event.dataTransfer!.getData('text/plain'))
    if (data.type === 'column' && data.columnId !== props.column.id) {
      isDragOver.value = true
    }
  } catch (error) {
    // Ignore parsing errors during drag over
  }
}

const handleColumnDrop = (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = false

  try {
    const data = JSON.parse(event.dataTransfer!.getData('text/plain'))

    if (data.type === 'column' && data.columnId !== props.column.id) {
      // Calculate new position based on drop location
      const rect = (event.currentTarget as Element).getBoundingClientRect()
      const x = event.clientX - rect.left
      const newPosition = x < rect.width / 2 ? props.column.position : props.column.position + 1

      emit('drag:column', {
        columnId: data.columnId,
        newPosition: Math.min(newPosition, props.board.columns.length - 1)
      })
    }
  } catch (error) {
    console.error('Error handling column drop:', error)
  }
}

const handleCardDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'move'

  // Check if we have a dragged card (from global state or local state)
  if (globalDraggedCard.value || draggedCard.value) {
    isDragOver.value = true

    // Calculate drop position for visual feedback
    const rect = cardsContainer.value!.getBoundingClientRect()
    const y = event.clientY - rect.top
    const cardElements = Array.from(cardsContainer.value!.querySelectorAll('.kanban-card:not(.is-dragging)'))

    let newPosition = filteredCards.value.length

    // Find the position to insert the card
    for (let i = 0; i < cardElements.length; i++) {
      const cardRect = cardElements[i].getBoundingClientRect()
      const cardMiddle = cardRect.top - rect.top + cardRect.height / 2

      if (y < cardMiddle) {
        newPosition = i
        break
      }
    }

    dropPosition.value = newPosition
    console.log('KanbanColumn: Drag over, drop position:', newPosition)
  } else {
    // Fallback: try to parse drag data (might not work in all browsers during dragover)
    try {
      const data = JSON.parse(event.dataTransfer!.getData('text/plain'))
      if (data.type === 'card') {
        isDragOver.value = true
      }
    } catch (error) {
      // Ignore parsing errors during drag over
    }
  }
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target || !target.closest('.dropdown')) {
    showDropdown.value = false
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.kanban-column {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 200px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: grab;
}

.kanban-column:active {
  cursor: grabbing;
}

.kanban-column:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.kanban-column.is-dragging {
  opacity: 0.6;
  transform: rotate(2deg) scale(1.02);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.kanban-column.drag-over {
  border: 2px dashed #007bff;
  background: rgba(0, 123, 255, 0.05);
  transform: scale(1.02);
}

.cards-container.drag-over {
  background: rgba(0, 123, 255, 0.03);
  border-radius: 6px;
}

.drop-indicator {
  height: 2px;
  background: #007bff;
  margin: 4px 0;
  border-radius: 1px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.drop-indicator.active {
  opacity: 1;
}

.column-header {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.column-title-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.column-title-input {
  border: none;
  background: transparent;
  font-weight: 600;
  font-size: 0.9rem;
  color: #2c3e50;
  outline: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  flex: 1;
  transition: background-color 0.2s;
}

.column-title-input:hover,
.column-title-input:focus {
  background: #f8f9fa;
}

.card-count {
  background: #e9ecef;
  color: #6c757d;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  min-width: 24px;
  text-align: center;
}

.column-actions {
  position: relative;
}

.cards-container {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.add-card-button {
  background: transparent;
  border: 2px dashed #dee2e6;
  border-radius: 6px;
  padding: 0.75rem;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.add-card-button:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
  color: #495057;
}

.add-card-form {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.75rem;
}

.add-card-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.color-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.color-picker {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  min-width: 300px;
}

.color-options {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.5rem;
  margin-top: 1rem;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.is-selected {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.color-option.is-clear {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  color: #6c757d;
}

/* Scrollbar styling */
.cards-container::-webkit-scrollbar {
  width: 6px;
}

.cards-container::-webkit-scrollbar-track {
  background: transparent;
}

.cards-container::-webkit-scrollbar-thumb {
  background: #dee2e6;
  border-radius: 3px;
}

.cards-container::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}

/* Column color utility classes */
.column-color-red {
  background: linear-gradient(135deg, #ff6b6b15, #ff6b6b08);
  border-top: 3px solid #ff6b6b;
}

.column-color-orange {
  background: linear-gradient(135deg, #ffa72615, #ffa72608);
  border-top: 3px solid #ffa726;
}

.column-color-yellow {
  background: linear-gradient(135deg, #ffeb3b15, #ffeb3b08);
  border-top: 3px solid #ffeb3b;
}

.column-color-green {
  background: linear-gradient(135deg, #66bb6a15, #66bb6a08);
  border-top: 3px solid #66bb6a;
}

.column-color-blue {
  background: linear-gradient(135deg, #42a5f515, #42a5f508);
  border-top: 3px solid #42a5f5;
}

.column-color-purple {
  background: linear-gradient(135deg, #ab47bc15, #ab47bc08);
  border-top: 3px solid #ab47bc;
}

.column-color-pink {
  background: linear-gradient(135deg, #ec407a15, #ec407a08);
  border-top: 3px solid #ec407a;
}

.column-color-teal {
  background: linear-gradient(135deg, #26a69a15, #26a69a08);
  border-top: 3px solid #26a69a;
}

.column-color-indigo {
  background: linear-gradient(135deg, #5c6bc015, #5c6bc008);
  border-top: 3px solid #5c6bc0;
}

.column-color-brown {
  background: linear-gradient(135deg, #8d6e6315, #8d6e6308);
  border-top: 3px solid #8d6e63;
}

.column-color-grey {
  background: linear-gradient(135deg, #78909c15, #78909c08);
  border-top: 3px solid #78909c;
}

/* Color option utility classes */
.color-option-red { background-color: #ff6b6b; }
.color-option-orange { background-color: #ffa726; }
.color-option-yellow { background-color: #ffeb3b; }
.color-option-green { background-color: #66bb6a; }
.color-option-blue { background-color: #42a5f5; }
.color-option-purple { background-color: #ab47bc; }
.color-option-pink { background-color: #ec407a; }
.color-option-teal { background-color: #26a69a; }
.color-option-indigo { background-color: #5c6bc0; }
.color-option-brown { background-color: #8d6e63; }
.color-option-grey { background-color: #78909c; }
</style>
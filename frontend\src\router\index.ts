import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

// All views are now lazy-loaded with route-specific code splitting for better performance
// Each route bundle is optimized to stay under 100KB

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    // Authentication routes - grouped into auth chunk
    {
      path: '/login',
      name: 'Login',
      component: () => import(/* webpackChunkName: "auth" */ '../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import(/* webpackChunkName: "auth" */ '../views/RegisterView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/forgot-password',
      name: 'ForgotPassword',
      component: () => import(/* webpackChunkName: "auth" */ '../views/ForgotPasswordView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/reset-password',
      name: 'ResetPassword',
      component: () => import(/* webpackChunkName: "auth" */ '../views/ResetPasswordView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/verify-email',
      name: 'EmailVerification',
      component: () => import(/* webpackChunkName: "auth" */ '../views/EmailVerificationView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/auth/callback',
      name: 'AuthCallback',
      component: () => import(/* webpackChunkName: "auth" */ '../views/AuthCallbackView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/auth/error',
      name: 'AuthError',
      component: () => import(/* webpackChunkName: "auth" */ '../views/AuthErrorView.vue'),
      meta: { requiresGuest: true }
    },
    // Dashboard routes - main dashboard chunk (most critical)
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard/notes',
      name: 'Notes',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard/recent',
      name: 'RecentNotes',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard/favorites',
      name: 'FavoriteNotes',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard/archived',
      name: 'ArchivedNotes',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard/notes/:id',
      name: 'Note',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard/shared',
      name: 'SharedNotes',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard/shared/:id',
      name: 'SharedNote',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard/groups',
      name: 'Groups',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard/groups/:id',
      name: 'Group',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard/settings',
      name: 'Settings',
      component: () => import(/* webpackChunkName: "dashboard" */ '../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    // Search functionality - separate chunk for search features
    {
      path: '/dashboard/search',
      name: 'Search',
      component: () => import(/* webpackChunkName: "search" */ '../views/SearchView.vue'),
      meta: { requiresAuth: true }
    },
    // Groups functionality - separate chunk for group management
    {
      path: '/groups',
      name: 'GroupsList',
      component: () => import(/* webpackChunkName: "groups" */ '../views/GroupsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/groups/:id',
      name: 'GroupDetail',
      component: () => import(/* webpackChunkName: "groups" */ '../views/GroupDetailView.vue'),
      meta: { requiresAuth: true }
    },
    // Shared content - separate chunk for public access
    {
      path: '/shared/:token',
      name: 'SharedNoteView',
      component: () => import(/* webpackChunkName: "shared" */ '../views/SharedNoteView.vue'),
      meta: { requiresGuest: false } // Public access, no auth required
    },
    // Board functionality - separate chunk for board features
    {
      path: '/boards',
      name: 'BoardsList',
      component: () => import(/* webpackChunkName: "boards" */ '../views/BoardsListView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/boards/new',
      name: 'NewBoard',
      component: () => import(/* webpackChunkName: "boards" */ '../views/NewBoardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/boards/:id',
      name: 'BoardView',
      component: () => import(/* webpackChunkName: "boards" */ '../views/BoardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/boards/:id/settings',
      name: 'BoardSettings',
      component: () => import(/* webpackChunkName: "boards" */ '../views/BoardSettingsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/board/:id',
      name: 'BoardViewLegacy',
      redirect: to => `/boards/${to.params.id}`,
      meta: { requiresAuth: true }
    },
    {
      path: '/shared/board/:token',
      name: 'SharedBoardView',
      component: () => import(/* webpackChunkName: "boards" */ '../views/SharedBoardView.vue'),
      meta: { requiresGuest: false } // Public access, no auth required
    },
    // Admin functionality - separate chunk for admin features
    {
      path: '/admin',
      name: 'AdminDashboard',
      component: () => import(/* webpackChunkName: "admin" */ '../views/AdminDashboardView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/users',
      name: 'AdminUsers',
      component: () => import(/* webpackChunkName: "admin" */ '../views/AdminUsersView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/reports',
      name: 'AdminReports',
      component: () => import(/* webpackChunkName: "admin" */ '../views/AdminReportsView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/system',
      name: 'AdminSystem',
      component: () => import(/* webpackChunkName: "admin" */ '../views/AdminSystemView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/metrics',
      name: 'AdminMetrics',
      component: () => import(/* webpackChunkName: "admin" */ '../views/AdminMetricsView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/notifications',
      name: 'AdminNotifications',
      component: () => import(/* webpackChunkName: "admin" */ '../views/AdminNotificationsView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/performance',
      name: 'AdminPerformance',
      component: () => import(/* webpackChunkName: "admin" */ '../views/AdminPerformanceView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    }
  ]
})

// Navigation guards with error handling
router.beforeEach(async (to, from, next) => {
  try {
    const authStore = useAuthStore()

    // Initialize auth in background if needed, but don't block navigation
    // Only initialize if we have a token, no user data, haven't initialized, and are going to a protected route
    if (authStore.token && !authStore.user && !authStore.isInitialized && !authStore.isInitializing && to.meta.requiresAuth) {
      console.log('Router guard: Starting background auth initialization for protected route:', to.path)
      // Start auth initialization in background without blocking navigation - use longer timeout
      authStore.initializeAuthWithTimeout(8000).catch(error => {
        console.warn('Background auth initialization failed:', error)
      })
    }

    // Dispatch navigation event for store management
    try {
      window.dispatchEvent(new CustomEvent('router-navigation', {
        detail: { from: from.path, to: to.path }
      }))
    } catch (error) {
      console.warn('Failed to dispatch navigation event:', error)
    }

    // Special handling for dashboard navigation from admin/group pages
    if (to.path === '/dashboard' && (from.path.startsWith('/admin') || from.path.startsWith('/groups'))) {
      console.log('Navigating to dashboard from admin/group page, ensuring stores are ready')

      // Start background auth initialization if needed, but don't block navigation
      if (authStore.token && !authStore.user && !authStore.isInitialized && !authStore.isInitializing) {
        console.log('Starting background auth initialization for dashboard navigation')
        authStore.initializeAuthWithTimeout(6000).catch(error => {
          console.warn('Background auth initialization failed:', error)
        })
      }
    }

    const isAuthenticated = authStore.isAuthenticated
    const hasToken = !!authStore.token
    const isAdmin = authStore.isAdmin
    const isLoggingOut = authStore.isLoggingOut
    const requiresAuth = to.meta.requiresAuth
    const requiresGuest = to.meta.requiresGuest
    const requiresAdmin = to.meta.requiresAdmin

    // Check for logout in progress in sessionStorage
    const logoutInProgress = sessionStorage.getItem('logout_in_progress')
    
    // If logout is in progress or completed, user should not be considered authenticated
    // and should be redirected to login unless already going there
    if (logoutInProgress || isLoggingOut) {
      console.log('Logout in progress or completed, redirecting to login', {
        logoutInProgress,
        isLoggingOut,
        to: to.path,
        from: from.path
      })
      if (to.name !== 'Login') {
        // Force redirect to login and clear the logout flag
        sessionStorage.removeItem('logout_in_progress')
        next({ name: 'Login' })
        return
      } else {
        // Already going to login, clear the flag and allow navigation
        sessionStorage.removeItem('logout_in_progress')
        next()
        return
      }
    }

    // Additional check: if we're going to login and there's no token, allow it
    if (to.name === 'Login' && !hasToken) {
      console.log('No token found, allowing navigation to login')
      next()
      return
    }

    // Be more lenient with auth checks - if we have a token, assume user is authenticated
    // even if the full auth initialization hasn't completed yet
    // But if we're logging out, don't consider the user authenticated
    const effectivelyAuthenticated = (isAuthenticated || hasToken) && !isLoggingOut && !logoutInProgress
    
    console.log('Router guard auth check:', {
      to: to.path,
      from: from.path,
      isAuthenticated,
      hasToken,
      isLoggingOut,
      logoutInProgress,
      effectivelyAuthenticated,
      requiresAuth,
      requiresGuest,
      requiresAdmin
    })

    if (requiresAuth && !effectivelyAuthenticated) {
      // Redirect to login with return URL
      next({
        name: 'Login',
        query: { redirect: to.fullPath }
      })
    } else if (requiresGuest && effectivelyAuthenticated) {
      // Special case: if navigating to login and logout is in progress, allow it
      if (to.name === 'Login' && (logoutInProgress || !hasToken)) {
        if (logoutInProgress) {
          console.log('Logout in progress, allowing navigation to login')
          sessionStorage.removeItem('logout_in_progress')
        }
        next()
      } else {
        // Redirect authenticated users away from auth pages
        next({ name: 'Dashboard' })
      }
    } else if (requiresAdmin && !isAdmin && effectivelyAuthenticated) {
      // For admin routes, still check isAdmin but only if we're authenticated
      // If auth is still initializing, allow navigation and let the component handle it
      if (authStore.isInitialized && !isAdmin) {
        next({ name: 'Dashboard' })
      } else {
        // Auth still initializing, allow navigation
        next()
      }
    } else {
      next()
    }
  } catch (error) {
    console.error('Router guard error:', error)
    // Continue navigation even if guard fails
    next()
  }
})

// Add afterEach hook for debugging
router.afterEach((to, from) => {
  console.log(`Navigation completed from ${from.path} to ${to.path}`)
})

export default router
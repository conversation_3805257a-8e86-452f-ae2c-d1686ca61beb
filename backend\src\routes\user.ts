import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { validateUpdateSettings, validateUpdateProfile } from '../middleware/validation';
import { validationChains, sanitizeAndValidate } from '../middleware/enhancedValidation';
import { authenticateToken } from '../middleware/auth';
import { generalRateLimit } from '../middleware/rateLimiting';
import { cacheUserSettings, invalidateUserCache } from '../middleware/caching';

const router = Router();

// All user routes require authentication
router.use(authenticateToken);

// Apply rate limiting to all user routes
router.use(generalRateLimit);

// User settings endpoints
router.get('/settings', cacheUserSettings, UserController.getSettings as any);
router.put('/settings', sanitizeAndValidate(validationChains.updateSettings), invalidateUserCache, UserController.updateSettings as any);

// User profile endpoints
router.put('/profile', sanitizeAndValidate(validationChains.updateProfile), invalidateUserCache, UserController.updateProfile as any);

// Data export endpoint
router.get('/export', UserController.exportData as any);

export default router;
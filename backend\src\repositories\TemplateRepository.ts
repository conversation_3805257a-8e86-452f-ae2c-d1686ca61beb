import { getDatabase } from '../config/database';
import { 
  Template, 
  CreateTemplateData, 
  UpdateTemplateData, 
  TemplateFilters, 
  PaginationOptions,
  TemplateModel 
} from '../models/Template';

export class TemplateRepository {
  private static getDb() {
    return getDatabase();
  }

  static async create(templateData: CreateTemplateData): Promise<Template> {
    const id = TemplateModel.generateId();
    const now = new Date().toISOString();
    const metadata = TemplateModel.updateMetadata(TemplateModel.getDefaultMetadata(), templateData.metadata);

    const query = `
      INSERT INTO templates (
        id, user_id, group_id, name, description, note_type, content, is_public, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      templateData.userId,
      templateData.groupId || null,
      templateData.name,
      templateData.description || null,
      templateData.noteType,
      templateData.content,
      templateData.isPublic || false,
      JSON.stringify(metadata),
      now,
      now
    ];

    return new Promise((resolve, reject) => {
      TemplateRepository.getDb().run(query, params, function(err) {
        if (err) {
          reject(err);
          return;
        }

        // Add tags if provided
        if (templateData.tags && templateData.tags.length > 0) {
          TemplateRepository.addTagsToTemplate(id, templateData.tags, templateData.userId)
            .then(() => TemplateRepository.findById(id))
            .then(template => {
              if (!template) {
                reject(new Error('Failed to create template'));
                return;
              }
              resolve(template);
            })
            .catch(reject);
        } else {
          TemplateRepository.findById(id)
            .then(template => {
              if (!template) {
                reject(new Error('Failed to create template'));
                return;
              }
              resolve(template);
            })
            .catch(reject);
        }
      });
    });
  }

  static async findById(id: string): Promise<Template | null> {
    const query = 'SELECT * FROM templates WHERE id = ?';
    
    return new Promise((resolve, reject) => {
      TemplateRepository.getDb().get(query, [id], async (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        
        if (!row) {
          resolve(null);
          return;
        }

        const template = this.mapRowToTemplate(row);
        
        // Add tags
        try {
          const tags = await this.getTagsForTemplate(id);
          template.tags = tags;
        } catch (error) {
          console.error('Error fetching template tags:', error);
          template.tags = [];
        }

        resolve(template);
      });
    });
  }

  static async findByFilters(
    filters: TemplateFilters, 
    pagination: PaginationOptions
  ): Promise<{ templates: Template[]; total: number }> {
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    // Add filters
    if (filters.userId) {
      whereClause += ' AND (user_id = ? OR is_public = 1)';
      params.push(filters.userId);
    }

    if (filters.groupId) {
      whereClause += ' AND group_id = ?';
      params.push(filters.groupId);
    }

    if (filters.noteType) {
      whereClause += ' AND note_type = ?';
      params.push(filters.noteType);
    }

    if (filters.isPublic !== undefined) {
      whereClause += ' AND is_public = ?';
      params.push(filters.isPublic);
    }

    if (filters.search) {
      whereClause += ' AND (name LIKE ? OR description LIKE ? OR content LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Handle tag filtering
    if (filters.tags && filters.tags.length > 0) {
      const tagPlaceholders = filters.tags.map(() => '?').join(',');
      whereClause += ` AND id IN (
        SELECT DISTINCT tt.template_id 
        FROM template_tags tt 
        JOIN tags t ON tt.tag_id = t.id 
        WHERE t.name IN (${tagPlaceholders})
      )`;
      params.push(...filters.tags);
    }

    // Handle category filtering
    if (filters.category) {
      whereClause += ` AND JSON_EXTRACT(metadata, '$.category') = ?`;
      params.push(filters.category);
    }

    // Count query
    const countQuery = `SELECT COUNT(*) as total FROM templates ${whereClause}`;
    
    // Main query with pagination and sorting
    const sortBy = pagination.sortBy || 'updated_at';
    const sortOrder = pagination.sortOrder || 'desc';
    const offset = (pagination.page - 1) * pagination.limit;

    const mainQuery = `
      SELECT * FROM templates 
      ${whereClause} 
      ORDER BY ${sortBy} ${sortOrder.toUpperCase()} 
      LIMIT ? OFFSET ?
    `;

    const mainParams = [...params, pagination.limit, offset];

    return new Promise((resolve, reject) => {
      // Get total count
      TemplateRepository.getDb().get(countQuery, params, (err, countRow) => {
        if (err) {
          reject(err);
          return;
        }

        const total = (countRow as any).total;

        // Get templates
        TemplateRepository.getDb().all(mainQuery, mainParams, async (err, rows) => {
          if (err) {
            reject(err);
            return;
          }

          try {
            const templates = await Promise.all(
              rows.map(async (row) => {
                const template = this.mapRowToTemplate(row);
                
                // Add tags
                try {
                  const tags = await this.getTagsForTemplate(template.id);
                  template.tags = tags;
                } catch (error) {
                  console.error('Error fetching template tags:', error);
                  template.tags = [];
                }

                return template;
              })
            );

            resolve({ templates, total });
          } catch (error) {
            reject(error);
          }
        });
      });
    });
  }

  static async update(id: string, updateData: UpdateTemplateData, userId: string): Promise<Template> {
    const fields: string[] = [];
    const params: any[] = [];

    if (updateData.name !== undefined) {
      fields.push('name = ?');
      params.push(updateData.name);
    }

    if (updateData.description !== undefined) {
      fields.push('description = ?');
      params.push(updateData.description);
    }

    if (updateData.content !== undefined) {
      fields.push('content = ?');
      params.push(updateData.content);
    }

    if (updateData.isPublic !== undefined) {
      fields.push('is_public = ?');
      params.push(updateData.isPublic);
    }

    if (updateData.metadata !== undefined) {
      const currentTemplate = await this.findById(id);
      if (currentTemplate) {
        const updatedMetadata = TemplateModel.updateMetadata(currentTemplate.metadata, updateData.metadata);
        fields.push('metadata = ?');
        params.push(JSON.stringify(updatedMetadata));
      }
    }

    if (fields.length === 0 && !updateData.tags) {
      // Nothing to update, return current template
      const template = await this.findById(id);
      if (!template) {
        throw new Error('Template not found');
      }
      return template;
    }

    if (fields.length > 0) {
      fields.push('updated_at = ?');
      params.push(new Date().toISOString());
      params.push(id);

      const query = `UPDATE templates SET ${fields.join(', ')} WHERE id = ?`;

      await new Promise<void>((resolve, reject) => {
        TemplateRepository.getDb().run(query, params, function(err) {
          if (err) {
            reject(err);
            return;
          }
          
          if (this.changes === 0) {
            reject(new Error('Template not found'));
            return;
          }

          resolve();
        });
      });
    }

    // Update tags if provided
    if (updateData.tags !== undefined) {
      await this.updateTemplateTags(id, updateData.tags, userId);
    }

    // Fetch the updated template
    const updatedTemplate = await this.findById(id);
    if (!updatedTemplate) {
      throw new Error('Template not found after update');
    }
    return updatedTemplate;
  }

  static async delete(id: string): Promise<void> {
    const query = 'DELETE FROM templates WHERE id = ?';

    return new Promise((resolve, reject) => {
      TemplateRepository.getDb().run(query, [id], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        if (this.changes === 0) {
          reject(new Error('Template not found'));
          return;
        }

        resolve();
      });
    });
  }

  static async incrementUsageCount(id: string): Promise<void> {
    const template = await this.findById(id);
    if (!template) {
      throw new Error('Template not found');
    }

    const updatedMetadata = TemplateModel.incrementUsageCount(template.metadata);
    
    const query = 'UPDATE templates SET metadata = ? WHERE id = ?';
    
    return new Promise((resolve, reject) => {
      TemplateRepository.getDb().run(query, [JSON.stringify(updatedMetadata), id], function(err) {
        if (err) {
          reject(err);
          return;
        }
        resolve();
      });
    });
  }

  static async getCategories(): Promise<string[]> {
    const query = `
      SELECT DISTINCT JSON_EXTRACT(metadata, '$.category') as category 
      FROM templates 
      WHERE JSON_EXTRACT(metadata, '$.category') IS NOT NULL
      ORDER BY category
    `;

    return new Promise((resolve, reject) => {
      TemplateRepository.getDb().all(query, [], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }

        const categories = (rows as any[])
          .map(row => row.category)
          .filter(category => category && category !== 'null');

        resolve(categories);
      });
    });
  }

  // Tag management
  private static async addTagsToTemplate(templateId: string, tagNames: string[], userId: string): Promise<void> {
    for (const tagName of tagNames) {
      try {
        // Create tag if it doesn't exist
        let tagId;
        try {
          const { NoteRepository } = await import('./NoteRepository');
          const tag = await NoteRepository.createTag(tagName.trim(), userId);
          tagId = tag.id;
        } catch (error) {
          // Tag might already exist, get existing tags and find it
          const { NoteRepository } = await import('./NoteRepository');
          const existingTags = await NoteRepository.getTagsByUserId(userId);
          const existingTag = existingTags.find(t => t.name === tagName.trim());
          if (existingTag) {
            tagId = existingTag.id;
          }
        }

        if (tagId) {
          await this.addTagToTemplate(templateId, tagId);
        }
      } catch (error) {
        console.error('Error adding tag to template:', error);
        // Continue with other tags
      }
    }
  }

  private static async addTagToTemplate(templateId: string, tagId: string): Promise<void> {
    const query = 'INSERT OR IGNORE INTO template_tags (template_id, tag_id) VALUES (?, ?)';

    return new Promise((resolve, reject) => {
      TemplateRepository.getDb().run(query, [templateId, tagId], function(err) {
        if (err) {
          reject(err);
          return;
        }
        resolve();
      });
    });
  }

  private static async removeTagFromTemplate(templateId: string, tagId: string): Promise<void> {
    const query = 'DELETE FROM template_tags WHERE template_id = ? AND tag_id = ?';

    return new Promise((resolve, reject) => {
      TemplateRepository.getDb().run(query, [templateId, tagId], function(err) {
        if (err) {
          reject(err);
          return;
        }
        resolve();
      });
    });
  }

  private static async getTagsForTemplate(templateId: string): Promise<string[]> {
    const query = `
      SELECT t.name FROM tags t
      JOIN template_tags tt ON t.id = tt.tag_id
      WHERE tt.template_id = ?
      ORDER BY t.name
    `;

    return new Promise((resolve, reject) => {
      TemplateRepository.getDb().all(query, [templateId], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }

        const tags = (rows as any[]).map(row => row.name);
        resolve(tags);
      });
    });
  }

  private static async updateTemplateTags(templateId: string, tagNames: string[], userId: string): Promise<void> {
    // Get current tags
    const currentTags = await this.getTagsForTemplate(templateId);
    
    // Remove tags that are no longer needed
    for (const currentTag of currentTags) {
      if (!tagNames.includes(currentTag)) {
        // Find tag ID and remove
        const { NoteRepository } = await import('./NoteRepository');
        const existingTags = await NoteRepository.getTagsByUserId(userId);
        const tagToRemove = existingTags.find(t => t.name === currentTag);
        if (tagToRemove) {
          await this.removeTagFromTemplate(templateId, tagToRemove.id);
        }
      }
    }

    // Add new tags
    const newTags = tagNames.filter(tagName => !currentTags.includes(tagName));
    if (newTags.length > 0) {
      await this.addTagsToTemplate(templateId, newTags, userId);
    }
  }

  private static mapRowToTemplate(row: any): Template {
    return {
      id: row.id,
      userId: row.user_id,
      groupId: row.group_id,
      name: row.name,
      description: row.description,
      noteType: row.note_type,
      content: row.content,
      isPublic: Boolean(row.is_public),
      tags: [], // Will be populated separately
      metadata: JSON.parse(row.metadata || '{}'),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}
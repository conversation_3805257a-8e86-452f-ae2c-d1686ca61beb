<template>
  <div class="tag-input-container">
    <div class="field">
      <label v-if="label" class="label" :class="labelClass">{{ label }}</label>
      <div class="control">
        <div class="tag-input" :class="{ 'is-focused': isFocused }">
          <!-- Selected Tags -->
          <div class="selected-tags">
            <span
              v-for="(tag, index) in selectedTags"
              :key="`selected-${tag.id || tag.name}-${index}`"
              class="tag"
              :class="tagClass"
              :style="getTagStyle(tag)"
            >
              <span v-if="getTagIcon(tag)" class="icon is-small">
                <i :class="getTagIcon(tag)"></i>
              </span>
              {{ tag.name }}
              <button
                v-if="!readonly"
                class="delete is-small"
                @click="removeTag(index)"
                :disabled="disabled"
              ></button>
            </span>
          </div>

          <!-- Input Field -->
          <input
            ref="inputRef"
            v-model="inputValue"
            type="text"
            class="tag-input-field"
            :placeholder="effectivePlaceholder"
            :disabled="disabled"
            :readonly="readonly"
            @input="handleInput"
            @keydown="handleKeydown"
            @focus="handleFocus"
            @blur="handleBlur"
            @paste="handlePaste"
          />
        </div>
      </div>

      <!-- Suggestions Dropdown -->
      <div 
        v-if="showSuggestions && (filteredSuggestions.length > 0 || showCreateOption)"
        class="dropdown is-active"
      >
        <div class="dropdown-menu tag-suggestions" role="menu">
          <div class="dropdown-content">
            <!-- Create New Tag Option -->
            <div v-if="showCreateOption" class="suggestion-section">
              <a
                class="dropdown-item suggestion-item create-tag"
                @click="createAndAddTag(inputValue.trim())"
              >
                <span class="icon is-small">
                  <i class="fas fa-plus"></i>
                </span>
                <span>Create "{{ inputValue.trim() }}"</span>
              </a>
              <hr class="dropdown-divider" v-if="filteredSuggestions.length > 0">
            </div>

            <!-- Existing Tag Suggestions -->
            <div v-if="filteredSuggestions.length > 0" class="suggestion-section">
              <a
                v-for="(suggestion, index) in filteredSuggestions"
                :key="`suggestion-${suggestion.id || suggestion.name}-${index}`"
                class="dropdown-item suggestion-item"
                :class="{ 'is-active': highlightedIndex === index }"
                @click="selectSuggestion(suggestion)"
              >
                <span class="icon is-small" :style="{ color: suggestion.color }">
                  <i :class="suggestion.icon || 'fas fa-tag'"></i>
                </span>
                <span v-html="highlightMatch(suggestion.name, inputValue)"></span>
                <span v-if="suggestion.count" class="tag is-small is-light ml-auto">
                  {{ suggestion.count }}
                </span>
              </a>
            </div>

            <!-- No Suggestions Message -->
            <div v-if="filteredSuggestions.length === 0 && !showCreateOption && inputValue.trim()" class="suggestion-section">
              <div class="dropdown-item">
                <span class="has-text-grey">No matching tags found</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Help Text -->
      <p v-if="help" class="help" :class="helpClass">{{ help }}</p>
      
      <!-- Error Message -->
      <p v-if="error" class="help is-danger">{{ error }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import type { Tag } from '../../services/noteService'
import type { Tag as TagStoreTag } from '../../stores/tags'

interface TagSuggestion extends TagStoreTag {
  // TagStoreTag already has count property
}

interface Props {
  modelValue: Tag[]
  suggestions?: TagSuggestion[]
  placeholder?: string
  label?: string
  help?: string
  error?: string
  disabled?: boolean
  readonly?: boolean
  allowCreate?: boolean
  maxTags?: number
  tagClass?: string
  labelClass?: string
  helpClass?: string
  validateTag?: (tagName: string) => boolean | string
  debounceMs?: number
}

interface Emits {
  (e: 'update:modelValue', tags: Tag[]): void
  (e: 'tag-added', tag: Tag): void
  (e: 'tag-removed', tag: Tag, index: number): void
  (e: 'tag-created', tagName: string): void
  (e: 'input-change', value: string): void
  (e: 'focus'): void
  (e: 'blur'): void
}

const props = withDefaults(defineProps<Props>(), {
  suggestions: () => [],
  placeholder: 'Add tags...',
  allowCreate: true,
  maxTags: 20,
  tagClass: 'is-primary',
  validateTag: () => true,
  debounceMs: 300
})

const emit = defineEmits<Emits>()

// Local state
const inputRef = ref<HTMLInputElement>()
const inputValue = ref('')
const isFocused = ref(false)
const showSuggestions = ref(false)
const highlightedIndex = ref(-1)
const debounceTimeout = ref<number>()

// Computed
const selectedTags = computed(() => props.modelValue || [])

const effectivePlaceholder = computed(() => {
  if (selectedTags.value.length === 0) {
    return props.placeholder
  }
  return props.maxTags && selectedTags.value.length >= props.maxTags 
    ? `Maximum ${props.maxTags} tags reached`
    : 'Add more tags...'
})

const filteredSuggestions = computed(() => {
  if (!inputValue.value.trim()) {
    return props.suggestions.slice(0, 10)
  }

  const query = inputValue.value.toLowerCase().trim()
  const selectedTagNames = selectedTags.value.map(tag => tag.name.toLowerCase())

  return props.suggestions
    .filter(suggestion => 
      suggestion.name.toLowerCase().includes(query) &&
      !selectedTagNames.includes(suggestion.name.toLowerCase())
    )
    .slice(0, 10)
})

const showCreateOption = computed(() => {
  if (!props.allowCreate || !inputValue.value.trim()) {
    return false
  }

  const trimmedInput = inputValue.value.trim()
  const selectedTagNames = selectedTags.value.map(tag => tag.name.toLowerCase())
  const suggestionNames = props.suggestions.map(s => s.name.toLowerCase())

  return (
    trimmedInput.length > 0 &&
    !selectedTagNames.includes(trimmedInput.toLowerCase()) &&
    !suggestionNames.includes(trimmedInput.toLowerCase()) &&
    isValidTagName(trimmedInput)
  )
})

// Methods
const handleInput = () => {
  // Clear existing timeout
  if (debounceTimeout.value) {
    clearTimeout(debounceTimeout.value)
  }

  // Set new timeout for debounced input
  debounceTimeout.value = window.setTimeout(() => {
    emit('input-change', inputValue.value)
    // Show suggestions when user types or when input has content
    showSuggestions.value = inputValue.value.length > 0 || isFocused.value
    highlightedIndex.value = -1
  }, props.debounceMs)
}

const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0 && filteredSuggestions.value[highlightedIndex.value]) {
        selectSuggestion(filteredSuggestions.value[highlightedIndex.value])
      } else if (showCreateOption.value) {
        createAndAddTag(inputValue.value.trim())
      }
      break

    case 'ArrowDown':
      event.preventDefault()
      if (showSuggestions.value) {
        const maxIndex = filteredSuggestions.value.length - 1
        highlightedIndex.value = highlightedIndex.value < maxIndex 
          ? highlightedIndex.value + 1 
          : 0
      }
      break

    case 'ArrowUp':
      event.preventDefault()
      if (showSuggestions.value) {
        const maxIndex = filteredSuggestions.value.length - 1
        highlightedIndex.value = highlightedIndex.value > 0 
          ? highlightedIndex.value - 1 
          : maxIndex
      }
      break

    case 'Escape':
      hideSuggestions()
      break

    case 'Backspace':
      if (!inputValue.value && selectedTags.value.length > 0) {
        removeTag(selectedTags.value.length - 1)
      }
      break

    case 'Tab':
      if (showSuggestions.value && highlightedIndex.value >= 0) {
        event.preventDefault()
        selectSuggestion(filteredSuggestions.value[highlightedIndex.value])
      }
      break

    case ',':
    case ';':
      event.preventDefault()
      if (inputValue.value.trim()) {
        if (showCreateOption.value) {
          createAndAddTag(inputValue.value.trim())
        }
      }
      break
  }
}

const handleFocus = () => {
  isFocused.value = true
  // Only show suggestions if there's input or if user starts typing
  // Don't auto-show suggestions on focus
  emit('focus')
}

const handleBlur = () => {
  // Delay hiding suggestions to allow clicking on them
  setTimeout(() => {
    isFocused.value = false
    showSuggestions.value = false
    highlightedIndex.value = -1
    emit('blur')
  }, 200)
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const pastedText = event.clipboardData?.getData('text') || ''
  const tags = pastedText
    .split(/[,;\n\t]/)
    .map(tag => tag.trim())
    .filter(tag => tag && isValidTagName(tag))

  tags.forEach(tagName => {
    if (!isTagSelected(tagName)) {
      createAndAddTag(tagName)
    }
  })
}

const selectSuggestion = (suggestion: TagSuggestion) => {
  if (isTagSelected(suggestion.name) || isMaxTagsReached()) {
    return
  }

  const newTags = [...selectedTags.value, suggestion]
  emit('update:modelValue', newTags)
  emit('tag-added', suggestion)

  inputValue.value = ''
  hideSuggestions()
  focusInput()
}

const createAndAddTag = async (tagName: string) => {
  if (!tagName || isTagSelected(tagName) || isMaxTagsReached()) {
    return
  }

  const validation = props.validateTag(tagName)
  if (validation !== true) {
    // Handle validation error
    return
  }

  // Emit tag creation event first - parent will handle creating it in the store
  emit('tag-created', tagName)

  // Create new tag object for local use
  const newTag: Tag = {
    id: `temp-${Date.now()}`, // Temporary ID
    name: tagName,
    userId: '', // Will be set by parent
    createdAt: new Date().toISOString()
  }

  const newTags = [...selectedTags.value, newTag]
  emit('update:modelValue', newTags)
  emit('tag-added', newTag)

  inputValue.value = ''
  hideSuggestions()
  focusInput()
}

const removeTag = (index: number) => {
  if (props.readonly || props.disabled) return

  const removedTag = selectedTags.value[index]
  const newTags = selectedTags.value.filter((_, i) => i !== index)
  
  emit('update:modelValue', newTags)
  emit('tag-removed', removedTag, index)
  
  focusInput()
}

const hideSuggestions = () => {
  showSuggestions.value = false
  highlightedIndex.value = -1
}

const focusInput = () => {
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const isTagSelected = (tagName: string): boolean => {
  return selectedTags.value.some(tag => 
    tag.name.toLowerCase() === tagName.toLowerCase()
  )
}

const isMaxTagsReached = (): boolean => {
  return props.maxTags ? selectedTags.value.length >= props.maxTags : false
}

const isValidTagName = (tagName: string): boolean => {
  if (!tagName || tagName.length === 0) return false
  if (tagName.length > 50) return false
  if (!/^[a-zA-Z0-9\s\-_]+$/.test(tagName)) return false
  return true
}

const highlightMatch = (text: string, query: string): string => {
  if (!query) return text
  
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const getTagStyle = (tag: Tag | TagSuggestion) => {
  const suggestion = props.suggestions.find(s => s.name === tag.name)
  if (suggestion && 'color' in suggestion && suggestion.color) {
    return {
      backgroundColor: suggestion.color,
      borderColor: suggestion.color,
      color: 'white'
    }
  }
  return {}
}

const getTagIcon = (tag: Tag | TagSuggestion) => {
  const suggestion = props.suggestions.find(s => s.name === tag.name)
  return suggestion && 'icon' in suggestion ? suggestion.icon : null
}

// Public methods
const clear = () => {
  emit('update:modelValue', [])
  inputValue.value = ''
  hideSuggestions()
}

const focus = () => {
  focusInput()
}

// Lifecycle
onMounted(() => {
  // Remove auto-focus behavior - let user decide when to focus
})

onUnmounted(() => {
  if (debounceTimeout.value) {
    clearTimeout(debounceTimeout.value)
  }
})

// Expose methods for parent components
defineExpose({
  clear,
  focus
})
</script>

<style scoped>
.tag-input-container {
  position: relative;
}

.tag-input {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-height: 2.5rem;
  padding: 0.375rem 0.75rem;
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.tag-input:hover {
  border-color: #b5b5b5;
}

.tag-input.is-focused {
  border-color: #3273dc;
  box-shadow: 0 0 0 0.125em rgba(50, 115, 220, 0.25);
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-right: 0.5rem;
}

.selected-tags .tag {
  margin-bottom: 0;
}

.tag-input-field {
  flex: 1;
  min-width: 120px;
  border: none;
  outline: none;
  background: transparent;
  font-size: 1rem;
  line-height: 1.5;
}

.tag-input-field::placeholder {
  color: #a0a0a0;
}

.tag-input-field:disabled {
  color: #7a7a7a;
  cursor: not-allowed;
}

.tag-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.suggestion-section:not(:last-child) {
  border-bottom: 1px solid #e0e0e0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s;
}

.suggestion-item:hover,
.suggestion-item.is-active {
  background-color: #f0f0f0;
}

.suggestion-item.create-tag {
  background-color: #f8f9fa;
  border-left: 3px solid #28a745;
}

.suggestion-item.create-tag:hover {
  background-color: #e9ecef;
}

/* Highlight styling */
:deep(mark) {
  background-color: #ffeb3b;
  padding: 0;
  font-weight: bold;
}

/* Disabled state */
.tag-input:has(.tag-input-field:disabled) {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.tag-input:has(.tag-input-field:disabled) .tag .delete {
  display: none;
}

/* Readonly state */
.tag-input:has(.tag-input-field:readonly) .tag .delete {
  display: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .tag-input-field {
    min-width: 80px;
  }
  
  .suggestion-item {
    padding: 0.75rem 1rem;
  }
  
  .tag-suggestions {
    max-height: 150px;
  }
}

/* Animation */
.tag {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}
</style>
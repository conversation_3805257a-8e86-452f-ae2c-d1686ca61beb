import { Request, Response } from 'express';
import { NoteShareRepository } from '../repositories/NoteShareRepository';
import { NoteRepository } from '../repositories/NoteRepository';
import { 
  NoteShareModel, 
  CreateShareData, 
  UpdateShareData, 
  ShareFilters,
  AccessLevel,
  Permission
} from '../models/NoteShare';

export class NoteShareController {
  // POST /api/notes/:id/share - Create a new share for a note
  static async createShare(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const noteId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if note exists and user has access
      const note = await NoteRepository.findById(noteId);
      if (!note) {
        res.status(404).json({ error: 'Note not found' });
        return;
      }

      if (note.userId !== userId) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const {
        accessLevel,
        permissions,
        expiresAt,
        password,
        allowedIps,
        watermark
      } = req.body;

      // Validate required fields
      if (!accessLevel || !permissions) {
        res.status(400).json({ error: 'Access level and permissions are required' });
        return;
      }

      const shareData: CreateShareData = {
        noteId,
        accessLevel: accessLevel as AccessLevel,
        permissions: permissions as Permission[],
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        password,
        allowedIps,
        watermark: Boolean(watermark),
        createdBy: userId
      };

      // Validate share data
      const validation = NoteShareModel.validateShareData(shareData);
      if (!validation.valid) {
        res.status(400).json({ error: 'Invalid share data', details: validation.errors });
        return;
      }

      const share = await NoteShareRepository.create(shareData);
      const sanitizedShare = NoteShareModel.sanitizeShareForResponse(share);

      // Generate share URL (use frontend URL, not backend URL)
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const shareUrl = NoteShareModel.getShareUrl(frontendUrl, share.shareToken);

      res.status(201).json({
        ...sanitizedShare,
        shareUrl
      });
    } catch (error) {
      console.error('Error creating share:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // GET /api/notes/:id/shares - Get all shares for a note
  static async getNoteShares(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const noteId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if note exists and user has access
      const note = await NoteRepository.findById(noteId);
      if (!note) {
        res.status(404).json({ error: 'Note not found' });
        return;
      }

      if (note.userId !== userId) {
        // TODO: Add group permission check when groups are implemented
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const shares = await NoteShareRepository.findByNoteId(noteId);
      const sanitizedShares = shares.map(share => NoteShareModel.sanitizeShareForResponse(share));

      // Add share URLs (use frontend URL, not backend URL)
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const sharesWithUrls = sanitizedShares.map(share => ({
        ...share,
        shareUrl: NoteShareModel.getShareUrl(frontendUrl, share.shareToken),
        isExpired: NoteShareModel.isShareExpired(share as any)
      }));

      res.json({ shares: sharesWithUrls });
    } catch (error) {
      console.error('Error fetching note shares:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // PUT /api/shares/:id - Update a share
  static async updateShare(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const shareId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if share exists and user has access
      const existingShare = await NoteShareRepository.findById(shareId);
      if (!existingShare) {
        res.status(404).json({ error: 'Share not found' });
        return;
      }

      if (existingShare.createdBy !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const {
        accessLevel,
        permissions,
        expiresAt,
        password,
        allowedIps,
        watermark
      } = req.body;

      const updateData: UpdateShareData = {};

      if (accessLevel !== undefined) {
        if (!NoteShareModel.validateAccessLevel(accessLevel)) {
          res.status(400).json({ error: 'Invalid access level' });
          return;
        }
        updateData.accessLevel = accessLevel as AccessLevel;
      }

      if (permissions !== undefined) {
        if (!Array.isArray(permissions) || !NoteShareModel.validatePermissions(permissions)) {
          res.status(400).json({ error: 'Invalid permissions' });
          return;
        }
        updateData.permissions = permissions as Permission[];
      }

      if (expiresAt !== undefined) {
        if (expiresAt && new Date(expiresAt) <= new Date()) {
          res.status(400).json({ error: 'Expiration date must be in the future' });
          return;
        }
        updateData.expiresAt = expiresAt ? new Date(expiresAt) : undefined;
      }

      if (password !== undefined) {
        if (password && password.length < 6) {
          res.status(400).json({ error: 'Password must be at least 6 characters long' });
          return;
        }
        updateData.password = password || undefined;
      }

      if (allowedIps !== undefined) {
        if (allowedIps && Array.isArray(allowedIps)) {
          for (const ip of allowedIps) {
            if (!NoteShareModel.validateIpAddress(ip)) {
              res.status(400).json({ error: `Invalid IP address: ${ip}` });
              return;
            }
          }
        }
        updateData.allowedIps = allowedIps;
      }

      if (watermark !== undefined) {
        updateData.watermark = Boolean(watermark);
      }

      const updatedShare = await NoteShareRepository.update(shareId, updateData);
      const sanitizedShare = NoteShareModel.sanitizeShareForResponse(updatedShare);

      // Generate share URL (use frontend URL, not backend URL)
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const shareUrl = NoteShareModel.getShareUrl(frontendUrl, updatedShare.shareToken);

      res.json({
        ...sanitizedShare,
        shareUrl
      });
    } catch (error) {
      console.error('Error updating share:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // DELETE /api/shares/:id - Delete a share
  static async deleteShare(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const shareId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if share exists and user has access
      const existingShare = await NoteShareRepository.findById(shareId);
      if (!existingShare) {
        res.status(404).json({ error: 'Share not found' });
        return;
      }

      if (existingShare.createdBy !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      await NoteShareRepository.delete(shareId);

      res.json({ message: 'Share deleted successfully' });
    } catch (error) {
      console.error('Error deleting share:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // GET /api/shared/:token - Access a shared note
  static async accessSharedNote(req: Request, res: Response): Promise<void> {
    try {
      const shareToken = req.params.token;
      const password = req.body.password || req.query.password;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent');
      const userId = req.user?.id; // Optional - user might not be logged in

      // Find share by token
      const share = await NoteShareRepository.findByToken(shareToken);
      if (!share) {
        res.status(404).json({ error: 'Shared note not found' });
        return;
      }

      // Check access permissions
      const accessCheck = NoteShareModel.canAccess(share, ipAddress, password);
      if (!accessCheck.canAccess) {
        res.status(403).json({ error: accessCheck.reason });
        return;
      }

      // Get the note
      const note = await NoteRepository.findById(share.noteId);
      if (!note) {
        res.status(404).json({ error: 'Note not found' });
        return;
      }

      // Log access
      try {
        await NoteShareRepository.logAccess(share.id, ipAddress, userAgent, userId);
      } catch (logError) {
        console.error('Failed to log share access:', logError);
        // Continue - logging failure shouldn't block access
      }

      // Prepare response based on permissions
      const response: any = {
        note: {
          id: note.id,
          title: note.title,
          content: note.content,
          noteType: note.noteType,
          metadata: note.metadata,
          createdAt: note.createdAt,
          updatedAt: note.updatedAt
        },
        share: {
          accessLevel: share.accessLevel,
          permissions: share.permissions,
          watermark: share.watermark,
          expiresAt: share.expiresAt
        }
      };

      // Add watermark if enabled
      if (share.watermark) {
        response.watermark = {
          text: `Shared via ${req.get('host')}`,
          timestamp: new Date().toISOString()
        };
      }

      res.json(response);
    } catch (error) {
      console.error('Error accessing shared note:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // GET /api/shares/:id/access-logs - Get access logs for a share
  static async getShareAccessLogs(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const shareId = req.params.id;
      const limit = Math.min(parseInt(req.query.limit as string) || 100, 1000);

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Check if share exists and user has access
      const share = await NoteShareRepository.findById(shareId);
      if (!share) {
        res.status(404).json({ error: 'Share not found' });
        return;
      }

      if (share.createdBy !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const accessLogs = await NoteShareRepository.getAccessLogs(shareId, limit);

      res.json({ accessLogs });
    } catch (error) {
      console.error('Error fetching share access logs:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // GET /api/shares - Get user's shares with filters
  static async getUserShares(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const filters: ShareFilters = {
        createdBy: userId,
        accessLevel: req.query.accessLevel as AccessLevel,
        isExpired: req.query.isExpired === 'true' ? true : req.query.isExpired === 'false' ? false : undefined
      };

      const shares = await NoteShareRepository.findWithFilters(filters);
      const sanitizedShares = shares.map(share => NoteShareModel.sanitizeShareForResponse(share));

      // Add share URLs and expiration status (use frontend URL, not backend URL)
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const sharesWithUrls = sanitizedShares.map(share => ({
        ...share,
        shareUrl: NoteShareModel.getShareUrl(frontendUrl, share.shareToken),
        isExpired: NoteShareModel.isShareExpired(share as any)
      }));

      res.json({ shares: sharesWithUrls });
    } catch (error) {
      console.error('Error fetching user shares:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // POST /api/shares/cleanup - Clean up expired shares (admin only)
  static async cleanupExpiredShares(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // TODO: Add admin check when admin roles are implemented
      // For now, any authenticated user can trigger cleanup

      const deletedCount = await NoteShareRepository.cleanupExpiredShares();

      res.json({ 
        message: 'Expired shares cleaned up successfully',
        deletedCount 
      });
    } catch (error) {
      console.error('Error cleaning up expired shares:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
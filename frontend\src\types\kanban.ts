export interface KanbanCard {
  id: string
  title: string
  description?: string
  position: number
  columnId: string
  dueDate?: string
  labels: Kan<PERSON><PERSON>abe<PERSON>[]
  assignees: Kanban<PERSON>signee[]
  priority: 'low' | 'medium' | 'high' | 'urgent'
  checklist: ChecklistItem[]
  attachments: Attachment[]
  comments: Comment[]
  color?: string // Custom card color
  createdAt: string
  updatedAt: string
}

export interface KanbanLabel {
  id: string
  name: string
  color: string
}

export interface KanbanAssignee {
  id: string
  name: string
  email: string
  avatar?: string
}

export interface ChecklistItem {
  id: string
  text: string
  completed: boolean
  createdAt: string
}

export interface Attachment {
  id: string
  name: string
  url: string
  type: string
  size: number
  uploadedAt: string
}

export interface Comment {
  id: string
  text: string
  authorId: string
  authorName: string
  createdAt: string
  updatedAt: string
}

export interface KanbanColumn {
  id: string
  title: string
  position: number
  boardId: string
  cards: KanbanCard[]
  color?: string // Custom column color
  createdAt: string
  updatedAt: string
}

export interface KanbanBoard {
  id: string
  title: string
  description?: string
  columns: KanbanColumn[]
  labels: KanbanLabel[]
  members: Kanban<PERSON>signee[]
  settings: BoardSettings
  shareSettings: BoardShareSettings
  template?: BoardTemplate
  createdAt: string
  updatedAt: string
}

export interface BoardSettings {
  allowComments: boolean
  allowAttachments: boolean
  cardCoverImages: boolean
  votingEnabled: boolean
  dueDateReminders: boolean
  backgroundColor: string
  backgroundImage?: string
}

export interface BoardShareSettings {
  isPublic: boolean
  allowedUsers: string[]
  permissions: {
    [userId: string]: 'view' | 'edit' | 'admin'
  }
  shareLink?: string
  linkExpiration?: string
}

export interface BoardTemplate {
  id: string
  name: string
  description: string
  columns: Omit<KanbanColumn, 'id' | 'boardId' | 'cards' | 'createdAt' | 'updatedAt'>[]
  labels: KanbanLabel[]
  isPublic: boolean
  category?: string
}

export interface DragEvent {
  cardId: string
  sourceColumnId: string
  targetColumnId: string
  newPosition: number
}

export interface ColumnDragEvent {
  columnId: string
  newPosition: number
}

export interface KanbanFilters {
  search?: string
  labels?: string[]
  assignees?: string[]
  dueDate?: 'overdue' | 'today' | 'week' | 'month' | 'none'
  priority?: ('low' | 'medium' | 'high' | 'urgent')[]
  hasAttachments?: boolean
  hasComments?: boolean
}

export interface CardSearchResult {
  card: KanbanCard
  columnTitle: string
  matchType: 'title' | 'description' | 'comment' | 'checklist'
  matchText: string
}
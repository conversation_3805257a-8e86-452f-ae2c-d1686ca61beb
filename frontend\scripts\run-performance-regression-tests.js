#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Run performance regression tests with comprehensive reporting
 */
async function runPerformanceRegressionTests(options = {}) {
  console.log('🧪 Starting Performance Regression Test Suite\n');

  const results = {
    regressionTests: false,
    bundleSizeCheck: false,
    initTimeValidation: false,
    webVitalsValidation: false,
    errors: [],
  };

  try {
    // Step 1: Run performance regression tests
    console.log('🔍 Running performance regression tests...');
    try {
      execSync(
        'npm run test:unit -- --run src/__tests__/performance/performance-regression.test.ts',
        {
          stdio: 'inherit',
          cwd: path.join(__dirname, '..'),
        }
      );
      results.regressionTests = true;
      console.log('✅ Performance regression tests passed\n');
    } catch (error) {
      results.errors.push('Performance regression tests failed');
      console.log('❌ Performance regression tests failed\n');
    }

    // Step 2: Validate bundle sizes
    console.log('📦 Validating bundle sizes...');
    try {
      execSync('node scripts/check-bundle-size.js', {
        stdio: 'inherit',
        cwd: path.join(__dirname, '..'),
      });
      results.bundleSizeCheck = true;
      console.log('✅ Bundle size validation passed\n');
    } catch (error) {
      results.errors.push('Bundle size validation failed');
      console.log('❌ Bundle size validation failed\n');
    }

    // Step 3: Check for performance regressions
    console.log('📊 Checking for performance regressions...');
    try {
      execSync('node scripts/performance-regression-check.js --no-fail', {
        stdio: 'inherit',
        cwd: path.join(__dirname, '..'),
      });
      results.initTimeValidation = true;
      console.log('✅ Performance regression check passed\n');
    } catch (error) {
      results.errors.push('Performance regression check failed');
      console.log('❌ Performance regression check failed\n');
    }

    // Step 4: Generate comprehensive report
    generateRegressionReport(results, options);

    return results;
  } catch (error) {
    console.error('❌ Performance regression test suite failed:', error.message);
    results.errors.push(error.message);
    return results;
  }
}

/**
 * Generate comprehensive regression test report
 */
function generateRegressionReport(results, options) {
  console.log('📋 Performance Regression Test Summary\n');
  console.log('='.repeat(60));

  const tests = [
    { name: 'Performance Regression Tests', status: results.regressionTests },
    { name: 'Bundle Size Validation', status: results.bundleSizeCheck },
    { name: 'Initialization Time Check', status: results.initTimeValidation },
    { name: 'Performance Regression Check', status: results.webVitalsValidation },
  ];

  tests.forEach(test => {
    const status = test.status ? '✅ PASSED' : '❌ FAILED';
    console.log(`${test.name.padEnd(35)} ${status}`);
  });

  console.log('='.repeat(60));

  const passedTests = tests.filter(test => test.status).length;
  const totalTests = tests.length;

  if (passedTests === totalTests) {
    console.log(`🎉 All ${totalTests} performance regression tests PASSED!`);
  } else {
    console.log(`⚠️  ${passedTests}/${totalTests} performance regression tests passed`);

    if (results.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
  }

  // Generate recommendations
  console.log('\n💡 Performance Recommendations:');

  if (!results.regressionTests) {
    console.log('   • Review performance regression test failures');
    console.log('   • Check for new performance bottlenecks');
  }

  if (!results.bundleSizeCheck) {
    console.log('   • Optimize bundle sizes to meet budget requirements');
    console.log('   • Consider code splitting and lazy loading');
  }

  if (!results.initTimeValidation) {
    console.log('   • Investigate initialization time regressions');
    console.log('   • Verify parallel store initialization is working');
  }

  if (passedTests === totalTests) {
    console.log('   • Continue monitoring performance metrics');
    console.log('   • Consider setting up automated performance alerts');
  }
}

/**
 * CLI interface
 */
function main() {
  const args = process.argv.slice(2);
  const options = {
    updateBaseline: args.includes('--update-baseline') || args.includes('-u'),
    failOnRegression: !args.includes('--no-fail'),
    verbose: args.includes('--verbose') || args.includes('-v'),
  };

  if (args.includes('--help') || args.includes('-h')) {
    console.log('Performance Regression Test Suite\n');
    console.log('Usage: node run-performance-regression-tests.js [options]\n');
    console.log('Options:');
    console.log('  --update-baseline, -u    Update performance baselines');
    console.log("  --no-fail               Don't exit with error code on failures");
    console.log('  --verbose, -v           Enable verbose output');
    console.log('  --help, -h              Show this help message\n');
    console.log('Examples:');
    console.log('  node run-performance-regression-tests.js');
    console.log('  node run-performance-regression-tests.js --update-baseline');
    console.log('  node run-performance-regression-tests.js --no-fail --verbose');
    return;
  }

  runPerformanceRegressionTests(options)
    .then(results => {
      const hasErrors = results.errors.length > 0;
      const allTestsPassed =
        results.regressionTests && results.bundleSizeCheck && results.initTimeValidation;

      if ((hasErrors || !allTestsPassed) && options.failOnRegression) {
        process.exit(1);
      } else {
        process.exit(0);
      }
    })
    .catch(error => {
      console.error('❌ Performance regression test suite crashed:', error);
      process.exit(1);
    });
}

// Run if called directly
if (
  import.meta.url === `file://${process.argv[1]}` ||
  process.argv[1].endsWith('run-performance-regression-tests.js')
) {
  main();
}

export { runPerformanceRegressionTests };
